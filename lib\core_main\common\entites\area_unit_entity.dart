class AreaUnitEntity{
  final String? id;
  final bool? isDeleted;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final String? unit;
  final double? conversionFactor;

  AreaUnitEntity({
    this.id,
    this.isDeleted,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.unit,
    this.conversionFactor,
  });

  AreaUnitEntity copyWith({
    String? id,
    bool? isDeleted,
    DateTime? createdOn,
    String? createdBy,
    DateTime? lastModifiedOn,
    String? lastModifiedBy,
    String? unit,
    double? conversionFactor,
  }) {
    return AreaUnitEntity(
      id: id ?? this.id,
      isDeleted: isDeleted ?? this.isDeleted,
      createdOn: createdOn ?? this.createdOn,
      createdBy: createdBy ?? this.createdBy,
      lastModifiedOn: lastModifiedOn ?? this.lastModifiedOn,
      lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      unit: unit ?? this.unit,
      conversionFactor: conversionFactor ?? this.conversionFactor,
    );
  }
}