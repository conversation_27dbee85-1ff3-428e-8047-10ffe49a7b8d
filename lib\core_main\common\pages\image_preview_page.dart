import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/cubit/image_preview_cubit.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../di/injection_container.dart';

class ImagePreviewDialogPage extends StatefulWidget {
  final String fileUrl;

  const ImagePreviewDialogPage({Key? key, required this.fileUrl}) : super(key: key);

  @override
  State<ImagePreviewDialogPage> createState() => _ImagePreviewDialogPageState();
}

class _ImagePreviewDialogPageState extends State<ImagePreviewDialogPage> {
  late final WebViewController controller;

  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(
        Uri.parse(widget.fileUrl),
      ).onError((error, stackTrace) {});
    getIt<ImagePreviewCubit>().previewImage(controller);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) => getIt<ImagePreviewCubit>(),
        child: BlocBuilder<ImagePreviewCubit, bool>(
          builder: (context, state) {
            return Dialog(
              backgroundColor: ColorPalette.black,
              insetPadding: EdgeInsets.zero,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: Column(
                  mainAxisAlignment: state ? MainAxisAlignment.center : MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    !state
                        ? Container(
                            decoration: const BoxDecoration(
                              color: ColorPalette.darkToneInk,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(20.0),
                                bottomRight: Radius.circular(20.0),
                              ),
                            ),
                            height: 90,
                            child: Stack(
                              alignment: Alignment.bottomLeft,
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(bottom: 10, left: 24, right: 15),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context);
                                        },
                                        child: SvgPicture.asset(ImageResources.iconBack),
                                      ),
                                      Text(
                                        "Document",
                                        style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.primary),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          shareUrl();
                                        },
                                        child: SvgPicture.asset(ImageResources.iconFilledShare),
                                        // SvgPicture.asset('assets/images/ic_filled_share.svg'),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  left: 10,
                                  child: SvgPicture.asset(
                                    ImageResources.imageFilePreviewPattern, // Replace with your image path
                                  ),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox(),
                    state
                        ? const Center(child: CircularProgressIndicator(color: ColorPalette.primaryGreen))
                        : Expanded(
                            child: WebViewWidget(
                              controller: controller,
                            ),
                          ),
                  ],
                ),
              ),
            );
          },
        ));
  }

  Future<void> shareUrl() async {
    Share.share(
      widget.fileUrl,
      subject: 'Lead-rat Document File URL',
    );
  }
}
