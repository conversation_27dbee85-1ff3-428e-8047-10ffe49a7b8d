{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ab963d61a3aa12729c495f176bb49a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98673c53734be68ab863cba48661191521", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f38dc71caa1463fba11d96b70b45f63", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989a4ebeb14de48f010b4d01efb3a973c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f38dc71caa1463fba11d96b70b45f63", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98626603c0dfae570c6a77d2bb642959a8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e14b906110d97926ca22d0292b050c63", "guid": "bfdfe7dc352907fc980b868725387e98945ee2f08004dec6429da2f465280a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f1a451e736f324b99056c806aca56e6", "guid": "bfdfe7dc352907fc980b868725387e98bc5a112a4e2f66df684f5177e763de08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98737676e540465280cc0ee3829a690c0f", "guid": "bfdfe7dc352907fc980b868725387e982da1895c29e6a8c31a7159fffff4fb47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e7783aad9878eb93c415636af46e34", "guid": "bfdfe7dc352907fc980b868725387e987e1cd18649abdf3f386c7a240327c7a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809681311550538cb1427816afd08ed1c", "guid": "bfdfe7dc352907fc980b868725387e9848782b986cc07a814a5da7025a1b88c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981860bc5640a90abacc845cbebde57a84", "guid": "bfdfe7dc352907fc980b868725387e98b83f744bcf2da0239e30fd48431c2b32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af27539367974260f926c8ae8b4293f6", "guid": "bfdfe7dc352907fc980b868725387e980bb23dc46f85b89a21bd82131a191836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e27496106459e4071ad3feef82ce8849", "guid": "bfdfe7dc352907fc980b868725387e98280f8b2633c600b8389f6d467da47e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ef91cc16c1b78de9e800a4787c1708", "guid": "bfdfe7dc352907fc980b868725387e9877f90d6984caaa812ba303db3e347a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b35a5869cb97912baa883ca809e7b1e", "guid": "bfdfe7dc352907fc980b868725387e98a606c099c79e15f7dcf5111d07c7b6d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9271671f35c20aa17b8a34a01054106", "guid": "bfdfe7dc352907fc980b868725387e9839d75d179adbf3ea586811ac5b9a59ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98387e5ffe8a23438a86ee076675159cda", "guid": "bfdfe7dc352907fc980b868725387e98fff0fa4e48c7c1bcb64d316b510b54bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d096b5b52dc167cf6a9bd1c30070f9", "guid": "bfdfe7dc352907fc980b868725387e9851edd3284080f021d8f3d3168cec74dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b125aa5ff00c4ceb3667c8c3c5e70e", "guid": "bfdfe7dc352907fc980b868725387e9874c538a12b60bae178c3218bd896587a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a45a5437449f98d262b59b024d1b71", "guid": "bfdfe7dc352907fc980b868725387e98291b9179f46d9c0e4338c09fdb04b029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c7b02e0cc7cb8e0148e58b5e76d5e7", "guid": "bfdfe7dc352907fc980b868725387e98f92ba49130e07dec02a62ac15986e0dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed4ac8cf132f6fef1217117a5afc823b", "guid": "bfdfe7dc352907fc980b868725387e982b3850adb2bbdf162c672539745809c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4b31cea5c33377eac307b2490530950", "guid": "bfdfe7dc352907fc980b868725387e980a8d5cc16adb968b0015eb98bcdf8149", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808280979ce24c531902c43b3d0acb0e6", "guid": "bfdfe7dc352907fc980b868725387e98a2aeec2cc9cc2f31d50bab9f1f518d17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0aa2f14b60944b8ee3c6902d30fd04b", "guid": "bfdfe7dc352907fc980b868725387e9834f0ff7b9854d000c72b68a383c90885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ae97a486031753859a94f8b9e57c3f", "guid": "bfdfe7dc352907fc980b868725387e98f072d566cafbbe91167fe8b16fcce34c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824803744ace48eb60cdff1f061f394ef", "guid": "bfdfe7dc352907fc980b868725387e9853dd972d2475900a1302fd9a0ce5e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef534d438cd86e87c5815dc795302888", "guid": "bfdfe7dc352907fc980b868725387e9801253a138b5e86640700118ddfc94748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b75556c0cacaed9e1d2c8b55a4c753", "guid": "bfdfe7dc352907fc980b868725387e986044984b0f0d118b73e40d7e93f0a8cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e53db7d5a3f6db6ce13985ed9e44bd0", "guid": "bfdfe7dc352907fc980b868725387e9887bf5dbce3f34855ca04f16079807bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57d8e7ac87fd0110b6c8e553ee10487", "guid": "bfdfe7dc352907fc980b868725387e989638e81ddd1cfa7276c640625bf6d7df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcec2f067b4f0ac7475c7ad8bdc85120", "guid": "bfdfe7dc352907fc980b868725387e981e260e0df105540d7dc2638d7c4857b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4279430b6c91052986b74c42e9028df", "guid": "bfdfe7dc352907fc980b868725387e98644b5221a3acdec4978a1aef2be9be40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6ab935d41fd825304f7e4f967609725", "guid": "bfdfe7dc352907fc980b868725387e98a6332d462b83fcc674111aa9ff4db7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc1fa4ea3b61bb9f03f886edbbfb4b6f", "guid": "bfdfe7dc352907fc980b868725387e9883bf11630fb77eb198cf75433eba516d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98476920359b45e733a2b9ddc7e23257ff", "guid": "bfdfe7dc352907fc980b868725387e98d31277f1b1f78e5ddf621685029ef93e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dec2c8850654399ff4998970740989de", "guid": "bfdfe7dc352907fc980b868725387e98ee7f807eb6537b94bf5f6a4827cb1e44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f46e3ab5fed1fe8c0b18a16f08fbf8d", "guid": "bfdfe7dc352907fc980b868725387e987523bc78defa8fb6dd16c9828373e66c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862eba76d09b7cc40c15640d83d759597", "guid": "bfdfe7dc352907fc980b868725387e98ba2276ae8aeeb9dc7035842820b5b8a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c26f4f7dba869b86bde19c91abc46bcc", "guid": "bfdfe7dc352907fc980b868725387e98ed62473c11e2090fc59e0c867fd19d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d680fd91438e61ca1e242ef461229f16", "guid": "bfdfe7dc352907fc980b868725387e98fe6ec8eed7b1a0908d1bebf7e5607b2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fc3a4d43d445a098c69878debcfadd", "guid": "bfdfe7dc352907fc980b868725387e98bf2f87b91e0f0b0a3ac9acbabf6bcf03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415a3c68fc88ce53fc39b0c81214c916", "guid": "bfdfe7dc352907fc980b868725387e98878aa82d1485e3967d8181e392e3ce24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd754d5c5ec35bbbbdb93a637385ec9b", "guid": "bfdfe7dc352907fc980b868725387e98c035845103ff8d8e240c19de1520e055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb60e862af3d71d011f4679c020a65b9", "guid": "bfdfe7dc352907fc980b868725387e980f13715921e3d171c0f40aee409acb7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c4c6921586065d174670754e1db690", "guid": "bfdfe7dc352907fc980b868725387e98f2d33cea20b46bef86e8985ccdba398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbfc119e4043c185bb68260838b58cad", "guid": "bfdfe7dc352907fc980b868725387e9838ff3c0708e829b20630f2c6d700630f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98577237020bbe4df2664293ed9629717a", "guid": "bfdfe7dc352907fc980b868725387e98e0580e20f24f87aee39d305de3d34125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e183fc982deb010af96e0c14d15fec67", "guid": "bfdfe7dc352907fc980b868725387e98923ca25133d9bbd1da120606b623768b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b52226223cc4884d35429b502c8487", "guid": "bfdfe7dc352907fc980b868725387e987fcff1a78fb7cc6284f843b5c7171f00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d76974a88996319e5f310a30b9616c", "guid": "bfdfe7dc352907fc980b868725387e986118dd9454fae5d30575ec43a2255402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419c2bc90e75e8283d1d91fbd117f5ed", "guid": "bfdfe7dc352907fc980b868725387e989ff9bfa814fa8eb24b0bbf3e5fd40ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802e2afc032768c99c51b13765bbb514d", "guid": "bfdfe7dc352907fc980b868725387e98503ac8084e5d2192f1179045472e5e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f36702e14cb0b84579106662feebf9", "guid": "bfdfe7dc352907fc980b868725387e98579583a2f70d8a539aaae51dbff2ac0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2d724969f87dec5e04929606e04b121", "guid": "bfdfe7dc352907fc980b868725387e9835e68dd4a9f8a55dac1caee3a4b87e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b501e4ad75c8bd7afde9ac202322bda", "guid": "bfdfe7dc352907fc980b868725387e983b5204394b84235fbc19bce93017bf6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c5bfbaf3a5e4920de8f27e097b0387", "guid": "bfdfe7dc352907fc980b868725387e9879561bc45e0f7720e3149f14cd1b65aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8bdc45c45cf90d874cc0d587609e61", "guid": "bfdfe7dc352907fc980b868725387e98de6c550cdede095b83ec76ca43ca1784"}], "guid": "bfdfe7dc352907fc980b868725387e989ff6aa6fe76f4368b35718f14c2bca7b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3be15ed0375bcce92b7d9a751cf0589", "guid": "bfdfe7dc352907fc980b868725387e98669a21f6c68a3ef65ce292f4bd547aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5359e360826f5c9690befad71f56218", "guid": "bfdfe7dc352907fc980b868725387e9859813a72f9487eb62ffe2f69d7c499cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813bc00f594fbbff1be6d70b2246c88a6", "guid": "bfdfe7dc352907fc980b868725387e981d1fae4cb3d5d9f0cc575f8f4597caa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d0f2a30edb16b3c7e922488903c710", "guid": "bfdfe7dc352907fc980b868725387e984274261069d7380f63f6d1a1c1a13aee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e298e88ce700604acbac8ceb9f4cb47", "guid": "bfdfe7dc352907fc980b868725387e989103b024f47905e967ee5c73fcd8f9ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41d6b9a4d3dfaf717fbe132092a56a3", "guid": "bfdfe7dc352907fc980b868725387e98d54207be2ad778ee590d1e85e66e62ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de6de9a51801bff13b9c96e57d5977a0", "guid": "bfdfe7dc352907fc980b868725387e98ec12b14eab043d641699f986beab7ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd8e771a078e94cfe04b89893dd6bed4", "guid": "bfdfe7dc352907fc980b868725387e9828964e88abccb1fb93b88ce76c6d4f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828afd5274467dac22962230bf39644f9", "guid": "bfdfe7dc352907fc980b868725387e983e6b272a090ad284ab8a9b35cd30f024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de97dabe5b964483efb6af7ab426ec0", "guid": "bfdfe7dc352907fc980b868725387e98ac6abf14c5b67d15f08ae2caf7ed52be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4fcd549fbad58486936ce286aeb61ff", "guid": "bfdfe7dc352907fc980b868725387e982004ab0421250a3e4d7d942cb74f46e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8353afd6ff05552ae07f97755b51545", "guid": "bfdfe7dc352907fc980b868725387e98476250ecfdf95773128ba0a3c0a5aa9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e02cbebb074bd003b677f5b5022dda", "guid": "bfdfe7dc352907fc980b868725387e98ab31bf79597a0316ffea733f09cbe5b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f70fab16517fdaaf1bcc0ab2fafbd40", "guid": "bfdfe7dc352907fc980b868725387e98726ba6b586976af90ba6972b37c50a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d550da7b6cc72a730286b397e2c09734", "guid": "bfdfe7dc352907fc980b868725387e98a509ac78f87098170819a96decb1e156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866160b929ac04cc5d5a4ec4f32784b51", "guid": "bfdfe7dc352907fc980b868725387e985987046832c84c254bbe31d48fcea292"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ee45be468bc6b2d76c1af29ab93e38", "guid": "bfdfe7dc352907fc980b868725387e98ef2b485170780703c31e2e5dcfd5c290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c2d2962e59eafd2fee16e289ebd220", "guid": "bfdfe7dc352907fc980b868725387e98a4722eeb8236466ef64dbd4e1630768e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ee13585bd2c08bf78da4de58f1b719c", "guid": "bfdfe7dc352907fc980b868725387e989d4d3015d381d6012ad528e6bc9b1877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a6b700ecfca269865dbcec5cc37a9bb", "guid": "bfdfe7dc352907fc980b868725387e9855e70900360f30cbee7c8b84d954fecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edcec5d605952c89aba3a850e6e46e18", "guid": "bfdfe7dc352907fc980b868725387e9851ae1a1fd483f19ab82c51b73de8d8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0cf4c68ea6a0409968f1fc49e88dae8", "guid": "bfdfe7dc352907fc980b868725387e982400d169019ad9ea50c70eebfbe50f91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810204d961443a78c9c5711deec0a2de9", "guid": "bfdfe7dc352907fc980b868725387e982fa18dd0bac163748154f5c9920cea9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3e882ad5d85e352b35f9f1317a4ee5", "guid": "bfdfe7dc352907fc980b868725387e98ebc02ed2c9f25106c6d07c71e9d48ed7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ebbb9d734a3eb16a748c5507dfa99f", "guid": "bfdfe7dc352907fc980b868725387e987ba701c41bc9b5e2b69c9a6af840479a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e266d81789cc388f547ef3254af7c6bc", "guid": "bfdfe7dc352907fc980b868725387e98c76839bd2606502ce10fda678dd15153"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f4f60bae324f0e61e4d2c0b398470e8", "guid": "bfdfe7dc352907fc980b868725387e98e06e646c507398e8f5e965fd47ba7463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980155574eb73ec64f7c3d88d2bf8cbec4", "guid": "bfdfe7dc352907fc980b868725387e98bf8a8187a2fc6e0ca3d27e82f46b9f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98517e9fede38f7c5ef436cc716c41d657", "guid": "bfdfe7dc352907fc980b868725387e986a5d6d396e29abdbcf9fa650d7b31e26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844bdff3c764cf4ecb018161333cf6db1", "guid": "bfdfe7dc352907fc980b868725387e98a63455b4c58f808c3c7758edf4321a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f856a089193e40f3e0020ce764899c1", "guid": "bfdfe7dc352907fc980b868725387e9826dcd187eff72d7293a60218455329a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ed363afd372eac86b13d6123f104a1", "guid": "bfdfe7dc352907fc980b868725387e98c87f605b3675ab00bb0a300650d23981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ace8ccd7719acb9822115f1cddd67d0", "guid": "bfdfe7dc352907fc980b868725387e98603e216dacfbe3593206aca98c3d4a2a"}], "guid": "bfdfe7dc352907fc980b868725387e984667b4903feadd43777339e2bf02708d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9871468e81f5c9d94643fc09871ffb5744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e98d58b4c5c605f0b46d6bd37c7c0250543"}], "guid": "bfdfe7dc352907fc980b868725387e9838e1c2eca5a4b8f86429094e3a222285", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981a01c21995a2b93258d4ff9bada61d62", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98f9b642ad0e9bd92bc1394f44aa8b445d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}