import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/remote/custom_amenities_and_attributes_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class CustomAmenitiesAndAttributesRemoteDataSourceImpl extends LeadratRestService implements CustomAmenitiesAndAttributesRemoteDataSource {
  @override
  Future<List<CustomAmenitiesModel?>?> getCustomAmenities({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(CustomAmenitiesAndAttributes.getAllCustomAmenities, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<List<CustomAmenitiesModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<CustomAmenitiesModel>?>.fromJson(json, (data) => fromJsonList(data, CustomAmenitiesModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<CustomAttributesModel?>?> getCustomAttributes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(CustomAmenitiesAndAttributes.getAllCustomAttributes, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<CustomAttributesModel?, String>>(
      restRequest,
      (json) => PagedResponse<CustomAttributesModel?, String>.fromJson(json, (data) => fromJsonObject(data, CustomAttributesModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<List<String?>?> getCustomAmenityCategories({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(CustomAmenitiesAndAttributes.getAllCustomAmenityCategories, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<List<String?>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String?>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }
}
