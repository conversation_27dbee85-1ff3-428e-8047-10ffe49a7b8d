import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'property_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.propertyTypeEnumTypeId)
@JsonEnum(valueField: 'index')
enum PropertyType {
  @HiveField(0)
  residential("Residential", PropertyTypeIdConstants.residential),
  @HiveField(1)
  commercial("Commercial", PropertyTypeIdConstants.commercial),
  @HiveField(2)
  agricultural("Agricultural", PropertyTypeIdConstants.agricultural);

  final String description;
  final String baseId;

  const PropertyType(this.description, this.baseId);
}
