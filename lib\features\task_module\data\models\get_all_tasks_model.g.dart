// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_all_tasks_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetAllTasksModel _$GetAllTasksModelFromJson(Map<String, dynamic> json) =>
    GetAllTasksModel(
      id: json['id'] as String?,
      title: json['title'] as String?,
      notes: json['notes'] as String?,
      priority: $enumDecodeNullable(_$TaskPriorityEnumMap, json['priority']),
      isMarkedDone: json['isMarkedDone'] as bool?,
      isRepeated: json['isRepeated'] as bool?,
      scheduledDateTime: json['scheduledDateTime'] == null
          ? null
          : DateTime.parse(json['scheduledDateTime'] as String),
      lastModifiedUser: json['lastModifiedUser'] == null
          ? null
          : UserDetailsModel.fromJson(
              json['lastModifiedUser'] as Map<String, dynamic>),
      assignedFrom: json['assignedFrom'] == null
          ? null
          : UserDetailsModel.fromJson(
              json['assignedFrom'] as Map<String, dynamic>),
      assignedUsers: (json['assignedUsers'] as List<dynamic>?)
          ?.map((e) => UserDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      status: (json['status'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GetAllTasksModelToJson(GetAllTasksModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.notes case final value?) 'notes': value,
      if (_$TaskPriorityEnumMap[instance.priority] case final value?)
        'priority': value,
      if (instance.isMarkedDone case final value?) 'isMarkedDone': value,
      if (instance.isRepeated case final value?) 'isRepeated': value,
      if (instance.scheduledDateTime?.toIso8601String() case final value?)
        'scheduledDateTime': value,
      if (instance.lastModifiedUser?.toJson() case final value?)
        'lastModifiedUser': value,
      if (instance.assignedFrom?.toJson() case final value?)
        'assignedFrom': value,
      if (instance.assignedUsers?.map((e) => e.toJson()).toList()
          case final value?)
        'assignedUsers': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
      if (instance.status case final value?) 'status': value,
    };

const _$TaskPriorityEnumMap = {
  TaskPriority.low: 0,
  TaskPriority.medium: 1,
  TaskPriority.critical: 2,
  TaskPriority.high: 3,
};

TodosCountModel _$TodosCountModelFromJson(Map<String, dynamic> json) =>
    TodosCountModel(
      todaysTodosCount: (json['todaysTodosCount'] as num?)?.toInt(),
      upcomingTodosCount: (json['upcomingTodosCount'] as num?)?.toInt(),
      completedTodosCount: (json['completedTodosCount'] as num?)?.toInt(),
      overdueTodosCount: (json['overdueTodosCount'] as num?)?.toInt(),
      allTodosCount: (json['allTodosCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TodosCountModelToJson(TodosCountModel instance) =>
    <String, dynamic>{
      if (instance.todaysTodosCount case final value?)
        'todaysTodosCount': value,
      if (instance.upcomingTodosCount case final value?)
        'upcomingTodosCount': value,
      if (instance.completedTodosCount case final value?)
        'completedTodosCount': value,
      if (instance.overdueTodosCount case final value?)
        'overdueTodosCount': value,
      if (instance.allTodosCount case final value?) 'allTodosCount': value,
    };
