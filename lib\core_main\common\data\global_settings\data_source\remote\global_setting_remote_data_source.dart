import 'package:leadrat/core_main/common/data/global_settings/models/country_info_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';

abstract class GlobalSettingRemoteDataSource {
  Future<GlobalSettingModel?> getGlobalSettingModel({Duration timeout = const Duration(seconds: 60)});

  Future<BaseCountryInfoModel?> getCountriesInfo({Duration timeout = const Duration(seconds: 60)});
}
