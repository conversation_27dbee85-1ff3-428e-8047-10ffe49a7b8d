// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'module_name.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppModuleNameAdapter extends TypeAdapter<AppModuleName> {
  @override
  final int typeId = 58;

  @override
  AppModuleName read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AppModuleName.lead;
      case 1:
        return AppModuleName.todo;
      case 2:
        return AppModuleName.integration;
      case 3:
        return AppModuleName.user;
      case 4:
        return AppModuleName.profile;
      case 5:
        return AppModuleName.project;
      case 6:
        return AppModuleName.property;
      case 7:
        return AppModuleName.team;
      case 8:
        return AppModuleName.email;
      default:
        return AppModuleName.lead;
    }
  }

  @override
  void write(BinaryWriter writer, AppModuleName obj) {
    switch (obj) {
      case AppModuleName.lead:
        writer.writeByte(0);
        break;
      case AppModuleName.todo:
        writer.writeByte(1);
        break;
      case AppModuleName.integration:
        writer.writeByte(2);
        break;
      case AppModuleName.user:
        writer.writeByte(3);
        break;
      case AppModuleName.profile:
        writer.writeByte(4);
        break;
      case AppModuleName.project:
        writer.writeByte(5);
        break;
      case AppModuleName.property:
        writer.writeByte(6);
        break;
      case AppModuleName.team:
        writer.writeByte(7);
        break;
      case AppModuleName.email:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppModuleNameAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
