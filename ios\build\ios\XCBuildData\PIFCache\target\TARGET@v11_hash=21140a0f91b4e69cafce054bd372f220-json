{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985446d136fd964e63d953c3f4456fd99c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d6612e8a577a93fe722415f6a91c59b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0b1aca7b1267a46e07c6e7210ada69a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c19918d8a70939f76cc39a6d6e84c408", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0b1aca7b1267a46e07c6e7210ada69a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b4434aadf5d4f4aec67b65d845bfece", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6f487665f2d43e92b70c985cef5b380", "guid": "bfdfe7dc352907fc980b868725387e9877932dafa96667db719261d186e3ed62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca0f60f45feee9e8a3be2bd392e042f7", "guid": "bfdfe7dc352907fc980b868725387e98b344fdece9938cb54b2882b9dec259d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ec3df907584a9decab461f283911e0", "guid": "bfdfe7dc352907fc980b868725387e982417c850d04044d58afc2008f945ae1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980880b618db0f6a9f18aa409fb61debaa", "guid": "bfdfe7dc352907fc980b868725387e983bd13b80108570e39628666ce5dcd752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0db46110d76ecbceecd45c3d5d298ef", "guid": "bfdfe7dc352907fc980b868725387e982bbab05b7f96bee7aa8e2eefe5d6f5f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f9358ed08104c2a276bf64ce98b268", "guid": "bfdfe7dc352907fc980b868725387e98f7981f9d148d3227fdbd73afa0aef190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba68478faae700d1143753021d70868c", "guid": "bfdfe7dc352907fc980b868725387e98b2c964c41265fa36df5aab0f0fba31be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a665b6721075a8c9d96985c00189e4a", "guid": "bfdfe7dc352907fc980b868725387e982fb42c00e21e67210a915110c5e26519", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1b8569c2d9d0470f131263c0ff4473", "guid": "bfdfe7dc352907fc980b868725387e9800be66d700f47498efe14ff4cf5ba2a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517681f9015115d39c0f0e772aa8bc", "guid": "bfdfe7dc352907fc980b868725387e98bd8a938837679d31a836eaf6dca281d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024d6cd7a824f1fc5ee107dcfe571bab", "guid": "bfdfe7dc352907fc980b868725387e9844f908713cabd9521b2adb2d502b3ea7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980877df8febacf26c43b1e18c8b97ba75", "guid": "bfdfe7dc352907fc980b868725387e98cc8af04c56ecd174185a66e7f1394397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b193767227334ceb38dd446cf82809", "guid": "bfdfe7dc352907fc980b868725387e981edb126de8f12b186cac4f049786adf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98061c38f0a8a46703198f6e2ff6fdfb5b", "guid": "bfdfe7dc352907fc980b868725387e987181d5478969af3c7e5a0c55a9df68bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a2c72069511cad337ed0e75f63f192", "guid": "bfdfe7dc352907fc980b868725387e988ab1707e1d7ebf317d23485ca48f478f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c362dde705ff33ae993611d6cb0c7d", "guid": "bfdfe7dc352907fc980b868725387e98bab835f41817158a25e55b612146b523", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847eea8ccbf33f54486675970e309d2c8", "guid": "bfdfe7dc352907fc980b868725387e987f9dd4db0613bd13ec9fb21b2bdee1e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd9011259008b4b8bdbb863079ab9e80", "guid": "bfdfe7dc352907fc980b868725387e98dcd8d1fa257dc373efa40b95fe608e4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98496071c94234bf322a19dc1460f62bb7", "guid": "bfdfe7dc352907fc980b868725387e98848f95f84d992fc6db6218831b87a6e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b78d5caa7fc76b3c919f91058a5b3c0", "guid": "bfdfe7dc352907fc980b868725387e98a10b751e2d80436e3b96d7db9228b0a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3335031a3f04af6c7e5f35b317f74dd", "guid": "bfdfe7dc352907fc980b868725387e98a1b8f12c3ed38ca11f519243ccfca16d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf8bf917e0ad7a2ad69221590aeb8cdb", "guid": "bfdfe7dc352907fc980b868725387e98521786efb09bc7e526c923430c9d5f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caccc3192d2987217762911ac15948a8", "guid": "bfdfe7dc352907fc980b868725387e985165b767e13b3f1f8e2b7d26e8df12a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0e252f86204614773d8140942a82c4", "guid": "bfdfe7dc352907fc980b868725387e98e5e46181a0a88b4ec178f130ae2a486e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a447d4fbaad16ef298515a96d25d6d7", "guid": "bfdfe7dc352907fc980b868725387e988d0f6178423414ffff5adf0a061cfc7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d0294b05dcbecf7d5a1519618df3bf7", "guid": "bfdfe7dc352907fc980b868725387e9893d456f98e1059f5e0da5e4d13965cb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e9cb4235ce5ab543e9fad0bd534089c", "guid": "bfdfe7dc352907fc980b868725387e98a37f4f14f384e045542288637d0fae6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a671cdb13b8e6100ed01c6052e0cbcf4", "guid": "bfdfe7dc352907fc980b868725387e981cfcc47f34c4167acc265ec767a759a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb9c61930b572b1a08b8069eedb1bdf", "guid": "bfdfe7dc352907fc980b868725387e981357276c7acebe8d67c4760a5191279a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839de7507e40b7a6517d708881eb3f71d", "guid": "bfdfe7dc352907fc980b868725387e986a9d2e8a5a4cbbf07ce2991ff53d18c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2fd37c2bb1f59023daa14d386abf706", "guid": "bfdfe7dc352907fc980b868725387e989597e08cb801224a8de0c02d938b18d3"}], "guid": "bfdfe7dc352907fc980b868725387e98381573cb9f75097cb1def9354f2c2493", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899e5c659e28315fb3f209ede16b141f8", "guid": "bfdfe7dc352907fc980b868725387e984f15f27ef170a7e3e5db6e402749f586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c22503e1bad753fa6cf5c4e629d972ee", "guid": "bfdfe7dc352907fc980b868725387e98df03a37b62418469aceec546b8395105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982430e2040419a346da490da089e1c53d", "guid": "bfdfe7dc352907fc980b868725387e989bd61d478c267844b985ed7fa1484f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da6de8911ed3c0ab6c92bdf57906247", "guid": "bfdfe7dc352907fc980b868725387e9850339efabed1d8a6259f87767bbc1330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3aed9bdc33d5a5cd1427272c959548", "guid": "bfdfe7dc352907fc980b868725387e98405fb80f4020da0130e620ff9ce2ef5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850867da7c1482ada704be2ac7ca08e0b", "guid": "bfdfe7dc352907fc980b868725387e985d4b4afbe044d7885fae49916bdbc17a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635983f3f0bb420d14997f4aad3b6e7b", "guid": "bfdfe7dc352907fc980b868725387e9839e7a0e6d0fd8d1fee5d70e86db34872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e839b0293635ca92ce140ce6e55b774", "guid": "bfdfe7dc352907fc980b868725387e985bee8342c37fa81a898e4a47fcab3728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1db35a6a1d38054ac4a98054f69101d", "guid": "bfdfe7dc352907fc980b868725387e98ed768927ed2a04c812f45c53ab1a0478"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864cbd1e6b08776d0fdb25ec3e6f72b40", "guid": "bfdfe7dc352907fc980b868725387e98271d8a42df7b74aa63d31fe2e08975dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccbf78e7927ac2de8bba502b2fb041b", "guid": "bfdfe7dc352907fc980b868725387e984e5197a88dfa1b96a069e2fb34e338d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983adcbb95c73a74ca562d526bb5ef719b", "guid": "bfdfe7dc352907fc980b868725387e98387d20ff7ced1f64c78537594b4b347e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876b0eb98150208294711cff8f0627ce3", "guid": "bfdfe7dc352907fc980b868725387e98492a554e5cd961a69f98c4e4ea25c98b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de6812814bab3fe1be4024b022ca4144", "guid": "bfdfe7dc352907fc980b868725387e98c5f1024da2edd0637df539551375c524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2e6bfcf134b098260da7a12d2dbf1f", "guid": "bfdfe7dc352907fc980b868725387e98315659ee6f4e8a4f6381cd7bc928948a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac3533b32339da9a4d88a2b855766a3e", "guid": "bfdfe7dc352907fc980b868725387e9860dbed6f92c3f95a5ed07b06d73898e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3700a2f2b0563b4f7f5f8b83c6d1191", "guid": "bfdfe7dc352907fc980b868725387e985cac7f23c01800833d4a33f3bf10ecb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff67dbc8deebb70cfaecc84e88e9f06", "guid": "bfdfe7dc352907fc980b868725387e980cb8cabf304abf2e6be0979e6f1569ae"}], "guid": "bfdfe7dc352907fc980b868725387e98f203bbe3d34317f4903dba79c1e1bf7c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98a5c0b7844b929bfd2cd35e42066bc7a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e9832a6f36658f8197cd3f72d3a7cb93f5c"}], "guid": "bfdfe7dc352907fc980b868725387e98a552f0def9dfd4d1b61d7849d93500ef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981eaafc0c8efa6eb0c56d87367e9d19b7", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9884548000bf17dd028dc15422b3faf81d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}