import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/confirm_action_bottom_modal.dart';
import 'package:leadrat/core_main/common/widgets/leading_icon_with_text.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_bloc/listing_management_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_tab_bloc/property_listing_tab_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_basic_info_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_facilities_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_gallery_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_info_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_tab.dart';

class AddPropertyListingPage extends LeadratStatefulWidget {
  final String? propertyId;

  const AddPropertyListingPage({super.key, this.propertyId});

  @override
  State<AddPropertyListingPage> createState() => _AddPropertyListingPageState();
}

class _AddPropertyListingPageState extends LeadratState<AddPropertyListingPage> {
  @override
  void initState() {
    super.initState();
    context.read<PropertyListingBasicInfoBloc>().add(PropertyListingBasicInfoInitialEvent(propertyId: widget.propertyId));
  }

  @override
  void dispose() {
    super.dispose();
    getIt<AddPropertyListingBloc>().add(ResetAddPropertyListingEvent());
    getIt<ListingManagementBloc>().add(ListingManagementInitialEvent());
  }

  @override
  Widget buildContent(BuildContext context) {
    return const AddPropertyListingView();
  }
}

class AddPropertyListingView extends StatelessWidget {
  const AddPropertyListingView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddPropertyListingBloc, AddPropertyListingState>(
      builder: (context, state) {
        if (state is AddPropertyListingLoaded) {
          return LeadratForm(
              leadingButton: LeadratFormButton(
                  onPressed: () {
                    if (!state.canGoPrevious) {
                      Navigator.pop(context);
                    } else if (state.canGoPrevious && state.currentTabIndex == 4) {
                      Navigator.pop(context);
                    } else {
                      context.read<AddPropertyListingBloc>().add(PreviousTabEvent());
                      context.read<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
                    }
                  },
                  buttonText: "back"),
              trailingButton: LeadratFormButton(onPressed: _saveAndContinue(context, state), buttonText: "save & continue", isTrailingVisible: true),
              padding: const EdgeInsets.all(0),
              wrapWithScrollView: false,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTabHeader(context, state),
                  Expanded(child: _buildTabContent(state.currentTabIndex)),
                ],
              ));
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildTabHeader(BuildContext context, AddPropertyListingLoaded state) {
    return Container(
      color: ColorPalette.white,
      padding: const EdgeInsets.only(top: 10, bottom: 1),
      child: Column(
        children: [
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              children: List.generate(state.tabTitles.length, (index) {
                final isActive = index == state.currentTabIndex;

                return Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // context.read<AddPropertyListingBloc>().add(TabChangedEvent(index));
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(state.tabTitles[index].imageResource!),
                          const SizedBox(width: 8),
                          Padding(
                            padding: const EdgeInsets.only(right: 18),
                            child: Text(
                              state.tabTitles[index].title,
                              style: isActive ? LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primaryLightColor) : LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.tertiaryTextColor),
                            ),
                          ),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    if (isActive) Container(height: 1, color: ColorPalette.black, width: 80),
                  ],
                );
              }),
            ),
          ),
          Container(height: .3, width: double.infinity, color: ColorPalette.tertiaryTextColor.withValues(alpha: .8)),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Quality Score", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.darkToneInk)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(color: ColorPalette.fadedRed.withValues(alpha: .4), borderRadius: BorderRadius.circular(20), border: Border.all(color: ColorPalette.fadedRed)),
                  child: LeadingIconWithText(
                    text: state.propertyQualityScore ?? "0%",
                    iconWidget: const Icon(Icons.speed_sharp, color: ColorPalette.fadedRed),
                    textStyle: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.fadedRed),
                  ),
                ),
              ],
            ),
          ),
          Container(height: .5, width: double.infinity, color: ColorPalette.tertiaryTextColor.withValues(alpha: .8))
        ],
      ),
    );
  }

  Widget _buildTabContent(int currentTabIndex) {
    switch (currentTabIndex) {
      case 0:
        return PropertyListingBasicInfoTab();
      case 1:
        return PropertyListingInfoTab();
      case 2:
        return PropertyListingFacilitiesTab();
      case 3:
        return PropertyListingGalleryTab();
      case 4:
        return PropertyListingTab();
      default:
        return PropertyListingBasicInfoTab();
    }
  }

  void Function() _saveAndContinue(BuildContext context, AddPropertyListingLoaded state) {
    return state.canGoNext
        ? () {
            context.read<AddPropertyListingBloc>().add(NextTabEvent());
            context.read<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
          }
        : () {
            _handleSubmit(context);
          };
  }

  void _handleSubmit(BuildContext context) {
    confirmActionBottomModal(
      imageVector: ImageResources.imageListingBottomPopUp,
      title: 'Continue As',
      subTitle: 'Would you like to save the property now either as a draft or publish it directly from here?',
      successButtonText: 'publish',
      cancelButtonText: 'save as draft',
      onSuccess: () {
        if (!getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.publishProperty)) {
          LeadratCustomSnackbar.show(message: "you don\'t have permission to publish or unpublish property", type: SnackbarType.warning, context: context);
          return;
        }
        getIt<PropertyListingTabBloc>().add(SaveAndListDelistPropertyEvent(listDeListProperty: true));
        Navigator.pop(context);
      },
      onCancel: () {
        getIt<PropertyListingTabBloc>().add(SaveAndListDelistPropertyEvent(listDeListProperty: false));
        Navigator.pop(context);
      },
    );
  }
}
