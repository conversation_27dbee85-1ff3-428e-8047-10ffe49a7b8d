import 'package:json_annotation/json_annotation.dart';

part 'create_saved_filter_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class CreateSavedFilterModel {
  final String? id;
  final String? name;
  final String? module;
  final String? filterCriteria;

  CreateSavedFilterModel({this.id, this.name, this.module, this.filterCriteria});

  factory CreateSavedFilterModel.fromJson(Map<String, dynamic> json) => _$CreateSavedFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateSavedFilterModelToJson(this);
}
