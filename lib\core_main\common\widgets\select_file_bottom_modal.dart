import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/enums/app_enum/select_file_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class SelectFileOption {
  final String label;
  final String icon;
  final SelectFileEnum option;

  const SelectFileOption({
    required this.label,
    required this.icon,
    required this.option,
  });
}

void selectFileBottomModal(BuildContext context, Function(SelectFileEnum) onSelect, {bool isDocument = false}) {
  final List<SelectFileOption> options = [
    const SelectFileOption(
      label: 'camera',
      icon: ImageResources.iconCamera,
      option: SelectFileEnum.camera,
    ),
    const SelectFileOption(
      label: 'gallery',
      icon: ImageResources.iconGallery,
      option: SelectFileEnum.gallery,
    ),
    const SelectFileOption(
      label: 'files',
      icon: ImageResources.iconFiles,
      option: SelectFileEnum.files,
    ),
  ];

  if (isDocument) {
    options.remove(options.firstWhere((option) => option.label == 'camera'));
    options.remove(options.firstWhere((option) => option.label == 'gallery'));
  }

  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -6,
            left: 0,
            right: 0,
            child: CustomPaint(
              size: Size(context.width(100), 6),
              painter: TrapeziumPainter(),
            ),
          ),
          Container(
            width: context.width(100),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            color: ColorPalette.primaryDarkColor,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "select from",
                      style: LexendTextStyles.lexend11Bold
                          .copyWith(color: ColorPalette.primary),
                    ),
                    const SizedBox(
                      width: 40,
                      child: Divider(
                        color: ColorPalette.primary,
                        thickness: 2.0,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                Row(
                  children: options.map((SelectFileOption option) {
                    return Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          onSelect(option.option);
                        },
                        child: Column(
                          children: [
                            Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: 50,
                              margin: const EdgeInsets.only(bottom: 10),
                              decoration: BoxDecoration(
                                  color: ColorPalette.primaryColor,
                                  border: Border.all(
                                      color: ColorPalette.lightBackground,
                                      width: 1)),
                              child: SvgPicture.asset(
                                option.icon,
                                height: 24,
                              ),
                            ),
                            Text(
                              option.label,
                              style: LexendTextStyles.lexend11Bold
                                  .copyWith(color: ColorPalette.primary),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      );
    },
  );
}
