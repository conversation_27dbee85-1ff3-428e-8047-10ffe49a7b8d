import 'package:hive/hive.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';

part 'get_user_designation_map_model.g.dart';

@HiveType(typeId: HiveModelConstants.getUserDesignationMapModelTypeId)
class GetUserDesignationMapModel {
  @HiveField(0)
  final String key;

  @HiveField(1)
  final List<GetUserDesignationModel> values;

  GetUserDesignationMapModel({required this.key, required this.values});
}