{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee1b83959d72391e53e246a39a79be92", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986142f99ccdc2a96c2a109ba81fda687a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986142f99ccdc2a96c2a109ba81fda687a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983eed15ba1cfda5e35ee3694fe1b5827e", "guid": "bfdfe7dc352907fc980b868725387e98e9677d32d8e217c45571c8820323a0f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fdd6436ab46fdbdd9f9f9b1ec7d420c", "guid": "bfdfe7dc352907fc980b868725387e982453142c4c6c06d7c6f99918f2ff02f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f67d7e0a4ab9952862a87c4ef067df8a", "guid": "bfdfe7dc352907fc980b868725387e980940bb69ae88f028dc7b5809212c0cbd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221f5615655dec9717876eab9f9b2233", "guid": "bfdfe7dc352907fc980b868725387e98c03beaf9b439209d7479fb5f4b693cc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf9791982bb1a07b7e07476c32a665ec", "guid": "bfdfe7dc352907fc980b868725387e98948b8f97d7f98dd67b2e95e47e7a2571", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6248bcca000379bddd15e6a927cea42", "guid": "bfdfe7dc352907fc980b868725387e98af8d4585bc0a579ff24fe2d524f00de7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76e44a087ca4f23dd71a90bfd52a0f0", "guid": "bfdfe7dc352907fc980b868725387e980f7e9e6520ca46b9f2b450b0baf315e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bac8549a394d9e5f25df7810e93b35e", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c00398ab515198271eb2d85f72409c6", "guid": "bfdfe7dc352907fc980b868725387e9878fa4e79e742f97803f592fca7be1298", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98074ef9719e942b5195aeaed3ee636cb3", "guid": "bfdfe7dc352907fc980b868725387e986f29b1c328efce6c9378f339b0484b73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989974baa29d01c50e7ab8bf7eebb63f45", "guid": "bfdfe7dc352907fc980b868725387e98971a9477d7d6a01dd800f80921e65967", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af8720bacc2f6250cfdac47b8fce5979", "guid": "bfdfe7dc352907fc980b868725387e98a87b3794d4290fbfcfd4d4c0c2a1d1e4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981010713632be2ace1462c8f886ec4345", "guid": "bfdfe7dc352907fc980b868725387e9876924e4695f5ed922e47f2b2d4c9db7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac274ddca72bca204acb549c740a9fd", "guid": "bfdfe7dc352907fc980b868725387e9814d4d2144f85ffc2496f342475c3fb7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f2f26a0f01677841807eef6d435288", "guid": "bfdfe7dc352907fc980b868725387e98b0b0b2c81f46e8927b543135eea5b3f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af6faf6876e533905011823bb25cdfc", "guid": "bfdfe7dc352907fc980b868725387e980143fdea8dabc7a6de1bb9da3f055626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98079353d938b5416a6ca506f85147963a", "guid": "bfdfe7dc352907fc980b868725387e9848c3988a5b6cc289e56144f6b4336236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec3ab1f680c21818159058393af2ba9", "guid": "bfdfe7dc352907fc980b868725387e98c395439b98a6c4574787f5ec9f8e8ed9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4b19a566957aad2fe4703167c23b35", "guid": "bfdfe7dc352907fc980b868725387e989c5fed5b3334411020a3d5924074b2cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8a34730cae83cf477e216fadcbce30", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5513ce5777006b477a1a3e745b0e23b", "guid": "bfdfe7dc352907fc980b868725387e98b6ae9c06304bbd29af92d48054477261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b3d70c6ba449f4cb9ca555411f644f", "guid": "bfdfe7dc352907fc980b868725387e98de4875f57db57df157a4bee215614498"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3ea8b5ec2b9643d56a172009f1fcfbe", "guid": "bfdfe7dc352907fc980b868725387e98b8a942cee0f8d56361af06fc24fe9f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fab7deecea6f86d6b2ef5287fef19ff", "guid": "bfdfe7dc352907fc980b868725387e982292013d4fdec2995d44d44cf26e8636"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}