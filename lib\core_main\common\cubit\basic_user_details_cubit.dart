
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';


class BasicUserDetailsCubit extends Cubit<BasicUserDetailsState> {
  final UsersDataRepository _usersDataRepository;

  BasicUserDetailsCubit(this._usersDataRepository) : super(BasicUserDetailsState()) {
    fetchLocalUserDetails();
  }

  void fetchLocalUserDetails() {
    UserDetailsModel? userDetailsModel = _usersDataRepository.getLoggedInUser();
    emit(state.copyWith(userDetailsModel: userDetailsModel));
  }
}

final class BasicUserDetailsState {
  final UserDetailsModel? userDetailsModel;

  BasicUserDetailsState({this.userDetailsModel});

  BasicUserDetailsState copyWith({UserDetailsModel? userDetailsModel}) {
    return BasicUserDetailsState(
        userDetailsModel: userDetailsModel ?? this.userDetailsModel);
  }
}
