import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/utilities/property_utils.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_facilities_bloc/property_listing_facilities_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_gallery_bloc/property_listing_gallery_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_tab_bloc/property_listing_tab_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/propety_listing_property_info_bloc/property_listing_property_info_bloc.dart';

part 'add_property_listing_event.dart';
part 'add_property_listing_state.dart';

class AddPropertyListingBloc extends Bloc<AddPropertyListingEvent, AddPropertyListingState> {
  static final List<ItemSimpleModel> _tabTitles = [
    ItemSimpleModel(title: "Basic Info", imageResource: ImageResources.propertyBasicInfo),
    ItemSimpleModel(title: "Property Info", imageResource: ImageResources.propertyInfo),
    ItemSimpleModel(title: "Facilities", imageResource: ImageResources.propertyFacilities),
    ItemSimpleModel(title: "Gallery", imageResource: ImageResources.propertyGallery),
    ItemSimpleModel(title: "Publishing", imageResource: ImageResources.iconPropertyListing),
  ];

  PropertyListingBasicInfoBloc get propertyListingBasicInfoBloc => getIt<PropertyListingBasicInfoBloc>();

  PropertyListingPropertyInfoBloc get propertyListingPropertyInfoBloc => getIt<PropertyListingPropertyInfoBloc>();

  PropertyListingFacilitiesBloc get propertyListingFacilitiesBloc => getIt<PropertyListingFacilitiesBloc>();

  PropertyListingGalleryBloc get propertyListingGalleryBloc => getIt<PropertyListingGalleryBloc>();

  PropertyListingTabBloc get propertyListingTabBloc => getIt<PropertyListingTabBloc>();

  AddPropertyListingBloc() : super(AddPropertyListingInitial()) {
    on<TabChangedEvent>(_onTabChanged);
    on<NextTabEvent>(_onNextTab);
    on<PreviousTabEvent>(_onPreviousTab);
    on<ResetAddPropertyListingEvent>(_onResetAddPropertyListing);
    on<ChangeTabEvent>(_onChangeTab);
    on<CalculatePropertyQualityScoreEvent>(_onCalculatePropertyQualityScore);

    add(ChangeTabEvent(0));
  }

  void _onTabChanged(TabChangedEvent event, Emitter<AddPropertyListingState> emit) {
    final tabIndex = event.tabIndex.clamp(0, _tabTitles.length - 1);
    final currentState = state;

    if (tabIndex > 0) {
      if (currentState is AddPropertyListingLoaded && tabIndex > currentState.currentTabIndex) {
        for (int i = currentState.currentTabIndex + 1; i <= tabIndex; i++) {
          switch (i) {
            case 1:
              final canNavigate = propertyListingBasicInfoBloc.canNavigateToNextTab();
              if (!canNavigate) return;
              propertyListingPropertyInfoBloc.add(PropertyListingPropertyInfoInitialEvent());
              add(ChangeTabEvent(tabIndex));
              break;
            case 2:
              final canNavigate = propertyListingPropertyInfoBloc.canNavigateToNextTab() && propertyListingBasicInfoBloc.canNavigateToNextTab();
              if (!canNavigate) return;
              propertyListingFacilitiesBloc.add(PropertyListingFacilitiesInitialEvent(addPropertyListingModel: propertyListingPropertyInfoBloc.addPropertyListingModel));
              add(ChangeTabEvent(tabIndex));
              break;
            case 3:
              final canNavigate = propertyListingPropertyInfoBloc.canNavigateToNextTab() && propertyListingBasicInfoBloc.canNavigateToNextTab();
              if (!canNavigate) return;
              propertyListingFacilitiesBloc.add(NavigateToNextTabEvent());
              add(ChangeTabEvent(tabIndex));
              break;
            case 4:
              // final canNavigate = propertyListingPropertyInfoBloc.canNavigateToNextTab() && propertyListingBasicInfoBloc.canNavigateToNextTab();
              // if (!canNavigate) return;
              propertyListingGalleryBloc.add(AddPropertyEvent());
              break;
          }
        }
      }
    }
  }

  void _onChangeTab(ChangeTabEvent event, Emitter<AddPropertyListingState> emit) {
    final tabIndex = event.tabIndex;
    emit(AddPropertyListingLoaded(currentTabIndex: tabIndex, tabTitles: _tabTitles, canGoNext: tabIndex < _tabTitles.length - 1, canGoPrevious: tabIndex > 0));
  }

  void _onNextTab(NextTabEvent event, Emitter<AddPropertyListingState> emit) {
    final currentState = state;
    if (currentState is AddPropertyListingLoaded && currentState.canGoNext) {
      final index = currentState.currentTabIndex + 1;
      switch (index) {
        case 1:
          if (propertyListingBasicInfoBloc.canNavigateToNextTab()) {
            propertyListingPropertyInfoBloc.add(PropertyListingPropertyInfoInitialEvent());
            add(TabChangedEvent(index));
          }
          break;
        case 2:
          if (propertyListingBasicInfoBloc.canNavigateToNextTab() && propertyListingPropertyInfoBloc.canNavigateToNextTab()) {
            propertyListingFacilitiesBloc.add(PropertyListingFacilitiesInitialEvent(addPropertyListingModel: propertyListingBasicInfoBloc.addPropertyListingModel));
            add(TabChangedEvent(index));
          }
          break;
        case 3:
          if (propertyListingBasicInfoBloc.canNavigateToNextTab() && propertyListingPropertyInfoBloc.canNavigateToNextTab()) {
            propertyListingFacilitiesBloc.add(NavigateToNextTabEvent());
            add(TabChangedEvent(index));
          }
          break;
        case 4:
          add(TabChangedEvent(index));
          break;
      }
    }
  }

  void _onPreviousTab(PreviousTabEvent event, Emitter<AddPropertyListingState> emit) {
    final currentState = state;
    if (currentState is AddPropertyListingLoaded && currentState.canGoPrevious) {
      add(ChangeTabEvent(currentState.currentTabIndex - 1));
    }
  }

  void _onResetAddPropertyListing(ResetAddPropertyListingEvent event, Emitter<AddPropertyListingState> emit) {
    add(ChangeTabEvent(0));
    propertyListingPropertyInfoBloc.add(ResetPropertyListingPropertyInfoEvent());
    propertyListingBasicInfoBloc.add(ResetPropertyListingBasicInfoStateEvent());
    propertyListingFacilitiesBloc.add(ResetPropertyListingFacilitiesStateEvent());
    propertyListingGalleryBloc.add(ClearGalleryTabStateEvent());
    propertyListingTabBloc.add(DisposePropertyListingStateEvent());
  }

  void _onCalculatePropertyQualityScore(CalculatePropertyQualityScoreEvent event, Emitter<AddPropertyListingState> emit) async {
    final basicInfoState = propertyListingBasicInfoBloc.state;
    final propertyInfoState = propertyListingPropertyInfoBloc.state;
    final galleryState = propertyListingGalleryBloc.state;

    final qualityScore = await PropertyUtils.calculatePropertyListingQualityScore(
      title: propertyListingBasicInfoBloc.propertyTitleEngController.text,
      description: propertyListingBasicInfoBloc.aboutPropertyEngController.text,
      dldPermitNumber: propertyListingPropertyInfoBloc.permitNumberController.text,
      dtcmPermit: propertyListingPropertyInfoBloc.permitNumberController.text,
      imageFiles: galleryState.photos?.map((e) => e.value).nonNulls.toList(),
      locationId: propertyInfoState.location?.locationId,
      enquiredCity: propertyInfoState.location?.city,
      enquiredLocality: propertyInfoState.location?.locality,
      enquiredState: propertyInfoState.location?.state,
      isShowManualLocation: propertyInfoState.location?.locationId == null,
      isListingComplete: _isListingComplete(propertyListingBasicInfoBloc, propertyListingPropertyInfoBloc, galleryState),
    );

    if (state is AddPropertyListingLoaded) {
      final newState = state as AddPropertyListingLoaded;
      emit(AddPropertyListingLoaded(
        currentTabIndex: newState.currentTabIndex,
        tabTitles: newState.tabTitles,
        canGoNext: newState.canGoNext,
        canGoPrevious: newState.canGoPrevious,
        propertyQualityScore: "$qualityScore%",
      ));
    }
  }

  bool _isListingComplete(dynamic basicInfoBloc, dynamic propertyInfoBloc, dynamic galleryState) {
    // Check if all mandatory fields are filled
    final hasTitle = basicInfoBloc.propertyTitleEngController.text.isNotEmpty;
    final hasDescription = basicInfoBloc.aboutPropertyEngController.text.isNotEmpty;
    final hasLocation = propertyInfoBloc.state.location != null;
    final hasImages = galleryState.photos?.isNotEmpty ?? false;

    // Check for price - either total price or rent amount
    final hasPrice = propertyInfoBloc.totalPriceController.text.isNotEmpty;

    // Check if property type is selected
    final hasPropertyType = basicInfoBloc.state.propertyTypes?.any((type) => type.isSelected) ?? false;

    // Check if offering type is selected (sale/rent)
    final hasOfferingType = basicInfoBloc.state.offeringTypes?.any((type) => type.isSelected) ?? false;

    return hasTitle && hasDescription && hasLocation && hasImages && hasPrice && hasPropertyType && hasOfferingType;
  }
}
