import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class TitleWithListItemsWidget extends LeadratStatelessWidget {
  final String title;
  final List<String>? items;

  const TitleWithListItemsWidget({
    super.key,
    required this.title,
    required this.items,
  });

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor)),
        const SizedBox(height: 6),
        items?.isNotEmpty ?? false
            ? Wrap(
          spacing: 8.0,
          children: List.generate(items?.length ?? 0, (index) {
            return Chip(
              label: Text(items?[index] ?? "",overflow: TextOverflow.ellipsis),
              padding: EdgeInsets.zero,
              labelStyle: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary),
              visualDensity: const VisualDensity(horizontal: 0.0, vertical: -4),
              backgroundColor: ColorPalette.primaryLightColor,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20), side: const BorderSide(color: ColorPalette.lightBackground)),
            );
          }),
        )
            : Text('--', style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary)),
      ],
    );
  }
}
