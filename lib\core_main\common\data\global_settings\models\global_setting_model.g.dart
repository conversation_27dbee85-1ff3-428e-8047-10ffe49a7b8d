// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_setting_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GlobalSettingModelAdapter extends TypeAdapter<GlobalSettingModel> {
  @override
  final int typeId = 5;

  @override
  GlobalSettingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GlobalSettingModel(
      id: fields[0] as String?,
      lastModifiedOn: fields[1] as DateTime?,
      lastModifiedBy: fields[2] as String?,
      hasInternationalSupport: fields[5] as bool?,
      isLeadsExportEnabled: fields[6] as bool?,
      isLeadSourceEditable: fields[7] as bool?,
      dayStartTime: fields[8] as DateTime?,
      dayEndTime: fields[9] as DateTime?,
      whatsAppMessageTemplates: fields[10] as WhatsAppTemplates?,
      isCallDetectionActivated: fields[11] as bool?,
      leadNotesSetting: fields[12] as LeadNotesSettings?,
      isMaskedLeadContactNo: fields[13] as bool?,
      isDualOwnershipEnabled: fields[14] as bool?,
      isIVROutboundEnabled: fields[15] as bool?,
      isVirtualNumberRequiredForOutbound: fields[16] as bool?,
      leadProjectSetting: fields[17] as LeadProjectSettingModel?,
      isTimeZoneEnabled: fields[18] as bool?,
      countries: (fields[19] as List?)?.cast<CountryInfoModel>(),
      otpSettings: fields[20] as OtpSettingsModel?,
      isCopyPasteEnabled: fields[21] as bool?,
      isScreenshotEnabled: fields[22] as bool?,
      isCustomStatusEnabled: fields[23] as bool?,
      shouldHideDashBoard: fields[24] as bool?,
      isWhatsAppDeepIntegration: fields[25] as bool?,
      notificationSettings: fields[3] as NotificationSettingsModel?,
      callSettings: fields[4] as CallSettingsModel?,
      isCustomLeadFormEnabled: fields[26] as bool?,
      isStickyAgentEnabled: fields[27] as bool?,
      shouldEnablePropertyListing: fields[28] as bool?,
      duplicateLeadFeatureInfo: fields[29] as DuplicateLeadFeatureInfo?,
      defaultValues: (fields[30] as Map?)?.cast<String, String?>(),
      isAssignedCallLogsEnabled: fields[31] as bool?,
      shouldRenameSiteVisitColumn: fields[33] as bool?,
      isPastDateSelectionEnabled: fields[32] as bool?,
      generalSettings: fields[34] as GeneralSettings?,
    );
  }

  @override
  void write(BinaryWriter writer, GlobalSettingModel obj) {
    writer
      ..writeByte(35)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.lastModifiedOn)
      ..writeByte(2)
      ..write(obj.lastModifiedBy)
      ..writeByte(3)
      ..write(obj.notificationSettings)
      ..writeByte(4)
      ..write(obj.callSettings)
      ..writeByte(5)
      ..write(obj.hasInternationalSupport)
      ..writeByte(6)
      ..write(obj.isLeadsExportEnabled)
      ..writeByte(7)
      ..write(obj.isLeadSourceEditable)
      ..writeByte(8)
      ..write(obj.dayStartTime)
      ..writeByte(9)
      ..write(obj.dayEndTime)
      ..writeByte(10)
      ..write(obj.whatsAppMessageTemplates)
      ..writeByte(11)
      ..write(obj.isCallDetectionActivated)
      ..writeByte(12)
      ..write(obj.leadNotesSetting)
      ..writeByte(13)
      ..write(obj.isMaskedLeadContactNo)
      ..writeByte(14)
      ..write(obj.isDualOwnershipEnabled)
      ..writeByte(15)
      ..write(obj.isIVROutboundEnabled)
      ..writeByte(16)
      ..write(obj.isVirtualNumberRequiredForOutbound)
      ..writeByte(17)
      ..write(obj.leadProjectSetting)
      ..writeByte(18)
      ..write(obj.isTimeZoneEnabled)
      ..writeByte(19)
      ..write(obj.countries)
      ..writeByte(20)
      ..write(obj.otpSettings)
      ..writeByte(21)
      ..write(obj.isCopyPasteEnabled)
      ..writeByte(22)
      ..write(obj.isScreenshotEnabled)
      ..writeByte(23)
      ..write(obj.isCustomStatusEnabled)
      ..writeByte(24)
      ..write(obj.shouldHideDashBoard)
      ..writeByte(25)
      ..write(obj.isWhatsAppDeepIntegration)
      ..writeByte(26)
      ..write(obj.isCustomLeadFormEnabled)
      ..writeByte(27)
      ..write(obj.isStickyAgentEnabled)
      ..writeByte(28)
      ..write(obj.shouldEnablePropertyListing)
      ..writeByte(29)
      ..write(obj.duplicateLeadFeatureInfo)
      ..writeByte(30)
      ..write(obj.defaultValues)
      ..writeByte(31)
      ..write(obj.isAssignedCallLogsEnabled)
      ..writeByte(32)
      ..write(obj.isPastDateSelectionEnabled)
      ..writeByte(33)
      ..write(obj.shouldRenameSiteVisitColumn)
      ..writeByte(34)
      ..write(obj.generalSettings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GlobalSettingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DuplicateLeadFeatureInfoAdapter
    extends TypeAdapter<DuplicateLeadFeatureInfo> {
  @override
  final int typeId = 113;

  @override
  DuplicateLeadFeatureInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DuplicateLeadFeatureInfo(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdBy: fields[2] as String?,
      createdOn: fields[3] as String?,
      lastModifiedBy: fields[4] as String?,
      lastModifiedOn: fields[5] as String?,
      deletedOn: fields[6] as String?,
      deletedBy: fields[7] as String?,
      allowAllDuplicates: fields[8] as bool?,
      isFeatureAdded: fields[9] as bool?,
      isSourceBased: fields[10] as bool?,
      isSubSourceBased: fields[11] as bool?,
      isProjectBased: fields[12] as bool?,
      isLocationBased: fields[33] as bool?,
      statusIds: (fields[14] as List?)?.cast<String?>(),
    );
  }

  @override
  void write(BinaryWriter writer, DuplicateLeadFeatureInfo obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.createdOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.deletedOn)
      ..writeByte(7)
      ..write(obj.deletedBy)
      ..writeByte(8)
      ..write(obj.allowAllDuplicates)
      ..writeByte(9)
      ..write(obj.isFeatureAdded)
      ..writeByte(10)
      ..write(obj.isSourceBased)
      ..writeByte(11)
      ..write(obj.isSubSourceBased)
      ..writeByte(12)
      ..write(obj.isProjectBased)
      ..writeByte(33)
      ..write(obj.isLocationBased)
      ..writeByte(14)
      ..write(obj.statusIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DuplicateLeadFeatureInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GeneralSettingsAdapter extends TypeAdapter<GeneralSettings> {
  @override
  final int typeId = 116;

  @override
  GeneralSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GeneralSettings(
      isLocationMandatory: fields[0] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, GeneralSettings obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.isLocationMandatory);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GeneralSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GlobalSettingModel _$GlobalSettingModelFromJson(Map<String, dynamic> json) =>
    GlobalSettingModel(
      id: json['id'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      hasInternationalSupport: json['hasInternationalSupport'] as bool?,
      isLeadsExportEnabled: json['isLeadsExportEnabled'] as bool?,
      isLeadSourceEditable: json['isLeadSourceEditable'] as bool?,
      dayStartTime: json['dayStartTime'] == null
          ? null
          : DateTime.parse(json['dayStartTime'] as String),
      dayEndTime: json['dayEndTime'] == null
          ? null
          : DateTime.parse(json['dayEndTime'] as String),
      whatsAppMessageTemplates: json['whatsAppMessageTemplates'] == null
          ? null
          : WhatsAppTemplates.fromJson(
              json['whatsAppMessageTemplates'] as Map<String, dynamic>),
      isCallDetectionActivated: json['isCallDetectionActivated'] as bool?,
      leadNotesSetting: json['leadNotesSetting'] == null
          ? null
          : LeadNotesSettings.fromJson(
              json['leadNotesSetting'] as Map<String, dynamic>),
      isMaskedLeadContactNo: json['isMaskedLeadContactNo'] as bool?,
      isDualOwnershipEnabled: json['isDualOwnershipEnabled'] as bool?,
      isIVROutboundEnabled: json['isIVROutboundEnabled'] as bool?,
      isVirtualNumberRequiredForOutbound:
          json['isVirtualNumberRequiredForOutbound'] as bool?,
      leadProjectSetting: json['leadProjectSetting'] == null
          ? null
          : LeadProjectSettingModel.fromJson(
              json['leadProjectSetting'] as Map<String, dynamic>),
      isTimeZoneEnabled: json['isTimeZoneEnabled'] as bool?,
      countries: (json['countries'] as List<dynamic>?)
          ?.map((e) => CountryInfoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      otpSettings: json['otpSettings'] == null
          ? null
          : OtpSettingsModel.fromJson(
              json['otpSettings'] as Map<String, dynamic>),
      isCopyPasteEnabled: json['isCopyPasteEnabled'] as bool?,
      isScreenshotEnabled: json['isScreenshotEnabled'] as bool?,
      isCustomStatusEnabled: json['isCustomStatusEnabled'] as bool?,
      shouldHideDashBoard: json['shouldHideDashBoard'] as bool?,
      isWhatsAppDeepIntegration: json['isWhatsAppDeepIntegration'] as bool?,
      notificationSettings: json['notificationSettings'] == null
          ? null
          : NotificationSettingsModel.fromJson(
              json['notificationSettings'] as Map<String, dynamic>),
      callSettings: json['callSettings'] == null
          ? null
          : CallSettingsModel.fromJson(
              json['callSettings'] as Map<String, dynamic>),
      isCustomLeadFormEnabled: json['isCustomLeadFormEnabled'] as bool?,
      isStickyAgentEnabled: json['isStickyAgentEnabled'] as bool?,
      shouldEnablePropertyListing: json['shouldEnablePropertyListing'] as bool?,
      duplicateLeadFeatureInfo: json['duplicateLeadFeatureInfo'] == null
          ? null
          : DuplicateLeadFeatureInfo.fromJson(
              json['duplicateLeadFeatureInfo'] as Map<String, dynamic>),
      defaultValues: (json['defaultValues'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String?),
      ),
      isAssignedCallLogsEnabled: json['isAssignedCallLogsEnabled'] as bool?,
      shouldRenameSiteVisitColumn: json['shouldRenameSiteVisitColumn'] as bool?,
      isPastDateSelectionEnabled: json['isPastDateSelectionEnabled'] as bool?,
      generalSettings: json['generalSettings'] == null
          ? null
          : GeneralSettings.fromJson(
              json['generalSettings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GlobalSettingModelToJson(GlobalSettingModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'notificationSettings': instance.notificationSettings,
      'callSettings': instance.callSettings,
      'hasInternationalSupport': instance.hasInternationalSupport,
      'isLeadsExportEnabled': instance.isLeadsExportEnabled,
      'isLeadSourceEditable': instance.isLeadSourceEditable,
      'dayStartTime': instance.dayStartTime?.toIso8601String(),
      'dayEndTime': instance.dayEndTime?.toIso8601String(),
      'whatsAppMessageTemplates': instance.whatsAppMessageTemplates,
      'isCallDetectionActivated': instance.isCallDetectionActivated,
      'leadNotesSetting': instance.leadNotesSetting,
      'isMaskedLeadContactNo': instance.isMaskedLeadContactNo,
      'isDualOwnershipEnabled': instance.isDualOwnershipEnabled,
      'isIVROutboundEnabled': instance.isIVROutboundEnabled,
      'isVirtualNumberRequiredForOutbound':
          instance.isVirtualNumberRequiredForOutbound,
      'leadProjectSetting': instance.leadProjectSetting,
      'isTimeZoneEnabled': instance.isTimeZoneEnabled,
      'countries': instance.countries,
      'otpSettings': instance.otpSettings,
      'isCopyPasteEnabled': instance.isCopyPasteEnabled,
      'isScreenshotEnabled': instance.isScreenshotEnabled,
      'isCustomStatusEnabled': instance.isCustomStatusEnabled,
      'shouldHideDashBoard': instance.shouldHideDashBoard,
      'isWhatsAppDeepIntegration': instance.isWhatsAppDeepIntegration,
      'isCustomLeadFormEnabled': instance.isCustomLeadFormEnabled,
      'isStickyAgentEnabled': instance.isStickyAgentEnabled,
      'shouldEnablePropertyListing': instance.shouldEnablePropertyListing,
      'duplicateLeadFeatureInfo': instance.duplicateLeadFeatureInfo,
      'defaultValues': instance.defaultValues,
      'isAssignedCallLogsEnabled': instance.isAssignedCallLogsEnabled,
      'isPastDateSelectionEnabled': instance.isPastDateSelectionEnabled,
      'shouldRenameSiteVisitColumn': instance.shouldRenameSiteVisitColumn,
      'generalSettings': instance.generalSettings,
    };

DuplicateLeadFeatureInfo _$DuplicateLeadFeatureInfoFromJson(
        Map<String, dynamic> json) =>
    DuplicateLeadFeatureInfo(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] as String?,
      deletedOn: json['deletedOn'] as String?,
      deletedBy: json['deletedBy'] as String?,
      allowAllDuplicates: json['allowAllDuplicates'] as bool?,
      isFeatureAdded: json['isFeatureAdded'] as bool?,
      isSourceBased: json['isSourceBased'] as bool?,
      isSubSourceBased: json['isSubSourceBased'] as bool?,
      isProjectBased: json['isProjectBased'] as bool?,
      isLocationBased: json['isLocationBased'] as bool?,
      statusIds: (json['statusIds'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
    );

Map<String, dynamic> _$DuplicateLeadFeatureInfoToJson(
        DuplicateLeadFeatureInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn,
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn,
      'deletedOn': instance.deletedOn,
      'deletedBy': instance.deletedBy,
      'allowAllDuplicates': instance.allowAllDuplicates,
      'isFeatureAdded': instance.isFeatureAdded,
      'isSourceBased': instance.isSourceBased,
      'isSubSourceBased': instance.isSubSourceBased,
      'isProjectBased': instance.isProjectBased,
      'isLocationBased': instance.isLocationBased,
      'statusIds': instance.statusIds,
    };

GeneralSettings _$GeneralSettingsFromJson(Map<String, dynamic> json) =>
    GeneralSettings(
      isLocationMandatory: json['isLocationMandatory'] as bool?,
    );

Map<String, dynamic> _$GeneralSettingsToJson(GeneralSettings instance) =>
    <String, dynamic>{
      'isLocationMandatory': instance.isLocationMandatory,
    };
