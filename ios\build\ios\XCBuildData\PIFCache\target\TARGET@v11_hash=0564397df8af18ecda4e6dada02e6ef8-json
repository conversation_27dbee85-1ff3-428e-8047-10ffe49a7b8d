{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3197fa31315bc59ce50e116c109c17f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea4598c9ad27d7c04db8c032ad4f3fd7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea4598c9ad27d7c04db8c032ad4f3fd7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d99b90e61db945d46d11fafb0c138d47", "guid": "bfdfe7dc352907fc980b868725387e98fadb486075b285ced26dc074123c7de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e985797638d9aabc32d3a59b6c20dc9", "guid": "bfdfe7dc352907fc980b868725387e982fe32e357c24218ffd83a8755a5aef4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059a2f4f659ea22e42e70822ca53cca9", "guid": "bfdfe7dc352907fc980b868725387e984e8ef2d0871966b0e1c3eb4e1dabb288", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b244ed56c6cf8d9c33a3126d66ffae1e", "guid": "bfdfe7dc352907fc980b868725387e98f617d1f5eb7c59ea0dbb9b965cfd32e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c95af347f0af3205c2db107906be26", "guid": "bfdfe7dc352907fc980b868725387e9845e19ba25df26c3ccc091052c94434e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c39a1de4996187399a8a624ea16667", "guid": "bfdfe7dc352907fc980b868725387e980f66a8466556e02887e640a18a15d2a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea3f9122abc41f6d01241cdd373e600b", "guid": "bfdfe7dc352907fc980b868725387e98c7c54b07bcddd6ccbf7ab80cf5a1cd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854f66081e984e37a0c2f1dfabf79d5bf", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff9966d27772c45f726cca542db8413", "guid": "bfdfe7dc352907fc980b868725387e989b10e4f1a2392b46188b46a696fe6f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ce1c5b29bbcae042f1c80ab91ef2e3", "guid": "bfdfe7dc352907fc980b868725387e98dd654257a1fd0c6e921147a42f67b5a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d8f661f782772d139ba929d3aa5cc0", "guid": "bfdfe7dc352907fc980b868725387e98a45519d9b7df12fe88c43fd3734edd28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a096e9ab485c6d7d608ef4cc6e9d833", "guid": "bfdfe7dc352907fc980b868725387e98f682da9a79143524b5f497de034aab22", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b97803a71000a1d854f000abb6e22f16", "guid": "bfdfe7dc352907fc980b868725387e982719fbf133fe11908b7646a8732e8c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a13a54dfbc819a7b85e48c215e9ef06e", "guid": "bfdfe7dc352907fc980b868725387e98e7f1adcf8bcdd041993e95b76576e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44b60b74544f839a1254453fcfeecc8", "guid": "bfdfe7dc352907fc980b868725387e98c8ecff818db0dd67fdeef6fae4daf174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b2b552c13c6fffc213710b957e77e2", "guid": "bfdfe7dc352907fc980b868725387e98c4a5745524eda0f2de3c7aafd595192d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f5391708a97d9f70f7b007d4bfc019", "guid": "bfdfe7dc352907fc980b868725387e98028d032725aacbef77923adda72dc897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf7c7badc2243e042030111b9c41312", "guid": "bfdfe7dc352907fc980b868725387e9862752dfe48b852effa95e644f27aa841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532afa025b9e61696ffd73b47309c74b", "guid": "bfdfe7dc352907fc980b868725387e980d82d6002380c17426352d2b6dcc403d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f6fc09f4ba1c39ffa904aae0ccdf07", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7cfc0b50c1a0983dc413d64a6302e2", "guid": "bfdfe7dc352907fc980b868725387e981104cbc90d9acbd26870d72ac58f83e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072172891a3bad031e55151c456011ff", "guid": "bfdfe7dc352907fc980b868725387e98228a73a0dea2578b100514b934e774fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d1d9f83a64439f98dc5f0acfface60", "guid": "bfdfe7dc352907fc980b868725387e98f01d1de579313563442377b603c7f1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d9bd5f29d3417ad48e5859c823904f", "guid": "bfdfe7dc352907fc980b868725387e9898e57582cd28f465ead1f88705177fee"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}