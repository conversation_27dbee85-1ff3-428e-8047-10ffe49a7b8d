import 'package:json_annotation/json_annotation.dart';

part 'device_config_model.g.dart';

@JsonSerializable()
class DeviceConfigModel {
  final String? id;
  final bool isMobileApp;
  final String? os;
  final String? osVersion;
  final String? ipAddress;
  final String? latitude;
  final String? logitude;
  final String? deviceType;
  final String? device;

  DeviceConfigModel(
      {this.id, this.isMobileApp = true, this.os, this.osVersion, this.ipAddress, this.latitude, this.logitude, this.deviceType, this.device});

  factory DeviceConfigModel.fromJson(Map<String, dynamic> json) => _$DeviceConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceConfigModelToJson(this);

  DeviceConfigModel copyWith({
    String? id,
    bool? isMobileApp,
    String? os,
    String? osVersion,
    String? ipAddress,
    String? latitude,
    String? logitude,
    String? deviceType,
    String? device,
  }) {
    return DeviceConfigModel(
      id: id ?? this.id,
      isMobileApp: isMobileApp ?? this.isMobileApp,
      os: os ?? this.os,
      osVersion: osVersion ?? this.osVersion,
      ipAddress: ipAddress ?? this.ipAddress,
      latitude: latitude ?? this.latitude,
      logitude: logitude ?? this.logitude,
      deviceType: deviceType ?? this.deviceType,
      device: device ?? this.device,
    );
  }
}
