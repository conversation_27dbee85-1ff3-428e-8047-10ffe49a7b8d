import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/common/widgets/saved_filter/saved_filter_widget.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class SearchFilterWidget extends StatefulWidget {
  final void Function()? onSearchTap;
  final void Function()? onFilterTap;
  final String hintText;
  final List<String>? rotatingHints;
  final HintAnimationType? animationType;
  final Duration animationDuration;
  final Duration typingSpeed;
  final Duration erasingSpeed;
  final Duration holdDuration;
  final Duration pauseDuration;

  final bool isSavedFilterEnabled;
  final SavedFilterModule? savedFilterModule;
  final void Function(ItemSimpleModel<GetSavedFilterModel> filter)? onEditSavedFilter;

  const SearchFilterWidget({
    super.key,
    this.onSearchTap,
    this.onFilterTap,
    required this.hintText,
    this.rotatingHints,
    this.animationType,
    this.animationDuration = const Duration(milliseconds: 800),
    this.typingSpeed = const Duration(milliseconds: 50),
    this.erasingSpeed = const Duration(milliseconds: 30),
    this.holdDuration = const Duration(milliseconds: 800),
    this.pauseDuration = const Duration(milliseconds: 300),
    this.isSavedFilterEnabled = false,
    this.savedFilterModule,
    this.onEditSavedFilter,
  }) : assert(
          !isSavedFilterEnabled || savedFilterModule != null,
          'savedFilterModule must not be null if isSavedFilterEnabled is true',
        );

  @override
  State<SearchFilterWidget> createState() => _SearchFilterWidgetState();
}

class _SearchFilterWidgetState extends State<SearchFilterWidget> with SingleTickerProviderStateMixin {
  AnimationController? _animationController;
  Animation<double>? _fadeAnimation;
  AnimatedHintCubit? _hintCubit;
  bool _isAnimated = false;

  @override
  void initState() {
    super.initState();

    _isAnimated = widget.rotatingHints != null && widget.rotatingHints!.isNotEmpty && widget.animationType != null;

    if (_isAnimated) {
      _animationController = AnimationController(
        vsync: this,
        duration: widget.animationDuration,
      );

      _fadeAnimation = Tween<double>(
        begin: 1.0,
        end: 0.0,
      ).animate(
        CurvedAnimation(
          parent: _animationController!,
          curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
          reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeIn),
        ),
      );

      if (widget.animationType == HintAnimationType.typewriter) {
        _hintCubit = AnimatedHintCubit();
        _hintCubit!.startTypewriter(widget.rotatingHints!, widget.typingSpeed, widget.erasingSpeed, widget.holdDuration, widget.pauseDuration);
      } else {
        _animationController!.addStatusListener((status) {
          if (status == AnimationStatus.completed) {
            setState(() {
              _currentFadeIndex = (_currentFadeIndex + 1) % widget.rotatingHints!.length;
            });
            _animationController!.reverse();
          } else if (status == AnimationStatus.dismissed) {
            _animationController!.forward();
          }
        });

        _animationController!.forward();
      }
    }
  }

  int _currentFadeIndex = 0;

  @override
  void dispose() {
    _animationController?.dispose();
    _hintCubit?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: widget.onSearchTap,
            child: Container(
              height: 40,
              padding: const EdgeInsets.only(left: 10, right: 6),
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: const Color.fromRGBO(73, 79, 86, 0.4), border: Border.all(color: ColorPalette.primaryLightColor, width: 1)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: _buildHintText(),
                  ),
                  SvgPicture.asset(
                    ImageResources.iconSearch,
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 10),
        if (widget.isSavedFilterEnabled)
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: SavedFilterWidget(
              savedFilterModule: widget.savedFilterModule ?? SavedFilterModule.leads,
              onEditSavedFilter: widget.onEditSavedFilter,
            ),
          ),
        GestureDetector(
          onTap: widget.onFilterTap,
          child: Container(
            height: 40,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: const Color.fromRGBO(73, 79, 86, 0.4),
                border: Border.all(
                  color: ColorPalette.primaryLightColor,
                  width: 1,
                )),
            child: SvgPicture.asset(ImageResources.iconFilter),
          ),
        ),
      ],
    );
  }

  Widget _buildHintText() {
    if (!_isAnimated) {
      return Text(
        widget.hintText,
        style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.gray600),
        overflow: TextOverflow.ellipsis,
      );
    }

    if (widget.animationType == HintAnimationType.typewriter) {
      return BlocProvider(
        create: (context) => AnimatedHintCubit(),
        child: BlocBuilder<AnimatedHintCubit, AnimatedHintState>(
          bloc: _hintCubit,
          builder: (context, state) {
            String baseText = widget.hintText;
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  baseText,
                  style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.gray600),
                ),
                Text(
                  state.typewriterText,
                  style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.gray600),
                ),
              ],
            );
          },
        ),
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.hintText,
            style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.gray600),
          ),
          AnimatedBuilder(
            animation: _animationController!,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation!,
                child: Text(
                  widget.rotatingHints![_currentFadeIndex],
                  style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.gray600),
                ),
              );
            },
          ),
        ],
      );
    }
  }
}

enum HintAnimationType { fade, typewriter }

class AnimatedHintCubit extends Cubit<AnimatedHintState> {
  AnimatedHintCubit() : super(AnimatedHintState.initial());

  Timer? _typewriterTimer;

  void updateCurrentIndex(int index) {
    if (isClosed) return;
    emit(state.copyWith(currentIndex: index));
  }

  void updateTypewriterText(String text) {
    if (isClosed) return;
    emit(state.copyWith(typewriterText: text));
  }

  void setIsTyping(bool value) {
    if (isClosed) return;
    emit(state.copyWith(isTyping: value));
  }

  void startTypewriter(List<String> hints, Duration typingSpeed, Duration erasingSpeed, Duration holdDuration, Duration pauseDuration) {
    if (hints.isEmpty) return;

    setIsTyping(true);
    updateTypewriterText('');
    int charIndex = 0;

    _typewriterTimer?.cancel();

    _typewriterTimer = Timer.periodic(typingSpeed, (timer) {
      if (isClosed) {
        timer.cancel();
        return;
      }

      String currentHint = hints[state.currentIndex];

      if (charIndex < currentHint.length) {
        updateTypewriterText(currentHint.substring(0, charIndex + 1));
        charIndex++;
      } else {
        timer.cancel();

        Future.delayed(holdDuration, () {
          if (isClosed) return;

          _typewriterTimer = Timer.periodic(erasingSpeed, (timer) {
            if (isClosed) {
              timer.cancel();
              return;
            }

            if (state.typewriterText.isNotEmpty) {
              updateTypewriterText(state.typewriterText.substring(0, state.typewriterText.length - 1));
            } else {
              timer.cancel();
              setIsTyping(false);

              Future.delayed(pauseDuration, () {
                if (isClosed) return;

                int nextIndex = (state.currentIndex + 1) % hints.length;
                updateCurrentIndex(nextIndex);
                startTypewriter(hints, typingSpeed, erasingSpeed, holdDuration, pauseDuration);
              });
            }
          });
        });
      }
    });
  }

  @override
  Future<void> close() {
    _typewriterTimer?.cancel();
    return super.close();
  }
}

class AnimatedHintState {
  final int currentIndex;
  final String typewriterText;
  final bool isTyping;

  AnimatedHintState({
    required this.currentIndex,
    required this.typewriterText,
    required this.isTyping,
  });

  factory AnimatedHintState.initial() {
    return AnimatedHintState(
      currentIndex: 0,
      typewriterText: '',
      isTyping: false,
    );
  }

  AnimatedHintState copyWith({
    int? currentIndex,
    String? typewriterText,
    bool? isTyping,
  }) {
    return AnimatedHintState(
      currentIndex: currentIndex ?? this.currentIndex,
      typewriterText: typewriterText ?? this.typewriterText,
      isTyping: isTyping ?? this.isTyping,
    );
  }
}
