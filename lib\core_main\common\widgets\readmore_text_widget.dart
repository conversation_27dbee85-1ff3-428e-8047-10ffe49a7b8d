import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/cubit/readmore_cubit.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

import 'link_text_span.dart';

class ReadMoreTextWidget extends LeadratStatelessWidget {
  final String notes;
  final bool isLeftWidth;
  final bool canBuildLinkTextSpan;

  const ReadMoreTextWidget({super.key, required this.notes, this.isLeftWidth = true, this.canBuildLinkTextSpan = false});

  @override
  Widget buildContent(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ReadMoreCubit>(),
      child: BlocBuilder<ReadMoreCubit, bool>(
        builder: (context, state) {
          return Column(
            children: [
              Padding(
                padding: EdgeInsets.only(left: isLeftWidth ? 10 : 0, top: isLeftWidth ? 10 : 0, bottom: 3),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(width: isLeftWidth ? 10 : 6),
                    SizedBox(
                      width: context.width(70),
                      child: canBuildLinkTextSpan
                          ? Text.rich(
                              buildLinkTextSpan(text: notes, style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.primary)),
                              maxLines: state ? notes.length : 2,
                            )
                          : Text(
                              notes,
                              style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.primary),
                              maxLines: state ? notes.length : 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => context.read<ReadMoreCubit>().toggleReadMore(),
                child: Visibility(
                  visible: notes.length > 75,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(!state ? 'read more' : 'read less', style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryGreen)),
                      Icon(!state ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_up, size: 15, color: ColorPalette.primaryGreen),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
