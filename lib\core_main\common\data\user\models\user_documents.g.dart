// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_documents.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserDocumentModelAdapter extends TypeAdapter<UserDocumentModel> {
  @override
  final int typeId = 108;

  @override
  UserDocumentModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserDocumentModel(
      id: fields[0] as String?,
      docName: fields[1] as String?,
      filePath: fields[2] as String?,
      documentType: fields[3] as DocumentType?,
      userId: fields[4] as String?,
      createdBy: fields[6] as String?,
      createdOn: fields[5] as DateTime?,
      lastModifiedOn: fields[7] as DateTime?,
      lastModifiedBy: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, UserDocumentModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.docName)
      ..writeByte(2)
      ..write(obj.filePath)
      ..writeByte(3)
      ..write(obj.documentType)
      ..writeByte(4)
      ..write(obj.userId)
      ..writeByte(5)
      ..write(obj.createdOn)
      ..writeByte(6)
      ..write(obj.createdBy)
      ..writeByte(7)
      ..write(obj.lastModifiedOn)
      ..writeByte(8)
      ..write(obj.lastModifiedBy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDocumentModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDocumentModel _$UserDocumentModelFromJson(Map<String, dynamic> json) =>
    UserDocumentModel(
      id: json['id'] as String?,
      docName: json['docName'] as String?,
      filePath: json['filePath'] as String?,
      documentType:
          $enumDecodeNullable(_$DocumentTypeEnumMap, json['documentType']),
      userId: json['userId'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$UserDocumentModelToJson(UserDocumentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'docName': instance.docName,
      'filePath': instance.filePath,
      'documentType': _$DocumentTypeEnumMap[instance.documentType],
      'userId': instance.userId,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.defaultType: 0,
  DocumentType.identity: 1,
  DocumentType.experience: 2,
  DocumentType.signature: 3,
  DocumentType.jpg: 4,
  DocumentType.png: 5,
  DocumentType.pdf: 6,
  DocumentType.docx: 7,
  DocumentType.txt: 8,
  DocumentType.csv: 9,
};
