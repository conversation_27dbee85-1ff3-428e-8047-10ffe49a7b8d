// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_project_attribute_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterProjectAttributeModelAdapter
    extends TypeAdapter<MasterProjectAttributeModel> {
  @override
  final int typeId = 31;

  @override
  MasterProjectAttributeModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterProjectAttributeModel(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdBy: fields[2] as String?,
      createdOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      lastModifiedOn: fields[5] as DateTime?,
      deletedOn: fields[6] as DateTime?,
      deletedBy: fields[7] as String?,
      attributeName: fields[8] as String?,
      attributeDisplayName: fields[9] as String?,
      attributeType: fields[10] as String?,
      defaultValue: fields[11] as String?,
      orderRank: fields[12] as int?,
      mobileIcon: fields[13] as String?,
      basePropertyType: (fields[14] as List?)?.cast<BasePropertyType>(),
    );
  }

  @override
  void write(BinaryWriter writer, MasterProjectAttributeModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.createdOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.deletedOn)
      ..writeByte(7)
      ..write(obj.deletedBy)
      ..writeByte(8)
      ..write(obj.attributeName)
      ..writeByte(9)
      ..write(obj.attributeDisplayName)
      ..writeByte(10)
      ..write(obj.attributeType)
      ..writeByte(11)
      ..write(obj.defaultValue)
      ..writeByte(12)
      ..write(obj.orderRank)
      ..writeByte(13)
      ..write(obj.mobileIcon)
      ..writeByte(14)
      ..write(obj.basePropertyType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterProjectAttributeModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterProjectAttributeModel _$MasterProjectAttributeModelFromJson(
        Map<String, dynamic> json) =>
    MasterProjectAttributeModel(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      attributeName: json['attributeName'] as String?,
      attributeDisplayName: json['attributeDisplayName'] as String?,
      attributeType: json['attributeType'] as String?,
      defaultValue: json['defaultValue'] as String?,
      orderRank: (json['orderRank'] as num?)?.toInt(),
      mobileIcon: json['mobileIcon'] as String?,
      basePropertyType: (json['basePropertyType'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BasePropertyTypeEnumMap, e))
          .toList(),
    );

Map<String, dynamic> _$MasterProjectAttributeModelToJson(
        MasterProjectAttributeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'attributeName': instance.attributeName,
      'attributeDisplayName': instance.attributeDisplayName,
      'attributeType': instance.attributeType,
      'defaultValue': instance.defaultValue,
      'orderRank': instance.orderRank,
      'mobileIcon': instance.mobileIcon,
      'basePropertyType': instance.basePropertyType
          ?.map((e) => _$BasePropertyTypeEnumMap[e]!)
          .toList(),
    };

const _$BasePropertyTypeEnumMap = {
  BasePropertyType.residential: 0,
  BasePropertyType.commercial: 1,
  BasePropertyType.agricultural: 2,
};
