import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'dart:ui' show lerpDouble;

class SelectableReorderAbleListWidget extends StatefulWidget {
  final List<ItemSimpleModel> categories;
  final int maxSelections;
  final ValueChanged<List<ItemSimpleModel>>? onSelectionChanged;
  final bool showDragHandle;
  final bool showPinOption;
  final bool Function(ItemSimpleModel)? isItemSelectable;
  final List<ItemSimpleModel>? defaultSelection;
  final List<ItemSimpleModel>? selectedItems;
  final Map<String, int>? itemIndexes;

  const SelectableReorderAbleListWidget({
    Key? key,
    required this.categories,
    this.maxSelections = 7,
    this.onSelectionChanged,
    this.showDragHandle = true,
    this.showPinOption = false,
    this.isItemSelectable,
    this.selectedItems,
    this.defaultSelection,
    this.itemIndexes,
  }) : super(key: key);

  @override
  State<SelectableReorderAbleListWidget> createState() => _SelectableReorderAbleListWidgetState();
}

class _SelectableReorderAbleListWidgetState extends State<SelectableReorderAbleListWidget> {
  late List<ItemSimpleModel> _categories;

  @override
  void initState() {
    super.initState();
    _categories = widget.categories.map((category) => category.copyWith()).toList();

    // If itemIndexes is provided, arrange items according to the specified indexes
    if (widget.itemIndexes != null) {
      _arrangeItemsByIndexes();
    }

    // Set default selection if provided
    if (widget.defaultSelection != null) {
      setDefaultSelection(widget.defaultSelection!);
    }

    if (widget.selectedItems != null) {
      setSelectionItems(widget.selectedItems!);
    }
  }

  // Method to arrange items according to provided indexes
  void _arrangeItemsByIndexes() {
    if (widget.itemIndexes == null) return;

    if (_categories.isNotEmpty) {
      // Create a new list to hold the arranged items
      final List<ItemSimpleModel> arrangedItems = List.filled(_categories.length, _categories.first);

      final List<ItemSimpleModel> unPositionedItems = [];

      // Place items at their specified positions
      for (var item in _categories) {
        final specifiedIndex = widget.itemIndexes![item.title];

        if (specifiedIndex != null && specifiedIndex < _categories.length) {
          arrangedItems[specifiedIndex] = item;
        } else {
          unPositionedItems.add(item);
        }
      }

      // Fill in any remaining positions with unpositioned items
      int unPositionedIndex = 0;
      for (int i = 0; i < arrangedItems.length; i++) {
        if (arrangedItems[i] == _categories.first && unPositionedIndex < unPositionedItems.length) {
          arrangedItems[i] = unPositionedItems[unPositionedIndex];
          unPositionedIndex++;
        }
      }

      // Update the categories list with the arranged items
      _categories = List.from(arrangedItems.where((item) => item != _categories.first));
    }
  }

  List<ItemSimpleModel> get selectedCategories => _categories.where((c) => c.isSelected).toList();

  List<ItemSimpleModel> get unselectedCategories => _categories.where((c) => !c.isSelected).toList();

  void _toggleSelection(ItemSimpleModel category) {
    setState(() {
      final index = _categories.indexWhere((c) => c.title == category.title);
      if (index == -1) return; // Safety check

      if (category.isSelected) {
        // Check if this is the last selected item
        if (selectedCategories.length == 1) {
          LeadratCustomSnackbar.show(context: context, message: "At least one category must remain selected.", type: SnackbarType.error);
          return;
        }

        // Deselect the item
        _categories[index] = _categories[index].copyWith(isSelected: false);
      } else {
        // Check if we've reached max selections
        if (selectedCategories.length >= widget.maxSelections) {
          LeadratCustomSnackbar.show(context: context, message: 'You can select a maximum of ${widget.maxSelections} categories.', type: SnackbarType.error);
          return;
        }

        // Select the item
        _categories[index] = _categories[index].copyWith(isSelected: true);
      }

      // Maintain the correct order: selected items first, then unselected
      final selected = selectedCategories;
      final unselected = unselectedCategories;
      _categories = [...selected, ...unselected];

      // Notify listener about the change
      widget.onSelectionChanged?.call(selected);
    });
  }

  void _reorderSelectedCategories(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) newIndex--;

      final selectedList = List<ItemSimpleModel>.from(selectedCategories);
      final item = selectedList.removeAt(oldIndex);
      selectedList.insert(newIndex, item);

      final unselected = unselectedCategories;
      _categories = [...selectedList, ...unselected];

      widget.onSelectionChanged?.call(selectedList);
    });
  }

  void setDefaultSelection(List<ItemSimpleModel> defaultSelected) {
    setState(() {
      final selectedTitles = defaultSelected.map((d) => d.title).toList();

      // Create a new list with updated selection states
      _categories = _categories.map((category) {
        final isSelected = selectedTitles.contains(category.title);
        return category.copyWith(isSelected: isSelected);
      }).toList();

      // Sort categories: selected first, then unselected
      final selected = _categories.where((c) => c.isSelected).toList();
      final unselected = _categories.where((c) => !c.isSelected).toList();
      _categories = [...selected, ...unselected];

      // Defer the callback to avoid calling setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelectionChanged?.call(selected);
      });
    });
  }

  void setSelectionItems(List<ItemSimpleModel> selectedItems) {
    setState(() {
      final selectedTitles = selectedItems.map((d) => d.title).toList();

      // Create a new list with updated selection states
      _categories = _categories.map((category) {
        final isSelected = selectedTitles.contains(category.title);
        return category.copyWith(isSelected: isSelected);
      }).toList();

      // Sort categories: selected first, then unselected
      final selected = _categories.where((c) => c.isSelected).toList();
      final unselected = _categories.where((c) => !c.isSelected).toList();
      _categories = [...selected, ...unselected];

      // Defer the callback to avoid calling setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelectionChanged?.call(selected);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final selected = selectedCategories;
    final unselected = unselectedCategories;

    return Material(
      color: ColorPalette.primaryDarkColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Selected', ColorPalette.green, () {
            if (widget.defaultSelection != null) {
              setDefaultSelection(widget.defaultSelection!);
            }
          }),
          // Selected items section - uses shrinkWrap to only take needed space
          Container(
            constraints: BoxConstraints(
              maxHeight: selected.isEmpty ? 0 : MediaQuery.of(context).size.height * 0.4,
              minHeight: selected.isEmpty ? 0 : 50, // Minimum height when there's at least one item
            ),
            child: selected.isEmpty
                ? const SizedBox.shrink()
                : ReorderableListView.builder(
                    shrinkWrap: true,
                    itemCount: selected.length,
                    onReorder: _reorderSelectedCategories,
                    // Add custom drag indicator decoration
                    proxyDecorator: (Widget child, int index, Animation<double> animation) {
                      return AnimatedBuilder(
                        animation: animation,
                        builder: (BuildContext context, Widget? child) {
                          final double animValue = Curves.easeInOut.transform(animation.value);
                          final double elevation = lerpDouble(0, 6, animValue)!;
                          return Material(
                            elevation: elevation,
                            color: Colors.transparent,
                            shadowColor: ColorPalette.black.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              decoration: BoxDecoration(
                                color: ColorPalette.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: ColorPalette.transparent,
                                  width: 2 * animValue,
                                ),
                              ),
                              child: child,
                            ),
                          );
                        },
                        child: child,
                      );
                    },
                    itemBuilder: (context, index) {
                      final category = selected[index];
                      return CategoryTile(
                        key: ValueKey('selected_${category.title}_$index'),
                        category: category,
                        onTap: () => _toggleSelection(category),
                        showDragHandle: widget.showDragHandle,
                      );
                    },
                  ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Divider(height: 1, color: ColorPalette.lightBackground),
          ),
          // Unselected items section - takes remaining space
          Expanded(
            child: ListView.builder(
              itemCount: unselected.length,
              itemBuilder: (context, index) {
                final category = unselected[index];
                return CategoryTile(
                  key: ValueKey('unselected_${category.title}_$index'),
                  category: category,
                  onTap: () => _toggleSelection(category),
                  showDragHandle: false,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, Color color, VoidCallback onResetTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: LexendTextStyles.lexend12Medium.copyWith(color: color),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(
                  Icons.restart_alt,
                  size: 20,
                  color: ColorPalette.gray500,
                ),
                onPressed: onResetTap,
              ),
            ],
          ),
          const SizedBox(height: 5),
          const Divider(height: 1, color: ColorPalette.lightBackground),
        ],
      ),
    );
  }
}

class CategoryTile extends StatelessWidget {
  final ItemSimpleModel category;
  final VoidCallback? onTap;
  final bool showDragHandle;

  const CategoryTile({
    Key? key,
    required this.category,
    this.onTap,
    this.showDragHandle = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSelectable = category.isEnabled ?? true;

    return InkWell(
      onTap: isSelectable ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        child: Row(
          children: [
            Checkbox(
              value: category.isSelected,
              onChanged: isSelectable ? (_) => onTap?.call() : null,
              activeColor: ColorPalette.green,
              checkColor: ColorPalette.white,
              side: BorderSide(
                color: isSelectable ? ColorPalette.gray500 : ColorPalette.green.withValues(alpha: 0.8),
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            if (category.imageResource != null)
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Image.asset(
                  category.imageResource!,
                  width: 20,
                  height: 20,
                  fit: BoxFit.cover,
                ),
              ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.title,
                    style: LexendTextStyles.lexend12ExtraLight.copyWith(
                      color: isSelectable ? ColorPalette.primary : ColorPalette.gray500,
                    ),
                  ),
                ],
              ),
            ),
            if (showDragHandle) const Icon(Icons.drag_handle_rounded, color: ColorPalette.gray200, size: 20),
          ],
        ),
      ),
    );
  }
}
