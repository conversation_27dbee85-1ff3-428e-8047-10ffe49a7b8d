import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/local/app_analysis_local_data_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/models/app_feature_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/local_storage_service/local_storage_service.dart';

class AppAnalysisLocalDataSourceImpl implements AppAnalysisLocalDataSource {
  final LocalStorageService _localStorageService;

  AppAnalysisLocalDataSourceImpl(this._localStorageService);

  @override
  List<AppFeatureModel>? getAppFeatures() {
    try {
      return _localStorageService.getAllItems<AppFeatureModel>(HiveModelConstants.appFeatureBoxName);
    } catch (ex) {
      ex.logException();
      return null;
    }
  }

  @override
  Future<void> saveAppFeatures(List<AppFeatureModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.appFeatureBoxName);
      final validItems = items.whereType<AppFeatureModel>().toList();
      await _localStorageService.addItems<AppFeatureModel>(HiveModelConstants.appFeatureBoxName, validItems);
    } catch (ex) {
      ex.logException();
    }
  }
}
