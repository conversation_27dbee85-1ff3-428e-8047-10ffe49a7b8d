import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'flag_model.g.dart';

@HiveType(typeId: HiveModelConstants.viewFlagModelTypeId)
@JsonSerializable()
class ViewFlagModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final String? activeImagePath;
  @HiveField(3)
  final String? inactiveImagePath;
  @HiveField(4)
  final String? activeBackgroundColor;
  @HiveField(5)
  final String? inactiveBackgroundColor;
  @HiveField(6)
  final String? module;
  @HiveField(7)
  final String? notes;
  @HiveField(8)
  final bool? isActive;
  @HiveField(9)
  final DateTime? lastModifiedOn;
  @HiveField(10)
  final DateTime? createdOn;
  @HiveField(11)
  final String? createdBy;
  @HiveField(12)
  final String? lastModifiedBy;

  ViewFlagModel({
    this.id,
    this.name,
    this.activeImagePath,
    this.inactiveImagePath,
    this.activeBackgroundColor,
    this.inactiveBackgroundColor,
    this.module,
    this.notes,
    this.isActive,
    this.lastModifiedOn,
    this.createdOn,
    this.createdBy,
    this.lastModifiedBy,
  });

  factory ViewFlagModel.fromJson(Map<String, dynamic> json) =>
      _$ViewFlagModelFromJson(json);

  Map<String, dynamic> toJson() => _$ViewFlagModelToJson(this);

}