import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum FurnishStatus {
  none(0, "None"),
  unfurnished(1, "Unfurnished"),
  semifunrished(2, "Semi-furnished"),
  furnished(3, "Furnished");

  final int value;
  final String description;

  const FurnishStatus(this.value, this.description);
}

String getFurnishStatusDescription(int value) {
  for (var status in FurnishStatus.values) {
    if (status.value == value) {
      return status.description;
    }
  }
  throw ArgumentError('Invalid value: $value');
}
