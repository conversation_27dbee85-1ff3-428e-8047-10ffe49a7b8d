part of 'search_bloc.dart';

@immutable
sealed class SearchEvent {}

final class SearchInitialEvent extends SearchEvent {
  final AppModule appModule;

  SearchInitialEvent(this.appModule);
}

final class SearchLeadEvent extends SearchEvent {
  final String? searchText;

  SearchLeadEvent({
    this.searchText,
  });
}

final class GetLeadsEvent extends SearchEvent {}

final class GetPropertiesEvent extends SearchEvent {}

final class GetPropertyListingEvent extends SearchEvent {}

final class UpdatePropertyListingsEvent extends SearchEvent {
  final List<ItemListingManagementModel>? propertyListing;

  UpdatePropertyListingsEvent(this.propertyListing);
}

final class GetProjectsEvent extends SearchEvent {}

final class ScrolledEvent extends SearchEvent {}

final class ToggleTagsEvent extends SearchEvent {
  final bool isVisible;

  ToggleTagsEvent({required this.isVisible});
}

final class RemoveSelectedItemEvent extends SearchEvent {
  final SearchFilterModel? removeItem;

  RemoveSelectedItemEvent({required this.removeItem});
}

final class RemoveLabelEvent extends SearchEvent {
  final SearchFilterModel? label;

  RemoveLabelEvent(this.label);
}

final class SearchDisposeEvent extends SearchEvent {}

final class UpdateRecentSearchEvent extends SearchEvent {}
