import 'package:flutter/material.dart';

extension SpacingExtension on BuildContext {
  double height(double percentage) {
    return (MediaQuery.of(this).size.height * percentage) / 100;
  }

  double width(double percentage) {
    return (MediaQuery.of(this).size.width * percentage) / 100;
  }

  double verticalSpacing(double percentage) {
    return MediaQuery.of(this).size.height * percentage / 100;
  }

  double horizontalSpacing(double percentage) {
    return MediaQuery.of(this).size.width * percentage / 100;
  }

  double topSpacing(double percentage) {
    return MediaQuery.of(this).size.height * percentage / 100;
  }

  double bottomSpacing(double percentage) {
    return MediaQuery.of(this).size.height * percentage / 100;
  }
}
