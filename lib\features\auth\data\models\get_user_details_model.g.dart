// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_user_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetUserDetailsModel _$GetUserDetailsModelFromJson(Map<String, dynamic> json) =>
    GetUserDetailsModel(
      id: json['id'] as String?,
      userName: json['userName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      email: json['email'] as String?,
      isActive: json['isActive'] as bool?,
      emailConfirmed: json['emailConfirmed'] as bool?,
      phoneNumber: json['phoneNumber'] as String?,
      imageUrl: json['imageUrl'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      isMFAEnabled: json['isMFAEnabled'] as bool?,
      otp: json['otp'] as String?,
      otpUpdatedOn: json['otpUpdatedOn'] == null
          ? null
          : DateTime.parse(json['otpUpdatedOn'] as String),
    );

Map<String, dynamic> _$GetUserDetailsModelToJson(
        GetUserDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userName': instance.userName,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'isActive': instance.isActive,
      'emailConfirmed': instance.emailConfirmed,
      'phoneNumber': instance.phoneNumber,
      'imageUrl': instance.imageUrl,
      'isDeleted': instance.isDeleted,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedBy': instance.lastModifiedBy,
      'isMFAEnabled': instance.isMFAEnabled,
      'otp': instance.otp,
      'otpUpdatedOn': instance.otpUpdatedOn?.toIso8601String(),
    };
