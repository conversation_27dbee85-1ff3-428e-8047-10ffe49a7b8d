import 'package:leadrat/core_main/common/data/saved_filter/models/create_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';

abstract interface class SavedFilterRemoteDataSource {
  Future<PagedResponse<GetSavedFilterModel, String>> getSavedFilters({required String module, String? searchText});

  Future<String> createSavedFilter(CreateSavedFilterModel model);

  Future<String> updateSavedFilter(CreateSavedFilterModel model);

  Future<GetSavedFilterModel?> getSavedFilterById(String id);

  Future<bool> deleteSavedFilter(String id);

  Future<bool> checkIfFilterNameExits({required String filterName,required String module});
}
