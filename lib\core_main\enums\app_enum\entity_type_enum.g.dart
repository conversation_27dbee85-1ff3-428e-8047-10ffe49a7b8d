// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'entity_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class EntityTypeEnumAdapter extends TypeAdapter<EntityTypeEnum> {
  @override
  final int typeId = 3;

  @override
  EntityTypeEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return EntityTypeEnum.none;
      case 1:
        return EntityTypeEnum.lead;
      case 2:
        return EntityTypeEnum.userDetails;
      case 3:
        return EntityTypeEnum.masterAreaUnit;
      case 4:
        return EntityTypeEnum.masterLeadSource;
      case 5:
        return EntityTypeEnum.masterLeadStatus;
      case 6:
        return EntityTypeEnum.masterPropertyAmenity;
      case 7:
        return EntityTypeEnum.masterPropertyAttribute;
      case 8:
        return EntityTypeEnum.masterPropertyType;
      case 9:
        return EntityTypeEnum.profile;
      case 10:
        return EntityTypeEnum.roles;
      case 11:
        return EntityTypeEnum.currentUser;
      case 12:
        return EntityTypeEnum.agencyNames;
      case 13:
        return EntityTypeEnum.masterWhatsAppTemplate;
      case 14:
        return EntityTypeEnum.whatsAppTemplate;
      case 15:
        return EntityTypeEnum.template;
      case 16:
        return EntityTypeEnum.tempProjects;
      case 17:
        return EntityTypeEnum.property;
      case 18:
        return EntityTypeEnum.leadAddresses;
      case 19:
        return EntityTypeEnum.userSettings;
      case 20:
        return EntityTypeEnum.customMasterLeadStatus;
      case 21:
        return EntityTypeEnum.globalSettings;
      case 22:
        return EntityTypeEnum.flag;
      case 23:
        return EntityTypeEnum.mobileCatalogDetail;
      case 24:
        return EntityTypeEnum.customMasterAttribute;
      case 25:
        return EntityTypeEnum.customMasterAmenity;
      case 26:
        return EntityTypeEnum.customFilters;
      case 27:
        return EntityTypeEnum.source;
      case 28:
        return EntityTypeEnum.customMasterDataStatus;
      case 29:
        return EntityTypeEnum.project;
      default:
        return EntityTypeEnum.none;
    }
  }

  @override
  void write(BinaryWriter writer, EntityTypeEnum obj) {
    switch (obj) {
      case EntityTypeEnum.none:
        writer.writeByte(0);
        break;
      case EntityTypeEnum.lead:
        writer.writeByte(1);
        break;
      case EntityTypeEnum.userDetails:
        writer.writeByte(2);
        break;
      case EntityTypeEnum.masterAreaUnit:
        writer.writeByte(3);
        break;
      case EntityTypeEnum.masterLeadSource:
        writer.writeByte(4);
        break;
      case EntityTypeEnum.masterLeadStatus:
        writer.writeByte(5);
        break;
      case EntityTypeEnum.masterPropertyAmenity:
        writer.writeByte(6);
        break;
      case EntityTypeEnum.masterPropertyAttribute:
        writer.writeByte(7);
        break;
      case EntityTypeEnum.masterPropertyType:
        writer.writeByte(8);
        break;
      case EntityTypeEnum.profile:
        writer.writeByte(9);
        break;
      case EntityTypeEnum.roles:
        writer.writeByte(10);
        break;
      case EntityTypeEnum.currentUser:
        writer.writeByte(11);
        break;
      case EntityTypeEnum.agencyNames:
        writer.writeByte(12);
        break;
      case EntityTypeEnum.masterWhatsAppTemplate:
        writer.writeByte(13);
        break;
      case EntityTypeEnum.whatsAppTemplate:
        writer.writeByte(14);
        break;
      case EntityTypeEnum.template:
        writer.writeByte(15);
        break;
      case EntityTypeEnum.tempProjects:
        writer.writeByte(16);
        break;
      case EntityTypeEnum.property:
        writer.writeByte(17);
        break;
      case EntityTypeEnum.leadAddresses:
        writer.writeByte(18);
        break;
      case EntityTypeEnum.userSettings:
        writer.writeByte(19);
        break;
      case EntityTypeEnum.customMasterLeadStatus:
        writer.writeByte(20);
        break;
      case EntityTypeEnum.globalSettings:
        writer.writeByte(21);
        break;
      case EntityTypeEnum.flag:
        writer.writeByte(22);
        break;
      case EntityTypeEnum.mobileCatalogDetail:
        writer.writeByte(23);
        break;
      case EntityTypeEnum.customMasterAttribute:
        writer.writeByte(24);
        break;
      case EntityTypeEnum.customMasterAmenity:
        writer.writeByte(25);
        break;
      case EntityTypeEnum.customFilters:
        writer.writeByte(26);
        break;
      case EntityTypeEnum.source:
        writer.writeByte(27);
        break;
      case EntityTypeEnum.customMasterDataStatus:
        writer.writeByte(28);
        break;
      case EntityTypeEnum.project:
        writer.writeByte(29);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EntityTypeEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
