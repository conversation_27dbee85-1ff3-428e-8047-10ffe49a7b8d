// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'todo_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetTodoDataModel _$GetTodoDataModelFromJson(Map<String, dynamic> json) =>
    GetTodoDataModel(
      todaysTodosCount: (json['todaysTodosCount'] as num?)?.toInt(),
      upcomingTodosCount: (json['upcomingTodosCount'] as num?)?.toInt(),
      completedTodosCount: (json['completedTodosCount'] as num?)?.toInt(),
      overdueTodosCount: (json['overdueTodosCount'] as num?)?.toInt(),
      allTodosCount: (json['allTodosCount'] as num?)?.toInt(),
      tasksDto: json['tasksDto'] == null
          ? null
          : TasksDto.fromJson(json['tasksDto'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetTodoDataModelToJson(GetTodoDataModel instance) =>
    <String, dynamic>{
      if (instance.todaysTodosCount case final value?)
        'todaysTodosCount': value,
      if (instance.upcomingTodosCount case final value?)
        'upcomingTodosCount': value,
      if (instance.completedTodosCount case final value?)
        'completedTodosCount': value,
      if (instance.overdueTodosCount case final value?)
        'overdueTodosCount': value,
      if (instance.allTodosCount case final value?) 'allTodosCount': value,
      if (instance.tasksDto?.toJson() case final value?) 'tasksDto': value,
    };

TasksDto _$TasksDtoFromJson(Map<String, dynamic> json) => TasksDto(
      tasks: (json['tasks'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => Task.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
    );

Map<String, dynamic> _$TasksDtoToJson(TasksDto instance) => <String, dynamic>{
      if (instance.tasks
              ?.map((k, e) => MapEntry(k, e.map((e) => e.toJson()).toList()))
          case final value?)
        'tasks': value,
    };

Task _$TaskFromJson(Map<String, dynamic> json) => Task(
      id: json['id'] as String?,
      title: json['title'] as String?,
      notes: json['notes'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
      isMarkedDone: json['isMarkedDone'] as bool?,
      isRepeated: json['isRepeated'] as bool?,
      scheduledDateTime: json['scheduledDateTime'] == null
          ? null
          : DateTime.parse(json['scheduledDateTime'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$TaskToJson(Task instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.priority case final value?) 'priority': value,
      if (instance.isMarkedDone case final value?) 'isMarkedDone': value,
      if (instance.isRepeated case final value?) 'isRepeated': value,
      if (instance.scheduledDateTime?.toIso8601String() case final value?)
        'scheduledDateTime': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
    };
