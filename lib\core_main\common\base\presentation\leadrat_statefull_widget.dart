import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/resources/theme/app_theme.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/main.dart';

abstract class LeadratStatefulWidget extends StatefulWidget {
  const LeadratStatefulWidget({Key? key}) : super(key: key);
}

abstract class LeadratState<T extends LeadratStatefulWidget> extends State<T> with WidgetsBindingObserver, RouteAware {
  late final Connectivity _connectivity;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  bool _wasConnected = true;
  bool _isFirstLoad = true;

  @protected
  Future<void> onInitAsync() async {}

  @protected
  void onInit() {}

  @protected
  void onVisible() {}

  @protected
  void onInvisible() {}

  @protected
  void onDispose() {}

  @protected
  void onConnectivityChanged(bool isConnected) {}

  void _showConnectivityToast(bool isConnected) {
    LeadratCustomSnackbar.show(
      navigatorKey: MyApp.navigatorKey,
      type: isConnected ? SnackbarType.success : SnackbarType.error,
      message: isConnected ? "Back Online" : "You're offline. Please connect to the internet.",
      persistent: !isConnected,
    );
  }

  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _handleConnectivityResult(result);
    } catch (e, stackTrace) {
      logError(e, stackTrace);
    }
  }

  void _handleConnectivityResult(List<ConnectivityResult> connectivityResult) {
    final isConnected = !connectivityResult.contains(ConnectivityResult.none);
    if (isConnected != _wasConnected) {
      _showConnectivityToast(isConnected);
      onConnectivityChanged(isConnected);
      _wasConnected = isConnected;
    }
  }

  @override
  void initState() {
    super.initState();
    _connectivity = Connectivity();
    WidgetsBinding.instance.addObserver(this);

    onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _checkConnectivity();
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_handleConnectivityResult);
      await onInitAsync();
      _isFirstLoad = false;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route != null) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _connectivitySubscription?.cancel();
    FocusManager.instance.primaryFocus?.unfocus();
    DialogManager().hideTransparentProgressDialog();
    onDispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Theme(
        data: AppTheme.lightThemeMode,
        child: buildContent(context),
      ),
    );
  }

  @protected
  Widget buildContent(BuildContext context);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        _checkConnectivity();
        break;
      case AppLifecycleState.paused:
        onInvisible();
        break;
      default:
        break;
    }
  }

  @override
  void didPush() {
    if (!_isFirstLoad) {
      onVisible();
    }
  }

  @override
  void didPopNext() {
    onVisible();
  }

  @override
  void didPushNext() {
    onInvisible();
  }

  @override
  void didPop() {
    onInvisible();
  }

  @protected
  void logError(Object error, [StackTrace? stackTrace]) {
    'Error: $error'.printInConsole();
    if (stackTrace != null) {
      'Stack Trace: $stackTrace'.printInConsole();
    }
  }
}
