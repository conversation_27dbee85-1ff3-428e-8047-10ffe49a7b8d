{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e77fe72f1be5354d628923aa42f11982", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98165d541f6f14676423524f422a6d55a5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831e403d13c225b739c0ea5e2ce36a8fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e03fe947764fae05b73fc8c7c22324bd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831e403d13c225b739c0ea5e2ce36a8fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fb9000a43c5f68354798571c02269d2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b92c1db8ac32abe975e6976e0b31119", "guid": "bfdfe7dc352907fc980b868725387e988fe14a6af83c74fc1714a945422b41e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667934a4c6301fb64c202600dfdef344", "guid": "bfdfe7dc352907fc980b868725387e986510c00feb0115739b0576aba6587104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8c38faa76c6fd1a294485c99c34e4b", "guid": "bfdfe7dc352907fc980b868725387e9877c3a5bfdef752ed635649985891d0d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98457f1485cca580a32cac62afdff3c8a8", "guid": "bfdfe7dc352907fc980b868725387e987146881623a21f772cb596bdd07b1adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bccfdbcab6e1fb1a42490067d14d5f9a", "guid": "bfdfe7dc352907fc980b868725387e98983946871cde5f3bad52698e82cccbe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb16ca770dfb9ce3a616923e5ae461b", "guid": "bfdfe7dc352907fc980b868725387e98f5b6e995aa613f5a0e5b06987b4f307e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea059dd573709dcc210de509c5f465a4", "guid": "bfdfe7dc352907fc980b868725387e988549120084a89ae95a59e764b6c1766a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558f27c2fc782b0cab30c42a9012983b", "guid": "bfdfe7dc352907fc980b868725387e989a328848122de9f540cafbd0f01696de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3cdd34c036432215a336d9af615738", "guid": "bfdfe7dc352907fc980b868725387e9821b81e50ac0ac7635131475aae3ce08f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f31ce89d36196afa2ea4efc07bc22cb", "guid": "bfdfe7dc352907fc980b868725387e983997188cfac275c9f03115b55d0afc1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee47d7af400ca2d0976d0c0f4a14e57", "guid": "bfdfe7dc352907fc980b868725387e98e8398457ffdc081bd2080a68b102a386", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2860f112a3098a5138e05ecf90e041", "guid": "bfdfe7dc352907fc980b868725387e98039b22451d8a21e6d4adf6077b14bccc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e519132f11c1f0c23c680cc8fd46819", "guid": "bfdfe7dc352907fc980b868725387e98b9c15eb9eeb8edaae96565bf4b822cc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec364a3b82c10fa7d544b93a11ebd4c", "guid": "bfdfe7dc352907fc980b868725387e983f6538903e11a1d3c3f6fca6c94558f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e5272617f26603dd1d60f677e015c6", "guid": "bfdfe7dc352907fc980b868725387e985282c2431f9f15eafd4b6a9fa4419891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3bd7d2eb3a53c0a7fa303db21b20f3d", "guid": "bfdfe7dc352907fc980b868725387e98c61cc9148688d26dc0b695a46c173799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b04a435db05b27340cee878b6a49075", "guid": "bfdfe7dc352907fc980b868725387e989d309c2329bfc22ea798f85652270be7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6f485649b9f5d74dedf659465ec22e", "guid": "bfdfe7dc352907fc980b868725387e989210a0a1d2200fa93a8e0186f8c12f7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d142aa951bb7f24741d0648807f6224", "guid": "bfdfe7dc352907fc980b868725387e98906f6143be5dc3ee7e253cd6b425ef13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab968ed38cf838b2ffbff5d805124b5", "guid": "bfdfe7dc352907fc980b868725387e984841a03420041ec78c6105593779df4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98783bd012a0ae1715a4c0caefef1f7bd8", "guid": "bfdfe7dc352907fc980b868725387e986690e2dd5af091f2bbb8000cf7ea4294"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a39eb5529685c9f01699ca78bd76739", "guid": "bfdfe7dc352907fc980b868725387e988b6328a2abf439aac38fbcc664821473"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9a0501d33a4222c028fe3b4a3d81e0", "guid": "bfdfe7dc352907fc980b868725387e98b013047a3492944a0c032a0bd198dcb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e858388139ce76ab8374e76e2d223805", "guid": "bfdfe7dc352907fc980b868725387e9867514664432b1590e21a35a295d88b2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5bb43bbbb58703dc9956c299036de2", "guid": "bfdfe7dc352907fc980b868725387e98f94c93a7cfb0375f433e336c037ea9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7a6b9f291951f5e8bc89214b750851", "guid": "bfdfe7dc352907fc980b868725387e98a2cbe147060c8d920b9ac3b052e61bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544d33a7683d7013a7ee5727254753b0", "guid": "bfdfe7dc352907fc980b868725387e98269bf7a4e705192d8ae9c2a54a453ae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c7de717084cb1d76f2074ea08db8fb", "guid": "bfdfe7dc352907fc980b868725387e98481bd5b52288068c3e58977d677074f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07b265b2a5564466a9dc0a38b45f2e3", "guid": "bfdfe7dc352907fc980b868725387e9853aae5f7a397a346e4ed57421339c327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595070815d6fde9937953b98cbd62485", "guid": "bfdfe7dc352907fc980b868725387e987c91f9e1356629f37393c81de855bc4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fced325eec624e0880443891ebbea1", "guid": "bfdfe7dc352907fc980b868725387e986a60fe45081086a15610181cd71db364"}], "guid": "bfdfe7dc352907fc980b868725387e98aec236a3cbb0026ae2d23cc2f9e46425", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838d61aab8ae4cc7acd9bbdbf9ef3b61c", "guid": "bfdfe7dc352907fc980b868725387e98848e6e1fee350ab4a1f2439e8e6e9b9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf13e80ceed2512e079c8f246d8f63ca", "guid": "bfdfe7dc352907fc980b868725387e984e8b6371748445f9195c36dd5205e117"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed9ac295e92f4a8ba367a936ef93af94", "guid": "bfdfe7dc352907fc980b868725387e98bbf5541b3d958f304adc184e8554ff17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d09f4ef615d704ad74a34df43fb26ec", "guid": "bfdfe7dc352907fc980b868725387e982e8dc15c14751a76bfef6c12ebcc70a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ac4fd11f09478367185d1d346db680", "guid": "bfdfe7dc352907fc980b868725387e985a0329ceed0a5a503b930e496e307c34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a21b6fc7a8775176e824bbf6403721", "guid": "bfdfe7dc352907fc980b868725387e98809ec0efdba4a0fed9870380f48716ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0cd11ef7e2c472ad54a2759fb220da0", "guid": "bfdfe7dc352907fc980b868725387e981f3b165b51251a7b102f3557b445ee16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886a2c34df717e820e71c8c123f14997f", "guid": "bfdfe7dc352907fc980b868725387e98b17e4fff4922a0d09a4be29d1cab0e0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1c529ab2a0d395f195b6d63b889b53", "guid": "bfdfe7dc352907fc980b868725387e982e75ad50ef4a7c78fbeb848fd5576587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d21b265286fb44b9bca44fd6c2181c", "guid": "bfdfe7dc352907fc980b868725387e987cf97f21253f3a0369c7e0438f05a464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae67ca1005fe3865dfb9ad3f3d994cb2", "guid": "bfdfe7dc352907fc980b868725387e9893d6cb74465cea74596ead2d7f22a683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2713aee289b679cfcce4e79d6c06a6", "guid": "bfdfe7dc352907fc980b868725387e985d840532524a9c4db4e46c0bd4cc0a29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ed0ee8172eb897080a5e31e9a245840", "guid": "bfdfe7dc352907fc980b868725387e981b760075684e059ab54f57f7ce8d46c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f32313cb91b24c5a361c4411ab3b58c", "guid": "bfdfe7dc352907fc980b868725387e98fdf25ea8d25c1dd44d01b8acae2237a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2be361aba3180ff30ee1d0fc24503f4", "guid": "bfdfe7dc352907fc980b868725387e98f8895318a552a08647f09a340f9701d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa0c5dfa1ef68bf0d6c213da5b1b554", "guid": "bfdfe7dc352907fc980b868725387e98aa6ef4bc62433c85779d2ed9f67a768e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82282897cd3c47262e1d34d17b4d9fa", "guid": "bfdfe7dc352907fc980b868725387e983aec598d4f5212b755664a1329ec7de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849962555c07f50d0c8ffa2be983e8f31", "guid": "bfdfe7dc352907fc980b868725387e98dc9c121ce947f666b66235b061a3b9b7"}], "guid": "bfdfe7dc352907fc980b868725387e9813b8b8d4b3d0b6d525045946cd8906fb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98378f2d21e3935e25091f2b8ead1cd4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e9872ee5f95503bf72e191b1b65986dd27f"}], "guid": "bfdfe7dc352907fc980b868725387e980b3e4cb34334e43a1bb82fde46c62388", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b390b0eccf514d60877ef75c9aff9729", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98f0a98c6749036a4be4d8d5fb185c59b6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}