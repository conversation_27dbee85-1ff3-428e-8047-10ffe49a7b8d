import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'event_enum.g.dart';

@HiveType(typeId: HiveModelConstants.eventEnumTypeId)
@JsonEnum(valueField: 'index')
enum Event {
  @HiveField(0)
  signUp,
  @HiveField(1)
  login,
  @HiveField(2)
  logout,
  @HiveField(3)
  leadStatusToCallback,
  @HiveField(4)
  callbackReminder,
  @HiveField(5)
  leadStatusToScheduleMeeting,
  @HiveField(6)
  scheduleMeetingReminder,
  @HiveField(7)
  leadStatusToScheduleSiteVisit,
  @HiveField(8)
  scheduleSiteVisitReminder,
  @HiveField(9)
  leadStatusToConvert,
  @HiveField(10)
  leadStatusToNotInterest,
  @HiveField(11)
  leadSatusToDropped,
  @HiveField(12)
  leadStatusToPending,
  @HiveField(13)
  singleTaskComplete,
  @HiveField(14)
  multipleTaskComplete,
  @HiveField(15)
  taskCreation,
  @HiveField(16)
  taskUpdation,
  @HiveField(17)
  scheduledTaskReminder,
  @HiveField(18)
  taskOverdue,
  @HiveField(19)
  taskPending,
  @HiveField(20)
  leadFromPropertyMicrositeToBuy,
  @HiveField(21)
  leadFromPropertyMicrositeForRent,
  @HiveField(22)
  leadFromPortfolioMicrosite,
  @HiveField(23)
  integrationAccCreation,
  @HiveField(24)
  leadFromIntegration,
  @HiveField(25)
  propertyCreation,
  @HiveField(26)
  propertyDeletion,
  @HiveField(27)
  propertySoftDeletion,
  @HiveField(28)
  dusKaDumChallengeStarts,
  @HiveField(29)
  supportQuery,
  @HiveField(30)
  leadAssignment,
  @HiveField(31)
  taskAssignment,
  @HiveField(32)
  unAssignedLeadUpdate
}
