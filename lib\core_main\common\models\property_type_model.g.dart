// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_type_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PropertyTypeModel _$PropertyTypeModelFromJson(Map<String, dynamic> json) =>
    PropertyTypeModel(
      id: json['id'] as String?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      type: json['type'] as String?,
      displayName: json['displayName'] as String?,
      childType: json['childType'] == null
          ? null
          : PropertyTypeModel.fromJson(
              json['childType'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyTypeModelToJson(PropertyTypeModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.baseId case final value?) 'baseId': value,
      if (instance.level case final value?) 'level': value,
      if (instance.type case final value?) 'type': value,
      if (instance.displayName case final value?) 'displayName': value,
      if (instance.childType?.toJson() case final value?) 'childType': value,
    };
