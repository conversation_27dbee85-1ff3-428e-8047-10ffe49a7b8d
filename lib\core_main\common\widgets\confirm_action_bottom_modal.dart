import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/main.dart';

void confirmActionBottomModal({
  Function()? onSuccess,
  Function()? onCancel,
  required String imageVector,
  required String title,
  required String subTitle,
  Widget? subTitleWidget,
  String? successButtonText,
  String? cancelButtonText,
  bool isDismissible = true,
  bool enableDrag = true,
  bool hideCancelButton = false,
  bool closePopupOnTap = false,
}) {
  final context = MyApp.navigatorKey.currentState?.context;
  if (context == null) {
    throw Exception("Context can't be null");
  }
  showModalBottomSheet(
    context: context,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    builder: (BuildContext context) {
      return WillPopScope(
        onWillPop: () async => isDismissible,
        child: ConfirmActionBottomModalWidget(
          title: title,
          imageVector: imageVector,
          subTitle: subTitle,
          onSuccess: onSuccess,
          onCancel: onCancel,
          cancelButtonText: cancelButtonText,
          successButtonText: successButtonText,
          subTitleWidget: subTitleWidget,
          hideCancelButton: hideCancelButton,
          closePopupOnTap: closePopupOnTap,
        ),
      );
    },
  );
}

class ConfirmActionBottomModalWidget extends StatelessWidget {
  final Function()? onSuccess;
  final Function()? onCancel;
  final String imageVector;
  final String title;
  final String subTitle;
  final String? successButtonText;
  final String? cancelButtonText;
  final Widget? subTitleWidget;
  final bool hideCancelButton;
  final bool closePopupOnTap;

  const ConfirmActionBottomModalWidget({
    super.key,
    this.onSuccess,
    this.onCancel,
    required this.imageVector,
    required this.title,
    required this.subTitle,
    this.successButtonText,
    this.cancelButtonText,
    this.subTitleWidget,
    this.hideCancelButton = false,
    this.closePopupOnTap = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned(
          top: -6,
          left: 0,
          right: 0,
          child: CustomPaint(
            size: Size(context.width(100), 6),
            painter: TrapeziumPainter(const Color(0xFFEDECF3)),
          ),
        ),
        Container(
          width: context.width(100),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          color: ColorPalette.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              if (imageVector.endsWith(".svg")) Center(child: SvgPicture.asset(imageVector)) else
                Center(child: Image(image: AssetImage(imageVector))),
              const SizedBox(height: 10),
              Text(title, style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.darkToneInk), textAlign: TextAlign.center),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: subTitleWidget ?? Text(subTitle, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.darkToneInk), textAlign: TextAlign.center),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (!hideCancelButton) ...[
                    InkWell(
                      onTap: () {
                        DialogManager().hideTransparentProgressDialog();
                        if (onCancel != null) {
                          onCancel!();
                        }
                        if (closePopupOnTap) Navigator.pop(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: ColorPalette.gray800,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(top: 5, left: 5, bottom: 5, right: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SvgPicture.asset(ImageResources.icCancelFilled),
                              SizedBox(width: context.width(2)),
                              Text(cancelButtonText ?? "Cancel", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.white)),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                  ],
                  InkWell(
                    onTap: () {
                      DialogManager().hideTransparentProgressDialog();
                      if (onSuccess != null) {
                        onSuccess!();
                      }
                      if (closePopupOnTap) Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: ColorPalette.leadratBgGreen,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 5, left: 5, bottom: 5, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(ImageResources.icCheckFilled),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: hideCancelButton ? 8 : 0),
                              child: Text(successButtonText ?? "Yes", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.white)),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        )
      ],
    );
  }
}
