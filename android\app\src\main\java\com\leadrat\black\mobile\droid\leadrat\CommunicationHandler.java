package com.leadrat.black.mobile.droid.leadrat;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.content.ComponentName;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class CommunicationHandler {
    private final Activity activity;

    public CommunicationHandler(Activity activity, FlutterEngine flutterEngine) {
        this.activity = activity;
    }

    public void launchSMS(String uriString, String message, MethodChannel.Result result) {
        if (uriString != null) {
            Uri uri = Uri.parse(uriString);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.putExtra("sms_body", message);
            activity.startActivity(intent);
            result.success("SMS opened successfully");
        } else {
            result.error("URI_NULL", "URI argument is null", null);
        }
    }

    public void checkBusinessWhatsappInstalled(MethodChannel.Result result) {
        PackageManager packageManager = activity.getPackageManager();
        boolean isInstalled;
        try {
            packageManager.getPackageInfo("com.whatsapp.w4b", PackageManager.GET_ACTIVITIES);
            isInstalled = true;
        } catch (PackageManager.NameNotFoundException e) {
            isInstalled = false;
        }
        result.success(isInstalled);
    }

    public void launchWhatsApp(String phoneNumber, String message, String packageName, MethodChannel.Result result) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            String url = "https://api.whatsapp.com/send?phone=" + phoneNumber + "&text=" + Uri.encode(message);
            intent.setData(Uri.parse(url));
            intent.setPackage(packageName); // Use the provided package name
            activity.startActivity(intent);
            result.success("WhatsApp opened successfully");
        } catch (Exception e) {
            result.error("WHATSAPP_ERROR", "Failed to open WhatsApp: " + e.getMessage(), null);
        }
    }

    public void sendIntentToBroadcast(String tenantId, String userId, String isLoggedIn) {
        String intentAction = "com.leadrat.black.mobile.droid.USER_LOGIN";
        Intent intent = new Intent(intentAction);
        intent.putExtra("TenantId", tenantId != null ? tenantId : "");
        intent.putExtra("UserId", userId != null ? userId : "");
        intent.putExtra("IsLoggedIn", isLoggedIn != null ? isLoggedIn : "false");
        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        intent.setAction("com.leadrat.black.mobile.droid.USER_LOGIN");
        intent.setComponent(new ComponentName("com.leadrat.call_detection.call_detection", "com.leadrat.call_detection.call_detection.CallReceiver"));

        activity.sendBroadcast(intent);

        Intent oldIntent = new Intent(intentAction);
        oldIntent.putExtra("TenantId", tenantId != null ? tenantId : "");
        oldIntent.putExtra("UserId", userId != null ? userId : "");
        oldIntent.putExtra("IsLoggedIn", isLoggedIn != null ? isLoggedIn : "false");

        activity.sendBroadcast(oldIntent);
    }
}