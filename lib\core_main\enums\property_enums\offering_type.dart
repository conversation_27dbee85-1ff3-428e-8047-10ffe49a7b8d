import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum OfferingType {
  none(0, "None"),
  ready(1, "Ready"),
  offPlan(2, "Off Plan"),
  secondary(3, "Secondary");

  final int value;
  final String description;

  const OfferingType(this.value, this.description);
}

String getOfferingTypeDescription(int value) {
  for (var status in OfferingType.values) {
    if (status.value == value) {
      return status.description;
    }
  }
  return OfferingType.none.description;
}
