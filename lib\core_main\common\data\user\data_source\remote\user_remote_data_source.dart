import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';
import 'package:leadrat/core_main/common/data/user/models/search_response_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/features/lead/data/models/lead_search_property_model.dart';

import '../../models/get_all_users_model.dart';
import '../../models/leadrat_subscription_details_model.dart';
import '../../models/user_details_clockin_model.dart';
import '../../models/user_details_model.dart';

abstract class UserRemoteDataSource {
  Future<List<GetAllUsersModel?>?> getAllUsers({Duration duration = const Duration(seconds: 60)});

  Future<UserDetailsModel?> getUser({Duration duration = const Duration(seconds: 60)});

  Future<List<GetAllUsersModel?>?> getAdminsAndReportee({Duration duration = const Duration(seconds: 60)});

  Future<List<GetAllUsersModel?>?> getAllReportees({Duration duration = const Duration(seconds: 60)});

  Future<Map<String, List<GetUserDesignationModel>>?> getAllUserWithDesignations({Duration duration = const Duration(seconds: 60)});

  Future<List<SearchFilterModel>?> getAllSearchResult(int moduleType);

  Future<bool?> updateSearch(SearchResponse searchResponseModel);

  Future<List<DTOWithNameModel?>?> getAllDesignations({Duration duration = const Duration(seconds: 60)});

  Future<UserDetailsClockInModel?> getGeoFenceDetails();

  Future<LeadratSubscriptionDetailsModel?>? getSubscriptionDetails(String? userId);

}
