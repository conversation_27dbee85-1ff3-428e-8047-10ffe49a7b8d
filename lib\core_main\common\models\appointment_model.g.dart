// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentModel _$AppointmentModelFromJson(Map<String, dynamic> json) =>
    AppointmentModel(
      projectName: json['projectName'] as String?,
      executiveName: json['executiveName'] as String?,
      executiveContactNo: json['executiveContactNo'] as String?,
      imagesWithName: (json['imagesWithName'] as List<dynamic>?)
          ?.map((e) =>
              AppointmentDocumentsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      notes: json['notes'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedByUser: json['lastModifiedByUser'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      location: json['location'] == null
          ? null
          : AddressModel.fromJson(json['location'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AppointmentModelToJson(AppointmentModel instance) =>
    <String, dynamic>{
      'projectName': instance.projectName,
      'executiveName': instance.executiveName,
      'executiveContactNo': instance.executiveContactNo,
      'imagesWithName': instance.imagesWithName,
      'notes': instance.notes,
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedByUser': instance.lastModifiedByUser,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'location': instance.location,
    };

AppointmentDocumentsModel _$AppointmentDocumentsModelFromJson(
        Map<String, dynamic> json) =>
    AppointmentDocumentsModel(
      documentName: json['documentName'] as String?,
      filePath: json['filePath'] as String?,
    );

Map<String, dynamic> _$AppointmentDocumentsModelToJson(
        AppointmentDocumentsModel instance) =>
    <String, dynamic>{
      'documentName': instance.documentName,
      'filePath': instance.filePath,
    };
