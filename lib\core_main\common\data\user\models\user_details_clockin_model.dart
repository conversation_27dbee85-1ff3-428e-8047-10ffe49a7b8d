import 'package:json_annotation/json_annotation.dart';

part 'user_details_clockin_model.g.dart';

@JsonSerializable(includeIfNull: false)
class UserDetailsClockInModel {
  final String? id;
  final List<UserProjectModel>? projects;
  final List<UserPropertyModel>? properties;
  final int? geoFenceRadius;
  final RadiusUnit? radiusUnit;

  UserDetailsClockInModel({this.id, this.geoFenceRadius, this.projects, this.properties, this.radiusUnit});

  factory UserDetailsClockInModel.fromJson(Map<String, dynamic> json) => _$UserDetailsClockInModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserDetailsClockInModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class UserProjectModel {
  final String? projectIds;
  final String? projectName;
  final double? longitude;
  final double? latitude;

  UserProjectModel({this.projectIds, this.projectName, this.latitude, this.longitude});

  factory UserProjectModel.fromJson(Map<String, dynamic> json) => _$UserProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserProjectModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class UserPropertyModel {
  final String? propertyId;
  final String? propertyName;
  final double? longitude;
  final double? latitude;

  UserPropertyModel({this.propertyId, this.propertyName, this.latitude, this.longitude});

  factory UserPropertyModel.fromJson(Map<String, dynamic> json) => _$UserPropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserPropertyModelToJson(this);
}

@JsonEnum(valueField: 'value')
enum RadiusUnit {
  none(0, "None"),
  meter(1, "M"),
  kiloMeter(2, "km");

  final int value;
  final String description;

  const RadiusUnit(
    this.value,
    this.description,
  );
}
