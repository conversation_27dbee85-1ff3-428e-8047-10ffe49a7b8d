part of 'saved_filter_bloc.dart';

@immutable
class SavedFilterState {
  final PageState pageState;
  final PageState dialogState;
  final SavedFilterModule savedFilterModule;
  final List<ItemSimpleModel<GetSavedFilterModel>> savedFilters;
  final String? errorMessage;
  final String? appliedFilter;

  const SavedFilterState({
    this.pageState = PageState.initial,
    this.dialogState = PageState.initial,
    this.savedFilterModule = SavedFilterModule.leads,
    this.savedFilters = const [],
    this.errorMessage,
    this.appliedFilter,
  });

  SavedFilterState copyWith({
    PageState? pageState,
    PageState? dialogState,
    SavedFilterModule? savedFilterModule,
    List<ItemSimpleModel<GetSavedFilterModel>>? savedFilters,
    String? errorMessage,
    String? appliedFilter,
    bool resetSelectedAppliedFilter = false,
  }) {
    return SavedFilterState(
      pageState: pageState ?? this.pageState,
      dialogState: dialogState ?? this.dialogState,
      savedFilterModule: savedFilterModule ?? this.savedFilterModule,
      savedFilters: savedFilters ?? this.savedFilters,
      errorMessage: errorMessage ?? this.errorMessage,
      appliedFilter: resetSelectedAppliedFilter ? null : (appliedFilter ?? this.appliedFilter),
    );
  }
}

enum SavedFilterModule {
  leads("Leads"),
  prospects("Prospects"),
  properties("Properties");

  final String description;

  const SavedFilterModule(this.description);
}
