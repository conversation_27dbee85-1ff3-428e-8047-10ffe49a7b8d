import 'package:json_annotation/json_annotation.dart';

part 'get_saved_filter_model.g.dart';

@JsonSerializable(explicitToJson: true)
class GetSavedFilterModel {
  final String? id;
  final String? name;
  final String? module;
  final String? filterCriteria;
  final DateTime? lastModifiedOn;
  final DateTime? createdOn;
  final String? createdBy;
  final String? lastModifiedBy;
  final String? userId;

  GetSavedFilterModel({
    this.id,
    this.name,
    this.module,
    this.filterCriteria,
    this.lastModifiedOn,
    this.createdOn,
    this.createdBy,
    this.lastModifiedBy,
    this.userId,
  });

  factory GetSavedFilterModel.fromJson(Map<String, dynamic> json) => _$GetSavedFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetSavedFilterModelToJson(this);
}
