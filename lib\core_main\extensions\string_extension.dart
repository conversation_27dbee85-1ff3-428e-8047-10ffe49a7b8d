import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_environment.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/media_type.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/utilities/validator_utils.dart';
import 'package:leadrat/features/auth/data/data_source/local/auth_local_data_source.dart';

extension StringExtension on String {
  String appendWithImageBaseUrl() {
    final appEnv = RestResources.appEnvironment;

    if (startsWith('https')) {
      return this;
    } else {
      switch (appEnv) {
        case AppEnvironment.prd:
          return 'https://leadrat-black.s3.ap-south-1.amazonaws.com/$this';
        case AppEnvironment.qa:
          return 'https://qleadrat-black.s3.ap-south-1.amazonaws.com/$this';
        default:
          return 'https://dleadrat-black.s3.ap-south-1.amazonaws.com/$this';
      }
    }
  }

  String appendWithSerialNumber({AppModule appModule = AppModule.project,bool isListingEnabled = false}) {
    final appEnv = RestResources.appEnvironment;
    final domain = getIt<AuthLocalDataSource>().getTenantDetails()?.id ?? "";
    final userName = getIt<UsersLocalDataSource>().getUser()?.userName ?? "";
    if (startsWith('https')) {
      return this;
    } else {
      final previewPath = appModule == AppModule.property ? (isListingEnabled ? 'listing' : 'property') : 'project';
      switch (appEnv) {
        case AppEnvironment.prd:
          return ' https://$domain.leadrat.com/external/$previewPath-preview/$userName/$this';
        case AppEnvironment.qa:
          return ' https://$domain.leadrat.info/external/$previewPath-preview/$userName/$this';
        default:
          return ' https://$domain.dleadrat.com/external/$previewPath-preview/$userName/$this';
      }
    }
  }

  String getUserName(BaseUserModel? baseUserModel) {
    if (baseUserModel == null) return '--';
    if (baseUserModel.id == (getIt<UsersDataRepository>().getLoggedInUser()?.userId ?? '')) {
      return 'you';
    } else {
      return baseUserModel.fullName;
    }
  }

  bool ismp4OrNull() {
    if (isEmpty) {
      return false;
    } else if (endsWith('.mp4')) {
      return false;
    } else {
      return true;
    }
  }

  bool isValidPhoneNumber() {
    return ValidationUtils.isValidPhoneNumber(this);
  }

  bool isValidEmail() {
    return ValidationUtils.isValidEmail(this);
  }

  String maskEmail() {
    if (!contains('@')) return this;
    final emailParts = split('@');
    final namePart = emailParts[0];
    final domainPart = emailParts[1];
    if (namePart.length <= 2) {
      return this;
    }
    final firstChar = namePart[0];
    final lastChar = namePart[namePart.length - 1];
    final maskedMiddle = List.filled(namePart.length - 2, '*').join();
    return '$firstChar$maskedMiddle$lastChar@$domainPart';
  }

  String maskPhone() {
    if (startsWith('+')) {
      final countryCode = substring(0, 3);
      final numberPart = substring(3);

      if (numberPart.length > 4) {
        final lastTwoDigits = numberPart.substring(numberPart.length - 2);
        final maskedMiddle = List.filled(numberPart.length - 2, '*').join();
        return '$countryCode$maskedMiddle$lastTwoDigits';
      }
      return this;
    } else {
      if (length > 4) {
        final firstTwo = substring(0, 2);
        final lastTwo = substring(length - 2);
        final maskedMiddle = List.filled(length - 4, '*').join();
        return '$firstTwo$maskedMiddle$lastTwo';
      }
      return this;
    }
  }

  String convertHexToColorCode() {
    final hex = substring(1);
    return "0xFF$hex";
  }
}

extension NullStringExtension on String? {
  bool isNullOrEmpty() => this?.isEmpty ?? true;

  bool isNotNullOrEmpty() => !isNullOrEmpty();

  bool isNullOrEmptyGuid() => isNullOrEmpty() || this == StringConstants.emptyGuidId;

  String locationString() {
    String returnString = '';
    int count = 0;

    if (this != null) {
      List<String?> list = this!.split(',');

      if (list.length >= 2) {
        for (int i = 0; i < list.length; i++) {
          if (list[i] != '') {
            if (count < 2) {
              returnString = '$returnString ${list[i]!}';
              count++;
            }
          }
        }
        return returnString;
      }
    }

    return '--';
  }

  String nameToInitial() {
    try {
      if (this?.isNullOrEmpty() ?? false) return "U.A";
      var initial = '';
      var words = this?.trim().split(' ');

      if (words!.length > 1) {
        var firstWord = words.firstOrNull?.trim();

        var lastWord = words.lastOrNull?.trim();

        initial = "${firstWord![0]} ${lastWord![0]}";
      } else if (words.length == 1) {
        initial += words[0][0];
      }
      return initial.toUpperCase();
    } catch (ex) {
      return 'U.A';
    }
  }

  String budgetToWord([String? currency]) {
    if (this == null) return '';
    try {
      var budget = double.tryParse(this!);
      return budget.budgetToWord(currency);
    } catch (ex) {
      return '';
    }
  }

  double? wordToDouble() {
    if (this == null) return null;
    try {
      var budget = double.tryParse(this!);
      return budget;
    } catch (ex) {
      return null;
    }
  }

  String get maskedPhone {
    if (this == null) return '';
    if (this!.length <= 4) return this!;
    String visiblePart = this!.substring(0, 4);
    String maskedPart = "X" * (this!.length - 4);
    return "$visiblePart$maskedPart";
  }

  String get maskedEmail {
    if (this == null || this!.isEmpty) return '';

    final email = this!;
    final atIndex = email.indexOf('@');

    if (atIndex == -1) return email;

    final localPart = email.substring(0, atIndex);
    final domainPart = email.substring(atIndex + 1);

    String maskedLocal = localPart.length <= 2 ? "${localPart[0]}X" : "${localPart[0]}${'X' * (localPart.length - 2)}${localPart[localPart.length - 1]}";

    final domainParts = domainPart.split('.');
    if (domainParts.length > 1) {
      String maskedDomain = "${domainParts[0][0]}${'X' * (domainParts[0].length - 1)}";
      maskedDomain += ".${domainParts.last}";
      return "$maskedLocal@$maskedDomain";
    }

    return "$maskedLocal@${'X' * domainPart.length}";
  }

  MediaType get mediaType {
    if (this == null || this!.isEmpty) return MediaType.unknown;
    final extension = this!.split('/').last.split('.').last.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'webm'];
    const audioExtensions = ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    if (imageExtensions.contains(extension)) {
      return MediaType.image;
    } else if (videoExtensions.contains(extension)) {
      return MediaType.video;
    } else if (audioExtensions.contains(extension)) {
      return MediaType.audio;
    } else if (documentExtensions.contains(extension)) {
      return MediaType.document;
    } else {
      return MediaType.unknown;
    }
  }
}

extension BudgetToWord on double? {
  String doubleToWord() {
    if (this == null || this! <= 0) return '0';
    try {
      String value = toString();
      if (value.length < 2) return '0';
      return value.substring(0, value.length - 2);
    } catch (ex) {
      return '0';
    }
  }

  String budgetToWord([String? currency]) {
    var selectedCurrency = currency ?? '';
    if (this == 0) {
      return "$selectedCurrency 0";
    } else if (this == null || this! <= 0) {
      return '';
    }
    List<String> similarIndianCurrencies = ["INR", "NPR", "BDT", "PKR", "LKR"];
    double value = this!;
    int length = value.toStringAsFixed(0).length;

    String formatNumber(double number, int decimals) {
      if (number == number.roundToDouble()) {
        return number.round().toString();
      }
      String result = number.toStringAsFixed(decimals);
      return result.replaceAll(RegExp(r'\.?0+$'), '');
    }

    if (length <= 3) {
      return "$selectedCurrency ${formatNumber(value, 2)}";
    }
    if (similarIndianCurrencies.contains(selectedCurrency) || selectedCurrency.isEmpty) {
      if (length <= 5) {
        return "$selectedCurrency ${formatNumber(value / 1000, 1)} K";
      } else if (length <= 7) {
        return '$selectedCurrency ${formatNumber(value / 100000, 1)} L';
      } else {
        return '$selectedCurrency ${formatNumber(value / 10000000, 1)} c';
      }
    } else {
      if (value < 1e6) {
        return "$selectedCurrency ${formatNumber(value / 1e3, 1)} K";
      } else if (value < 1e9) {
        return "$selectedCurrency ${formatNumber(value / 1e6, 1)} M";
      } else {
        return "$selectedCurrency ${formatNumber(value / 1e9, 1)} B";
      }
    }
  }
}

String getDefaultCountryCode(GlobalSettingModel? globalSettingModel) {
  final defaultCountryCode = globalSettingModel?.countries?.firstOrNull?.defaultCallingCode;
  final isCustomDubaiFormEnabled = globalSettingModel?.isCustomLeadFormEnabled;
  if (defaultCountryCode != null) return defaultCountryCode;
  if (isCustomDubaiFormEnabled ?? false) return "+971";
  return "+91";
}

String getDefaultCurrency(GlobalSettingModel? globalSettingModel) {
  final defaultCurrency = globalSettingModel?.countries?.firstOrNull?.defaultCurrency;
  final isCustomDubaiFormEnabled = globalSettingModel?.isCustomLeadFormEnabled;
  if (defaultCurrency != null) return defaultCurrency;
  if (isCustomDubaiFormEnabled ?? false) return "AED";
  return "INR";
}
