{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985108752ed0ac4b9426d93aa584f4984c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9851685fdc6a13f9a35ae58d134440e63e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987aee8116d097a9978e31285a0f44a48c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98bed63eb47a152d110908ab4abf923852", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987aee8116d097a9978e31285a0f44a48c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c7f8ed7c7c923c1342c9324337057c2f", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808084ab04c2c2e5cd2abf595e74b5bb7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9875f4e748e7b30cdb17d8244aa57eb4cb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f42fca67cdf364507f07a31cee01310c", "guid": "bfdfe7dc352907fc980b868725387e98f6bc32b7cd6dc00a9b21078f5f01fbce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a851b6245afbccdd37535c4c317a44a", "guid": "bfdfe7dc352907fc980b868725387e9872e9b547d9747fbb6a5aa91635eda7a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810aa2e6bae60562c1a2845d8425bd6ac", "guid": "bfdfe7dc352907fc980b868725387e98c91defb5a9a3e487e73a4aa4be746c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6113f064ea02364a9ed18336acf05d1", "guid": "bfdfe7dc352907fc980b868725387e98c0027767a0784d22fe2ae371b5c36fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45ae3ec1ebefb77d2986cc346135037", "guid": "bfdfe7dc352907fc980b868725387e98f7d995029f658e7c833ae9ec1f659657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df10686c86d1cbe7e091b948a977a803", "guid": "bfdfe7dc352907fc980b868725387e98c716b011056b0f3d1328181a2e33b0f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252f4491a077e0777159a559357d5563", "guid": "bfdfe7dc352907fc980b868725387e98bb2e7792f8eaa8ab5c1568c34785ac22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6eefcd00ee0a6ef3e0b5ba36ff8ab48", "guid": "bfdfe7dc352907fc980b868725387e9895288d5a7398d916d5a83808f2a69a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a0a8c5bb2fefb1613194da267d7e7c", "guid": "bfdfe7dc352907fc980b868725387e98c6046352d016a6adf028b336b6a88633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da9eda5e2a9b492a3b9309f49afb2d5", "guid": "bfdfe7dc352907fc980b868725387e98052d415c4bfae447623a01650ee0b864"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f279c84cdafa90e78adb68b5b94bd9b", "guid": "bfdfe7dc352907fc980b868725387e98e776933e9dda4ded12edfbe65952c4f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d729c1cd017334923f9cc0d5d1a4af9", "guid": "bfdfe7dc352907fc980b868725387e98295475c0143d122e70f4f5ed1f239ab7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dd5d1cc975409fa15aa96968037cc33", "guid": "bfdfe7dc352907fc980b868725387e9874233f4ebae9fa56551cab1dd346d5de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e4fb6948ca5c9637e2713f3b07789ec", "guid": "bfdfe7dc352907fc980b868725387e98466ab5733c3d87f3334d52e211720b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f707b2c4889249bab03461ad3a19dbff", "guid": "bfdfe7dc352907fc980b868725387e98b0da984d1bb1b25e54d8ce6d7ba60d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ae80c4884e50438b4620e6bb8f499d", "guid": "bfdfe7dc352907fc980b868725387e980add7447a158733b6d13aeb41a2ec495"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd517fca97766682ad75827789f33ea3", "guid": "bfdfe7dc352907fc980b868725387e98e66d63620e969845c65321ed56b15797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980927ff78d41b7e0a7dc0cbaf1f0b3b0c", "guid": "bfdfe7dc352907fc980b868725387e98a01e6cd4a4b3e0c27fc1f5c599d6707e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65c6bd0e432720a9315776deb023d53", "guid": "bfdfe7dc352907fc980b868725387e98b690ab392fbf99ac74ad59e4bf9769ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86482c7d95e709e97bf778451f22626", "guid": "bfdfe7dc352907fc980b868725387e98a9eed1b6a2a730721a673bea38d46dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d3b665c94f742c158e34015009c45b0", "guid": "bfdfe7dc352907fc980b868725387e983e5d0b14b07c698fb4e1c66210ded1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c2e0e323973cacd90ae55ed4bb3cb0", "guid": "bfdfe7dc352907fc980b868725387e985ffb7e3d15d154e9077edc6ac959aef6"}], "guid": "bfdfe7dc352907fc980b868725387e98302fb0bac39ecde08297f23fcd3cfa1a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}