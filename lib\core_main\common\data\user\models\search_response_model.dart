
import 'package:json_annotation/json_annotation.dart';
part 'search_response_model.g.dart';

@JsonSerializable(includeIfNull: false)
class SearchResponse {
  final int? module;
  final List<String?>? searchResults;

  SearchResponse({
    this.module,
    this.searchResults,
  });
  factory SearchResponse.fromJson(Map<String, dynamic> json) => _$SearchResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SearchResponseToJson(this);
}
