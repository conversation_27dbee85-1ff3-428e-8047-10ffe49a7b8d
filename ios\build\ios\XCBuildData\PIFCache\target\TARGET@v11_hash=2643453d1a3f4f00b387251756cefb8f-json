{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818af82847c901c363795f4876bc0f75c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c92d0dc4bc8728909c3a9d43a29b5307", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faae051753d5408f8658766108628cbf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d30f49be79c7012599347606c902398", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faae051753d5408f8658766108628cbf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b646ea58f920975c6257202c5f7b2830", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e0db59dbfebc1acc5fe22212f092de6e", "guid": "bfdfe7dc352907fc980b868725387e98ff57ee3db6b209441bd993d9745f128a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2f5a35842096a9a0fdd02fc00172e89", "guid": "bfdfe7dc352907fc980b868725387e9855ceb6c4a36cadfe22ea2842f1f50eb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c2a91119e53f471128778bde51ff27", "guid": "bfdfe7dc352907fc980b868725387e98ffc0617af0b866365dca4098ff1f34a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a34c1f314602602d75cb0d7615afa9", "guid": "bfdfe7dc352907fc980b868725387e98883a651b24c9957762339ce59fcb6118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7e32e9f2f1a34946babb23b77e3ba6", "guid": "bfdfe7dc352907fc980b868725387e9860a95c17a0245a7ff60e9680119ef62c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c65e2ca651192477cf1626f32b2cad", "guid": "bfdfe7dc352907fc980b868725387e9885d533f3b3c9f9689f4a6a1c231666a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23a1134aca1c9dc1a6a38936157c2a0", "guid": "bfdfe7dc352907fc980b868725387e98486df6f5192a6e7a05d1153ed5b20f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826558b0a7c539f36b848237a0a74064d", "guid": "bfdfe7dc352907fc980b868725387e989802f926e62b7397699a5828b832e368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a98d15c332d8efbce0caaa103f67eed", "guid": "bfdfe7dc352907fc980b868725387e98608e81ae1719ba7200298c05eb343e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f89210ce2dc90cd668a16881e4b4198b", "guid": "bfdfe7dc352907fc980b868725387e98304f2278ef64d0e507d2ce9f92d8686f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec7869dd2a6c24545c4386b359f735e", "guid": "bfdfe7dc352907fc980b868725387e98fc7eb2ec4160a7b2d9ad5c7c4cdc6f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2e78a73e59145b2ad10c8faf318fee", "guid": "bfdfe7dc352907fc980b868725387e98bab97e4ac9bab5876562db1e9b094c33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cfddfdf2b1ad5826ad79484e48e8398", "guid": "bfdfe7dc352907fc980b868725387e980b1727e52a561a6f8127f47970b284c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800dbea63b200fdccf018569cf6448735", "guid": "bfdfe7dc352907fc980b868725387e9847803d783cc5f434826992abb20bab8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aea6d2a384a4c25da29a0afd594259b", "guid": "bfdfe7dc352907fc980b868725387e98d7dce1cead642ed3274b3ae8995eae20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414f268dbba74474a6334eabc819cb71", "guid": "bfdfe7dc352907fc980b868725387e98c29acc54830f5e11107945f94eef7bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dba0cdd3bc1f9baf72a417f51ab4217", "guid": "bfdfe7dc352907fc980b868725387e98ab0792f92a1a113e537915c2f9ae0ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190ab902f67e3aed403c059cb1896ce8", "guid": "bfdfe7dc352907fc980b868725387e989ff192fa24fd4d6a2ba0edd8d78018c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a5a484cd9855895ebc6dba55e63db09", "guid": "bfdfe7dc352907fc980b868725387e98bff932ff7b128ff3929bb43ace293a88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346853f8a63564835d5fa4698293e253", "guid": "bfdfe7dc352907fc980b868725387e984231973537aaeb9d08fd354e0930e474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823c313ce70078e3f5e46249b245d6b49", "guid": "bfdfe7dc352907fc980b868725387e98c5bc46854265eb81612290ae41383fb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac23dbc36807dac362b5eac24a317723", "guid": "bfdfe7dc352907fc980b868725387e98746cfdbc5912388e73435afc5ad7983a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db8f8e9af291538a2cc99f0b675fa98", "guid": "bfdfe7dc352907fc980b868725387e984a97e1c589ea7b44978f19d7d7c70a46", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988e3ad5e87360f3520e68b71e9554efca", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986247edf3cd261ec8390eea1abe9dfc55", "guid": "bfdfe7dc352907fc980b868725387e9848543a3cb54a30f1e36df8dac45861bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d54ae9a25c45a2f8169a41fabc0c480", "guid": "bfdfe7dc352907fc980b868725387e9845ff108a298cebfe4d80c6c99fb3425c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aaa6843c766cc2d6404c109181a328b", "guid": "bfdfe7dc352907fc980b868725387e98f48d1cff866c2ca6145fa5d4e69d41f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986890b3666e10280380897f5eeb938168", "guid": "bfdfe7dc352907fc980b868725387e98205c620a5e6c9cce247c2c9ebfb4236d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb39a4257cdab3a62d33c89dbb91077", "guid": "bfdfe7dc352907fc980b868725387e98c35bffd1725ac3baeaadeb68f3d2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5267373bbccefb5cb8844af6103911", "guid": "bfdfe7dc352907fc980b868725387e988b278d05d70be4b8ea3ba311595e5e64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f75aec96ed09d82a6dce431f68499e9", "guid": "bfdfe7dc352907fc980b868725387e984eeeddf742e5bfe8e4411526eddec78d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e387f1ff30c1408dc6bb948c0e097b7", "guid": "bfdfe7dc352907fc980b868725387e98106377d6047e3ea370d7def9bf3a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64563a0955ea4c007274a3794cbc2c4", "guid": "bfdfe7dc352907fc980b868725387e98f5d8e63a01687efd4d426a02d441b8fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5fe897676c2bfabe273f7b247351ee", "guid": "bfdfe7dc352907fc980b868725387e982db291534c1d37ac3a1b5d8a7b74f9b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981493963a686ee5f8b3e606a7b3a7964a", "guid": "bfdfe7dc352907fc980b868725387e983f6b4db22bac720221bb93390a0b6f35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d79f07bc6efbac9108d0effb5ffbbd", "guid": "bfdfe7dc352907fc980b868725387e988372417de8f10cb6112db1b4c99c17a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673c2d6eaff0b85accd5f74c19813779", "guid": "bfdfe7dc352907fc980b868725387e9866f1d159434a546303a090b624d86141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b469bd8bd1a9452e3e873efb93e0363", "guid": "bfdfe7dc352907fc980b868725387e983e5e963fc8471cb34a492318c963233f"}], "guid": "bfdfe7dc352907fc980b868725387e9868117a1d2f8261f3c6d05aaa938a212c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e986ba69cae40f0ad031efb62bc518d9c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98594990685b0807d8cd535f4d31230438"}], "guid": "bfdfe7dc352907fc980b868725387e98f27d52d77de0c96b0b46d0a821cfafb6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a81ccc391e3bd18edf683ac5decb7774", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98ad1b573b48c16503b56cf1fa1776c6a6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}