import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'get_user_designation_model.g.dart';

@HiveType(typeId: HiveModelConstants.getUserDesignationTypeId)
@JsonSerializable(includeIfNull: false)
class GetUserDesignationModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? firstName;
  @HiveField(2)
  final String? lastName;
  @HiveField(3)
  final UserDesignationModel? designation;

  const GetUserDesignationModel({this.id, this.designation, this.firstName, this.lastName});

  factory GetUserDesignationModel.fromJson(Map<String, dynamic> json) => _$GetUserDesignationModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetUserDesignationModelToJson(this);
}

@HiveType(typeId: HiveModelConstants.userDesignationTypeId)
@JsonSerializable(includeIfNull: false)
class UserDesignationModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;

  const UserDesignationModel({this.id, this.name});

  factory UserDesignationModel.fromJson(Map<String, dynamic> json) => _$UserDesignationModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserDesignationModelToJson(this);
}
