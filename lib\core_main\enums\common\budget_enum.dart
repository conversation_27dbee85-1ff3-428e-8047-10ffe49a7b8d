import 'package:leadrat/core_main/common/models/budget_range_model.dart';

enum BudgetEnum {
  upToTenLakhs("Upto 10 lakhs", BudgetRangeModel(minBudget: 0, maxBudget: 1000000)),
  tenToTwentyLakhs("10 lakhs to 20 lakhs", BudgetRangeModel(minBudget: 1000000, maxBudget: 2000000)),
  twentyToThirtyLakhs("20 lakhs to 30 lakhs", BudgetRangeModel(minBudget: 2000000, maxBudget: 3000000)),
  thirtyToFourtyLakhs("30 lakhs to 40 lakhs", BudgetRangeModel(minBudget: 3000000, maxBudget: 4000000)),
  fourtyToFiftyLakhs("40 lakhs to 50 lakhs", BudgetRangeModel(minBudget: 4000000, maxBudget: 5000000)),
  fiftyToOneCrore("50 lakhs to 1 Crore", BudgetRangeModel(minBudget: 5000000, maxBudget: 10000000)),
  moreThanOneCrore("1 Crore to 10 Crore", BudgetRangeModel(minBudget: 10000000, maxBudget: 100000000)),
  moreThanTenCrore("More than 10 Crore", BudgetRangeModel(minBudget: 100000000, maxBudget: 0x7FFFFFFFFFFFFFFF)),
  customRange("Custom Range", BudgetRangeModel(isCustomBudget: true));

  final String description;
  final BudgetRangeModel? budget;

  const BudgetEnum(this.description, this.budget);
}

enum InternationalBudgetEnum {
  upToTenLakhs("Upto 1 million", BudgetRangeModel(minBudget: 0, maxBudget: 1000000)),
  tenToTwentyLakhs("1 million to 2 million", BudgetRangeModel(minBudget: 1000000, maxBudget: 2000000)),
  twentyToThirtyLakhs("2 million to 3 million", BudgetRangeModel(minBudget: 2000000, maxBudget: 3000000)),
  thirtyToFourtyLakhs("3 million to 4 million", BudgetRangeModel(minBudget: 3000000, maxBudget: 4000000)),
  fourtyToFiftyLakhs("4 million to 5 million", BudgetRangeModel(minBudget: 4000000, maxBudget: 5000000)),
  fiftyToOneCrore("5 million to 10 million", BudgetRangeModel(minBudget: 5000000, maxBudget: 10000000)),
  moreThanOneCrore("10 million to 100 million", BudgetRangeModel(minBudget: 10000000, maxBudget: 100000000)),
  moreThanTenCrore("More than 100 million", BudgetRangeModel(minBudget: 100000000, maxBudget: 0x7FFFFFFFFFFFFFFF)),
  customRange("Custom Range", BudgetRangeModel(isCustomBudget: true));

  final String description;
  final BudgetRangeModel? budget;

  const InternationalBudgetEnum(this.description, this.budget);
}
