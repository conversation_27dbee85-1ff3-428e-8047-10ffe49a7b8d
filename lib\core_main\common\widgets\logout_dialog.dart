import 'package:flutter/material.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class CustomRoundedAlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final String positiveBtnText;
  final String negativeBtnText;
  final Function()? onPositivePressed;
  final Function()? onNegativePressed;

  const CustomRoundedAlertDialog({super.key,
    required this.title,
    required this.content,
    required this.positiveBtnText,
    required this.negativeBtnText,
    required this.onPositivePressed,
    required this.onNegativePressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      content: SizedBox(
        height: 240,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            Container(
              height: 90,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                left: 30,
              ),
              decoration: const BoxDecoration(
                color: Color(0xFFF55757),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(5),
                  topRight: Radius.circular(5),
                ),
                image: DecorationImage(
                  image: AssetImage('assets/images/ic_delete.png'),
                  alignment: Alignment.centerRight,
                ),
              ),
              alignment: Alignment.centerLeft,
              child: Text(
                'Logging out?',
                style: LexendTextStyles.lexend6Medium.copyWith(color: ColorPalette.white,fontSize: 22),
              ),
            ),
            Container(
              height: 140,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 14,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(5),
                  bottomRight: Radius.circular(5),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                   Text(
                    'You are about to logout from the app, Do you want to continue?',
                    style: LexendTextStyles.lexend14Regular.copyWith(color: const Color(0xFF595667)),
                    textAlign: TextAlign.center,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      getButton('Cancel', onNegativePressed!),
                      const SizedBox(
                        width: 14,
                      ),
                      getButton('Continue', onPositivePressed!),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget getButton(String text, Function() onTap) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 17,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: text == 'Cancel' ? Colors.transparent : const Color(0xFFF55757),
        border: text == 'Cancel'
            ? Border.all(
                color: const Color(0xFF292A2B),
              )
            : Border.all(
                color: const Color(0xFFF55757),
              ),
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(
        text,
        style: LexendTextStyles.lexend14Medium.copyWith(color: text == 'Cancel' ? const Color(0xFF292A2B) : Colors.white),
      ),
    ),
  );
}
