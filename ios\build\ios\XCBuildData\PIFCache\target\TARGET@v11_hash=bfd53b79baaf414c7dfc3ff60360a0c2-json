{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984eccf263712fcae50482aee3dd122354", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cb7b1ddffb2341ea92ce6f0f0118e6f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cb7b1ddffb2341ea92ce6f0f0118e6f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ba2015229539f712777fad798d385e0", "guid": "bfdfe7dc352907fc980b868725387e98fbbe543b71214f4509d8c92248a60d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfeeef9ef62665826301140202894bfb", "guid": "bfdfe7dc352907fc980b868725387e98471ff3f9f1b2efe128b7c2d23d239746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0788c9e461fdc1c2897db69f99c3a36", "guid": "bfdfe7dc352907fc980b868725387e98b76c0130342994e0dd30f1d68cb8abfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881876c7ca119c8e13a5f64e7bbf34028", "guid": "bfdfe7dc352907fc980b868725387e98b026e65d04846ce716b6283013c9b796", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981deea2dd220031f5547c54fdb9ee82b5", "guid": "bfdfe7dc352907fc980b868725387e985e0782c485d14f7513f47a5628a6b51b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98376c40bf6d0f05ad056f7459ca29113c", "guid": "bfdfe7dc352907fc980b868725387e980dc402cd31c13f836903d7124fb9cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0084c443b52f9e757d09b22c800996e", "guid": "bfdfe7dc352907fc980b868725387e989c0fc48a74ff41a0a9254c97ece8daaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fd4790b8cba6d2868cbb21cb802699", "guid": "bfdfe7dc352907fc980b868725387e9861fa11d4d6cf54947eab5534b60f0d93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71b7ef53fc43af76b2b40e277fd82ed", "guid": "bfdfe7dc352907fc980b868725387e985abce06578652c4d8dc3d24c702bd3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759725870032fc4972fabc82661614c2", "guid": "bfdfe7dc352907fc980b868725387e98b8c7585471bcf10789da28bb2a96c405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943147e92f741025547765afa9847932", "guid": "bfdfe7dc352907fc980b868725387e98e293850950354b35a2786639c3e3939e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3a02db994fc2e4c72da780db2e156a", "guid": "bfdfe7dc352907fc980b868725387e980ef10060e448131985daa2036af5ae3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d14d6d2c3819f6b8c11f8d9fdfbc4f", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871847bbf5dabed6d6889e171ec72df45", "guid": "bfdfe7dc352907fc980b868725387e98c1277031b6540d3602e8c79a5b369c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c2f1b54f71bc5537d386270fabf44d", "guid": "bfdfe7dc352907fc980b868725387e98b129ca47461bd3877af22b5d56ec22fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e402e3803090a697d27ddc339f565fbf", "guid": "bfdfe7dc352907fc980b868725387e98808ab512e47ea5c54aad6b2807ee7bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b005325ad34cd3ba69d75d47e8d8f12c", "guid": "bfdfe7dc352907fc980b868725387e98c191f1ff744a395685e3770c58c824f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870ed3952cbb9c9a9c76f84f53d4c2add", "guid": "bfdfe7dc352907fc980b868725387e982bc1b5cee954a27e7861f672675db061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a09e52da588a3ad3405ee3a6ca4e3ea", "guid": "bfdfe7dc352907fc980b868725387e984e7a5baa4ea128e81cc7b93ea895a0bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c97f774676b027692121f3980bce9a68", "guid": "bfdfe7dc352907fc980b868725387e986f2e44c4deb448e91b7f9dcbd9877023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd4ff82299a92e3280b11a9e85110d45", "guid": "bfdfe7dc352907fc980b868725387e9878438d158bdc6c70e3cc6f1422a1fd5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ade00ac3f46c390a50bd021825e5a78", "guid": "bfdfe7dc352907fc980b868725387e9896c5db8f09f591e14135d056fe937d39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c61409faa7b78f0c1eea2712d55e2ac", "guid": "bfdfe7dc352907fc980b868725387e98ed733eeeb4cf442e79221b43ec6ab543", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859d59f5013c8cbe2b381f923f896d6e9", "guid": "bfdfe7dc352907fc980b868725387e9863f74156f992cef9610ea0027b158797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825de4636b86a2f3f43c18e904da9ca92", "guid": "bfdfe7dc352907fc980b868725387e9817dcb77d7f539f0ba7cf6bba23ab025a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b748fde53436fa09e141cf0e4e5354e1", "guid": "bfdfe7dc352907fc980b868725387e98430e4d66334ef552e35896edf0800fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989268242b1432581de8d6a633aaf5c4c9", "guid": "bfdfe7dc352907fc980b868725387e98ac1c4a6aecb934ba3f7ce5f4b7a5ce10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4af3bd8ba74d56a901a3cafccbe861", "guid": "bfdfe7dc352907fc980b868725387e980d88881e8f1864ad44addeb4b546f957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b015fcc3ba8a5f04df1cca00a5c5d4", "guid": "bfdfe7dc352907fc980b868725387e981d2fe54f8f024aea0393135c6622c36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c468d405a73dd5c806cd31050ca63c41", "guid": "bfdfe7dc352907fc980b868725387e9875a7bbe49ed36b7b4958102bd487c186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d90281a1acdee48227fdbc50f147382", "guid": "bfdfe7dc352907fc980b868725387e98c2fd66d30ed9c469a6f9e66511104826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ceded3adbce1859a3a40405efda3df", "guid": "bfdfe7dc352907fc980b868725387e986898536a32f1ba314b25df11fafa9a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e4f9a0c188504c3b876110632e7759", "guid": "bfdfe7dc352907fc980b868725387e989f1c87cce3ba04c5b99d6bc86b996ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98728af374bb4932bf8ff995f8d2fd140f", "guid": "bfdfe7dc352907fc980b868725387e98892efd917ae976f6c02ccd36be495e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a92504be151c22d13e734e6e4758e2c5", "guid": "bfdfe7dc352907fc980b868725387e988f6dd6984240ec7b3e112a512bcbd51f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd5f54cc10aad463a4a96c7f9d0fef72", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afffc12d223f5f238a882489dbe74846", "guid": "bfdfe7dc352907fc980b868725387e98111a96713a87f9cb39e8152dd6dde744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893a58b70a01e77dfe36af7cc9a871bdb", "guid": "bfdfe7dc352907fc980b868725387e985b7f8093353d96741cf9ecdce14f0a8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e6b772952f0197a72f525b8984a4cb", "guid": "bfdfe7dc352907fc980b868725387e98a735fa885496d6e8c24d59f0cb2548ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626cdeb1e574c3a8806a1ece4a9e6a3c", "guid": "bfdfe7dc352907fc980b868725387e9837a1e7be82ce3385de98f18fd5990f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d046cc7daa1dbdda69aa541a79b0e56", "guid": "bfdfe7dc352907fc980b868725387e9892af032a9b1b9cd1201f3d2ed313dfed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877cd1909c529b887dfb7773f5b466836", "guid": "bfdfe7dc352907fc980b868725387e9826eaae4dc2c7871e7fbd53b8b7eb1b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890afbb1c8123aed12c421c4c9603e47b", "guid": "bfdfe7dc352907fc980b868725387e98241a83cbc6157ce812894eb73152a9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984530de0292994ac194aaa639dc6de822", "guid": "bfdfe7dc352907fc980b868725387e98160b038fa4d62a21aeddf4fe7e39ba83"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}