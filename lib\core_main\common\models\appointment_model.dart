import 'package:json_annotation/json_annotation.dart';

import 'address_model.dart';
part 'appointment_model.g.dart';

@JsonSerializable()
class AppointmentModel {
  final String? projectName;
  final String? executiveName;
  final String? executiveContactNo;
  final List<AppointmentDocumentsModel>? imagesWithName;
  final String? notes;
  final String? lastModifiedBy;
  final String? lastModifiedByUser;
  final DateTime? lastModifiedOn;
  final DateTime? createdOn;
  final AddressModel? location;

  AppointmentModel({
    this.projectName,
    this.executiveName,
    this.executiveContactNo,
    this.imagesWithName,
    this.notes,
    this.lastModifiedBy,
    this.lastModifiedByUser,
    this.lastModifiedOn,
    this.createdOn,
    this.location,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) => _$AppointmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppointmentModelToJson(this);
}

@JsonSerializable()
class AppointmentDocumentsModel {
  final String? documentName;
  final String? filePath;

  AppointmentDocumentsModel({
    this.documentName,
    this.filePath,
  });

  factory AppointmentDocumentsModel.fromJson(Map<String, dynamic> json) => _$AppointmentDocumentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppointmentDocumentsModelToJson(this);
}
