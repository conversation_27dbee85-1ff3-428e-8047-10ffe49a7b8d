import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../constants/hive_model_constants.dart';

part 'leadrat_subscription_details_model.g.dart';

@HiveType(typeId: HiveModelConstants.leadratSubscriptionDetailsModelTypeId)
@JsonSerializable(includeIfNull: false)
class LeadratSubscriptionDetailsModel {
  @HiveField(0)
  final int? validDays;
  @HiveField(1)
  final bool? isAdmin;
  @HiveField(2)
  final DateTime? licenseValidity;
  @HiveField(3)
  final int? totalSoldLicenses;

  LeadratSubscriptionDetailsModel({
    this.validDays,
    this.isAdmin,
    this.licenseValidity,
    this.totalSoldLicenses,
  });

  factory LeadratSubscriptionDetailsModel.fromJson(Map<String, dynamic> json) => _$LeadratSubscriptionDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadratSubscriptionDetailsModelToJson(this);
}