// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_modified_date_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModifiedDateEnumAdapter extends TypeAdapter<UserModifiedDateEnum> {
  @override
  final int typeId = 192;

  @override
  UserModifiedDateEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return UserModifiedDateEnum.none;
      case 1:
        return UserModifiedDateEnum.user;
      case 2:
        return UserModifiedDateEnum.allUsers;
      case 3:
        return UserModifiedDateEnum.report;
      case 4:
        return UserModifiedDateEnum.allReport;
      case 5:
        return UserModifiedDateEnum.designations;
      default:
        return UserModifiedDateEnum.none;
    }
  }

  @override
  void write(BinaryWriter writer, UserModifiedDateEnum obj) {
    switch (obj) {
      case UserModifiedDateEnum.none:
        writer.writeByte(0);
        break;
      case UserModifiedDateEnum.user:
        writer.writeByte(1);
        break;
      case UserModifiedDateEnum.allUsers:
        writer.writeByte(2);
        break;
      case UserModifiedDateEnum.report:
        writer.writeByte(3);
        break;
      case UserModifiedDateEnum.allReport:
        writer.writeByte(4);
        break;
      case UserModifiedDateEnum.designations:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModifiedDateEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
