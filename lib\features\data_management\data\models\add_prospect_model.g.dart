// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_prospect_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddProspectModel _$AddProspectModelFromJson(Map<String, dynamic> json) =>
    AddProspectModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
      notes: json['notes'] as String?,
      assignTo: json['assignTo'] as String?,
      assignedFrom: json['assignedFrom'] as String?,
      agencyName: json['agencyName'] as String?,
      companyName: json['companyName'] as String?,
      possesionDate: json['possesionDate'] == null
          ? null
          : DateTime.parse(json['possesionDate'] as String),
      profession: $enumDecodeNullable(_$ProfessionEnumMap, json['profession']),
      closingManager: json['closingManager'] as String?,
      sourcingManager: json['sourcingManager'] as String?,
      addressDto: json['addressDto'] == null
          ? null
          : AddressModel.fromJson(json['addressDto'] as Map<String, dynamic>),
      executiveName: json['executiveName'] as String?,
      executiveContactNo: json['executiveContactNo'] as String?,
      scheduleDate: json['scheduleDate'] == null
          ? null
          : DateTime.parse(json['scheduleDate'] as String),
      isQualified: json['isQualified'] as bool?,
      designation: json['designation'] as String?,
      qualifiedDate: json['qualifiedDate'] == null
          ? null
          : DateTime.parse(json['qualifiedDate'] as String),
      convertedDate: json['convertedDate'] == null
          ? null
          : DateTime.parse(json['convertedDate'] as String),
      isConvertedToLead: json['isConvertedToLead'] as bool?,
      agencies: (json['agencies'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      enquiry: json['enquiry'] == null
          ? null
          : CreateProspectEnquiryModel.fromJson(
              json['enquiry'] as Map<String, dynamic>),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      channelPartnerList: (json['channelPartnerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      agencyList: (json['agencyList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      statusId: json['statusId'] as String?,
      campaigns: (json['campaigns'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaignName: json['campaignName'] as String?,
      referralName: json['referralName'] as String?,
      referralContactNo: json['referralContactNo'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      nationality: json['nationality'] as String?,
      serialNumber: json['serialNumber'] as String?,
      landLine: json['landLine'] as String?,
      referralEmail: json['referralEmail'] as String?,
      gender: $enumDecodeNullable(_$GenderEnumEnumMap, json['gender']),
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      maritalStatus: $enumDecodeNullable(
          _$MaritalStatusTypeEnumMap, json['maritalStatus']),
    );

Map<String, dynamic> _$AddProspectModelToJson(AddProspectModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.alternateContactNo case final value?)
        'alternateContactNo': value,
      if (instance.email case final value?) 'email': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.assignedFrom case final value?) 'assignedFrom': value,
      if (instance.agencyName case final value?) 'agencyName': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.possesionDate?.toIso8601String() case final value?)
        'possesionDate': value,
      if (_$ProfessionEnumMap[instance.profession] case final value?)
        'profession': value,
      if (instance.closingManager case final value?) 'closingManager': value,
      if (instance.sourcingManager case final value?) 'sourcingManager': value,
      if (instance.addressDto?.toJson() case final value?) 'addressDto': value,
      if (instance.executiveName case final value?) 'executiveName': value,
      if (instance.executiveContactNo case final value?)
        'executiveContactNo': value,
      if (instance.scheduleDate?.toIso8601String() case final value?)
        'scheduleDate': value,
      if (instance.isQualified case final value?) 'isQualified': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.qualifiedDate?.toIso8601String() case final value?)
        'qualifiedDate': value,
      if (instance.convertedDate?.toIso8601String() case final value?)
        'convertedDate': value,
      if (instance.isConvertedToLead case final value?)
        'isConvertedToLead': value,
      if (instance.agencies?.map((e) => e?.toJson()).toList() case final value?)
        'agencies': value,
      if (instance.enquiry?.toJson() case final value?) 'enquiry': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.channelPartnerList case final value?)
        'channelPartnerList': value,
      if (instance.agencyList case final value?) 'agencyList': value,
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.campaigns?.map((e) => e.toJson()).toList() case final value?)
        'campaigns': value,
      if (instance.campaignName case final value?) 'campaignName': value,
      if (instance.referralContactNo case final value?)
        'referralContactNo': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (instance.nationality case final value?) 'nationality': value,
      if (instance.serialNumber case final value?) 'serialNumber': value,
      if (instance.landLine case final value?) 'landLine': value,
      if (instance.referralEmail case final value?) 'referralEmail': value,
      if (_$GenderEnumEnumMap[instance.gender] case final value?)
        'gender': value,
      if (instance.dateOfBirth?.toIso8601String() case final value?)
        'dateOfBirth': value,
      if (_$MaritalStatusTypeEnumMap[instance.maritalStatus] case final value?)
        'maritalStatus': value,
    };

const _$ProfessionEnumMap = {
  Profession.none: 0,
  Profession.salaried: 1,
  Profession.business: 2,
  Profession.selfEmployed: 3,
  Profession.doctor: 4,
  Profession.retired: 5,
  Profession.housewife: 6,
  Profession.student: 7,
  Profession.unemployed: 8,
  Profession.others: 9,
};

const _$GenderEnumEnumMap = {
  GenderEnum.notMentioned: 0,
  GenderEnum.male: 1,
  GenderEnum.female: 2,
  GenderEnum.other: 3,
};

const _$MaritalStatusTypeEnumMap = {
  MaritalStatusType.notMentioned: 0,
  MaritalStatusType.single: 1,
  MaritalStatusType.married: 2,
};

UpdateProspectModel _$UpdateProspectModelFromJson(Map<String, dynamic> json) =>
    UpdateProspectModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
      notes: json['notes'] as String?,
      assignTo: json['assignTo'] as String?,
      assignedFrom: json['assignedFrom'] as String?,
      agencyName: json['agencyName'] as String?,
      companyName: json['companyName'] as String?,
      possesionDate: json['possesionDate'] == null
          ? null
          : DateTime.parse(json['possesionDate'] as String),
      profession: $enumDecodeNullable(_$ProfessionEnumMap, json['profession']),
      closingManager: json['closingManager'] as String?,
      sourcingManager: json['sourcingManager'] as String?,
      addressDto: json['addressDto'] == null
          ? null
          : AddressModel.fromJson(json['addressDto'] as Map<String, dynamic>),
      executiveName: json['executiveName'] as String?,
      executiveContactNo: json['executiveContactNo'] as String?,
      scheduleDate: json['scheduleDate'] == null
          ? null
          : DateTime.parse(json['scheduleDate'] as String),
      isQualified: json['isQualified'] as bool?,
      designation: json['designation'] as String?,
      qualifiedDate: json['qualifiedDate'] == null
          ? null
          : DateTime.parse(json['qualifiedDate'] as String),
      convertedDate: json['convertedDate'] == null
          ? null
          : DateTime.parse(json['convertedDate'] as String),
      isConvertedToLead: json['isConvertedToLead'] as bool?,
      agencies: (json['agencies'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      enquiry: json['enquiry'] == null
          ? null
          : CreateProspectEnquiryModel.fromJson(
              json['enquiry'] as Map<String, dynamic>),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      channelPartnerList: (json['channelPartnerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      agencyList: (json['agencyList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      statusId: json['statusId'] as String?,
      campaigns: (json['campaigns'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaignName: json['campaignName'] as String?,
      referralContactNo: json['referralContactNo'] as String?,
      referralName: json['referralName'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      nationality: json['nationality'] as String?,
      serialNumber: json['serialNumber'] as String?,
      landLine: json['landLine'] as String?,
      referralEmail: json['referralEmail'] as String?,
      gender: $enumDecodeNullable(_$GenderEnumEnumMap, json['gender']),
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      maritalStatus: $enumDecodeNullable(
          _$MaritalStatusTypeEnumMap, json['maritalStatus']),
    );

Map<String, dynamic> _$UpdateProspectModelToJson(
        UpdateProspectModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.alternateContactNo case final value?)
        'alternateContactNo': value,
      if (instance.email case final value?) 'email': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.assignedFrom case final value?) 'assignedFrom': value,
      if (instance.agencyName case final value?) 'agencyName': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.possesionDate?.toIso8601String() case final value?)
        'possesionDate': value,
      if (_$ProfessionEnumMap[instance.profession] case final value?)
        'profession': value,
      if (instance.closingManager case final value?) 'closingManager': value,
      if (instance.sourcingManager case final value?) 'sourcingManager': value,
      if (instance.addressDto?.toJson() case final value?) 'addressDto': value,
      if (instance.executiveName case final value?) 'executiveName': value,
      if (instance.executiveContactNo case final value?)
        'executiveContactNo': value,
      if (instance.scheduleDate?.toIso8601String() case final value?)
        'scheduleDate': value,
      if (instance.isQualified case final value?) 'isQualified': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.qualifiedDate?.toIso8601String() case final value?)
        'qualifiedDate': value,
      if (instance.convertedDate?.toIso8601String() case final value?)
        'convertedDate': value,
      if (instance.isConvertedToLead case final value?)
        'isConvertedToLead': value,
      if (instance.agencies?.map((e) => e?.toJson()).toList() case final value?)
        'agencies': value,
      if (instance.enquiry?.toJson() case final value?) 'enquiry': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.channelPartnerList case final value?)
        'channelPartnerList': value,
      if (instance.agencyList case final value?) 'agencyList': value,
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.campaigns?.map((e) => e.toJson()).toList() case final value?)
        'campaigns': value,
      if (instance.campaignName case final value?) 'campaignName': value,
      if (instance.referralContactNo case final value?)
        'referralContactNo': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (instance.nationality case final value?) 'nationality': value,
      if (instance.serialNumber case final value?) 'serialNumber': value,
      if (instance.landLine case final value?) 'landLine': value,
      if (instance.referralEmail case final value?) 'referralEmail': value,
      if (_$GenderEnumEnumMap[instance.gender] case final value?)
        'gender': value,
      if (instance.dateOfBirth?.toIso8601String() case final value?)
        'dateOfBirth': value,
      if (_$MaritalStatusTypeEnumMap[instance.maritalStatus] case final value?)
        'maritalStatus': value,
    };
