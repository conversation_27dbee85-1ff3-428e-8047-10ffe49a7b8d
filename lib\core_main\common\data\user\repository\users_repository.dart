import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';
import 'package:leadrat/core_main/common/data/user/models/search_response_model.dart';
import 'package:leadrat/core_main/common/data/user/models/leadrat_subscription_details_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_clockin_model.dart';
import 'package:leadrat/core_main/common/entites/dto_with_name_entity.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/features/lead/data/models/lead_search_property_model.dart';

import '../models/get_all_users_model.dart';
import '../models/user_details_model.dart';

abstract class UsersDataRepository {
  Future<List<GetAllUsersModel?>?> getAllUsers({bool restore = false, Duration duration = const Duration(seconds: 60)});

  Future<List<ItemSimpleModel<GetAllUsersModel>>?> getAllSortedUsers();

  Future<UserDetailsModel?> getUser({bool restore = true, Duration duration = const Duration(seconds: 60)});

  UserDetailsModel? getLoggedInUser();

  List<String?> getUserPermissions();

  bool checkHasPermission(AppModule appModule, CommandType commandType);

  Future<List<GetAllUsersModel?>?> getAdminsAndReportee({bool restore = true, Duration duration = const Duration(seconds: 60)});

  Future<List<GetAllUsersModel?>?> getAllReportees({bool restore = true, Duration duration = const Duration(seconds: 60)});

  Future<List<GetAllUsersModel?>?> getAssignUser();

  Future<Map<String, List<GetUserDesignationModel>>?> getAllUserDesignations({bool restore = false});

  Future<List<DtoWithNameEntity?>?> getAllDesignations();

  Future<List<SearchFilterModel>?> getAllSearchResult(int moduleType);

  Future<bool?> updateSearch(SearchResponse searchResponseModel);

  Future<UserDetailsClockInModel?>? getGeoFenceDetails();

  Future<LeadratSubscriptionDetailsModel?>? getSubscriptionDetails(String? userId);
}
