import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';

part 'master_property_type_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterPropertyTypeId)
@JsonSerializable()
class MasterPropertyTypeModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? baseId;
  @HiveField(2)
  final int? level;
  @HiveField(3)
  final String? type;
  @HiveField(4)
  final String? displayName;
  @HiveField(5)
  final List<MasterPropertyTypeModel>? childTypes;

  MasterPropertyTypeModel({
    this.id,
    this.baseId,
    this.level,
    this.type,
    this.displayName,
    this.childTypes,
  });

  factory MasterPropertyTypeModel.fromJson(Map<String, dynamic> json) => _$MasterPropertyTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterPropertyTypeModelToJson(this);

  PropertyTypeEntity toEntity() {
    return PropertyTypeEntity(
      id: id,
      baseId: baseId,
      level: level,
      type: type,
      displayName: displayName,
      childType: childTypes?.firstOrNull?.toEntity(),
    );
  }
}
