import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'master_associated_bank_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterAssociatedBankModelTypeId)
@JsonSerializable()
class MasterAssociatedBankModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final DateTime? createdOn;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? lastModifiedOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? deletedOn;
  @HiveField(6)
  final String? deletedBy;
  @HiveField(7)
  final String? name;
  @HiveField(8)
  final String? imageUrl;

  MasterAssociatedBankModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.deletedOn,
    this.deletedBy,
    this.name,
    this.imageUrl,
  });

  factory MasterAssociatedBankModel.fromJson(Map<String, dynamic> json) => _$MasterAssociatedBankModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterAssociatedBankModelToJson(this);
}
