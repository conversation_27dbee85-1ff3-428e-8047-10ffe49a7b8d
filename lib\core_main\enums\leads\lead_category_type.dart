import 'package:hive_flutter/adapters.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'lead_category_type.g.dart';

@HiveType(typeId: HiveModelConstants.leadCategoryTypeEnumTypeId)
@JsonEnum(valueField: 'value')
enum LeadCategoryType {
  @HiveField(0)
  newLead(0, "Fresh Leads"),
  @HiveField(1)
  pending(1, "Pending"),
  @HiveField(2)
  scheduledMeeting(2, "Meeting Scheduled"),
  @HiveField(3)
  siteVisitScheduled(3, "Site Visit Scheduled"),
  @HiveField(4)
  notInterested(4, "Not Interested"),
  @HiveField(5)
  callBack(5, "Callback"),
  @HiveField(6)
  unassignedLeads(6, "Unassigned"),
  @HiveField(7)
  booked(7, "Booked"),
  @HiveField(8)
  dropped(8, "Dropped"),
  @HiveField(9)
  hotLeads(9, "Hot Leads"),
  @HiveField(10)
  escalated(10, "Escalated"),
  @HiveField(11)
  aboutToConvert(11, "About To Convert"),
  @HiveField(12)
  scheduleToday(12, "Scheduled Today"),
  @HiveField(13)
  overdue(13, "Overdue"),
  @HiveField(14)
  warmLeads(15, "Warm Leads"),
  @HiveField(15)
  coldLeads(16, "Cold Leads"),
  @HiveField(16)
  highlightedLeads(17, "Highlighted Leads"),
  @HiveField(17)
  allLeads(14, "All Leads"),
  @HiveField(18)
  scheduledTomorrow(18, "Scheduled Tomorrow"),
  @HiveField(19)
  upcomingSchedules(19, "Upcoming"),
  @HiveField(20)
  active(20, "Active"),
  @HiveField(21)
  allWithNID(21, "All With NID"),
  @HiveField(22)
  bookingCancel(22, "Booking Cancel"),
  @HiveField(23)
  untouched(23, "Untouched"),
  @HiveField(24)
  expressionOfInterest(24, "Expression Of Interest"),
  @HiveField(25)
  siteVisitDone(25, "Site Visit Done"),
  @HiveField(26)
  meetingDone(26, "Meeting Done"),
  @HiveField(27)
  invoiced(27, "Invoiced");

  final String description;
  final int value;

  const LeadCategoryType(this.value, this.description);
}
