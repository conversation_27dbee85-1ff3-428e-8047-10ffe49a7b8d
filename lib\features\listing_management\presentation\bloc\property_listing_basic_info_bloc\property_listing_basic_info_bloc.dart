import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_listing/finishing_type.dart';
import 'package:leadrat/core_main/enums/property_listing/uae_emirate.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/listing_management/data/models/add_property_listing_model.dart';
import 'package:leadrat/features/listing_management/data/models/get_property_listing_model.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_property_listing_details_by_id.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/propety_listing_property_info_bloc/property_listing_property_info_bloc.dart';
import 'package:leadrat/main.dart';

part 'property_listing_basic_info_event.dart';

part 'property_listing_basic_info_state.dart';

class PropertyListingBasicInfoBloc extends Bloc<PropertyListingBasicInfoEvent, PropertyListingBasicInfoState> {
  final _uaeEmirates = UaeEmirate.values.where((element) => element != UaeEmirate.none).map((uaeEmirate) => ItemSimpleModel(title: uaeEmirate.description, value: uaeEmirate)).toList();
  final _offeringTypes = OfferingType.values.where((element) => element != OfferingType.none).map((offeringType) => ItemSimpleModel(title: offeringType.description, value: offeringType)).toList();
  final _finishingTypes = FinishingType.values.where((element) => element != FinishingType.none).map((finishingType) => ItemSimpleModel(title: finishingType.description, value: finishingType)).toList();
  final _completionStatuses = CompletionStatus.values.where((element) => element != CompletionStatus.none).map((completionStatus) => ItemSimpleModel<CompletionStatus>(title: completionStatus.description, value: completionStatus)).toList();
  final _enquiredTypes = EnquiryType.values.where((element) => element != EnquiryType.buy).where((element) => element != EnquiryType.none).map((enquiryType) => ItemSimpleModel(title: enquiryType.description, value: enquiryType)).toList();
  final _noOfBhks = NoOfBHK.values.where((bhk) => bhk.noOfBhk % 1 == 0 || bhk.noOfBhk == 0.5).map((bhk) {
    final title = bhk.noOfBhk == 0.5 ? "Studio" : bhk.description.replaceAll("BHK", "BR");
    return ItemSimpleModel<double>(title: title, value: bhk.noOfBhk);
  }).toList();

  final TextEditingController propertyAgeController = TextEditingController();
  final TextEditingController propertySizeController = TextEditingController();
  final TextEditingController propertyTitleEngController = TextEditingController();
  final TextEditingController propertyTitleArbController = TextEditingController();
  final TextEditingController aboutPropertyEngController = TextEditingController();
  final TextEditingController aboutPropertyArbController = TextEditingController();

  List<ItemSimpleModel<GetAllUsersModel>>? _allUsers = [];
  GlobalSettingModel? globalSettingsModel;
  List<MasterPropertyTypeModel>? _masterPropertyTypes;

  AddPropertyListingModel addPropertyListingModel = AddPropertyListingModel(monetaryInfo: PropertyListingMonetaryInfoModel());
  GetPropertyListingModel? getPropertyListingModel;

  PropertyListingPropertyInfoBloc get propertyListingPropertyInfoBloc => getIt<PropertyListingPropertyInfoBloc>();

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  bool get isRentLeaseSelected => state.enquiryTypes.firstWhereOrNull((element) => element.value == EnquiryType.rent)?.isSelected ?? false;

  GlobalKey<FormState> get propertyListingBasicInfoFormKey => GlobalKey<FormState>();

  bool isResidentialSelected = false;
  bool isSaleSelected = false;

  final UsersDataRepository _usersDataRepository;
  final MasterDataRepository _masterDataRepository;
  final GlobalSettingRepository _globalSettingRepository;
  final GetPropertyListingDetailsById _getPropertyListingDetailsById;

  PropertyListingBasicInfoBloc(
    this._usersDataRepository,
    this._masterDataRepository,
    this._globalSettingRepository,
    this._getPropertyListingDetailsById,
  ) : super(const PropertyListingBasicInfoState()) {
    on<PropertyListingBasicInfoInitialEvent>(_onPropertyListingBasicInfoInitial);
    on<ToggleUaeEmiratesEvent>(_onSelectUaeEmirates);
    on<ToggleOfferingTypeEvent>(_onSelectOfferingType);
    on<ToggleFinishingTypeEvent>(_onSelectFinishingType);
    on<ToggleCompletionStatusEvent>(_onSelectCompletionStatus);
    on<ToggleEnquiredForEvent>(_onSelectEnquiredFor);
    on<TogglePropertyTypeEvent>(_onSelectPropertyType);
    on<SelectListingOnBehalfUsersEvent>(_onSelectListingOnBehalfUsers);
    on<SelectListingByUserEvent>(_onSelectListingByUser);
    on<SelectPropertySizeUnitEvent>(_onSelectPropertySizeUnit);
    on<TogglePropertySubTypeEvent>(_onTogglePropertySubType);
    on<ToggleNoOfBhkEvent>(_onToggleNoOfBhk);
    on<ToggleHidePriceEvent>((event, emit) => emit(state.copyWith(hidePriceOnApplication: event.value)));
    on<ResetPropertyListingBasicInfoStateEvent>((event, emit) => emit(const PropertyListingBasicInfoState()));

    // Add listeners to text controllers to trigger quality score calculation
    _setupTextControllerListeners();
  }

  void _setupTextControllerListeners() {
    // Add listeners to text controllers to trigger quality score calculation when fields change
    propertyTitleEngController.addListener(_onFieldChanged);
    propertyTitleArbController.addListener(_onFieldChanged);
    aboutPropertyEngController.addListener(_onFieldChanged);
    aboutPropertyArbController.addListener(_onFieldChanged);
    propertySizeController.addListener(_onFieldChanged);
    propertyAgeController.addListener(_onFieldChanged);
  }

  void _onFieldChanged() {
    // Trigger quality score calculation when any field changes
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onPropertyListingBasicInfoInitial(PropertyListingBasicInfoInitialEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    addPropertyListingModel = AddPropertyListingModel(monetaryInfo: PropertyListingMonetaryInfoModel());
    getPropertyListingModel = GetPropertyListingModel();
    propertyTitleEngController.text = "";
    propertyTitleArbController.text = "";
    aboutPropertyEngController.text = "";
    aboutPropertyArbController.text = "";
    propertySizeController.text = "";
    propertyAgeController.text = "";
    emit(state.copyWith(
      offeringTypes: _offeringTypes,
      enquiryTypes: _enquiredTypes,
      completionStatus: _completionStatuses,
      finishingTypes: _finishingTypes,
      uaeEmirates: _uaeEmirates,
      pageState: event.propertyId.isNullOrEmptyGuid() ? PageState.initial : PageState.loading,
      propertyTypes: event.propertyId.isNullOrEmptyGuid() ? _initPropertyTypes() : [],
    ));
    if (!event.propertyId.isNullOrEmptyGuid()) {
      final propertyDetails = await _getPropertyListingDetailsById(event.propertyId!);
      await propertyDetails.fold(
        (failure) {
          emit(state.copyWith(pageState: PageState.initial));
          LeadratCustomSnackbar.show(message: "Failed to get the property details", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          DialogManager().hideTransparentProgressDialog();
        },
        (success) async {
          if (success != null) {
            getPropertyListingModel = success;
            addPropertyListingModel = success.toAddPropertyListingModel();
            await _initRemoteData(emit);
            await _initInitialData();
            getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
          }
          DialogManager().hideTransparentProgressDialog();
        },
      );
    } else {
      await _initRemoteData(emit);
      DialogManager().hideTransparentProgressDialog();
    }
  }

  FutureOr<void> _onSelectUaeEmirates(ToggleUaeEmiratesEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.uaeEmirates.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(uaeEmirates: updatedItems));
  }

  FutureOr<void> _onSelectOfferingType(ToggleOfferingTypeEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.offeringTypes.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(offeringTypes: updatedItems));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onSelectCompletionStatus(ToggleCompletionStatusEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.completionStatus.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(completionStatus: updatedItems));
  }

  FutureOr<void> _onSelectFinishingType(ToggleFinishingTypeEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.finishingTypes.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(finishingTypes: updatedItems));
  }

  FutureOr<void> _onSelectEnquiredFor(ToggleEnquiredForEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.enquiryTypes.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    isSaleSelected = updatedItems.firstWhereOrNull((element) => element.value == EnquiryType.sale)?.isSelected ?? false;
    emit(state.copyWith(enquiryTypes: updatedItems));
  }

  FutureOr<void> _onTogglePropertySubType(TogglePropertySubTypeEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.propertySubTypes.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(propertySubTypes: updatedItems));
  }

  FutureOr<void> _onSelectPropertyType(TogglePropertyTypeEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final List<ItemSimpleModel<String>> propertySubTypes = !(event.selectedItem?.isSelected ?? false) ? initPropertySubTypes(event.selectedItem?.description ?? '') : [];
    final updatedItems = state.propertyTypes.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    isResidentialSelected = updatedItems.firstWhereOrNull((element) => element.value == PropertyType.residential)?.isSelected ?? false;
    emit(state.copyWith(propertyTypes: updatedItems, propertySubTypes: propertySubTypes, noOfBhk: isResidentialSelected ? _noOfBhks : []));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onToggleNoOfBhk(ToggleNoOfBhkEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    final updatedItems = state.noOfBhk.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(noOfBhk: updatedItems));
  }

  FutureOr<void> _onSelectListingOnBehalfUsers(SelectListingOnBehalfUsersEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    emit(state.copyWith(selectedListingOnBehalfByUsers: event.selectedUser));
  }

  FutureOr<void> _onSelectListingByUser(SelectListingByUserEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    emit(state.copyWith(selectedListingByUsers: event.selectedUsers));
  }

  FutureOr<void> _onSelectPropertySizeUnit(SelectPropertySizeUnitEvent event, Emitter<PropertyListingBasicInfoState> emit) async {
    emit(state.copyWith(selectedPropertySizeUnit: event.selectedPropertySizeUnit));
  }

  _initRemoteData(Emitter<PropertyListingBasicInfoState> emit) async {
    try {
      _allUsers = await _usersDataRepository.getAllSortedUsers();
      globalSettingsModel = await _globalSettingRepository.getGlobalSettings();
      await _initUsers(emit);
      await _initMasterAreaUnits(emit);
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initMasterAreaUnits(Emitter<PropertyListingBasicInfoState> emit) async {
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    _masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: isPropertyListingEnabled);
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final areaUnits = masterAreaUnits?.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit)).toList();
      emit(state.copyWith(pageState: PageState.initial, propertySizeUnits: areaUnits, propertyTypes: getPropertyListingModel?.id == null ? state.propertyTypes : _initPropertyTypes()));
    }
  }

  _initUsers(Emitter<PropertyListingBasicInfoState> emit) async {
    _allUsers = (_allUsers?.isEmpty ?? false) ? await _usersDataRepository.getAllSortedUsers() : _allUsers;
    final listingOnBehalfByUsers = _allUsers?.map((e) => SelectableItem<GetAllUsersModel>(title: e.title, value: e.value, isEnabled: e.isEnabled ?? false)).nonNulls.toList();
    final listingByUsers = _allUsers?.map((e) => SelectableItem<GetAllUsersModel>(title: e.title, value: e.value, isEnabled: e.isEnabled ?? false)).nonNulls.toList();
    emit(state.copyWith(listingByUsers: listingByUsers, listingOnBehalfByUsers: listingOnBehalfByUsers));
  }

  _initInitialData() {
    if (getPropertyListingModel?.uaeEmirate != null && getPropertyListingModel?.uaeEmirate != UaeEmirate.none) {
      add(ToggleUaeEmiratesEvent(_uaeEmirates.firstWhereOrNull((element) => element.value == getPropertyListingModel?.uaeEmirate)));
    }
    if (getPropertyListingModel?.offeringType != null) {
      add(ToggleOfferingTypeEvent(_offeringTypes.firstWhereOrNull((element) => element.value == getPropertyListingModel?.offeringType)));
    }
    if (getPropertyListingModel?.finishingType != null) {
      add(ToggleFinishingTypeEvent(_finishingTypes.firstWhereOrNull((element) => element.value == getPropertyListingModel?.finishingType)));
    }
    if (getPropertyListingModel?.completionStatus != null) {
      add(ToggleCompletionStatusEvent(_completionStatuses.firstWhereOrNull((element) => element.value == getPropertyListingModel?.completionStatus)));
    }
    if (getPropertyListingModel?.enquiredFor != null) {
      add(ToggleEnquiredForEvent(_enquiredTypes.firstWhereOrNull((element) => element.value == getPropertyListingModel?.enquiredFor)));
    }
    if (getPropertyListingModel?.listingOnBehalf?.isNotEmpty ?? false) {
      final selectedListingOnBehalfUser = state.listingByUsers.firstWhereOrNull((element) => getPropertyListingModel?.listingOnBehalf?.firstOrNull == element.value?.id);
      if (selectedListingOnBehalfUser != null) add(SelectListingOnBehalfUsersEvent(selectedListingOnBehalfUser));
    }
    if (getPropertyListingModel?.assignedTo?.isNotEmpty ?? false) {
      final selectedListingOnBehalfUser = state.listingOnBehalfByUsers.where((element) => getPropertyListingModel?.assignedTo?.contains(element.value?.id) ?? false).toList();
      if (selectedListingOnBehalfUser.isNotEmpty) add(SelectListingByUserEvent(selectedListingOnBehalfUser));
    }
    if (getPropertyListingModel?.noOfBHK != null && getPropertyListingModel?.noOfBHK != 0.0) {
      add(ToggleNoOfBhkEvent(_noOfBhks.firstWhereOrNull((element) => element.value == getPropertyListingModel?.noOfBHK)));
    }
    if (getPropertyListingModel?.dimension?.areaUnitId?.isNotNullOrEmpty() ?? false) {
      final selectedAreaUnit = state.propertySizeUnits.firstWhereOrNull((element) => element.value?.id == getPropertyListingModel?.dimension?.areaUnitId);
      add(SelectPropertySizeUnitEvent(selectedAreaUnit?.copyWith(isSelected: true)));
    }
    if (getPropertyListingModel?.monetaryInfo?.isPriceVissible ?? false) {
      add(ToggleHidePriceEvent(getPropertyListingModel!.monetaryInfo!.isPriceVissible!));
    }

    propertyTitleEngController.text = addPropertyListingModel.title ?? '';
    propertyTitleArbController.text = addPropertyListingModel.titleWithLanguage ?? '';
    aboutPropertyEngController.text = addPropertyListingModel.aboutProperty ?? '';
    aboutPropertyArbController.text = addPropertyListingModel.aboutPropertyWithLanguage ?? '';
    propertySizeController.text = addPropertyListingModel.dimension?.area?.doubleToWord() ?? '';
    propertyAgeController.text = addPropertyListingModel.age?.doubleToWord() ?? '';
  }

  List<ItemSimpleModel<String>> initPropertySubTypes(String propertyTypeId) {
    if (_masterPropertyTypes == null) return [];
    final initSelectedPropertySubType = getPropertyListingModel?.propertyType?.childType;
    final propertySubTypes = _masterPropertyTypes!
        .where((type) => type.id == propertyTypeId && type.childTypes != null)
        .expand((type) => type.childTypes!)
        .map(
          (subType) => ItemSimpleModel<String>(title: subType.displayName ?? "", value: subType.id, description: subType.baseId),
        )
        .toList();
    if (initSelectedPropertySubType != null) {
      final selectedPropertySubType = propertySubTypes.firstWhereOrNull((element) => element.value == initSelectedPropertySubType.id);
      if (selectedPropertySubType != null) add(TogglePropertySubTypeEvent(selectedPropertySubType));
    }
    return propertySubTypes;
  }

  List<ItemSimpleModel<PropertyType>> _initPropertyTypes() {
    PropertyType? propertyType;
    if (getPropertyListingModel?.propertyType?.childType != null) {
      propertyType = PropertyType.values.firstWhereOrNull((propertyType) => propertyType.baseId == getPropertyListingModel?.propertyType?.childType?.baseId);
    } else {
      propertyType = PropertyType.values.firstWhereOrNull((propertyType) => propertyType.baseId == getPropertyListingModel?.propertyType?.id);
    }
    final propertyTypes = PropertyType.values.where((type) => type != PropertyType.agricultural).map((type) => ItemSimpleModel<PropertyType>(title: type.description, value: type, description: type.baseId)).toList();
    if (propertyType != null) {
      final selectedPropertyType = propertyTypes.firstWhereOrNull((element) => element.value == propertyType);
      if (selectedPropertyType != null) add(TogglePropertyTypeEvent(selectedPropertyType));
    }
    return propertyTypes;
  }

  bool canNavigateToNextTab() {
    final isEmirateSelected = state.uaeEmirates.any((element) => element.isSelected);
    final isCompletionSelected = state.completionStatus.any((element) => element.isSelected);
    final isEnquiredForSelected = state.enquiryTypes.any((element) => element.isSelected);
    final isPropertyTypeSelected = state.propertyTypes.any((element) => element.isSelected);
    final isPropertySubTypeSelected = state.propertySubTypes.any((element) => element.isSelected);
    final isPropertySizeAndUnitsSelected = propertySizeController.text.isNotNullOrEmpty() && (state.selectedPropertySizeUnit?.isSelected ?? false);
    final isPropertyTitleExits = propertyTitleEngController.text.isNotNullOrEmpty();
    final isResidentialAndBrSelected = isResidentialSelected ? state.noOfBhk.any((element) => element.isSelected) : true;

    if (!isEmirateSelected) {
      LeadratCustomSnackbar.show(message: "kindly select emirates", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isCompletionSelected) {
      LeadratCustomSnackbar.show(message: "kindly select completion status", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isEnquiredForSelected) {
      LeadratCustomSnackbar.show(message: "kindly select enquired for", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isPropertyTypeSelected) {
      LeadratCustomSnackbar.show(message: "kindly select property type", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isPropertySubTypeSelected) {
      LeadratCustomSnackbar.show(message: "kindly select property sub-type", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isPropertySizeAndUnitsSelected) {
      LeadratCustomSnackbar.show(message: "kindly enter property size & select unit", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isPropertyTitleExits) {
      LeadratCustomSnackbar.show(message: "kindly enter property title", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isResidentialAndBrSelected) {
      LeadratCustomSnackbar.show(message: "kindly select no of br", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }
}
