import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/call_type_enum.dart';

part 'call_settings_model.g.dart';

@HiveType(typeId: HiveModelConstants.callSettingsModelTypeId)
@JsonSerializable()
class CallSettingsModel {
  @HiveField(0)
  final CallType? callType;

  CallSettingsModel({this.callType});

  factory CallSettingsModel.fromJson(Map<String, dynamic> json) => _$CallSettingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$CallSettingsModelToJson(this);

 }
