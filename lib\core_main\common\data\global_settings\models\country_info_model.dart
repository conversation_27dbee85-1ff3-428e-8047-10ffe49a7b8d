import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/currencies_model.dart';

part 'country_info_model.g.dart';

@HiveType(typeId: HiveModelConstants.countryInfoModelTypeId)
@JsonSerializable()
class CountryInfoModel {
  @HiveField(0)
  final String? name;
  @HiveField(1)
  final List<String>? callingCode;
  @HiveField(2)
  final String? defaultCallingCode;
  @HiveField(3)
  final String? defaultCurrency;
  @HiveField(4)
  final String? defaultSymbol;
  @HiveField(5)
  final List<String>? codes;
  @HiveField(6)
  final String? code;
  @HiveField(7)
  final String? timeZoneId;
  @HiveField(8)
  final List<CurrenciesModel>? currencies;

  CountryInfoModel({
    this.name,
    this.callingCode,
    this.defaultCallingCode,
    this.defaultCurrency,
    this.defaultSymbol,
    this.codes,
    this.code,
    this.timeZoneId,
    this.currencies,
  });

  factory CountryInfoModel.fromJson(Map<String, dynamic> json) => _$CountryInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountryInfoModelToJson(this);
}

@JsonSerializable()
class BaseCountryInfoModel {
  final List<String>? currencies;
  final List<String>? symbols;
  final List<CountryInfoFormattedModel>? countries;

  BaseCountryInfoModel({this.currencies, this.symbols, this.countries});

  factory BaseCountryInfoModel.fromJson(Map<String, dynamic> json) => _$BaseCountryInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$BaseCountryInfoModelToJson(this);
}

@JsonSerializable()
final class CountryInfoFormattedModel {
  final String? name;
  final String? currency;
  final String? symbol;
  final String? code;
  final String? callingCode;

  CountryInfoFormattedModel({this.name, this.currency, this.symbol, this.code, this.callingCode});

  factory CountryInfoFormattedModel.fromJson(Map<String, dynamic> json) => _$CountryInfoFormattedModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountryInfoFormattedModelToJson(this);
}
