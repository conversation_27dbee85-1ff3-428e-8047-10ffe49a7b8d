import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum ProjectStatusType {
  unKnown(0, 'Un Known'),
  upComing(1, "Up Coming"),
  onGoing(2, "On Going"),
  readyToMove(3, "Ready To Move"),
  fresh(4, "New"),
  resale(5, "Resale"),
  preLaunch(6, "Pre Launch"),
  launch(7, "Launch");

  final int value;
  final String description;

  const ProjectStatusType(this.value, this.description);
}
