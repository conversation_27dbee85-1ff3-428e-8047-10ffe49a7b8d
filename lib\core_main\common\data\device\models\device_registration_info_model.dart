import 'package:json_annotation/json_annotation.dart';

import '../../../../enums/app_enum/operating_system_enum.dart';
part 'device_registration_info_model.g.dart';

@JsonSerializable()
class DeviceRegistrationInfoModel {
  final String? endpointId;
  final OperatingSystemEnum? platform;
  final String? deviceModel;
  final String? deviceUDID;
  final String? applicationTokenValue;
  final String? userId;
  final String? userName;
  final String? email;
  final String? token;
  final bool isActive;
  final bool isTablet;
  final String? appVersion;
  final List<String>? aDSecurityGroups;
  final List<String>? topics;

  DeviceRegistrationInfoModel(
      {this.endpointId,
      this.platform,
      this.deviceModel,
      this.deviceUDID,
      this.applicationTokenValue,
      this.userId,
      this.userName,
      this.email,
      this.token,
      this.isActive = true,
      this.isTablet = false,
      this.appVersion,
      this.aDSecurityGroups,
      this.topics});

  factory DeviceRegistrationInfoModel.fromJson(Map<String, dynamic> json) => _$DeviceRegistrationInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceRegistrationInfoModelToJson(this);

  DeviceRegistrationInfoModel copyWith({
    String? endpointId,
    OperatingSystemEnum? platform,
    String? deviceModel,
    String? deviceUDID,
    String? applicationTokenValue,
    String? userId,
    String? userName,
    String? email,
    String? token,
    bool? isActive,
    bool? isTablet,
    String? appVersion,
    List<String>? aDSecurityGroups,
    List<String>? topics,
  }) {
    return DeviceRegistrationInfoModel(
      endpointId: endpointId ?? this.endpointId,
      platform: platform ?? this.platform,
      deviceModel: deviceModel ?? this.deviceModel,
      deviceUDID: deviceUDID ?? this.deviceUDID,
      applicationTokenValue: applicationTokenValue ?? this.applicationTokenValue,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      email: email ?? this.email,
      token: token ?? this.token,
      isActive: isActive ?? this.isActive,
      isTablet: isTablet ?? this.isTablet,
      appVersion: appVersion ?? this.appVersion,
      aDSecurityGroups: aDSecurityGroups ?? this.aDSecurityGroups,
      topics: topics ?? this.topics,
    );
  }
}
