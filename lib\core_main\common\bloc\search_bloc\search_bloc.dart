import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/search_response_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/lead_property_module_wise_enum.dart';
import 'package:leadrat/core_main/enums/leads/lead_category_type.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/data/models/lead_search_property_model.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_property_module_wise_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/search_leads_use_case.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_category_model.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_model.dart';
import 'package:leadrat/features/listing_management/domain/usecase/search_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_listing_management_model.dart';
import 'package:leadrat/features/projects/domain/usecase/get_all_projects_usecase.dart';
import 'package:leadrat/features/projects/presentation/items/item_project_model.dart';
import 'package:leadrat/features/properties/domain/usecase/search_properties_usecase.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_model.dart';

part 'search_event.dart';
part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final SearchLeadsUseCase _searchLeadsUseCase;
  final GetAllProjectsUseCase _getAllProjectsUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final SearchPropertiesUseCase _getPropertiesBySearchUseCase;
  final SearchPropertyListingUseCase _searchPropertyListingUseCase;
  final GetLeadPropertyModuleWiseUseCase _getLeadPropertyModuleWiseUseCase;
  final UsersDataRepository _usersDataRepository;

  final int _minSearchLength = 2;
  final Duration _debounceTime = const Duration(milliseconds: 500);

  final ScrollController itemListScrollController = ScrollController();
  final TextEditingController searchController = TextEditingController();
  GlobalSettingModel? _globalSettings;
  Timer? _debounce;
  bool _isSearching = false;
  String _lastSearchText = '';

  final isPropertyListingEnabled = getIt<LeadratHomeBloc>().isPropertyListingEnabled;

  SearchBloc(
    this._searchLeadsUseCase,
    this._getAllProjectsUseCase,
    this._globalSettingRepository,
    this._getPropertiesBySearchUseCase,
    this._searchPropertyListingUseCase,
    this._getLeadPropertyModuleWiseUseCase,
    this._usersDataRepository,
  ) : super(const SearchState()) {
    on<SearchInitialEvent>(_onSearchLeadInitial);
    on<SearchLeadEvent>(_onSearchLead);
    on<GetLeadsEvent>(_onGetLeads);
    on<ScrolledEvent>(_onScrolled);
    on<GetPropertiesEvent>(_onGetProperties);
    on<GetProjectsEvent>(_onGetProjectsEvent);
    on<GetPropertyListingEvent>(_onGetPropertyListing);
    on<ToggleTagsEvent>(_onToggleTagsEvent);
    on<RemoveSelectedItemEvent>(_onRemoveSelectedItemEvent);
    on<RemoveLabelEvent>(_onRemoveLabelEvent);
    on<SearchDisposeEvent>(_onSearchDisposeEvent);
    on<UpdatePropertyListingsEvent>(_onUpdatePropertyListings);
    on<UpdateRecentSearchEvent>(_onUpdateRecentSearchEvent);

    searchController.addListener(_onSearchTextChanged);
    itemListScrollController.addListener(_onScrolledListener);
  }

  void _onSearchTextChanged() {
    if (!_isSearching && searchController.text.trim().isNotEmpty && _lastSearchText != searchController.text) {
      _lastSearchText = searchController.text;
      add(SearchLeadEvent());
    }
  }

  @override
  Future<void> close() async {
    _debounce?.cancel();
    searchController
      ..removeListener(_onSearchTextChanged)
      ..dispose();
    itemListScrollController
      ..removeListener(_onScrolledListener)
      ..dispose();
    return super.close();
  }

  Future<void> _onSearchLeadInitial(SearchInitialEvent event, Emitter<SearchState> emit) async {
    try {
      _lastSearchText = '';
      _debounce?.cancel();
      searchController.clear();
      _isSearching = false;
      if (event.appModule == AppModule.lead) {
        await _initLabels(emit);
        await _initRecentSearch(emit);
      }

      emit(state.copyWith(
        pageState: PageState.initial,
        pageNo: 1,
        leads: const [],
        loadMore: false,
        errorMessage: '',
        totalCount: 0,
        properties: const [],
        propertyListings: const [],
        appModule: event.appModule,
        projects: [],
      ));

      _globalSettings ??= await _globalSettingRepository.getGlobalSettings();
    } catch (e) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: 'Failed to initialize search: ${e.toString()}'));
    }
  }

  Future<void> _initLabels(Emitter<SearchState> emit) async {
    final label = await _getLeadPropertyModuleWiseUseCase.call(LeadPropertyModuleWise.leads.index);
    label.fold(
      (failure) => emit(state.copyWith(pageState: PageState.failure)),
      (success) {
        emit(state.copyWith(labels: success, originalLevels: success));
      },
    );
  }

  Future<void> _initRecentSearch(Emitter<SearchState> emit) async {
    final recentSearch = await _usersDataRepository.getAllSearchResult(LeadPropertyModuleWise.leads.index);
    const defaultProperties = {'leadname', 'contactno', 'serialnumber'};

    final recentList = recentSearch ?? [];

    final labelList = state.labels ?? [];
    if (recentList.isEmpty) {
      final defaultItems = labelList.where((labelItem) {
        final property = labelItem?.propertyName?.toLowerCase().trim();
        return defaultProperties.contains(property);
      }).toList();
      recentList.addAll(defaultItems as Iterable<SearchFilterModel>);
    }

    var filteredLabels = labelList.where((labelItem) {
      final labelName = labelItem?.propertyName?.toLowerCase().trim();
      return !recentList.any((recentItem) => recentItem.propertyName?.toLowerCase().trim() == labelName);
    }).toList();

    emit(state.copyWith(
      recentSearch: recentList,
      selectedItem: recentList,
      labels: filteredLabels,
      isRecentSearch: false,
    ));
  }

  Future<void> _onSearchLead(SearchLeadEvent event, Emitter<SearchState> emit) async {
    try {
      _debounce?.cancel();

      if (event.searchText != null) {
        searchController.text = event.searchText!;
      }

      final initialSearchText = searchController.text.trim();

      if (initialSearchText.isEmpty) {
        emit(state.copyWith(
          pageState: PageState.initial,
          leads: const [],
          properties: const [],
          propertyListings: const [],
          pageNo: 1,
          projects: [],
          totalCount: 0,
        ));
        return;
      }

      if (initialSearchText.length < _minSearchLength || _isSearching) return;

      await Future.delayed(const Duration(seconds: 1));

      final latestSearchText = searchController.text.trim();

      if (latestSearchText.length < _minSearchLength || _isSearching) return;

      emit(state.copyWith(
        pageState: PageState.loading,
        pageNo: 1,
        leads: const [],
        properties: const [],
        propertyListings: const [],
      ));

      _debounce = Timer(_debounceTime, () {
        if (!_isSearching && searchController.text.trim() == latestSearchText) {
          _searchData(state);
        }
      });
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Search failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onGetLeads(GetLeadsEvent event, Emitter<SearchState> emit) async {
    if (_isSearching) return;

    _isSearching = true;

    try {
      final searchText = searchController.text.trim();

      if (searchText.length < _minSearchLength) {
        _isSearching = false;
        return;
      }
      List<String?> selectedSearch = state.selectedItem.map((e) => e?.propertyName).toList();

      final searchedItems = await _searchLeadsUseCase.call(
        SearchLeadsUseCaseParams(pageNo: state.pageNo, query: searchText, propertyToSearch: selectedSearch, canAccessAllLeads: getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.viewAllLeads)),
      );

      final List<ItemLeadModel> initialItems = List.from(state.leads ?? []);

      await searchedItems.fold(
        (failure) async {
          emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, leads: const [], pageNo: state.pageNo, loadMore: false));
        },
        (success) async {
          if (success?.leads?.isNotEmpty ?? false) {
            final leads = success?.leads
                ?.map((leadEntity) => ItemLeadModel(
                      lead: leadEntity,
                      isDualOwnership: _globalSettings?.isDualOwnershipEnabled ?? false,
                      isCustomStatusEnabled: _globalSettings?.isCustomStatusEnabled ?? false,
                      shouldRenameSiteVisit: _globalSettings?.shouldRenameSiteVisitColumn ?? false,
                    ))
                .toList();
            var itemCategory = ItemLeadCategoryModel(leads: [...initialItems, ...?leads], title: "Searched Leads", totalLeadCount: success?.totalCount ?? 0, leadCategoryType: LeadCategoryType.allLeads);
            if (leads != null) {
              emit(state.copyWith(leads: [...initialItems, ...leads], totalCount: success?.totalCount ?? 0, errorMessage: null, pageState: PageState.success, pageNo: state.pageNo + 1, loadMore: false, searchedLeadCategory: itemCategory, isTagsVisible: false));
            }
          } else if (success?.totalCount == 0) {
            emit(state.copyWith(pageState: PageState.failure, errorMessage: "No leads found", leads: const [], pageNo: 1, loadMore: false));
          } else {
            emit(state.copyWith(loadMore: false));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to get leads: ${e.toString()}',
        loadMore: false,
      ));
    } finally {
      _isSearching = false;
    }
  }

  Future<void> _onScrolled(
    ScrolledEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (!state.loadMore && state.pageState == PageState.success) {
      emit(state.copyWith(loadMore: true));
      _searchData(state);
    }
  }

  void _onScrolledListener() {
    if (!state.loadMore && !_isSearching && searchController.text.trim().length >= _minSearchLength && itemListScrollController.hasClients && itemListScrollController.position.pixels >= itemListScrollController.position.maxScrollExtent * 0.5) {
      add(ScrolledEvent());
    }
  }

  FutureOr<void> _onGetProperties(GetPropertiesEvent event, Emitter<SearchState> emit) async {
    if (_isSearching) return;

    _isSearching = true;

    try {
      final searchText = searchController.text.trim();

      if (searchText.length < _minSearchLength) {
        _isSearching = false;
        return;
      }

      final searchedItems = await _getPropertiesBySearchUseCase.call(
        SearchPropertiesUseCaseParams(searchText, state.pageNo, isPropertyListingEnabled: isPropertyListingEnabled),
      );

      if (searchText != searchController.text.trim()) {
        _isSearching = false;
        return;
      }

      final List<ItemPropertyModel> initialItems = List.from(state.properties ?? []);

      await searchedItems.fold(
        (failure) async {
          emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, leads: const [], properties: [], pageNo: state.pageNo, loadMore: false));
        },
        (success) async {
          if (success?.items.isNotEmpty ?? false) {
            final properties = success?.items.map((propertyEntity) => ItemPropertyModel(propertyItem: propertyEntity)).toList();
            if (properties != null) {
              emit(state.copyWith(properties: [...initialItems, ...properties], totalCount: success?.totalCount.toInt() ?? 0, errorMessage: null, pageState: PageState.success, pageNo: state.pageNo + 1, loadMore: false));
            }
          } else if (success?.totalCount == 0) {
            emit(state.copyWith(pageState: PageState.failure, errorMessage: "No properties found", leads: const [], properties: [], pageNo: 1, loadMore: false));
          } else {
            emit(state.copyWith(loadMore: false));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to get properties: ${e.toString()}',
        loadMore: false,
      ));
    } finally {
      _isSearching = false;
    }
  }

  FutureOr<void> _onGetProjectsEvent(GetProjectsEvent event, Emitter<SearchState> emit) async {
    if (_isSearching) return;

    _isSearching = true;

    try {
      final searchText = searchController.text.trim();

      if (searchText.length < _minSearchLength) {
        _isSearching = false;
        return;
      }

      final searchedItems = await _getAllProjectsUseCase.call(GetAllProjectsParams(pageNumber: state.pageNo, searchText: searchText));

      final List<ItemProjectModel> initialItems = List.from(state.projects ?? []);

      await searchedItems.fold(
        (failure) async {
          emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, leads: const [], pageNo: state.pageNo, loadMore: false));
        },
        (success) async {
          if (success?.items.isNotEmpty ?? false) {
            final projects = success?.items.map((leadEntity) => ItemProjectModel(projectItem: leadEntity)).toList();
            if (projects != null) {
              emit(state.copyWith(
                projects: [...initialItems, ...projects],
                totalCount: success?.totalCount.toInt() ?? 0,
                errorMessage: null,
                pageState: PageState.success,
                pageNo: state.pageNo + 1,
                loadMore: false,
              ));
            }
          } else if (success?.totalCount == 0) {
            emit(state.copyWith(pageState: PageState.failure, errorMessage: "No projects found", projects: const [], pageNo: 1, loadMore: false));
          } else {
            emit(state.copyWith(loadMore: false));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to get projects: ${e.toString()}',
        loadMore: false,
      ));
    } finally {
      _isSearching = false;
    }
  }

  FutureOr<void> _onGetPropertyListing(GetPropertyListingEvent event, Emitter<SearchState> emit) async {
    if (_isSearching) return;

    _isSearching = true;

    try {
      final searchText = searchController.text.trim();

      if (searchText.length < _minSearchLength) {
        _isSearching = false;
        return;
      }

      final searchedItems = await _searchPropertyListingUseCase.call(
        SearchPropertyListingUseCaseParams(searchText, state.pageNo),
      );

      if (searchText != searchController.text.trim()) {
        _isSearching = false;
        return;
      }

      final List<ItemListingManagementModel> initialItems = List.from(state.propertyListings ?? []);

      await searchedItems.fold(
        (failure) async {
          emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, leads: const [], properties: [], propertyListings: [], pageNo: state.pageNo, loadMore: false));
        },
        (success) async {
          if (success?.items.isNotEmpty ?? false) {
            final properties = success?.items.map((propertyEntity) => ItemListingManagementModel(property: propertyEntity)).toList();
            if (properties != null) {
              emit(state.copyWith(propertyListings: [...initialItems, ...properties], totalCount: success?.totalCount.toInt() ?? 0, errorMessage: null, pageState: PageState.success, pageNo: state.pageNo + 1, loadMore: false));
            }
          } else if (success?.totalCount == 0) {
            emit(state.copyWith(pageState: PageState.failure, errorMessage: "No properties found", leads: const [], propertyListings: [], properties: [], pageNo: 1, loadMore: false));
          } else {
            emit(state.copyWith(loadMore: false));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to get properties: ${e.toString()}',
        loadMore: false,
      ));
    } finally {
      _isSearching = false;
    }
  }

  FutureOr<void> _onToggleTagsEvent(ToggleTagsEvent event, Emitter<SearchState> emit) {
    emit(state.copyWith(isTagsVisible: event.isVisible));
  }

  FutureOr<void> _onRemoveSelectedItemEvent(RemoveSelectedItemEvent event, Emitter<SearchState> emit) {
    if (event.removeItem == null) return null;
    final updatedSelectedItems = List<SearchFilterModel>.from(state.selectedItem)..remove(event.removeItem);

    final updatedLabels = List<SearchFilterModel>.from(state.labels);

    final originalIndex = state.originalLevels.indexOf(event.removeItem);

    if (originalIndex != -1) {
      int insertIndex = 0;
      for (int i = 0; i < updatedLabels.length; i++) {
        final currentOriginalIndex = state.originalLevels.indexOf(updatedLabels[i]);
        if (currentOriginalIndex > originalIndex) {
          break;
        }
        insertIndex++;
      }

      updatedLabels.insert(insertIndex, event.removeItem!);
    } else {
      updatedLabels.add(event.removeItem!);
    }

    emit(state.copyWith(
      selectedItem: updatedSelectedItems,
      labels: updatedLabels,
      isMoreThanSevenSelected: false,
      isRecentSearch: true,
    ));
  }

  FutureOr<void> _onUpdatePropertyListings(UpdatePropertyListingsEvent event, Emitter<SearchState> emit) {
    emit(state.copyWith(propertyListings: event.propertyListing));
  }

  FutureOr<void> _onUpdateRecentSearchEvent(UpdateRecentSearchEvent event, Emitter<SearchState> emit) async {
    SearchResponse searchResponse = SearchResponse(module: LeadPropertyModuleWise.leads.index, searchResults: state.selectedItem.map((e) => e?.propertyName).toList());
    final result = await _usersDataRepository.updateSearch(searchResponse);
    if (result == true) {
      emit(state.copyWith(isTagsVisible: false));
      await _initRecentSearch(emit);
    }
  }

  FutureOr<void> _onRemoveLabelEvent(RemoveLabelEvent event, Emitter<SearchState> emit) {
    if (state.selectedItem.length >= 7) {
      emit(state.copyWith(isMoreThanSevenSelected: true));
    } else {
      final updatedLabels = List<SearchFilterModel>.from(state.labels)..remove(event.label);

      emit(state.copyWith(
          labels: updatedLabels,
          selectedItem: [
            ...state.selectedItem,
            ...[event.label]
          ],
          isRecentSearch: true));
    }
  }

  FutureOr<void> _onSearchDisposeEvent(SearchDisposeEvent event, Emitter<SearchState> emit) {
    emit(state.copyWith(isTagsVisible: false, selectedItem: []));
  }

  void _searchData(SearchState state) {
    switch (state.appModule) {
      case AppModule.lead:
        add(GetLeadsEvent());
        break;
      case AppModule.property:
        isPropertyListingEnabled ? add(GetPropertyListingEvent()) : add(GetPropertiesEvent());
        break;
      case AppModule.project:
        add(GetProjectsEvent());
      default:
        break;
    }
  }
}
