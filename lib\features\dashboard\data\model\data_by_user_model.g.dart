// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_by_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DataByUserModel _$DataByUserModelFromJson(Map<String, dynamic> json) =>
    DataByUserModel(
      userId: json['userId'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => ViewDataUserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      convertedData: (json['convertedData'] as num?)?.toInt(),
      all: (json['all'] as num?)?.toInt(),
      active: (json['active'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DataByUserModelToJson(DataByUserModel instance) =>
    <String, dynamic>{
      if (instance.userId case final value?) 'userId': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.data case final value?) 'data': value,
      if (instance.convertedData case final value?) 'convertedData': value,
      if (instance.all case final value?) 'all': value,
      if (instance.active case final value?) 'active': value,
    };

ViewDataUserModel _$ViewDataUserModelFromJson(Map<String, dynamic> json) =>
    ViewDataUserModel(
      statusId: json['statusId'] as String?,
      statusDisplayName: json['statusDisplayName'] as String?,
      data: (json['data'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ViewDataUserModelToJson(ViewDataUserModel instance) =>
    <String, dynamic>{
      'statusId': instance.statusId,
      'statusDisplayName': instance.statusDisplayName,
      'data': instance.data,
    };
