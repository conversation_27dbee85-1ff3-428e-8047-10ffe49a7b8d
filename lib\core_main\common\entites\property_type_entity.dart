class PropertyTypeEntity {
  final String? id;
  final String? baseId;
  final int? level;
  final String? type;
  final String? displayName;
  final PropertyTypeEntity? childType;

  PropertyTypeEntity({
    this.id,
    this.baseId,
    this.level,
    this.type,
    this.displayName,
    this.childType,
  });

  PropertyTypeEntity copyWith({
    String? id,
    String? baseId,
    int? level,
    String? type,
    String? displayName,
    PropertyTypeEntity? childType,
  }) {
    return PropertyTypeEntity(
      id: id ?? this.id,
      baseId: baseId ?? this.baseId,
      level: level ?? this.level,
      type: type ?? this.type,
      displayName: displayName ?? this.displayName,
      childType: childType ?? this.childType,
    );
  }
}
