import 'package:collection/collection.dart';
import 'package:dlibphonenumber/generated/classes/phone_number/phonenumber.pb.dart';
import 'package:dlibphonenumber/phone_number_util.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/local/global_settings_local_data_source.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_text_form_field.dart';
import 'package:leadrat/core_main/common/widgets/lrb_phone_field/countries.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class LrbPhoneField extends LeadratStatefulWidget {
  final String labelText;
  final String? hintText;
  final bool isRequired;
  final TextEditingController? controller;
  final String? selectedDialCode;
  final Function(bool isValid, String? countryCode, String? phoneNumber)? onValidationChanged;
  final bool isEnabled;
  final String? Function(String? phoneNumber, String? countryCode)? validation;
  final bool takeDefaultBottomSpace;

  const LrbPhoneField({
    super.key,
    required this.labelText,
    this.hintText,
    this.isRequired = false,
    this.controller,
    this.onValidationChanged,
    this.validation,
    this.selectedDialCode,
    this.isEnabled = true,
    this.takeDefaultBottomSpace = true,
  });

  @override
  State<LrbPhoneField> createState() => _LrbPhoneFieldState();
}

class _LrbPhoneFieldState extends LeadratState<LrbPhoneField> {
  final phoneUtil = PhoneNumberUtil.instance;
  late final List<SelectableItem<Country>> _countries;
  SelectableItem<Country>? _selectedCountry;
  final TextEditingController _internalController = TextEditingController();
  String? _lastProcessedValue;
  late bool canSelectInternationalNumber = true;

  TextEditingController get _effectiveController => widget.controller ?? _internalController;

  @override
  void initState() {
    super.initState();
    _initializeCountries();
    _setupControllerListener();
    _processInitialValue();
    canSelectInternationalNumber = getIt<GlobalSettingsLocalDataSource>().getGlobalSettings()?.hasInternationalSupport ?? false;
  }

  void _initializeCountries() {
    _countries = countryCodes.map((e) => SelectableItem<Country>(title: "${e.flag} ${e.name}", value: e)).toList();
    _updateSelectedCountry();
  }

  void _updateSelectedCountry() {
    final dialCode = widget.selectedDialCode?.replaceAll("+", '') ?? "91";

    setState(() {
      _selectedCountry = _countries.firstWhere(
        (element) => element.value?.dialCode == dialCode,
        orElse: () => _countries.first,
      );
    });
  }

  void _setupControllerListener() {
    _effectiveController.addListener(() {
      final newValue = _effectiveController.text;

      if (newValue != _lastProcessedValue) {
        _lastProcessedValue = newValue;
        _processPhoneNumber(newValue);
      }
    });
  }

  void _processInitialValue() {
    if (_effectiveController.text.isNotEmpty) {
      _processPhoneNumber(_effectiveController.text);
    }
  }

  void _processPhoneNumber(String value) {
    if (value.isEmpty) return;

    if (value.startsWith('+')) {
      String normalizedNumber = value.replaceAll(RegExp(r'[^\d+]'), '');
      String numberWithoutPlus = normalizedNumber.substring(1);

      Country? matchingCountry = countryCodes.firstWhereOrNull(
        (country) => numberWithoutPlus.startsWith(country.dialCode),
      );

      if (matchingCountry != null) {
        var country = _countries.firstWhereOrNull((element) => element.value?.dialCode == matchingCountry.dialCode);

        if (mounted) {
          setState(() {
            _selectedCountry = country;
          });
        }

        String phoneWithoutCountry = value.replaceAll("+${matchingCountry.dialCode}", '');
        if (_effectiveController.text != phoneWithoutCountry) {
          _effectiveController.text = phoneWithoutCountry;
        }
        _triggerValidationCallback(true, matchingCountry.dialCode, phoneWithoutCountry);
      }
    }
  }

  @override
  void didUpdateWidget(LrbPhoneField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller?.removeListener(_setupControllerListener);
      _setupControllerListener();
      _processInitialValue();
    }

    if (widget.selectedDialCode != oldWidget.selectedDialCode && (widget.controller?.text.isNullOrEmpty() ?? false)) {
      _updateSelectedCountry();
    }
  }

  @override
  void dispose() {
    _effectiveController.removeListener(_setupControllerListener);
    if (widget.controller == null) {
      _internalController.dispose();
    }
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return LeadratTextFormField(
      keyboardType: TextInputType.phone,
      defaultPrefixIcon: false,
      labelText: widget.labelText,
      isRequired: widget.isRequired,
      hintText: widget.hintText,
      takeDefaultBottomSpace: widget.takeDefaultBottomSpace,

      // maxLength: _selectedCountry?.value?.maxLength ?? 10,
      counterText: '',
      controller: _effectiveController,
      isEnabled: widget.isEnabled,
      validator: (value) {
        final baseValidation = _baseValidator(value);
        if (baseValidation != null) {
          return baseValidation;
        }
        if (widget.validation != null) {
          final additionalValidation = widget.validation!(
            value,
            _selectedCountry?.value?.dialCode,
          );
          if (additionalValidation != null) {
            _triggerValidationCallback(false, _selectedCountry?.value?.dialCode, value);
            return additionalValidation;
          }
        }

        return null;
      },
      prefixIcon: SelectableItemBottomSheet(
        title: "select country",
        selectableItems: _countries,
        selectedItem: _selectedCountry,
        canSearchItems: true,
        isEnabled: widget.isEnabled && canSelectInternationalNumber,
        disabledSnackBarMessage: canSelectInternationalNumber ? null : "You don't have permission to add international number",
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                " +${_selectedCountry?.value?.dialCode ?? ''} ${_selectedCountry?.value?.flag ?? ''}",
                style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.primaryColor),
              ),
              Container(
                width: 1.5,
                height: 40,
                color: ColorPalette.veryLightGray,
                margin: const EdgeInsets.only(left: 10, right: 5),
              ),
            ],
          ),
        ),
        onItemSelected: (selectedValue) {
          setState(() {
            _selectedCountry = selectedValue;
          });
          final temp = _effectiveController.text;
          _effectiveController.text = '';
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _effectiveController.text = temp;
          });
        },
      ),
    );
  }

  String? _baseValidator(String? value) {
    final digitsOnly = RegExp(r'^\d+$');
    if (value == null || value.isEmpty) {
      if (widget.isRequired) {
        _triggerValidationCallback(false, null, null);
        return "${widget.labelText} is required field";
      } else {
        _triggerValidationCallback(true, _selectedCountry?.value?.dialCode, null);
        return null;
      }
    }

    if (!digitsOnly.hasMatch(value)) {
      _triggerValidationCallback(false, null, null);
      return "Only digits are allowed";
    }

    try {
      PhoneNumber phoneNumber = phoneUtil.parseAndKeepRawInput(value, _selectedCountry?.value?.code);
      bool isValid = phoneUtil.isValidNumber(phoneNumber);
      if (!isValid) return "Invalid contact number";
    } catch (ex) {
      if (value.length < (_selectedCountry?.value?.minLength ?? 10) || value.length > (_selectedCountry?.value?.maxLength ?? 10) || (int.tryParse(value) ?? 0) <= 0) {
        _triggerValidationCallback(false, _selectedCountry?.value?.dialCode, null);
        return "Invalid contact number";
      }
    }

    _triggerValidationCallback(true, _selectedCountry?.value?.dialCode, value);
    return null;
  }

  void _triggerValidationCallback(bool isValid, String? countryCode, String? phoneNumber) {
    if (widget.onValidationChanged != null) {
      widget.onValidationChanged!(isValid, countryCode, phoneNumber);
    }
  }
}
