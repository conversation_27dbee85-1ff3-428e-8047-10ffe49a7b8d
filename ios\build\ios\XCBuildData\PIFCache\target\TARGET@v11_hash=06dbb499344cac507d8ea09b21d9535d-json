{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98648f13ea353974d1bcdbdfa90e7414d6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7780d3b755670cef34375119dc6bb1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7780d3b755670cef34375119dc6bb1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b5b9795eb5807efc6a57176f274ea249", "guid": "bfdfe7dc352907fc980b868725387e989d10e7d3a23565665ecad4bb21b006ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5dd35ef2ebb8d448601177a63f19759", "guid": "bfdfe7dc352907fc980b868725387e98a437d7843677599243314f9796cca63f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e75fb38569aa0e58411f37da03f200c", "guid": "bfdfe7dc352907fc980b868725387e98f7508504560af08492884b5aa74b4a58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98022fe300dcf36cd76adda392cd494f0a", "guid": "bfdfe7dc352907fc980b868725387e987e2d7e2d258cc9b76c86040a5462944c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72666f391c0d38c275a4e0e3101d641", "guid": "bfdfe7dc352907fc980b868725387e982e2bb68a7f5f5c6a2b1070763c0adb8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc186c13703e6a2cbfcb66fccd09d90", "guid": "bfdfe7dc352907fc980b868725387e988ee816fad95681358d9e5868ce1be1d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e6e167d32a963528467fa37a0e0136", "guid": "bfdfe7dc352907fc980b868725387e98717dcd7fc3cd7ad8289651cf0d99dceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176cde516e920b65616734bd0af8faa3", "guid": "bfdfe7dc352907fc980b868725387e9813a13c07d683bba187d804fc2328f9e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4a0fa654b844361e4a72740462c5191", "guid": "bfdfe7dc352907fc980b868725387e984ca9e2a0dabba19c7d0be9178ea47d87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bdaa101e0f5af99b6035ecad273d18f", "guid": "bfdfe7dc352907fc980b868725387e988c23767f8f859a5605150a47c93aacfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3f402ba7abf82fce97f2a430c41991d", "guid": "bfdfe7dc352907fc980b868725387e98b104a33f14df2b9ec9fa0fbdfa5b86b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f60bb0d886c11c5a909e3e57f7dc572", "guid": "bfdfe7dc352907fc980b868725387e984f899f56f70e275b22b1fec8d3a58ba4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983701fd9db054994d11b2d95480a085c4", "guid": "bfdfe7dc352907fc980b868725387e9806d958b9136febe6b5d320bcb2ed21cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb86b3d008fc404e95f5de9d26f3241", "guid": "bfdfe7dc352907fc980b868725387e98ca2f0db09652e0d3acaa095d5e93bcae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e964114ca7c2f3fdc7ca4fac32f5477", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98392ca1e433d075964c07306a0271d941", "guid": "bfdfe7dc352907fc980b868725387e982eb0b3c19d046186551e61f0546b3a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897831c270882917e5e878a1a6f61af27", "guid": "bfdfe7dc352907fc980b868725387e983c7d9bdc7f94f786c11277fd3d9cbbec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8b79995bcb331d125d4543c7c7eaad", "guid": "bfdfe7dc352907fc980b868725387e98f795b3c0efbed74a1c7eafc8571edb73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0ba5ba913b72789473e45bdcfa9c0b", "guid": "bfdfe7dc352907fc980b868725387e98b55695732aae845f6666bc708d5f8eb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1fb9e7083cda5877ec86cebf4bf2bca", "guid": "bfdfe7dc352907fc980b868725387e987add46a6da30b48a62a998d4c6b775ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee5ba1d5fa55054d911db18b47bd33e", "guid": "bfdfe7dc352907fc980b868725387e982e89c660bba527ecfb1a557172ade444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d918b727c69197f7dc40025ec621320a", "guid": "bfdfe7dc352907fc980b868725387e9801edc74c43caa165d192a43f00ca9be5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982473fe166f3ee9f8f3983a65d81dd7ff", "guid": "bfdfe7dc352907fc980b868725387e98938c6e33b823dca4b1e04f77b8133d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852af8265d3db88aaf19427fb3c9031ed", "guid": "bfdfe7dc352907fc980b868725387e98597c83acd83c2684c9649cc5681a377f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ab4efb2b95456b6e339b764e3470c7", "guid": "bfdfe7dc352907fc980b868725387e98731e1d634136e6a2bd0bf1fe18f590cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988028a84a7c9944118723615111d40b23", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}