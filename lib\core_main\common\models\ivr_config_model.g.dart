// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ivr_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IvrConfigModel _$IvrConfigModelFromJson(Map<String, dynamic> json) =>
    IvrConfigModel(
      destinationNumber: json['destinationNumber'] as String?,
      agentNumber: json['agentNumber'] as String?,
      callerIdOrVirtualNumber: json['callerIdOrVirtualNumber'] as String?,
      userEmail: json['userEmail'] as String?,
      callerId: json['callerId'] as String?,
      leadId: json['leadId'] as String?,
      prospectId: json['prospectId'] as String?,
      userId: json['userId'] as String?,
    );

Map<String, dynamic> _$IvrConfigModelToJson(IvrConfigModel instance) =>
    <String, dynamic>{
      if (instance.destinationNumber case final value?)
        'destinationNumber': value,
      if (instance.agentNumber case final value?) 'agentNumber': value,
      if (instance.callerIdOrVirtualNumber case final value?)
        'callerIdOrVirtualNumber': value,
      if (instance.userEmail case final value?) 'userEmail': value,
      if (instance.callerId case final value?) 'callerId': value,
      if (instance.leadId case final value?) 'leadId': value,
      if (instance.prospectId case final value?) 'prospectId': value,
      if (instance.userId case final value?) 'userId': value,
    };

VirtualNumberModel _$VirtualNumberModelFromJson(Map<String, dynamic> json) =>
    VirtualNumberModel(
      isVirtualNumberAssigned: json['isVirtualNumberAssigned'] as bool?,
      shouldFetchVirtualNumbers: json['shouldFetchVirtualNumbers'] as bool?,
      virtualNumber: json['virtualNumber'] as String?,
    );

Map<String, dynamic> _$VirtualNumberModelToJson(VirtualNumberModel instance) =>
    <String, dynamic>{
      'isVirtualNumberAssigned': instance.isVirtualNumberAssigned,
      'shouldFetchVirtualNumbers': instance.shouldFetchVirtualNumbers,
      'virtualNumber': instance.virtualNumber,
    };

IvrResponseModel _$IvrResponseModelFromJson(Map<String, dynamic> json) =>
    IvrResponseModel(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      clickToCallCommonDto: json['clickToCallCommonDto'] == null
          ? null
          : IvrConfigModel.fromJson(
              json['clickToCallCommonDto'] as Map<String, dynamic>),
      callId: json['callId'] as String?,
    );

Map<String, dynamic> _$IvrResponseModelToJson(IvrResponseModel instance) =>
    <String, dynamic>{
      if (instance.success case final value?) 'success': value,
      if (instance.message case final value?) 'message': value,
      if (instance.clickToCallCommonDto?.toJson() case final value?)
        'clickToCallCommonDto': value,
      if (instance.callId case final value?) 'callId': value,
    };
