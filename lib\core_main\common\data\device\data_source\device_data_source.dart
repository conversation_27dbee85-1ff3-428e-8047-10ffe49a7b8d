import 'package:leadrat/core_main/common/data/device/models/device_info_model.dart';
import 'package:leadrat/core_main/common/data/device/models/device_config_model.dart';
import 'package:leadrat/core_main/common/data/device/models/device_registration_info_model.dart';

import '../models/registration_device_response_model.dart';

abstract interface class DeviceDataSource {
  Future<RegistrationDeviceResponseModel> changeDeviceRegistration(DeviceRegistrationInfoModel deviceRegistrationInfoModel);
  Future<String?> postDeviceInfo (DeviceInfoModel deviceInfo);
  Future<String?> postDeviceConfig (DeviceConfigModel deviceConfigModel);
}