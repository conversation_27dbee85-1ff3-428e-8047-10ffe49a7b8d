import 'package:flutter/material.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';

import '../../resources/theme/text_styles.dart';

Future<void> showActionBottomSheet(BuildContext context, Function()? onTap, String leadingText, String actionText, Widget title) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: ColorPalette.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.zero, // Removes the curve at the top
      ),
    ),
    builder: (BuildContext context) {
      return SizedBox(
        height: MediaQuery.of(context).size.height * 0.2,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            title,
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 15),
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color: ColorPalette.gray200, // Background color
                          borderRadius: BorderRadius.circular(12.0), // Rounded corners
                        ), // Background color
                        // Adds some padding inside the container
                        child: Center(
                          child: Text(
                            actionText,
                            style: LexendTextStyles.lexend14Medium.copyWith(
                              color: ColorPalette.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 20,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 15),
                    child: GestureDetector(
                      onTap: onTap,
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color: ColorPalette.black, // Background color
                          borderRadius: BorderRadius.circular(12.0), // Rounded corners
                        ), // Background color

                        child: Center(
                          child: Text(
                            leadingText,
                            style: LexendTextStyles.lexend12Bold.copyWith(
                              color: ColorPalette.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      );
    },
  );
}
