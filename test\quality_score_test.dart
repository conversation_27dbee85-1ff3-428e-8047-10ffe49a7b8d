import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:leadrat/core_main/utilities/property_utils.dart';

void main() {
  group('PropertyUtils Quality Score Tests', () {
    test('should calculate correct quality score with all criteria met', () async {
      // Test with perfect conditions
      final score = await PropertyUtils.calculatePropertyListingQualityScore(
        description: 'A' * 1000, // 1000 characters (within 750-2000 range)
        title: 'Perfect Property Title Here', // 40 characters (within 30-50 range)
        locationId: 'location123',
        imageFiles: List.generate(10, (index) => File('test_image_$index.jpg')), // 10 images
        dldPermitNumber: 'DLD123456',
        isListingComplete: true,
      );

      // Expected: 10 + 6 + 5 + 18 + 2 + 19 + 10 + 20 + 10 = 100
      // Note: Image dimensions and diversity will fail in test environment
      // So expected score would be lower
      expect(score, greaterThan(0));
      expect(score, lessThanOrEqualTo(100));
    });

    test('should return 0 for empty inputs', () async {
      final score = await PropertyUtils.calculatePropertyListingQualityScore();
      expect(score, equals(0));
    });

    test('should award points for description in correct range', () async {
      // Test description scoring
      final scoreWithGoodDescription = await PropertyUtils.calculatePropertyListingQualityScore(
        description: 'A' * 1000, // 1000 characters (within range)
      );

      final scoreWithBadDescription = await PropertyUtils.calculatePropertyListingQualityScore(
        description: 'Short', // Too short
      );

      expect(scoreWithGoodDescription, greaterThan(scoreWithBadDescription));
    });

    test('should award points for title in correct range', () async {
      // Test title scoring
      final scoreWithGoodTitle = await PropertyUtils.calculatePropertyListingQualityScore(
        title: 'Perfect Property Title Here For Sale', // 35 characters (within 30-50 range)
      );

      final scoreWithBadTitle = await PropertyUtils.calculatePropertyListingQualityScore(
        title: 'Short', // Too short
      );

      // Good title should score 10 points, bad title should score 0 points
      expect(scoreWithGoodTitle, equals(10));
      expect(scoreWithBadTitle, equals(0));
    });

    test('should award points for location', () async {
      final scoreWithLocation = await PropertyUtils.calculatePropertyListingQualityScore(
        locationId: 'location123',
      );

      final scoreWithoutLocation = await PropertyUtils.calculatePropertyListingQualityScore();

      expect(scoreWithLocation, greaterThan(scoreWithoutLocation));
    });

    test('should award points for verification', () async {
      final scoreWithPermit = await PropertyUtils.calculatePropertyListingQualityScore(
        dldPermitNumber: 'DLD123456',
      );

      final scoreWithoutPermit = await PropertyUtils.calculatePropertyListingQualityScore();

      expect(scoreWithPermit, greaterThan(scoreWithoutPermit));
    });

    test('should award points for listing completion', () async {
      final scoreComplete = await PropertyUtils.calculatePropertyListingQualityScore(
        isListingComplete: true,
      );

      final scoreIncomplete = await PropertyUtils.calculatePropertyListingQualityScore(
        isListingComplete: false,
      );

      expect(scoreComplete, greaterThan(scoreIncomplete));
    });
  });
}
