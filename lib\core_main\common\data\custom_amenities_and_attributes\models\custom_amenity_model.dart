import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
part 'custom_amenity_model.g.dart';
@HiveType(typeId: HiveModelConstants.customAmenitiesTypeId)
@JsonSerializable()
class CustomAmenitiesModel {
  @HiveField(0)
  final String? categoryName;
  @HiveField(1)
  final List<CustomAmenityModel>? amenities;

  CustomAmenitiesModel({
    this.categoryName,
    this.amenities,
  });
  factory CustomAmenitiesModel.fromJson(Map<String, dynamic> json) => _$CustomAmenitiesModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomAmenitiesModelToJson(this);

}
@HiveType(typeId: HiveModelConstants.customAmenityTypeId)
@JsonSerializable()
class CustomAmenityModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final String? amenityName;
  @HiveField(9)
  final String? amenityDisplayName;
  @HiveField(10)
  final String? imageURL;
  @HiveField(11)
  final String? inActiveImageURL;
  @HiveField(12)
  final String? amenityType;
  @HiveField(13)
  final String? category;
  @HiveField(14)
  final List<PropertyType>? propertyType;
  @HiveField(15)
  final int? orderRank;
  @HiveField(16)
  final String? mobileIcon;
  @HiveField(17)
  final bool? isActive;
  @HiveField(18)
  final String? masterAmenityId;

  CustomAmenityModel({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.amenityName,
    this.amenityDisplayName,
    this.imageURL,
    this.inActiveImageURL,
    this.amenityType,
    this.category,
    this.propertyType,
    this.orderRank,
    this.mobileIcon,
    this.isActive,
    this.masterAmenityId,
  });
  factory CustomAmenityModel.fromJson(Map<String, dynamic> json) => _$CustomAmenityModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomAmenityModelToJson(this);

}
