class BudgetRangeModel {
  final int? minBudget;
  final int? maxBudget;
  final bool? isCustomBudget;

  const BudgetRangeModel({this.minBudget, this.maxBudget, this.isCustomBudget});

  BudgetRangeModel copyWith({
    int? minBudget,
    int? maxBudget,
    bool? isCustomBudget,
  }) {
    return BudgetRangeModel(
      minBudget: minBudget ?? this.minBudget,
      maxBudget: maxBudget ?? this.maxBudget,
      isCustomBudget: isCustomBudget ?? this.isCustomBudget,
    );
  }
}
