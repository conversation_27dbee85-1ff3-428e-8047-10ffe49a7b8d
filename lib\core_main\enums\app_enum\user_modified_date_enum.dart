import 'package:hive/hive.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'user_modified_date_enum.g.dart';

@HiveType(typeId: HiveModelConstants.userModifiedDateEnumTypeId)
enum UserModifiedDateEnum {
  @HiveField(0)
  none(0, 'None'),
  @HiveField(1)
  user(1, 'User'),
  @HiveField(2)
  allUsers(2, 'AllUsers'),
  @HiveField(3)
  report(3, 'Report'),
  @HiveField(4)
  allReport(4, 'AllReport'),
  @HiveField(5)
  designations(5, 'Designations');

  final int value;
  final String key;

  const UserModifiedDateEnum(this.value, this.key);

  factory UserModifiedDateEnum.fromString(String key) {
    return UserModifiedDateEnum.values.firstWhere(
      (e) => e.key == key,
      orElse: () => UserModifiedDateEnum.none,
    );
  }
}
