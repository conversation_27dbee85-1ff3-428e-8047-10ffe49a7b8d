class PhoneContactModel {
  final String? fullName;
  final PhoneNumber? phoneNumber;
  final List<String>? phoneNumbers;

  PhoneContactModel({this.fullName, this.phoneNumber, this.phoneNumbers});

  PhoneContactModel copyWith({
    String? fullName,
    PhoneNumber? phoneNumber,
    List<String>? phoneNumbers,
  }) {
    return PhoneContactModel(
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      phoneNumbers: phoneNumbers ?? this.phoneNumbers,
    );
  }
}

class PhoneNumber {
  final String? number;

  PhoneNumber({this.number});
}
