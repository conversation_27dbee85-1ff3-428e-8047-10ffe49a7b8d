part of 'message_template_bloc.dart';

@immutable
class MessageTemplateState {
  final PageState pageState;
  final String? errorMessage;
  final String? leadNames;
  final SelectableItem<TemplateEntity>? selectedTemplate;
  final List<SelectableItem<AppModuleName>> templatesCategories;
  final List<SelectableItem<String?>> propertiesList;
  final List<SelectableItem<String?>> projectsList;
  final List<SelectableItem<String?>> selectedPropertyOrProjects;
  final List<SelectableItem<TemplateEntity>>? templates;

  const MessageTemplateState({
    this.selectedPropertyOrProjects = const [],
    this.pageState = PageState.initial,
    this.propertiesList = const [],
    this.projectsList = const [],
    this.errorMessage,
    this.templates = const [],
    this.leadNames,
    this.selectedTemplate,
    this.templatesCategories = const [],
  });

  MessageTemplateState copyWith({
    List<SelectableItem<String?>>? selectedPropertyOrProjects,
    PageState? pageState,
    String? errorMessage,
    CopyWithValue<SelectableItem<TemplateEntity>?>? selectedTemplate,
    String? leadNames,
    List<SelectableItem<AppModuleName>>? templatesCategories,
    List<SelectableItem<String?>>? propertiesList,
    List<SelectableItem<String?>>? projectsList,
    List<SelectableItem<TemplateEntity>>? templates,
  }) {
    return MessageTemplateState(
      templates: templates ?? this.templates,
      selectedPropertyOrProjects: selectedPropertyOrProjects ?? this.selectedPropertyOrProjects,
      pageState: pageState ?? this.pageState,
      leadNames: leadNames ?? this.leadNames,
      propertiesList: propertiesList ?? this.propertiesList,
      projectsList: projectsList ?? this.projectsList,
      errorMessage: errorMessage,
      selectedTemplate: selectedTemplate != null && selectedTemplate.canUpdateValue ? selectedTemplate.value : this.selectedTemplate,
      templatesCategories: templatesCategories ?? this.templatesCategories,
    );
  }
}
