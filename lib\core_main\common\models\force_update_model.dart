import 'package:json_annotation/json_annotation.dart';

part 'force_update_model.g.dart';

@JsonSerializable()
class ForceUpdateModel {
  @J<PERSON><PERSON><PERSON>(name: "AndroidLatestVersion")
  final String? androidLatestVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: "IOSLatestVersion")
  final String? iosLatestVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: "AndroidForceUpdateVersion")
  final String? androidForceUpdateVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: "IOSForceUpdateVersion")
  final String? iosForceUpdateVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: "IsUnderMaintenance")
  final String? isUnderMaintenance;
  @J<PERSON><PERSON><PERSON>(name: "AndroidSoftUpdateVersion")
  final String? androidSoftUpdateVersion;
  @Json<PERSON><PERSON>(name: "IOSSoftUpdateVersion")
  final String? iOSSoftUpdateVersion;

  ForceUpdateModel({
    this.androidLatestVersion,
    this.iosLatestVersion,
    this.androidForceUpdateVersion,
    this.iosForceUpdateVersion,
    this.isUnderMaintenance,
    this.androidSoftUpdateVersion,
    this.iOSSoftUpdateVersion,
  });

  factory ForceUpdateModel.fromJson(Map<String, dynamic> json) => _$ForceUpdateModelFromJson(json);

  Map<String, dynamic> toJson() => _$ForceUpdateModelToJson(this);
}
