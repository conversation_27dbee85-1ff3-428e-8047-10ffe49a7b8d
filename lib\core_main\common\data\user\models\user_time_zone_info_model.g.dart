// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_time_zone_info_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserTimeZoneInfoModelAdapter extends TypeAdapter<UserTimeZoneInfoModel> {
  @override
  final int typeId = 110;

  @override
  UserTimeZoneInfoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserTimeZoneInfoModel();
  }

  @override
  void write(BinaryWriter writer, UserTimeZoneInfoModel obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserTimeZoneInfoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserTimeZoneInfoModel _$UserTimeZoneInfoModelFromJson(
        Map<String, dynamic> json) =>
    UserTimeZoneInfoModel(
      timeZoneId: json['timeZoneId'] as String?,
      timeZoneDisplay: json['timeZoneDisplay'] as String?,
      timeZoneName: json['timeZoneName'] as String?,
      baseUTcOffset: json['baseUTcOffset'] as String?,
    );

Map<String, dynamic> _$UserTimeZoneInfoModelToJson(
        UserTimeZoneInfoModel instance) =>
    <String, dynamic>{
      if (instance.timeZoneId case final value?) 'timeZoneId': value,
      if (instance.timeZoneDisplay case final value?) 'timeZoneDisplay': value,
      if (instance.timeZoneName case final value?) 'timeZoneName': value,
      if (instance.baseUTcOffset case final value?) 'baseUTcOffset': value,
    };
