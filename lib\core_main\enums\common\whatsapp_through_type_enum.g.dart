// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whatsapp_through_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WhatsappThroughTypeEnumAdapter
    extends TypeAdapter<WhatsappThroughTypeEnum> {
  @override
  final int typeId = 118;

  @override
  WhatsappThroughTypeEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return WhatsappThroughTypeEnum.askEveryTime;
      case 1:
        return WhatsappThroughTypeEnum.templateShare;
      case 2:
        return WhatsappThroughTypeEnum.openConversation;
      default:
        return WhatsappThroughTypeEnum.askEveryTime;
    }
  }

  @override
  void write(BinaryWriter writer, WhatsappThroughTypeEnum obj) {
    switch (obj) {
      case WhatsappThroughTypeEnum.askEveryTime:
        writer.writeByte(0);
        break;
      case WhatsappThroughTypeEnum.templateShare:
        writer.writeByte(1);
        break;
      case WhatsappThroughTypeEnum.openConversation:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WhatsappThroughTypeEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
