{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ed63360b0415701b7f5f88672a135b3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986ee3153ccc8bbc771c0c683c2fdb0ec9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858d6d8898bfaa0d4518f6e677f7e16ea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d67fe55628bb7cbe4863a5728b3a843", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858d6d8898bfaa0d4518f6e677f7e16ea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987dd691eba051c78b6e2665fb609ed594", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6dff1ce26036cf0322333dfd7e1a552", "guid": "bfdfe7dc352907fc980b868725387e98fae7c14c84275abcae295a1bd21bd960", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c087c5dbbdc985dee84a56ff7caddeea", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851a76464274ba447610dc5e650c65023", "guid": "bfdfe7dc352907fc980b868725387e9891fca22710c1a941eb560d5eb9bd1f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3b97fd408be7aeb1d255a9c1652eaf", "guid": "bfdfe7dc352907fc980b868725387e988542b482704c19b4492bb0bc7f4807dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987826d55d34eb7e9c0d098eebff9c5598", "guid": "bfdfe7dc352907fc980b868725387e9880864e78f139a7f6715cbb8fee429ad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05422d85933b3212cde13b52c4394a0", "guid": "bfdfe7dc352907fc980b868725387e9875c488e6bf22e04bffb2240988f4f994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91f3b124e4e4b2a1ff94496d055fd70", "guid": "bfdfe7dc352907fc980b868725387e9884005b5b11358d4d3949abe40139bfbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dccd970e91fb716c5ffe814e00cb1765", "guid": "bfdfe7dc352907fc980b868725387e98d7f21836f1de7ba3ee26ad587e090796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d0b1b28b121ef9bd5332b7b00f86c6", "guid": "bfdfe7dc352907fc980b868725387e987cb23b3ea3e795bdb3c257f91387af2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d17b244b949c4c966d52acc9f03f19c", "guid": "bfdfe7dc352907fc980b868725387e9809d8a69453b39d65564a5168d942ec16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986723efedf2acaca411fe52593bbc09c4", "guid": "bfdfe7dc352907fc980b868725387e987d3e383a2a8beb3507850e44e02184e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98822ea313fd5db89b5141707bc2d87267", "guid": "bfdfe7dc352907fc980b868725387e98e5d90185702790cdb32abed5649a4a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98861ec9cc9c1c819167fdef9ba59f9720", "guid": "bfdfe7dc352907fc980b868725387e9883d7865e926b57710bb2c36c4396edcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f69e7c80dcbf57f4b519fb1ba3bb471e", "guid": "bfdfe7dc352907fc980b868725387e987f93174f62ca0b8be4f405fcf1b159dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4381f07d7ce921b827e61fea81bfeef", "guid": "bfdfe7dc352907fc980b868725387e987123fb68299b052c017999d781837ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f42d3c39156f12d712e46e9fed1683b", "guid": "bfdfe7dc352907fc980b868725387e983ececd6f258d99b308bd2b502d4d4752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4036778b5f592e0d0ff8d1dfbc70cfb", "guid": "bfdfe7dc352907fc980b868725387e98c0b97e3fdbbe56924d0dedfade341027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981005d72c0f499fdfae5d689778582813", "guid": "bfdfe7dc352907fc980b868725387e98803250ea350668b41bed2546ad774f68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf259242a482e3fa791dd047b135477", "guid": "bfdfe7dc352907fc980b868725387e98e562d5c35df4be6f71646c1b46531176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbffe2a547bbcdd87892b541e77dde0f", "guid": "bfdfe7dc352907fc980b868725387e9833abf0c0d50370345ed36f54e100eafe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c0d6c215c9fe880b8847572dc9ef83", "guid": "bfdfe7dc352907fc980b868725387e98deeeaef2de70449b6907a679429b1174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee4f2987dd1619b981d213d48c469cb", "guid": "bfdfe7dc352907fc980b868725387e98461c78b9bc817962a0966e0d362bc620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7861046cb987f2c443cc2702095c199", "guid": "bfdfe7dc352907fc980b868725387e986030a2319df7941570f04f8726ffc6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881fa076a11a115b4f91fadc399016f4f", "guid": "bfdfe7dc352907fc980b868725387e983aa10ad40ebce498570933a46def1594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe910acbd3a9b6152fcadf4f3c20f3c3", "guid": "bfdfe7dc352907fc980b868725387e98c65c48340549bc1e7d7f59148e2d0ff4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb09ad260d5f5b02bb0acc15732c0336", "guid": "bfdfe7dc352907fc980b868725387e98185c538dcb7c561641672f3096454f9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd4ff7016d971b1e80dfff182402c5f", "guid": "bfdfe7dc352907fc980b868725387e98b302d79d5ad5ab8de5b665bd31c88802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d99bff74610c7ae60b0799be4e4c18", "guid": "bfdfe7dc352907fc980b868725387e985c3e0aee9f6f840d9d2e885301d7911b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1318df0a0eeee7962e3ba8c298e5ba8", "guid": "bfdfe7dc352907fc980b868725387e9853b6752b12f03fa533f4fce0339623a2"}], "guid": "bfdfe7dc352907fc980b868725387e9822dafba81346bf794432d245828672fc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8f76d148b86e45f5a4fde2844b1b676", "guid": "bfdfe7dc352907fc980b868725387e98387e9520458da5108ba18d7e61fa84aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ca0483875414283457834de9e17fdc", "guid": "bfdfe7dc352907fc980b868725387e98b45aab94c5357b6408bf4699f3af2db7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98a7d24b69e881985833dd29d77beb93b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989febcf50f0594135daa125ad9e14dbc4", "guid": "bfdfe7dc352907fc980b868725387e985eff687237a7c9de9a1538398f637f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98568c88918002791a98a483e5911b1a73"}], "guid": "bfdfe7dc352907fc980b868725387e981f0ff85da5ca53180a4ee2cd6d6c296b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d2eb700dca82647c835c40df078a9f81", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98506c0d96d9c3d8a6c8f1f6d64038665d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}