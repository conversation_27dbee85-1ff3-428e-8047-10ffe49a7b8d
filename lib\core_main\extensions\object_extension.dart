import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/services/utility_service/utility_service.dart';

extension ObjectExtension on Object {
  void logException([StackTrace? stackTrace]) {
    if (this is String) {
      if (kDebugMode) {
        print(this);
      }
    } else if (this is Exception) {
      if (kDebugMode) {
        final error = this as Exception;
        print("\x1B[31m${error.toString()}\x1B[0m");
      }
    }
  }

  void printInConsole() {
    if (this is String) {
      if (kDebugMode) {
        print("DEBUG LOG: $this");
      }
    }
  }
}
