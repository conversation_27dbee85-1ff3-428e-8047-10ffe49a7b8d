import 'package:leadrat/core_main/managers/shared_preference_manager/shared_preference_manager.dart';

class TokenManager {
  final SharedPreferenceManager sharedPreferenceManager;

  TokenManager({required this.sharedPreferenceManager});

  String? idToken;
  String? tenant;
  String? refreshToken;
  String? userName;
  String? userId;
  String? pass;

  Future<void> loadTokens() async {
    idToken = await sharedPreferenceManager.getString("id_token");
    tenant = await sharedPreferenceManager.getString("domain");
    refreshToken =  await sharedPreferenceManager.getString("refresh_token");
    pass =  await sharedPreferenceManager.getString("pass");
    userName =  await sharedPreferenceManager.getString("user_name");
    userId = await sharedPreferenceManager.getString("user_id");
  }

  Future<void> setIdToken(String token) async {
    idToken = token;
    await sharedPreferenceManager.setString("id_token", token);
  }

  Future<void> setTenant(String tenantValue) async {
    tenant = tenantValue;
    await sharedPreferenceManager.setString("domain", tenantValue);
  }

  Future<void> setRefreshToken(String newRefreshToken) async {
    refreshToken = newRefreshToken;
    await sharedPreferenceManager.setString("refresh_token", newRefreshToken);
  }

  Future<void> setUsername(String uname) async {
    pass = uname;
    await sharedPreferenceManager.setString("user_name", uname);
  }

  Future<void> setPass(String password) async {
    pass = password;
    await sharedPreferenceManager.setString("pass", password);
  }

  Future<void> setUserId(String id) async {
    userId = id;
    await sharedPreferenceManager.setString("user_id", id);
  }
}
