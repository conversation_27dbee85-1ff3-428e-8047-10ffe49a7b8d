import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/time_zone_utils.dart';

class LeadratDatePicker extends LeadratStatefulWidget {
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? onDateSelected;
  final String? placeholder;
  final Widget? child;
  final Color? placeHolderTextColor;
  final bool allowPastDates;
  final bool allowFutureDates;
  final DateTime? minDate;
  final DateTime? maxDate;
  final bool selectDateAndTime;
  final bool showIconAtPrefix;

  const LeadratDatePicker({
    Key? key,
    this.selectedDate,
    this.onDateSelected,
    this.placeholder = "Select date",
    this.child,
    this.placeHolderTextColor,
    this.allowPastDates = true,
    this.allowFutureDates = true,
    this.minDate,
    this.maxDate,
    this.selectDateAndTime = false,
    this.showIconAtPrefix = false,
  }) : super(key: key);

  @override
  State<LeadratDatePicker> createState() => _LeadratDatePickerState();
}

class _LeadratDatePickerState extends LeadratState<LeadratDatePicker> {
  late String _displayText;

  @override
  void initState() {
    super.initState();
    _updateDisplayText();
  }

  @override
  void didUpdateWidget(LeadratDatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDate != widget.selectedDate) {
      _updateDisplayText();
    }
  }

  void _updateDisplayText() {
    if (widget.selectedDate != null) {
      _displayText = widget.selectDateAndTime ? DateFormat('dd/MM/yyyy hh:mm a').format(widget.selectedDate!) : DateFormat('dd/MM/yyyy').format(widget.selectedDate!);
    } else {
      _displayText = widget.placeholder ?? (widget.selectDateAndTime ? "Select date and time" : "Select date");
    }
  }

  DateTime _getFirstDate() {
    if (widget.minDate != null) return widget.minDate!;
    DateTime today = TimeZoneUtils.getCurrentDateInUserTimeZone();
    return widget.allowPastDates ? DateTime(1900) : today;
  }

  DateTime _getLastDate() {
    if (widget.maxDate != null) return widget.maxDate!;
    DateTime today = TimeZoneUtils.getCurrentDateInUserTimeZone();
    return widget.allowFutureDates ? DateTime(2100) : today;
  }

  DateTime _getInitialDate() {
    final today = TimeZoneUtils.getCurrentDateInUserTimeZone();
    final selected = widget.selectedDate ?? today;
    final first = _getFirstDate();
    final last = _getLastDate();

    if (selected.isBefore(first)) return first;
    if (selected.isAfter(last)) return last;
    return selected;
  }

  Future<void> _selectDate(BuildContext context) async {
    final firstDate = _getFirstDate();
    final lastDate = _getLastDate();
    final initialDate = _getInitialDate();

    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      currentDate: TimeZoneUtils.getCurrentDateInUserTimeZone(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );

    if (pickedDate != null && mounted) {
      DateTime finalDateTime = pickedDate;

      if (widget.selectDateAndTime) {
        final pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(widget.selectedDate ?? DateTime.now().toUserTimeZone()!),
          builder: (context, child) => MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          ),
        );

        if (pickedTime == null) return;

        finalDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
      }

      setState(() {
        _displayText = widget.selectDateAndTime ? DateFormat('dd/MM/yyyy hh:mm a').format(finalDateTime) : DateFormat('dd/MM/yyyy').format(finalDateTime);
      });

      widget.onDateSelected?.call(finalDateTime);
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return widget.child != null
        ? GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
              _selectDate(context);
            },
            child: widget.child,
          )
        : TextFormField(
            controller: TextEditingController(text: _displayText),
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: widget.placeHolderTextColor ?? ColorPalette.primaryDarkColor,
              overflow: TextOverflow.ellipsis,
            ),
            decoration: InputDecoration(
              suffixIcon: widget.showIconAtPrefix ? null : Icon(widget.selectDateAndTime ? Icons.access_time : Icons.calendar_today),
              prefixIcon: widget.showIconAtPrefix ? Icon(widget.selectDateAndTime ? Icons.access_time : Icons.calendar_today) : null,
              hintStyle: LexendTextStyles.lexend10Regular.copyWith(
                color: ColorPalette.primary,
              ),
            ),
            readOnly: true,
            onTap: () {
              FocusScope.of(context).unfocus();
              _selectDate(context);
            },
          );
  }
}