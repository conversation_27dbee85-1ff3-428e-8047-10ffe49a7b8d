import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';

class PagedResponse<T, T1> {
  final bool succeeded;
  final String? message;
  final List<String>? errors;
  final T1? data;
  final List<T> items;
  final double totalCount;

  PagedResponse({
    required this.succeeded,
    this.message,
    this.errors,
    this.data,
    required this.items,
    required this.totalCount,
  });

  factory PagedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic json) fromJsonT,
    T1 Function(dynamic json) fromJsonT1,
  ) {
    return PagedResponse<T, T1>(
      succeeded: json['succeeded'] ?? false,
      message: json['message'],
      errors: json['errors'] != null ? List<String>.from(json['errors']) : null,
      data: json['data'] != null ? fromJsonT1(json['data']) : null,
      items: json['items'] != null ? fromJsonList(json['items'], fromJsonT) : <T>[],
      totalCount: (json['totalCount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson(
    Map<String, dynamic> Function(T) toJsonT,
    Map<String, dynamic> Function(T1) toJsonT1,
  ) {
    return {
      'succeeded': succeeded,
      'message': message,
      'errors': errors,
      'data': data != null ? toJsonT1(data as T1) : null,
      'items': items.map((item) => toJsonT(item)).toList(),
      'totalCount': totalCount,
    };
  }
}
