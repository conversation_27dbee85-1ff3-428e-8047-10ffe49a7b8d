import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../../enums/user_profile/document_type_enum.dart';
import '../../../constants/hive_model_constants.dart';

part 'user_documents.g.dart';

@HiveType(typeId: HiveModelConstants.userDocumentModelTypeId)
@JsonSerializable(explicitToJson: true, includeIfNull: true)
class UserDocumentModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? docName;
  @HiveField(2)
  final String? filePath;
  @HiveField(3)
  final DocumentType? documentType;
  @HiveField(4)
  final String? userId;
  @HiveField(5)
  final DateTime? createdOn;
  @HiveField(6)
  final String? createdBy;
  @HiveField(7)
  final DateTime? lastModifiedOn;
  @HiveField(8)
  final String? lastModifiedBy;

  UserDocumentModel({
    this.id,
    this.docName,
    this.filePath,
    this.documentType,
    this.userId,
    this.createdBy,
    this.createdOn,
    this.lastModifiedOn,
    this.lastModifiedBy,
  });

  factory UserDocumentModel.fromJson(Map<String, dynamic> json) => _$UserDocumentModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserDocumentModelToJson(this);
}
