import 'dart:io';

import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/managers/app_version_manager/app_version_manager.dart';
import 'package:leadrat/core_main/utilities/app_utils.dart';

class AppVersionManagerImpl implements AppVersionManager {
  final AuthTokenRepository _tokenRepository;

  AppVersionManagerImpl(this._tokenRepository);

  @override
  Future<(bool, bool)> isUpdateAvailable(String marketingVersion) async {
    final appVersions = await _tokenRepository.getUpdateAppVersion();
    if (appVersions == null || appVersions.androidForceUpdateVersion == null) return (false, false);
    final isSoftUpdate = AppUtils.compareVersions(
      Platform.isAndroid ? appVersions.androidSoftUpdateVersion! : appVersions.iOSSoftUpdateVersion!,
      marketingVersion,
    );
    final isForceUpdate = AppUtils.compareVersions(
      Platform.isAndroid ? appVersions.androidForceUpdateVersion! : appVersions.iosForceUpdateVersion!,
      marketingVersion,
    );
    return (isForceUpdate > 0, isSoftUpdate > 0);
  }
}
