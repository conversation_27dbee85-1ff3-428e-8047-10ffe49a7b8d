// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_count_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateCountModel _$UpdateCountModelFromJson(Map<String, dynamic> json) =>
    UpdateCountModel(
      id: json['id'] as String?,
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
    );

Map<String, dynamic> _$UpdateCountModelToJson(UpdateCountModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'contactType': _$ContactType<PERSON>numMap[instance.contactType],
    };

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};
