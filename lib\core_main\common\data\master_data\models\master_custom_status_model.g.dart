// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_custom_status_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterCustomStatusModelAdapter
    extends TypeAdapter<MasterCustomStatusModel> {
  @override
  final int typeId = 39;

  @override
  MasterCustomStatusModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterCustomStatusModel(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdOn: fields[2] as DateTime?,
      createdBy: fields[3] as String?,
      lastModifiedOn: fields[4] as DateTime?,
      lastModifiedBy: fields[5] as String?,
      customFields: (fields[6] as List?)?.cast<CustomFieldModel>(),
      isLrbStatus: fields[7] as bool?,
      baseId: fields[8] as String?,
      level: fields[9] as int?,
      status: fields[10] as String?,
      orderRank: fields[11] as int?,
      displayName: fields[12] as String?,
      actionName: fields[13] as String?,
      masterLeadStatusId: fields[14] as String?,
      idDefaultChild: fields[15] as bool?,
      shouldUseForBooking: fields[16] as bool?,
      shouldUseForBookingCancel: fields[17] as bool?,
      shouldBeHidden: fields[18] as bool?,
      shouldOpenAppointmentPage: fields[19] as bool?,
      shouldUseForMeeting: fields[20] as bool?,
      childTypes: (fields[21] as List?)?.cast<MasterCustomStatusModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, MasterCustomStatusModel obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdBy)
      ..writeByte(4)
      ..write(obj.lastModifiedOn)
      ..writeByte(5)
      ..write(obj.lastModifiedBy)
      ..writeByte(6)
      ..write(obj.customFields)
      ..writeByte(7)
      ..write(obj.isLrbStatus)
      ..writeByte(8)
      ..write(obj.baseId)
      ..writeByte(9)
      ..write(obj.level)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.orderRank)
      ..writeByte(12)
      ..write(obj.displayName)
      ..writeByte(13)
      ..write(obj.actionName)
      ..writeByte(14)
      ..write(obj.masterLeadStatusId)
      ..writeByte(15)
      ..write(obj.idDefaultChild)
      ..writeByte(16)
      ..write(obj.shouldUseForBooking)
      ..writeByte(17)
      ..write(obj.shouldUseForBookingCancel)
      ..writeByte(18)
      ..write(obj.shouldBeHidden)
      ..writeByte(19)
      ..write(obj.shouldOpenAppointmentPage)
      ..writeByte(20)
      ..write(obj.shouldUseForMeeting)
      ..writeByte(21)
      ..write(obj.childTypes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterCustomStatusModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomFieldModelAdapter extends TypeAdapter<CustomFieldModel> {
  @override
  final int typeId = 40;

  @override
  CustomFieldModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomFieldModel(
      isRequired: fields[0] as bool?,
      value: fields[1] as String?,
      validators: (fields[2] as List?)?.cast<String>(),
      field: fields[3] as ViewFieldModel?,
    );
  }

  @override
  void write(BinaryWriter writer, CustomFieldModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.isRequired)
      ..writeByte(1)
      ..write(obj.value)
      ..writeByte(2)
      ..write(obj.validators)
      ..writeByte(3)
      ..write(obj.field);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomFieldModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ViewFieldModelAdapter extends TypeAdapter<ViewFieldModel> {
  @override
  final int typeId = 41;

  @override
  ViewFieldModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ViewFieldModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      orderRank: fields[2] as int?,
      module: fields[3] as String?,
      notes: fields[4] as String?,
      lastModifiedOn: fields[5] as DateTime?,
      createdOn: fields[6] as DateTime?,
      createdBy: fields[7] as String?,
      lastModifiedBy: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ViewFieldModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.orderRank)
      ..writeByte(3)
      ..write(obj.module)
      ..writeByte(4)
      ..write(obj.notes)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.createdOn)
      ..writeByte(7)
      ..write(obj.createdBy)
      ..writeByte(8)
      ..write(obj.lastModifiedBy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ViewFieldModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterCustomStatusModel _$MasterCustomStatusModelFromJson(
        Map<String, dynamic> json) =>
    MasterCustomStatusModel(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      customFields: (json['customFields'] as List<dynamic>?)
          ?.map((e) => CustomFieldModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isLrbStatus: json['isLrbStatus'] as bool?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      status: json['status'] as String?,
      orderRank: (json['orderRank'] as num?)?.toInt(),
      displayName: json['displayName'] as String?,
      actionName: json['actionName'] as String?,
      masterLeadStatusId: json['masterLeadStatusId'] as String?,
      idDefaultChild: json['idDefaultChild'] as bool?,
      shouldUseForBooking: json['shouldUseForBooking'] as bool?,
      shouldUseForBookingCancel: json['shouldUseForBookingCancel'] as bool?,
      shouldBeHidden: json['shouldBeHidden'] as bool?,
      shouldOpenAppointmentPage: json['shouldOpenAppointmentPage'] as bool?,
      shouldUseForMeeting: json['shouldUseForMeeting'] as bool?,
      childTypes: (json['childTypes'] as List<dynamic>?)
          ?.map((e) =>
              MasterCustomStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MasterCustomStatusModelToJson(
        MasterCustomStatusModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'customFields': instance.customFields,
      'isLrbStatus': instance.isLrbStatus,
      'baseId': instance.baseId,
      'level': instance.level,
      'status': instance.status,
      'orderRank': instance.orderRank,
      'displayName': instance.displayName,
      'actionName': instance.actionName,
      'masterLeadStatusId': instance.masterLeadStatusId,
      'idDefaultChild': instance.idDefaultChild,
      'shouldUseForBooking': instance.shouldUseForBooking,
      'shouldUseForBookingCancel': instance.shouldUseForBookingCancel,
      'shouldBeHidden': instance.shouldBeHidden,
      'shouldOpenAppointmentPage': instance.shouldOpenAppointmentPage,
      'shouldUseForMeeting': instance.shouldUseForMeeting,
      'childTypes': instance.childTypes,
    };

CustomFieldModel _$CustomFieldModelFromJson(Map<String, dynamic> json) =>
    CustomFieldModel(
      isRequired: json['isRequired'] as bool?,
      value: json['value'] as String?,
      validators: (json['validators'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      field: json['field'] == null
          ? null
          : ViewFieldModel.fromJson(json['field'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomFieldModelToJson(CustomFieldModel instance) =>
    <String, dynamic>{
      'isRequired': instance.isRequired,
      'value': instance.value,
      'validators': instance.validators,
      'field': instance.field,
    };

ViewFieldModel _$ViewFieldModelFromJson(Map<String, dynamic> json) =>
    ViewFieldModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      orderRank: (json['orderRank'] as num?)?.toInt(),
      module: json['module'] as String?,
      notes: json['notes'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$ViewFieldModelToJson(ViewFieldModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'orderRank': instance.orderRank,
      'module': instance.module,
      'notes': instance.notes,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedBy': instance.lastModifiedBy,
    };
