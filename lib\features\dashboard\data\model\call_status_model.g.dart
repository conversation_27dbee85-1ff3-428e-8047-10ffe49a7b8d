// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CallStatusModel _$CallStatusModelFromJson(Map<String, dynamic> json) =>
    CallStatusModel(
      connected: (json['connected'] as num?)?.toInt(),
      notConnected: (json['notConnected'] as num?)?.toInt(),
      disconnected: (json['disconnected'] as num?)?.toInt(),
      missed: (json['missed'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CallStatusModelToJson(CallStatusModel instance) =>
    <String, dynamic>{
      if (instance.connected case final value?) 'connected': value,
      if (instance.notConnected case final value?) 'notConnected': value,
      if (instance.disconnected case final value?) 'disconnected': value,
      if (instance.missed case final value?) 'missed': value,
    };
