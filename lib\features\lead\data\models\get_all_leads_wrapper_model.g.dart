// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_all_leads_wrapper_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetAllLeadsWrapperModel _$GetAllLeadsWrapperModelFromJson(
        Map<String, dynamic> json) =>
    GetAllLeadsWrapperModel(
      totalLeadsCount: (json['totalLeadsCount'] as num?)?.toInt(),
      showAllLeadsCount: (json['showAllLeadsCount'] as num?)?.toInt(),
      leads: (json['leads'] as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, LeadCategoryModel.fromJson(e as Map<String, dynamic>)),
      ),
    );

Map<String, dynamic> _$GetAllLeadsWrapperModelToJson(
        GetAllLeadsWrapperModel instance) =>
    <String, dynamic>{
      'totalLeadsCount': instance.totalLeadsCount,
      'showAllLeadsCount': instance.showAllLeadsCount,
      'leads': instance.leads,
    };

GetAllLeadsWrapperCustomModel _$GetAllLeadsWrapperCustomModelFromJson(
        Map<String, dynamic> json) =>
    GetAllLeadsWrapperCustomModel(
      totalLeadsCount: (json['totalLeadsCount'] as num?)?.toInt(),
      showAllLeadsCount: (json['showAllLeadsCount'] as num?)?.toInt(),
      leads: (json['leads'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k, LeadCategoryCustomModel.fromJson(e as Map<String, dynamic>)),
      ),
    );

Map<String, dynamic> _$GetAllLeadsWrapperCustomModelToJson(
        GetAllLeadsWrapperCustomModel instance) =>
    <String, dynamic>{
      'totalLeadsCount': instance.totalLeadsCount,
      'showAllLeadsCount': instance.showAllLeadsCount,
      'leads': instance.leads,
    };
