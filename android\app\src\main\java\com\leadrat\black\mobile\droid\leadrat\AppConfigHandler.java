package com.leadrat.black.mobile.droid.leadrat;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;

import io.flutter.embedding.engine.FlutterEngine;

public class AppConfigHandler {
    private static final String PREFS_NAME = "AppConfigPrefs";
    private final Activity activity;

    public AppConfigHandler(Activity activity, FlutterEngine flutterEngine) {
        this.activity = activity;
    }

    public boolean changeAppIcon(String iconName) {
        try {
            PackageManager packageManager = activity.getApplicationContext().getPackageManager();

            disableAllActivityAliases(packageManager);

            ComponentName aliasComponentName;
            switch (iconName) {
                case "hjProperties":
                    aliasComponentName = new ComponentName(activity.getApplicationContext(),
                            "com.leadrat.black.mobile.droid.leadrat.HJPropertiesAlias");
                    break;
                case "prowinIcon":
                    aliasComponentName = new ComponentName(activity.getApplicationContext(),
                            "com.leadrat.black.mobile.droid.leadrat.ProwinnAlias");
                    break;
                case "realtorsHubIcon":
                    aliasComponentName = new ComponentName(activity.getApplicationContext(),
                            "com.leadrat.black.mobile.droid.leadrat.RealtorsHubAlias");
                    break;
                case "kanjauIcon":
                    aliasComponentName = new ComponentName(activity.getApplicationContext(),
                            "com.leadrat.black.mobile.droid.leadrat.Kunj4uAlias");
                    break;

                default:
                    aliasComponentName = new ComponentName(activity.getApplicationContext(),
                            "com.leadrat.black.mobile.droid.leadrat.DefaultIconAlias");
                    break;
            }

            packageManager.setComponentEnabledSetting(
                    aliasComponentName,
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    PackageManager.DONT_KILL_APP
            );

            SharedPreferences sharedPreferences = activity.getApplicationContext()
                    .getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString("currentIcon", iconName);
            editor.apply();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private void disableAllActivityAliases(PackageManager packageManager) {
        String[] aliasNames = {
                "com.leadrat.black.mobile.droid.leadrat.HJPropertiesAlias",
                "com.leadrat.black.mobile.droid.leadrat.ProwinnAlias",
                "com.leadrat.black.mobile.droid.leadrat.DefaultIconAlias"
        };

        for (String alias : aliasNames) {
            ComponentName componentName = new ComponentName(activity.getApplicationContext(), alias);
            packageManager.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                    PackageManager.DONT_KILL_APP
            );
        }
    }

    public boolean saveAppName(String appName) {
        try {
            SharedPreferences sharedPreferences = activity.getApplicationContext()
                    .getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString("appName", appName);
            editor.apply();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public void disableMainActivity() {
        PackageManager packageManager = activity.getApplicationContext().getPackageManager();
        ComponentName mainActivity = new ComponentName(activity.getApplicationContext(),
                "com.leadrat.black.mobile.droid.leadrat.MainActivity");
        packageManager.setComponentEnabledSetting(
                mainActivity,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP
        );
    }
}