// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateUserModel _$UpdateUserModelFromJson(Map<String, dynamic> json) =>
    UpdateUserModel(
      userId: json['userId'] as String,
      altPhoneNumber: json['altPhoneNumber'] as String?,
      altEmail: json['altEmail'] as String?,
      imageUrl: json['imageUrl'] as String?,
      address: json['address'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      bloodGroup:
          $enumDecodeNullable(_$BloodGroupTypeEnumMap, json['bloodGroup']),
      gender: $enumDecodeNullable(_$GenderEnumEnumMap, json['gender']),
      permanentAddress: json['permanentAddress'] as String?,
      empNo: json['empNo'] as String?,
      officeName: json['officeName'] as String?,
      officeAddress: json['officeAddress'] as String?,
      reportsTo: json['reportsTo'] as String?,
      departmentId: json['departmentId'] as String?,
      designationId: json['designationId'] as String?,
      description: json['description'] as String?,
      userRoles: (json['userRoles'] as List<dynamic>?)
          ?.map((e) => UserRoleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UpdateUserModelToJson(UpdateUserModel instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'altPhoneNumber': instance.altPhoneNumber,
      'altEmail': instance.altEmail,
      'imageUrl': instance.imageUrl,
      'address': instance.address,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'bloodGroup': _$BloodGroupTypeEnumMap[instance.bloodGroup],
      'gender': _$GenderEnumEnumMap[instance.gender],
      'permanentAddress': instance.permanentAddress,
      'empNo': instance.empNo,
      'officeName': instance.officeName,
      'officeAddress': instance.officeAddress,
      'reportsTo': instance.reportsTo,
      'departmentId': instance.departmentId,
      'designationId': instance.designationId,
      'description': instance.description,
      'userRoles': instance.userRoles?.map((e) => e.toJson()).toList(),
    };

const _$BloodGroupTypeEnumMap = {
  BloodGroupType.none: 0,
  BloodGroupType.aPositive: 1,
  BloodGroupType.aNegative: 2,
  BloodGroupType.bPositive: 3,
  BloodGroupType.bNegative: 4,
  BloodGroupType.oPositive: 5,
  BloodGroupType.oNegative: 6,
  BloodGroupType.abPositive: 7,
  BloodGroupType.abNegative: 8,
};

const _$GenderEnumEnumMap = {
  GenderEnum.notMentioned: 0,
  GenderEnum.male: 1,
  GenderEnum.female: 2,
  GenderEnum.other: 3,
};
