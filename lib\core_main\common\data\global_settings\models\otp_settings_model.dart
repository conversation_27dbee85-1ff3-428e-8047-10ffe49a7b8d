import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/otp_receiver.dart';

part 'otp_settings_model.g.dart';

@HiveType(typeId: HiveModelConstants.otpSettingModelTypeId)
@JsonSerializable()
class OtpSettingsModel {
  @HiveField(0)
  final bool? isEnabled;
  @HiveField(1)
  @JsonKey(unknownEnumValue: OTPReceiver.self)
  final OTPReceiver? receiver;
  @HiveField(2)
  final List<String>? receiverUserIds;
  @HiveField(3)
  final List<ContactType>? channels;
  @HiveField(4)
  final int? retryInSecs;

  OtpSettingsModel({
    this.isEnabled,
    this.receiver,
    this.receiverUserIds,
    this.retryInSecs,
    this.channels,
  });

  factory OtpSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$OtpSettingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtpSettingsModelToJson(this);
}
