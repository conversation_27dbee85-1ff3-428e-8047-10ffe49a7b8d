
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'otp_receiver.g.dart';

@HiveType(typeId: HiveModelConstants.otpReceiverEnumTypeId)
@JsonEnum(valueField: 'index')
enum OTPReceiver {
  @HiveField(0)
  self,
  @HiveField(1)
  admin,
  @HiveField(2)
  manager,
  @HiveField(3)
  specific;
}
