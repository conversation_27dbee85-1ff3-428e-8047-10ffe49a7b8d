{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d77f1af85b8e1944b55072ed4359ba7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d36cce6c2706236f30ab4e223acfa95a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f0e77905fbd466c1a9cda3c3d888935", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897b1f4ec071cc524e9aa7a539b0bb5e0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f0e77905fbd466c1a9cda3c3d888935", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989407719299bba9bcd9bc15bc23180df8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980cff071b3401fad6ae121ada1ddb0e79", "guid": "bfdfe7dc352907fc980b868725387e98cb6dbfbbaa253797812635fbc6f23a48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982985f6c890b42c537975d41329ffd0f8", "guid": "bfdfe7dc352907fc980b868725387e981437220c7926e40ce9fa0d6a9317e525", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98582e9fbcee77fa3765ac38c96a0d506c", "guid": "bfdfe7dc352907fc980b868725387e98841bcd525360e790a93c4d370331435e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd3e6fae5977c96b920a49a69a0074d7", "guid": "bfdfe7dc352907fc980b868725387e985fe82d457190c636e6296332a1103a99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98120304ad9591d51d78c00c47b7726dd7", "guid": "bfdfe7dc352907fc980b868725387e98c661a28750fadb9da29c04cfe8cf1d3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfcd305664fe180dfdfbe36f2fc68921", "guid": "bfdfe7dc352907fc980b868725387e9832723057a94c2af5dd84b8c41f43b76d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c02487087b8373349cb34fb07a733a1", "guid": "bfdfe7dc352907fc980b868725387e98f528da9ccfb8c9a19549c8e1a5791a67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982543c1ca221d0d5786528080e7b1bc9d", "guid": "bfdfe7dc352907fc980b868725387e9822de70437ddd795a423990b53ea7050f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844e86ada68435bab2012b9a2e90bd437", "guid": "bfdfe7dc352907fc980b868725387e98cd764dd9df62b083dc7c17956a648b61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a14b530fee539ecf3531771cff329e54", "guid": "bfdfe7dc352907fc980b868725387e988e7deab3e525519271fa4b82960bb61f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefdc6dfd37ac743bf3c355c19a1ed4b", "guid": "bfdfe7dc352907fc980b868725387e98b87bc5db264f728b5d47e8332232b45d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e50798b539e21015f016e7115773597", "guid": "bfdfe7dc352907fc980b868725387e9886f945e6ba3e5e17ca7c0b98f1d30cd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956c2ea6e2a72c617d69b8809fbeafe5", "guid": "bfdfe7dc352907fc980b868725387e98b0b61a12fb6757c9f317d4e8bff37241", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f275b82804fc96610fd8d2c262b6372", "guid": "bfdfe7dc352907fc980b868725387e9895b467baa86c8709d2b7592fe0cff84f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f55b7c897f114f27718128ce064e6b", "guid": "bfdfe7dc352907fc980b868725387e984880a5ce82b9f656b62ce4eaf20f8fbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943a3f5e6996d9a8d43cf5f88e54811f", "guid": "bfdfe7dc352907fc980b868725387e98fce1476efe80c5ed577dd0f9bfb0670f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98107e79031b09961a78a832b9024a083c", "guid": "bfdfe7dc352907fc980b868725387e989c86cb712fb08ecf31281a806ecf337a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5494b5ad8d3a5b3ede3a216214b0cc7", "guid": "bfdfe7dc352907fc980b868725387e984ca8ce318a454547899c1afc2ce48001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152869727a912e796101025bc8bb4d60", "guid": "bfdfe7dc352907fc980b868725387e9870f44f1e8c3df1bd39bdf88f0f63ac8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed332bddf9c2752746f25d7bec62d61", "guid": "bfdfe7dc352907fc980b868725387e987f510e0d205732bde211bdc450ccd702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f9ce97ce31a32d91d5c316b0d4a355", "guid": "bfdfe7dc352907fc980b868725387e98f407afd8c3f2b6c9d680c310ba0b031f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556cb49cf7820eb19e22ba9bca5b8fb0", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a14371d2369e74bed1103c0be22105c7", "guid": "bfdfe7dc352907fc980b868725387e983d4d9cdf065000f4aea1daba6a16a028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bb493d64c7f644be20334892f0b400", "guid": "bfdfe7dc352907fc980b868725387e9861f73c01ed3fda8b5d48937aa0b40811"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846bf32e83b3537df2729cdd6e993869e", "guid": "bfdfe7dc352907fc980b868725387e984b40fab5a3ecbb204cb60112883225e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69529f7f90707bd2ad3c1f9edc4851f", "guid": "bfdfe7dc352907fc980b868725387e980af78a229233d2ba719892669172f660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399927ea70434b2d743c0d5e4ad929ba", "guid": "bfdfe7dc352907fc980b868725387e98a4731aad651a148c1d950c78ad47ff4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e33f0bb3e6c3ebd1d5ba5b66954273", "guid": "bfdfe7dc352907fc980b868725387e9800f9c57d308b78ac43d744ff0a7adacd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fea7c8b1fd781e2671249635987f6dc", "guid": "bfdfe7dc352907fc980b868725387e98811b4a99d57926e6521867cca912271a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98727d462c54a0cc7cb0d8807cfcf43479", "guid": "bfdfe7dc352907fc980b868725387e98592e1352f001dc5e6574f9ff7ed8a6c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ced39ff26bc3466a856a914536acb84", "guid": "bfdfe7dc352907fc980b868725387e986385cfc89971e8fe85c1a21f24192907"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}