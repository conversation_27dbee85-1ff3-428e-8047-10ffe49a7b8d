import 'package:leadrat/core_main/common/data/auth_tokens/data_source/local/auth_token_local_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/errors/business_exception/rest_token_invalid_exception.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/rest_request_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/remote/rest_client.dart';
import 'package:leadrat/core_main/remote/rest_service_base.dart';

class LeadratRestService extends RestServiceBase {
  LeadratRestService() : super(restClientCode: "core");

  @override
  RestRequest createGetRequest(String resource, {body, Duration? timeout}) {

    final request = super.createGetRequest(resource, body: body, timeout: timeout);
    request.addAuthorizationHeader();
    return request;
  }

  @override
  RestRequest createPostRequest(String resource, {body, Duration? timeout}) {
    final request = super.createPostRequest(resource, body: body, timeout: timeout);
    request.addAuthorizationHeader();
    return request;
  }

  @override
  RestRequest createPutRequest(String resource, {body, Duration? timeout}) {
    final request = super.createPutRequest(resource, body: body, timeout: timeout);
    request.addAuthorizationHeader();
    return request;
  }

  @override
  RestRequest createDeleteRequest(String resource, {body, Duration? timeout}) {
    final request = super.createDeleteRequest(resource, body: body, timeout: timeout);
    request.addAuthorizationHeader();
    return request;
  }

  @override
  Future<T> executeRequestAsync<T>(RestRequest restRequest, T Function(Map<String, dynamic>) fromJson, {int retryCount = 0}) async {
    await _handleRefreshToken();
    restRequest.updateAuthorizationHeader();
    return super.executeRequestAsync(restRequest, fromJson, retryCount: retryCount);
  }

  Future<void> _handleRefreshToken() async {
    final tokenLocalDataSource = getIt<AuthTokenLocalDataSource>().getTokenDetails();
    if (tokenLocalDataSource?.idToken.isNullOrEmpty() ?? false) return;

    final decodedIdToken = decodeJwt(tokenLocalDataSource!.idToken!);
    final tokenExpirationTime = decodedIdToken['exp'] as int?;

    if (tokenExpirationTime == null) {
      'Token expiration time not found in JWT payload'.printInConsole();
      return;
    }

    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (currentTime >= tokenExpirationTime) {
      final newToken = await _refreshToken();
      if (newToken == null) {
        throw RestTokenInvalidException();
      }
    }
  }

  Future<String?> _refreshToken() async {
    final authTokenRepository = getIt<AuthTokenRepository>();
    final result = await authTokenRepository.getRefreshToken();
    return result?.idToken;
  }
}
