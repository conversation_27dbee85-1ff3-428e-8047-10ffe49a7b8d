import 'package:leadrat/core_main/common/data/flags/data_source/remote/flag_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/models/flag_model.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class FlagRemoteDataSourceImpl extends LeadratRestService implements FlagRemoteDataSource{
  @override
  Future<List<ViewFlagModel>?> getFlagsByModule(String module) async {
    final restRequest = createGetRequest(FlagRestResources.getFlagsByModule(module));
    final response = await executeRequestAsync<ResponseWrapper<List<ViewFlagModel>?>>(
      restRequest,
          (json) => ResponseWrapper<List<ViewFlagModel>?>.fromJson(json, (data) => fromJsonList(data, ViewFlagModel.fromJson)),
    );
    return response.data;
  }

}