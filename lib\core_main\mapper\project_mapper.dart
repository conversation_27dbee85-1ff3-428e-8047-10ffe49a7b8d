import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';

class ProjectEntityMapper {
  final MasterDataRepository _masterDataRepository;
  static List<MasterAssociatedBankModel>? masterAssociatedBankModels;

  ProjectEntityMapper(this._masterDataRepository);

  Future<void> getAssociateBankDetails() async {
    if (ProjectEntityMapper.masterAssociatedBankModels == null) {
      final masterAssociatedBankModels = await _masterDataRepository.getAssociatedBank();
      ProjectEntityMapper.masterAssociatedBankModels = masterAssociatedBankModels;
    }
  }

  MasterAssociatedBankModel? getAssociateBankDetailsWithId(String? bankId) {
    if (bankId == null) return null;
    return ProjectEntityMapper.masterAssociatedBankModels?.where((element) => element.id == bankId).firstOrNull;
  }
}
