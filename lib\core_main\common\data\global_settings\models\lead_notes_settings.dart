import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'lead_notes_settings.g.dart';

@HiveType(typeId: HiveModelConstants.leadNotesSettingModelTypeId)
@JsonSerializable()
class LeadNotesSettings {
  @HiveField(0)
  final bool? isNotesMandatoryEnabled;
  @HiveField(1)
  final bool? isNotesMandatoryOnAddLead;
  @HiveField(2)
  final bool? isNotesMandatoryOnSiteVisitDone;
  @HiveField(3)
  final bool? isNotesMandatoryOnMeetingDone;
  @HiveField(4)
  final bool? isNotesMandatoryOnUpdateLead;

  LeadNotesSettings({
    this.isNotesMandatoryEnabled,
    this.isNotesMandatoryOnAddLead,
    this.isNotesMandatoryOnSiteVisitDone,
    this.isNotesMandatoryOnMeetingDone,
    this.isNotesMandatoryOnUpdateLead,
  });

  factory LeadNotesSettings.fromJson(Map<String, dynamic> json) =>
      _$LeadNotesSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$LeadNotesSettingsToJson(this);
}
