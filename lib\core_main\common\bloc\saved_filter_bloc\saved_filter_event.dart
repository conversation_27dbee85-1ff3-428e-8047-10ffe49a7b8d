part of 'saved_filter_bloc.dart';

@immutable
sealed class SavedFilterEvent {}

final class InitSavedFiltersEvent extends SavedFilterEvent {
  final SavedFilterModule savedFilterModule;

  InitSavedFiltersEvent(this.savedFilterModule);
}

final class SearchSavedFilterEvent extends SavedFilterEvent {
  final String searchText;

  SearchSavedFilterEvent(this.searchText);
}

final class CreateSavedFilterEvent extends SavedFilterEvent {
  final String filterName;
  final Map<String, dynamic>? filterCriteria;
  final SavedFilterModule module;

  CreateSavedFilterEvent({
    required this.filterName,
    required this.filterCriteria,
    required this.module,
  });
}

final class DeleteSavedFilterEvent extends SavedFilterEvent {
  final ItemSimpleModel<GetSavedFilterModel> item;

  DeleteSavedFilterEvent(this.item);
}

final class UpdateSavedFilterEvent extends SavedFilterEvent {
  final String filterId;
  final String filterName;
  final Map<String, dynamic>? filterCriteria;
  final SavedFilterModule module;
  final bool checkFilterExits;

  UpdateSavedFilterEvent({
    required this.filterId,
    required this.filterName,
    required this.filterCriteria,
    required this.module,
    this.checkFilterExits = false,
  });
}

final class ApplySavedFilterEvent extends SavedFilterEvent {
  final ItemSimpleModel<GetSavedFilterModel> filter;

  ApplySavedFilterEvent(this.filter);
}

final class ToggleAppliedFilterEvent extends SavedFilterEvent {
  final String? filterName;

  ToggleAppliedFilterEvent({this.filterName});
}
