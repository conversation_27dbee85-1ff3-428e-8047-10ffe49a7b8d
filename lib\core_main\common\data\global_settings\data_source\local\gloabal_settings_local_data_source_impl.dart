import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import '../../../../../services/local_storage_service/local_storage_service.dart';
import 'global_settings_local_data_source.dart';

class GlobalSettingsLocalDataSourceImpl implements GlobalSettingsLocalDataSource {
  final LocalStorageService _localStorageService;

  GlobalSettingsLocalDataSourceImpl(this._localStorageService);

  @override
  Future<void> saveGlobalSettings(GlobalSettingModel globalSettingModel) async {
    try {
      await _localStorageService.clearContainer(HiveModelConstants.globalSettingsBoxName);
      await _localStorageService.addItem<GlobalSettingModel>(HiveModelConstants.globalSettingsBoxName, globalSettingModel);
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
    }
  }

  @override
  GlobalSettingModel? getGlobalSettings() {
    try {
      final items = _localStorageService.getAllItems<GlobalSettingModel>(HiveModelConstants.globalSettingsBoxName);
      return items.isNotEmpty ? items.first : null;
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
      return null;
    }
  }
}
