// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_all_users_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GetAllUsersModelAdapter extends TypeAdapter<GetAllUsersModel> {
  @override
  final int typeId = 101;

  @override
  GetAllUsersModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GetAllUsersModel(
      id: fields[0] as String?,
      firstName: fields[1] as String?,
      lastName: fields[2] as String?,
      userName: fields[3] as String?,
      imageUrl: fields[4] as String?,
      isActive: fields[5] as bool?,
      isMFAEnabled: fields[6] as bool?,
      licenseNo: fields[7] as String?,
      isGeoFenceActive: fields[8] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, GetAllUsersModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.firstName)
      ..writeByte(2)
      ..write(obj.lastName)
      ..writeByte(3)
      ..write(obj.userName)
      ..writeByte(4)
      ..write(obj.imageUrl)
      ..writeByte(5)
      ..write(obj.isActive)
      ..writeByte(6)
      ..write(obj.isMFAEnabled)
      ..writeByte(7)
      ..write(obj.licenseNo)
      ..writeByte(8)
      ..write(obj.isGeoFenceActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetAllUsersModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetAllUsersModel _$GetAllUsersModelFromJson(Map<String, dynamic> json) =>
    GetAllUsersModel(
      id: json['id'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      userName: json['userName'] as String?,
      imageUrl: json['imageUrl'] as String?,
      isActive: json['isActive'] as bool?,
      isMFAEnabled: json['isMFAEnabled'] as bool?,
      licenseNo: json['licenseNo'] as String?,
      isGeoFenceActive: json['isGeoFenceActive'] as bool?,
    );

Map<String, dynamic> _$GetAllUsersModelToJson(GetAllUsersModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'userName': instance.userName,
      'imageUrl': instance.imageUrl,
      'isActive': instance.isActive,
      'isMFAEnabled': instance.isMFAEnabled,
      'licenseNo': instance.licenseNo,
      'isGeoFenceActive': instance.isGeoFenceActive,
    };
