// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_logs_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceLogsListModel _$AttendanceLogsListModelFromJson(
        Map<String, dynamic> json) =>
    AttendanceLogsListModel(
      fromDate: json['fromDate'] == null
          ? null
          : DateTime.parse(json['fromDate'] as String),
      toDate: json['toDate'] == null
          ? null
          : DateTime.parse(json['toDate'] as String),
      logs: (json['logs'] as List<dynamic>?)
          ?.map((e) => AttendanceLogModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AttendanceLogsListModelToJson(
        AttendanceLogsListModel instance) =>
    <String, dynamic>{
      'fromDate': instance.fromDate?.toIso8601String(),
      'toDate': instance.toDate?.toIso8601String(),
      'logs': instance.logs?.map((e) => e.toJson()).toList(),
    };
