import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum ProspectVisibility {
  selfWithReportee("All Data", 0),
  self("My Data", 1),
  reportee("Team Data", 2),
  convertedData("Converted Data", 3),
  deletedData("Deleted", 4),
  unassignData("Unassigned", 5);

  final String description;
  final int value;
  const ProspectVisibility(this.description, this.value);
}