// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whatsapp_template.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WhatsAppTemplatesAdapter extends TypeAdapter<WhatsAppTemplates> {
  @override
  final int typeId = 14;

  @override
  WhatsAppTemplates read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WhatsAppTemplates(
      templates: (fields[0] as List?)?.cast<Template>(),
    );
  }

  @override
  void write(BinaryWriter writer, WhatsAppTemplates obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.templates);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WhatsAppTemplatesAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WhatsAppTemplates _$WhatsAppTemplatesFromJson(Map<String, dynamic> json) =>
    WhatsAppTemplates(
      templates: (json['templates'] as List<dynamic>?)
          ?.map((e) => Template.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WhatsAppTemplatesToJson(WhatsAppTemplates instance) =>
    <String, dynamic>{
      'templates': instance.templates,
    };
