import 'package:flutter/material.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

void showIvrDialogBottomModal(BuildContext context, [bool isCallingModal = false, bool isLead = false]) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -6,
            left: 0,
            right: 0,
            child: CustomPaint(size: Size(context.width(100), 6), painter: TrapeziumPainter()),
          ),
          Container(
            width: context.width(100),
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
            color: ColorPalette.black,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("call lead", style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary)),
                    const SizedBox(width: 30, child: Divider(color: ColorPalette.primary, thickness: 2.0)),
                  ],
                ),
                const SizedBox(height: 30),
                Container(
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: isCallingModal ? ColorPalette.darkToneInk : ColorPalette.transparent,
                    border: isCallingModal
                        ? const Border(
                            left: BorderSide(color: ColorPalette.accentLightColor, width: 6),
                            top: BorderSide.none,
                            right: BorderSide.none,
                            bottom: BorderSide.none,
                          )
                        : Border.all(color: ColorPalette.transparent),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Row(
                    mainAxisAlignment: isCallingModal ? MainAxisAlignment.start : MainAxisAlignment.center,
                    children: [
                      if (isCallingModal)
                        Padding(
                          padding: const EdgeInsets.only(left: 20, right: 15),
                          child: Image.asset(ImageResources.imageCall, height: 50),
                        ),
                      if (!isLead)
                        Text(
                          isCallingModal ? 'Calling data ' : 'Validating agent ',
                          style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.accentLightColor),
                        ),
                      if (isLead)
                        Text(
                          isCallingModal ? 'Calling lead ' : 'Validating agent ',
                          style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.accentLightColor),
                        ),
                      JumpingDots(color: ColorPalette.accentLightColor, radius: 4, numberOfDots: 3, animationDuration: const Duration(milliseconds: 200)),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      );
    },
  );
}
