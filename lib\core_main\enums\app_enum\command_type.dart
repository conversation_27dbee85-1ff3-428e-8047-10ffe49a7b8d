enum CommandType {
  create("Create"),
  update("Update"),
  delete("Delete"),
  export("Export"),
  search("Search"),
  view("View"),
  viewUnassignedLead("ViewUnAssignedLead"),
  generate("Generate"),
  clean("Clean"),
  upgradeSubscription("UpgradeSubscription"),
  assign("Assign"),
  assignToAny("AssignToAny"),
  ivrCall("IVRCall"),
  viewConfidentialNotes("ViewConfidentialNotes"),
  viewLeadSource("ViewLeadSource"),
  updateBookedLead("UpdateBookedLead"),
  viewOwnerInfo("ViewOwnerInfo"),
  updateBasicInfo("UpdateBasicInfo"),
  updateLeadStatus("UpdateLeadStatus"),
  viewAssigned("ViewAssigned"),
  viewBrokerageInfo("ViewBrokerageInfo"),
  viewAllUsers("ViewAllUsers"),
  viewReportees("ViewReportees"),
  updateNotes("UpdateNotes"),
  updateDocuments("UpdateDocuments"),
  updateTags("UpdateTags"),
  communications("Communications"),
  viewOrg("ViewOrg"),
  viewTeam("ViewTeam"),
  updateSource("UpdateSource"),
  publishProperty("PublishProperty"),
  viewAllLeads("ViewAllLeads"),
  createDuplicateLeads("CreateDuplicateLeads"),
  convertToLead("ConvertToLead"),
  bulkShare("BulkShare"),
  bulkReAssign("BulkReassign"),
  bulkDelete("BulkDelete"),
  bulkDeList("BulkDeList"),
  bulkList("BulkList"),
  createDuplicateProspects("CreateDuplicateProspects"),
  viewAllProspects("ViewAllProspects"),
  viewUnassignedData("ViewUnAssignedProspects"),
  viewForFilter("ViewForFilter"),
  cloneProperty("CloneProperty");

  final String description;

  const CommandType(this.description);
}
