import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'app_feature_model.g.dart';

@JsonSerializable()
@HiveType(typeId: HiveModelConstants.appFeatureTypeId)
class AppFeatureModel {
  @JsonKey(name: '_id')
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final DateTime? createdAt;
  @HiveField(3)
  final DateTime? updatedAt;
  @HiveField(4)
  final List<FeatureActionModel>? actions;

  AppFeatureModel({
    this.id,
    this.name,
    this.createdAt,
    this.updatedAt,
    this.actions,
  });

  factory AppFeatureModel.fromJson(Map<String, dynamic> json) => _$AppFeatureModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppFeatureModelToJson(this);
}

@JsonSerializable()
@HiveType(typeId: HiveModelConstants.featureActionTypeId)
class FeatureActionModel {
  @JsonKey(name: '_id')
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;

  FeatureActionModel({
    this.id,
    this.name,
  });

  factory FeatureActionModel.fromJson(Map<String, dynamic> json) => _$FeatureActionModelFromJson(json);

  Map<String, dynamic> toJson() => _$FeatureActionModelToJson(this);
}
