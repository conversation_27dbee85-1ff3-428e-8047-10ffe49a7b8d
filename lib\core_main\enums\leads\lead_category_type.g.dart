// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_category_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadCategoryTypeAdapter extends TypeAdapter<LeadCategoryType> {
  @override
  final int typeId = 44;

  @override
  LeadCategoryType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LeadCategoryType.newLead;
      case 1:
        return LeadCategoryType.pending;
      case 2:
        return LeadCategoryType.scheduledMeeting;
      case 3:
        return LeadCategoryType.siteVisitScheduled;
      case 4:
        return LeadCategoryType.notInterested;
      case 5:
        return LeadCategoryType.callBack;
      case 6:
        return LeadCategoryType.unassignedLeads;
      case 7:
        return LeadCategoryType.booked;
      case 8:
        return LeadCategoryType.dropped;
      case 9:
        return LeadCategoryType.hotLeads;
      case 10:
        return LeadCategoryType.escalated;
      case 11:
        return LeadCategoryType.aboutToConvert;
      case 12:
        return LeadCategoryType.scheduleToday;
      case 13:
        return LeadCategoryType.overdue;
      case 14:
        return LeadCategoryType.warmLeads;
      case 15:
        return LeadCategoryType.coldLeads;
      case 16:
        return LeadCategoryType.highlightedLeads;
      case 17:
        return LeadCategoryType.allLeads;
      case 18:
        return LeadCategoryType.scheduledTomorrow;
      case 19:
        return LeadCategoryType.upcomingSchedules;
      case 20:
        return LeadCategoryType.active;
      case 21:
        return LeadCategoryType.allWithNID;
      case 22:
        return LeadCategoryType.bookingCancel;
      case 23:
        return LeadCategoryType.untouched;
      case 24:
        return LeadCategoryType.expressionOfInterest;
      case 25:
        return LeadCategoryType.siteVisitDone;
      case 26:
        return LeadCategoryType.meetingDone;
      case 27:
        return LeadCategoryType.invoiced;
      default:
        return LeadCategoryType.newLead;
    }
  }

  @override
  void write(BinaryWriter writer, LeadCategoryType obj) {
    switch (obj) {
      case LeadCategoryType.newLead:
        writer.writeByte(0);
        break;
      case LeadCategoryType.pending:
        writer.writeByte(1);
        break;
      case LeadCategoryType.scheduledMeeting:
        writer.writeByte(2);
        break;
      case LeadCategoryType.siteVisitScheduled:
        writer.writeByte(3);
        break;
      case LeadCategoryType.notInterested:
        writer.writeByte(4);
        break;
      case LeadCategoryType.callBack:
        writer.writeByte(5);
        break;
      case LeadCategoryType.unassignedLeads:
        writer.writeByte(6);
        break;
      case LeadCategoryType.booked:
        writer.writeByte(7);
        break;
      case LeadCategoryType.dropped:
        writer.writeByte(8);
        break;
      case LeadCategoryType.hotLeads:
        writer.writeByte(9);
        break;
      case LeadCategoryType.escalated:
        writer.writeByte(10);
        break;
      case LeadCategoryType.aboutToConvert:
        writer.writeByte(11);
        break;
      case LeadCategoryType.scheduleToday:
        writer.writeByte(12);
        break;
      case LeadCategoryType.overdue:
        writer.writeByte(13);
        break;
      case LeadCategoryType.warmLeads:
        writer.writeByte(14);
        break;
      case LeadCategoryType.coldLeads:
        writer.writeByte(15);
        break;
      case LeadCategoryType.highlightedLeads:
        writer.writeByte(16);
        break;
      case LeadCategoryType.allLeads:
        writer.writeByte(17);
        break;
      case LeadCategoryType.scheduledTomorrow:
        writer.writeByte(18);
        break;
      case LeadCategoryType.upcomingSchedules:
        writer.writeByte(19);
        break;
      case LeadCategoryType.active:
        writer.writeByte(20);
        break;
      case LeadCategoryType.allWithNID:
        writer.writeByte(21);
        break;
      case LeadCategoryType.bookingCancel:
        writer.writeByte(22);
        break;
      case LeadCategoryType.untouched:
        writer.writeByte(23);
        break;
      case LeadCategoryType.expressionOfInterest:
        writer.writeByte(24);
        break;
      case LeadCategoryType.siteVisitDone:
        writer.writeByte(25);
        break;
      case LeadCategoryType.meetingDone:
        writer.writeByte(26);
        break;
      case LeadCategoryType.invoiced:
        writer.writeByte(27);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadCategoryTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
