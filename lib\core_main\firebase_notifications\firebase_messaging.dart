import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/utilities/app_utils.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';

import '../../features/lead/presentation/pages/lead_info_page.dart';

class FirebaseMessagingService {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  final String kDEEPLINKURL = StringConstants.deepLinkUrl;

  //initialising firebase message plugin
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  void isTokenRefresh() async {
    messaging.onTokenRefresh.listen((event) {
      event.toString();
      if (kDebugMode) {}
    });
  }

  Future<void> setupFirebase() async {
    try {
      messaging.getToken().then((value) {});
    } catch (ex) {
      ex.logException();
    }
  }

  //function to initialise flutter local notification plugin to show notifications for android when app is active

  void initLocalNotifications(BuildContext context, RemoteMessage message) async {
    try {
      var androidInitializationSettings = const AndroidInitializationSettings('@mipmap/ic_launcher');
      var iosInitializationSettings = const DarwinInitializationSettings();
      if (message.data.containsKey(kDEEPLINKURL)) await AppUtils.makePhoneCall(message.data);
      var initializationSetting = InitializationSettings(android: androidInitializationSettings, iOS: iosInitializationSettings);
      await _flutterLocalNotificationsPlugin.initialize(initializationSetting, onDidReceiveNotificationResponse: (payload) {
        // handle interaction when app is active for android
        handleMessage(context, message);
      }, onDidReceiveBackgroundNotificationResponse: (payload) {
        handleMessage(context, message);
      });
    } catch(ex,stackTrace){
      ex.logException(stackTrace);
    }
  }

  void firebaseInit(BuildContext context) {
    requestNotificationPermission(); // Ensure permissions are requested
    forgroundMessage();
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      RemoteNotification? notification = message.notification;
      // AndroidNotification? android = message.notification?.android;

      if (notification != null) {
        if (Platform.isIOS) {
          forgroundMessage();
        }
        if (Platform.isAndroid) {
          if (context.mounted) {
            initLocalNotifications(context, message);
          }
          showNotification(message); // Show the notification
        }
      }
    });

    setupInteractMessage(context);
    isTokenRefresh();
    setupFirebase();
  }

  void requestNotificationPermission() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: true,
      badge: true,
      carPlay: true,
      criticalAlert: true,
      provisional: true,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      if (kDebugMode) {}
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      if (kDebugMode) {}
    } else {
      if (kDebugMode) {}
    }
  }

  // function to show visible notification when app is active
  Future<void> showNotification(RemoteMessage message) async {
    AndroidNotificationChannel channel = const AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title
      description: 'This channel is used for important notifications.',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification_sound'),
      playSound: true,
    );

    AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
      channel.id,
      channel.name,
      channelDescription: channel.description,
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      sound: const RawResourceAndroidNotificationSound('notification_sound'),
      playSound: true,
    );

    DarwinNotificationDetails iosNotificationDetails = const DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    // Display the notification
    await _flutterLocalNotificationsPlugin.show(
      0, // Notification ID
      message.notification?.title, // Notification title
      message.notification?.body, // Notification body
      notificationDetails, // Notification details
    );
  }

  Future<void> setupInteractMessage(BuildContext context) async {
    // Handle notification when the app is terminated
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null && context.mounted) {
        handleMessage(context, message);
      }
    });

    // Handle notification when the app is in the background
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      if (context.mounted) {
        handleMessage(context, event);
      }
    });
  }

  void handleMessage(BuildContext context, RemoteMessage message) {
    if (message.data.isNotEmpty) {
      if (message.data.containsKey(kDEEPLINKURL)) {
        _handelDeepLinkUrl(data: message.data, context: context);
      } else {}
    }
  }

  Future forgroundMessage() async {
    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  int getPageNumber(int index, int itemsPerPage) {
    int pageNumber = 0;
    pageNumber = (index ~/ itemsPerPage) + 1;

    return pageNumber;
  }

  Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String uniqueId = '';
    try {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        uniqueId = '${androidInfo.model}-${androidInfo.manufacturer}';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        uniqueId = '${iosInfo.model}-${iosInfo.identifierForVendor}';
      }
    } catch (e,stackTrace) {
      e.logException(stackTrace);
    }

    return uniqueId;
  }

  Future<String> getDeviceModel(BuildContext context) async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.model;
    } else {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
    }
  }

  Future<void> _handelDeepLinkUrl({required BuildContext context, required Map<String, dynamic> data}) async {
    try {
      String deeplinkUrl = data[kDEEPLINKURL];
      if (deeplinkUrl.contains("screen") && deeplinkUrl.contains("id")) {
        _makeApiCall(context, data);
      } else if (deeplinkUrl.contains("phone")) {
        await AppUtils.makePhoneCall(data);
      }
    } catch (ex) {
      ex.logException();
    }
  }

  Future<void> _makeApiCall(BuildContext context, Map<String, dynamic> data) async {
    try {
      String deeplinkUrl = data[kDEEPLINKURL];
      Uri uri = Uri.parse(deeplinkUrl);
      if (deeplinkUrl.contains("screen") && deeplinkUrl.contains("id")) {
        String? screen = uri.queryParameters['screen'];
        String? leadId = uri.queryParameters['id'];
        if (screen != null && leadId != null) {
          var leadRepository = getIt<LeadsRepository>();
          var checkLeadAssignedByLeadId = await leadRepository.checkLeadAssignedByLeadId(leadId);
          await checkLeadAssignedByLeadId.fold(
            (failure) => null,
            (success) async {
              if (success ?? false) {
                var lead = await leadRepository.getLeadDetails(leadId);
                lead.fold(
                  (failure) => null,
                  (result) => {Navigator.push(context, MaterialPageRoute(builder: (context) => LeadInfoPage(result ?? GetLeadEntity(), null), settings: const RouteSettings(name: 'LeadInfoPage')))},
                );
              }
              else{
                LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'This lead is not assigned to you');
              }
            },
          );
        }
      }
    } catch (e) {
      e.logException();
    }
  }
}
