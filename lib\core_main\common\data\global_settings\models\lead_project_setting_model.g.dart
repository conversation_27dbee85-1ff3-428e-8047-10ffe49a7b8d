// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_project_setting_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadProjectSettingModelAdapter
    extends TypeAdapter<LeadProjectSettingModel> {
  @override
  final int typeId = 11;

  @override
  LeadProjectSettingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LeadProjectSettingModel(
      isProjectMandatoryEnabled: fields[0] as bool?,
      isProjectMandatoryOnSiteVisitDone: fields[1] as bool?,
      isProjectMandatoryOnMeetingDone: fields[2] as bool?,
      isProjectMandatoryOnBooking: fields[3] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, LeadProjectSettingModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.isProjectMandatoryEnabled)
      ..writeByte(1)
      ..write(obj.isProjectMandatoryOnSiteVisitDone)
      ..writeByte(2)
      ..write(obj.isProjectMandatoryOnMeetingDone)
      ..writeByte(3)
      ..write(obj.isProjectMandatoryOnBooking);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadProjectSettingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadProjectSettingModel _$LeadProjectSettingModelFromJson(
        Map<String, dynamic> json) =>
    LeadProjectSettingModel(
      isProjectMandatoryEnabled: json['isProjectMandatoryEnabled'] as bool?,
      isProjectMandatoryOnSiteVisitDone:
          json['isProjectMandatoryOnSiteVisitDone'] as bool?,
      isProjectMandatoryOnMeetingDone:
          json['isProjectMandatoryOnMeetingDone'] as bool?,
      isProjectMandatoryOnBooking: json['isProjectMandatoryOnBooking'] as bool?,
    );

Map<String, dynamic> _$LeadProjectSettingModelToJson(
        LeadProjectSettingModel instance) =>
    <String, dynamic>{
      'isProjectMandatoryEnabled': instance.isProjectMandatoryEnabled,
      'isProjectMandatoryOnSiteVisitDone':
          instance.isProjectMandatoryOnSiteVisitDone,
      'isProjectMandatoryOnMeetingDone':
          instance.isProjectMandatoryOnMeetingDone,
      'isProjectMandatoryOnBooking': instance.isProjectMandatoryOnBooking,
    };
