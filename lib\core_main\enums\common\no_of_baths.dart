import 'package:leadrat/core_main/extensions/object_extension.dart';

enum NoOfBaths {
  oneBr("1", 1),
  twoBr("2", 2),
  threeBr("3", 3),
  fourBr("4", 4),
  fiveBr("5", 5),
  sixBr("6", 6),
  sevenBr("7", 7),
  eightBr("8", 8),
  nineBr("9", 9),
  tenBr("10", 10);

  final String description;
  final int noOfBaths;

  const NoOfBaths(this.description, this.noOfBaths);
}

T? getEnumFromNoOfBr<T extends Enum>(List<T> values, int? noOfBaths) {
  try {
    if (noOfBaths == null) return null;
    return values.firstWhere((e) => (e as dynamic).noOfBaths == noOfBaths);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}
