import 'package:json_annotation/json_annotation.dart';

part 'app_anaylsis_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class AppAnalysisModel {
  final String? tenantId;
  final String? userId;
  final String? refId;
  final String? featureId;
  final String? actionId;
  final String? source;
  final String? deviceId;
  final String? deviceType;
  final String? osVer;
  final String? appVer;
  final String? iP;


  AppAnalysisModel({this.tenantId, this.userId, this.refId, required this.featureId, required this.actionId, this.source, this.deviceId, this.deviceType, this.osVer,
    this.appVer, this.iP,});

  factory AppAnalysisModel.fromJson(Map<String, dynamic> json) => _$AppAnalysisModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppAnalysisModelToJson(this);

  AppAnalysisModel copyWith({
    String? tenantId,
    String? userId,
    String? refId,
    String? featureId,
    String? actionId,
    String? source,
    String? deviceId,
    String? deviceType,
    String? osVer,
    String? appVer,
    String? iP,
  }) {
    return AppAnalysisModel(
      tenantId: tenantId ?? this.tenantId,
      userId: userId ?? this.userId,
      refId: refId ?? this.refId,
      featureId: featureId ?? this.featureId,
      actionId: actionId ?? this.actionId,
      source: source ?? this.source,
      deviceId: deviceId ?? this.deviceId,
      deviceType: deviceType ?? this.deviceType,
      osVer: osVer ?? this.osVer,
      appVer: appVer ?? this.appVer,
      iP: iP ?? this.iP,
    );
  }
}
