// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country_info_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CountryInfoModelAdapter extends TypeAdapter<CountryInfoModel> {
  @override
  final int typeId = 20;

  @override
  CountryInfoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CountryInfoModel(
      name: fields[0] as String?,
      callingCode: (fields[1] as List?)?.cast<String>(),
      defaultCallingCode: fields[2] as String?,
      defaultCurrency: fields[3] as String?,
      defaultSymbol: fields[4] as String?,
      codes: (fields[5] as List?)?.cast<String>(),
      code: fields[6] as String?,
      timeZoneId: fields[7] as String?,
      currencies: (fields[8] as List?)?.cast<CurrenciesModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, CountryInfoModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.callingCode)
      ..writeByte(2)
      ..write(obj.defaultCallingCode)
      ..writeByte(3)
      ..write(obj.defaultCurrency)
      ..writeByte(4)
      ..write(obj.defaultSymbol)
      ..writeByte(5)
      ..write(obj.codes)
      ..writeByte(6)
      ..write(obj.code)
      ..writeByte(7)
      ..write(obj.timeZoneId)
      ..writeByte(8)
      ..write(obj.currencies);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CountryInfoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CountryInfoModel _$CountryInfoModelFromJson(Map<String, dynamic> json) =>
    CountryInfoModel(
      name: json['name'] as String?,
      callingCode: (json['callingCode'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      defaultCallingCode: json['defaultCallingCode'] as String?,
      defaultCurrency: json['defaultCurrency'] as String?,
      defaultSymbol: json['defaultSymbol'] as String?,
      codes:
          (json['codes'] as List<dynamic>?)?.map((e) => e as String).toList(),
      code: json['code'] as String?,
      timeZoneId: json['timeZoneId'] as String?,
      currencies: (json['currencies'] as List<dynamic>?)
          ?.map((e) => CurrenciesModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CountryInfoModelToJson(CountryInfoModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'callingCode': instance.callingCode,
      'defaultCallingCode': instance.defaultCallingCode,
      'defaultCurrency': instance.defaultCurrency,
      'defaultSymbol': instance.defaultSymbol,
      'codes': instance.codes,
      'code': instance.code,
      'timeZoneId': instance.timeZoneId,
      'currencies': instance.currencies,
    };

BaseCountryInfoModel _$BaseCountryInfoModelFromJson(
        Map<String, dynamic> json) =>
    BaseCountryInfoModel(
      currencies: (json['currencies'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      symbols:
          (json['symbols'] as List<dynamic>?)?.map((e) => e as String).toList(),
      countries: (json['countries'] as List<dynamic>?)
          ?.map((e) =>
              CountryInfoFormattedModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BaseCountryInfoModelToJson(
        BaseCountryInfoModel instance) =>
    <String, dynamic>{
      'currencies': instance.currencies,
      'symbols': instance.symbols,
      'countries': instance.countries,
    };

CountryInfoFormattedModel _$CountryInfoFormattedModelFromJson(
        Map<String, dynamic> json) =>
    CountryInfoFormattedModel(
      name: json['name'] as String?,
      currency: json['currency'] as String?,
      symbol: json['symbol'] as String?,
      code: json['code'] as String?,
      callingCode: json['callingCode'] as String?,
    );

Map<String, dynamic> _$CountryInfoFormattedModelToJson(
        CountryInfoFormattedModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'currency': instance.currency,
      'symbol': instance.symbol,
      'code': instance.code,
      'callingCode': instance.callingCode,
    };
