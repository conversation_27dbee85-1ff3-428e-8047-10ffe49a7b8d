// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'currencies_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CurrenciesModelAdapter extends TypeAdapter<CurrenciesModel> {
  @override
  final int typeId = 19;

  @override
  CurrenciesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CurrenciesModel(
      currency: fields[0] as String?,
      symbol: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CurrenciesModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.currency)
      ..writeByte(1)
      ..write(obj.symbol);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CurrenciesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CurrenciesModel _$CurrenciesModelFromJson(Map<String, dynamic> json) =>
    CurrenciesModel(
      currency: json['currency'] as String?,
      symbol: json['symbol'] as String?,
    );

Map<String, dynamic> _$CurrenciesModelToJson(CurrenciesModel instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'symbol': instance.symbol,
    };
