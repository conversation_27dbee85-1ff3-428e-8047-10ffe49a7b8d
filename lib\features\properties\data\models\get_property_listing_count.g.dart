// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_property_listing_count.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetPropertyListingCountModel _$GetPropertyListingCountModelFromJson(
        Map<String, dynamic> json) =>
    GetPropertyListingCountModel(
      allCount: (json['allCount'] as num?)?.toInt(),
      draftCount: (json['draftCount'] as num?)?.toInt(),
      approvedCount: (json['approvedCount'] as num?)?.toInt(),
      refusedCount: (json['refusedCount'] as num?)?.toInt(),
      archivedCount: (json['archivedCount'] as num?)?.toInt(),
      soldCount: (json['soldCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GetPropertyListingCountModelToJson(
        GetPropertyListingCountModel instance) =>
    <String, dynamic>{
      'allCount': instance.allCount,
      'draftCount': instance.draftCount,
      'approvedCount': instance.approvedCount,
      'refusedCount': instance.refusedCount,
      'archivedCount': instance.archivedCount,
      'soldCount': instance.soldCount,
    };
