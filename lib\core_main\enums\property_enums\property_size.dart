import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum PropertySize {
  belowThousand(999, "Below 1000 Sqft"),
  thousandPlus(1000, "1000+ Sqft"),
  twoThousandPlus(2000, "2000+ Sqft"),
  threeThousandPlus(3000, "3000+ Sqft"),
  fourThousandPlus(4000, "4000+ Sqft"),
  fiveThousandPlus(5000, "5000+ Sqft"),
  sixThousandPlus(6000, "6000+ Sqft"),
  sevenThousandPlus(7000, "7000+ Sqft"),
  eightThousandPlus(8000, "8000+ Sqft"),
  nineThousandPlus(9000, "9000+ Sqft"),
  tenThousandPlus(10000, "10000+ Sqft");

  final int value;
  final String description;

  const PropertySize(this.value, this.description);
}
