import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'base_property_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.basePropertyTypeTypeId)
@JsonEnum(valueField: 'index')
enum BasePropertyType {
  @HiveField(0)
  residential("Residential", ''),
  @HiveField(1)
  commercial('Commercial', ''),
  @HiveField(2)
  agricultural('Agricultural', '');

  final String description;
  final String id;

  const BasePropertyType(this.description, this.id);
}
