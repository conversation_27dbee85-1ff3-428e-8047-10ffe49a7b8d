<svg width="96" height="110" viewBox="0 0 96 110" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_18274_65368)">
<rect x="30" y="30" width="50" height="50" rx="25" fill="url(#paint0_linear_18274_65368)" shape-rendering="crispEdges"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.1109 54.9998C65.1109 60.584 60.584 65.1109 54.9998 65.1109C49.4156 65.1109 44.8887 60.584 44.8887 54.9998C44.8887 49.4156 49.4156 44.8887 54.9998 44.8887C60.584 44.8887 65.1109 49.4156 65.1109 54.9998ZM54.9998 50.197C55.4186 50.197 55.7581 50.5365 55.7581 50.9553V54.2414H59.0442C59.463 54.2414 59.8026 54.581 59.8026 54.9998C59.8026 55.4186 59.463 55.7581 59.0442 55.7581H55.7581V59.0442C55.7581 59.463 55.4186 59.8026 54.9998 59.8026C54.581 59.8026 54.2415 59.463 54.2415 59.0442V55.7581H50.9553C50.5365 55.7581 50.197 55.4186 50.197 54.9998C50.197 54.581 50.5365 54.2414 50.9553 54.2414H54.2415V50.9553C54.2415 50.5365 54.581 50.197 54.9998 50.197Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_18274_65368" x="0" y="0" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.313726 0 0 0 0 0.745098 0 0 0 0 0.654902 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18274_65368"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18274_65368" result="shape"/>
</filter>
<linearGradient id="paint0_linear_18274_65368" x1="30" y1="30" x2="89.3599" y2="63.3306" gradientUnits="userSpaceOnUse">
<stop stop-color="#50BFA8"/>
<stop offset="0.760417" stop-color="#348D7A"/>
</linearGradient>
</defs>
</svg>
