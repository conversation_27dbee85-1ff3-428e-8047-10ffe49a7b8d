// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_attributes_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomAttributesModelAdapter extends TypeAdapter<CustomAttributesModel> {
  @override
  final int typeId = 47;

  @override
  CustomAttributesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomAttributesModel(
      attributeName: fields[0] as String?,
      attributeDisplayName: fields[1] as String?,
      attributeType: fields[2] as String?,
      fieldType: fields[3] as int?,
      defaultValue: fields[4] as String?,
      basePropertyType: (fields[5] as List?)?.cast<BasePropertyType>(),
      orderRank: fields[6] as int?,
      mobileIcon: fields[7] as String?,
      activeImageURL: fields[8] as String?,
      inActiveImageURL: fields[9] as String?,
      isActive: fields[10] as bool?,
      masterAttributeId: fields[11] as String?,
      createdBy: fields[12] as String?,
      createdOn: fields[13] as DateTime?,
      lastModifiedBy: fields[14] as String?,
      lastModifiedOn: fields[15] as DateTime?,
      deletedOn: fields[16] as DateTime?,
      deletedBy: fields[17] as String?,
      id: fields[18] as String?,
      isDeleted: fields[19] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, CustomAttributesModel obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.attributeName)
      ..writeByte(1)
      ..write(obj.attributeDisplayName)
      ..writeByte(2)
      ..write(obj.attributeType)
      ..writeByte(3)
      ..write(obj.fieldType)
      ..writeByte(4)
      ..write(obj.defaultValue)
      ..writeByte(5)
      ..write(obj.basePropertyType)
      ..writeByte(6)
      ..write(obj.orderRank)
      ..writeByte(7)
      ..write(obj.mobileIcon)
      ..writeByte(8)
      ..write(obj.activeImageURL)
      ..writeByte(9)
      ..write(obj.inActiveImageURL)
      ..writeByte(10)
      ..write(obj.isActive)
      ..writeByte(11)
      ..write(obj.masterAttributeId)
      ..writeByte(12)
      ..write(obj.createdBy)
      ..writeByte(13)
      ..write(obj.createdOn)
      ..writeByte(14)
      ..write(obj.lastModifiedBy)
      ..writeByte(15)
      ..write(obj.lastModifiedOn)
      ..writeByte(16)
      ..write(obj.deletedOn)
      ..writeByte(17)
      ..write(obj.deletedBy)
      ..writeByte(18)
      ..write(obj.id)
      ..writeByte(19)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomAttributesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomAttributesModel _$CustomAttributesModelFromJson(
        Map<String, dynamic> json) =>
    CustomAttributesModel(
      attributeName: json['attributeName'] as String?,
      attributeDisplayName: json['attributeDisplayName'] as String?,
      attributeType: json['attributeType'] as String?,
      fieldType: (json['fieldType'] as num?)?.toInt(),
      defaultValue: json['defaultValue'] as String?,
      basePropertyType: (json['basePropertyType'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BasePropertyTypeEnumMap, e))
          .toList(),
      orderRank: (json['orderRank'] as num?)?.toInt(),
      mobileIcon: json['mobileIcon'] as String?,
      activeImageURL: json['activeImageURL'] as String?,
      inActiveImageURL: json['inActiveImageURL'] as String?,
      isActive: json['isActive'] as bool?,
      masterAttributeId: json['masterAttributeId'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
    );

Map<String, dynamic> _$CustomAttributesModelToJson(
        CustomAttributesModel instance) =>
    <String, dynamic>{
      'attributeName': instance.attributeName,
      'attributeDisplayName': instance.attributeDisplayName,
      'attributeType': instance.attributeType,
      'fieldType': instance.fieldType,
      'defaultValue': instance.defaultValue,
      'basePropertyType': instance.basePropertyType
          ?.map((e) => _$BasePropertyTypeEnumMap[e]!)
          .toList(),
      'orderRank': instance.orderRank,
      'mobileIcon': instance.mobileIcon,
      'activeImageURL': instance.activeImageURL,
      'inActiveImageURL': instance.inActiveImageURL,
      'isActive': instance.isActive,
      'masterAttributeId': instance.masterAttributeId,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'id': instance.id,
      'isDeleted': instance.isDeleted,
    };

const _$BasePropertyTypeEnumMap = {
  BasePropertyType.residential: 0,
  BasePropertyType.commercial: 1,
  BasePropertyType.agricultural: 2,
};
