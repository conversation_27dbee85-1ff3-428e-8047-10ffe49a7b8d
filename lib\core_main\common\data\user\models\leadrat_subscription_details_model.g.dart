// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leadrat_subscription_details_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadratSubscriptionDetailsModelAdapter
    extends TypeAdapter<LeadratSubscriptionDetailsModel> {
  @override
  final int typeId = 56;

  @override
  LeadratSubscriptionDetailsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LeadratSubscriptionDetailsModel(
      validDays: fields[0] as int?,
      isAdmin: fields[1] as bool?,
      licenseValidity: fields[2] as DateTime?,
      totalSoldLicenses: fields[3] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, LeadratSubscriptionDetailsModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.validDays)
      ..writeByte(1)
      ..write(obj.isAdmin)
      ..writeByte(2)
      ..write(obj.licenseValidity)
      ..writeByte(3)
      ..write(obj.totalSoldLicenses);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadratSubscriptionDetailsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadratSubscriptionDetailsModel _$LeadratSubscriptionDetailsModelFromJson(
        Map<String, dynamic> json) =>
    LeadratSubscriptionDetailsModel(
      validDays: (json['validDays'] as num?)?.toInt(),
      isAdmin: json['isAdmin'] as bool?,
      licenseValidity: json['licenseValidity'] == null
          ? null
          : DateTime.parse(json['licenseValidity'] as String),
      totalSoldLicenses: (json['totalSoldLicenses'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LeadratSubscriptionDetailsModelToJson(
        LeadratSubscriptionDetailsModel instance) =>
    <String, dynamic>{
      if (instance.validDays case final value?) 'validDays': value,
      if (instance.isAdmin case final value?) 'isAdmin': value,
      if (instance.licenseValidity?.toIso8601String() case final value?)
        'licenseValidity': value,
      if (instance.totalSoldLicenses case final value?)
        'totalSoldLicenses': value,
    };
