import 'package:leadrat/core_main/extensions/object_extension.dart';

enum NoOfBHK {
  oneRK("1 RK", 0.5),
  oneBHK("1 BHK", 1),
  onePointFive("1.5 BHK", 1.5),
  twoBHK("2 BHK", 2),
  twoPointFive("2.5 BHK", 2.5),
  threeBHK("3 BHK", 3),
  threePointFive("3.5 BHK", 3.5),
  fourBHK("4 BHK", 4),
  fourPointFive("4.5 BHK", 4.5),
  fiveBHK("5 BHK", 5),
  fivePointFive("5.5 BHK", 5.5),
  sixBHK("6 BHK", 6),
  sixPointFive("6.5 BHK", 6.5),
  sevenBHK("7 BHK", 7),
  sevenPointFive("7.5 BHK", 7.5),
  eightBHK("8 BHK", 8),
  eightPointFive("8.5 BHK", 8.5),
  nineBHK("9 BHK", 9),
  ninePointFive("9.5 BHK", 9.5),
  tenBHK("10 BHK", 10);

  final String description;
  final double noOfBhk;

  const NoOfBHK(this.description, this.noOfBhk);
}

T? getEnumFromNoOfBhk<T extends Enum>(List<T> values, double? noOfBhk) {
  try {
    if (noOfBhk == null) return null;
    return values.firstWhere((e) => (e as dynamic).noOfBhk == noOfBhk);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}
