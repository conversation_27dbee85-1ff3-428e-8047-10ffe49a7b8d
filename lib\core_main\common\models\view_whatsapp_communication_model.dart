import 'package:json_annotation/json_annotation.dart';

part 'view_whatsapp_communication_model.g.dart';

@JsonSerializable()
class ViewWhatsAppCommunicationModel {
  final String? templateName;
  final DateTime? createdOn;
  final String? lastModifiedBy;
  final String? lastModifiedByUser;
  final DateTime? lastModifiedOn;

  ViewWhatsAppCommunicationModel({
    this.templateName,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedByUser,
    this.lastModifiedOn,
  });

  factory ViewWhatsAppCommunicationModel.fromJson(Map<String, dynamic> json) => _$ViewWhatsAppCommunicationModelFromJson(json);

  Map<String, dynamic> toJson() => _$ViewWhatsAppCommunicationModelToJson(this);
}
