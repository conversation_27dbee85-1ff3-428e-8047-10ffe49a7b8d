// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gender_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GenderEnumAdapter extends TypeAdapter<GenderEnum> {
  @override
  final int typeId = 103;

  @override
  GenderEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return GenderEnum.notMentioned;
      case 1:
        return GenderEnum.male;
      case 2:
        return GenderEnum.female;
      case 3:
        return GenderEnum.other;
      default:
        return GenderEnum.notMentioned;
    }
  }

  @override
  void write(BinaryWriter writer, GenderEnum obj) {
    switch (obj) {
      case GenderEnum.notMentioned:
        writer.writeByte(0);
        break;
      case GenderEnum.male:
        writer.writeByte(1);
        break;
      case GenderEnum.female:
        writer.writeByte(2);
        break;
      case GenderEnum.other:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GenderEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
