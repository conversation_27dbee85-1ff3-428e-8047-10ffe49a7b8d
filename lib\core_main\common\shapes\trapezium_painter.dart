import 'package:flutter/material.dart';

class TrapeziumPainter extends CustomPainter {
  final Color color;

  TrapeziumPainter([this.color = const Color(0xFF343739)]);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color // Trapezium color
      ..style = PaintingStyle.fill;

    Path path = Path();
    path.moveTo(0, size.height); // Bottom left corner
    path.lineTo(size.width * 0.03, 0); // Top left corner
    path.lineTo(size.width * 0.97, 0); // Top right corner
    path.lineTo(size.width, size.height); // Bottom right corner
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
