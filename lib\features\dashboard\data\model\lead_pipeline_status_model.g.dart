// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_pipeline_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadPipeLineStatusModel _$LeadPipeLineStatusModelFromJson(
        Map<String, dynamic> json) =>
    LeadPipeLineStatusModel(
      total: (json['total'] as num?)?.toInt(),
      newStatus: (json['new'] as num?)?.toInt(),
      inEngagement: (json['inEngagement'] as num?)?.toInt(),
      siteVisitScheduled: (json['siteVisitScheduled'] as num?)?.toInt(),
      siteVisitDone: (json['siteVisitDone'] as num?)?.toInt(),
      booked: (json['booked'] as num?)?.toInt(),
      bookingCancel: (json['bookingCancel'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LeadPipeLineStatusModelToJson(
        LeadPipeLineStatusModel instance) =>
    <String, dynamic>{
      if (instance.total case final value?) 'total': value,
      if (instance.newStatus case final value?) 'new': value,
      if (instance.inEngagement case final value?) 'inEngagement': value,
      if (instance.siteVisitScheduled case final value?)
        'siteVisitScheduled': value,
      if (instance.siteVisitDone case final value?) 'siteVisitDone': value,
      if (instance.booked case final value?) 'booked': value,
      if (instance.bookingCancel case final value?) 'bookingCancel': value,
    };
