import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum TaskPriority {
  low(0, "Low"),
  medium(1, "Medium"),
  critical(2, "Critical"),
  high(3, "High");

  final int value;
  final String description;

  static TaskPriority getEnum(int value) {
    return TaskPriority.values.where((element) => element.value == value).firstOrNull ?? TaskPriority.medium;
  }

  const TaskPriority(this.value, this.description);
}
