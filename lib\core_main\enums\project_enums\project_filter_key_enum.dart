enum ProjectFilterKey {
  projectType("Project Type"),
  status("Status"),
  availability("Availability"),
  builderName("Builder Name"),
  projectSubType("Project SubType"),
  facing("Facing"),
  startDate("Start Date"),
  endDate("End Date"),
  possession("Possession"),
  locations("Locations"),
  amenities("Amenities"),
  prospectCount("Prospect Count"),
  leadCount("Lead Count"),
  minBudget("Min Price"),
  maxBudget("Max Price"),
  landArea("Land Area");

  final String description;

  const ProjectFilterKey(this.description);
}
