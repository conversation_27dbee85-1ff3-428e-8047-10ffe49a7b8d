// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_associated_bank_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterAssociatedBankModelAdapter
    extends TypeAdapter<MasterAssociatedBankModel> {
  @override
  final int typeId = 33;

  @override
  MasterAssociatedBankModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterAssociatedBankModel(
      id: fields[0] as String?,
      createdOn: fields[1] as DateTime?,
      createdBy: fields[2] as String?,
      lastModifiedOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      deletedOn: fields[5] as DateTime?,
      deletedBy: fields[6] as String?,
      name: fields[7] as String?,
      imageUrl: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterAssociatedBankModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdOn)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.lastModifiedOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.deletedOn)
      ..writeByte(6)
      ..write(obj.deletedBy)
      ..writeByte(7)
      ..write(obj.name)
      ..writeByte(8)
      ..write(obj.imageUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterAssociatedBankModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterAssociatedBankModel _$MasterAssociatedBankModelFromJson(
        Map<String, dynamic> json) =>
    MasterAssociatedBankModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$MasterAssociatedBankModelToJson(
        MasterAssociatedBankModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
    };
