import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'user_time_zone_info_model.g.dart';

@HiveType(typeId: HiveModelConstants.userTimeZoneInfoTypeId)
@JsonSerializable(includeIfNull: false)
class UserTimeZoneInfoModel {
  final String? timeZoneId;
  final String? timeZoneDisplay;
  final String? timeZoneName;
  final String? baseUTcOffset;

  UserTimeZoneInfoModel({
    this.timeZoneId,
    this.timeZoneDisplay,
    this.timeZoneName,
    this.baseUTcOffset,
  });

  factory UserTimeZoneInfoModel.fromJson(Map<String, dynamic> json) => _$UserTimeZoneInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserTimeZoneInfoModelToJson(this);
}
