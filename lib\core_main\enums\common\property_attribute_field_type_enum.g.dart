// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_attribute_field_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PropertyAttributeFieldTypeAdapter
    extends TypeAdapter<PropertyAttributeFieldType> {
  @override
  final int typeId = 29;

  @override
  PropertyAttributeFieldType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyAttributeFieldType.bool;
      case 1:
        return PropertyAttributeFieldType.eNum;
      case 2:
        return PropertyAttributeFieldType.int;
      default:
        return PropertyAttributeFieldType.bool;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyAttributeFieldType obj) {
    switch (obj) {
      case PropertyAttributeFieldType.bool:
        writer.writeByte(0);
        break;
      case PropertyAttributeFieldType.eNum:
        writer.writeByte(1);
        break;
      case PropertyAttributeFieldType.int:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyAttributeFieldTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
