import 'package:leadrat/core_main/common/data/user/data_source/remote/user_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';
import 'package:leadrat/core_main/common/data/user/models/leadrat_subscription_details_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_clockin_model.dart';
import 'package:leadrat/core_main/common/data/user/models/search_response_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/shared_preference_manager.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/features/lead/data/models/lead_search_property_model.dart';

class UserRemoteDataSourceImpl extends LeadratRestService implements UserRemoteDataSource {
  @override
  Future<List<GetAllUsersModel?>?> getAllUsers({Duration duration = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(UsersRestResources.getAllUsers, timeout: duration);
    final response = await executeRequestAsync<PagedResponse<GetAllUsersModel?, String>>(
      restRequest,
      (json) => PagedResponse<GetAllUsersModel?, String>.fromJson(json, (data) => fromJsonObject(data, GetAllUsersModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<UserDetailsModel?> getUser({Duration duration = const Duration(seconds: 60)}) async {
    String userId = await getIt<SharedPreferenceManager>().getString("user_id") ?? "";
    final restRequest = createGetRequest(UsersRestResources.getUserById(userId), timeout: duration);

    final response = await executeRequestAsync<ResponseWrapper<UserDetailsModel?>>(
      restRequest,
      (json) => ResponseWrapper<UserDetailsModel?>.fromJson(json, (data) => fromJsonObject(data, UserDetailsModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<GetAllUsersModel?>?> getAdminsAndReportee({Duration duration = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(UsersRestResources.getAdminsAndReportees, timeout: duration);
    final response = await executeRequestAsync<PagedResponse<GetAllUsersModel?, String>>(
      restRequest,
      (json) => PagedResponse<GetAllUsersModel?, String>.fromJson(json, (data) => fromJsonObject(data, GetAllUsersModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<List<GetAllUsersModel?>?> getAllReportees({Duration duration = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(UsersRestResources.getAllReportees, timeout: duration);
    final response = await executeRequestAsync<PagedResponse<GetAllUsersModel?, String>>(
      restRequest,
      (json) => PagedResponse<GetAllUsersModel?, String>.fromJson(json, (data) => fromJsonObject(data, GetAllUsersModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<List<DTOWithNameModel?>?> getAllDesignations({Duration duration = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(UsersRestResources.getAllDesignations, timeout: duration);
    final response = await executeRequestAsync<PagedResponse<DTOWithNameModel?, String>>(
      restRequest,
      (json) => PagedResponse<DTOWithNameModel?, String>.fromJson(json, (data) => fromJsonObject(data, DTOWithNameModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<Map<String, List<GetUserDesignationModel>>?> getAllUserWithDesignations({Duration duration = const Duration(seconds: 60)}) async {
    final restResponse = createGetRequest(UsersRestResources.getAllDesignationsWithUsers, timeout: duration);
    final response = await executeRequestAsync<ResponseWrapper<Map<String, List<GetUserDesignationModel>>>>(
      restResponse,
      (json) => ResponseWrapper<Map<String, List<GetUserDesignationModel>>>.fromJson(
        json,
        (data) => data.map<String, List<GetUserDesignationModel>>(
          (key, value) => MapEntry(
            key as String,
            (value as List<dynamic>).map((item) => GetUserDesignationModel.fromJson(item as Map<String, dynamic>)).toList(),
          ),
        ),
      ),
    );
    return response.data;
  }

  @override
  Future<UserDetailsClockInModel?> getGeoFenceDetails() async {
    String userId = await getIt<SharedPreferenceManager>().getString("user_id") ?? "";
    final restRequest = createGetRequest(UsersRestResources.geoFenceDetails(userId));
    final response = await executeRequestAsync<ResponseWrapper<UserDetailsClockInModel?>>(
      restRequest,
          (json) => ResponseWrapper<UserDetailsClockInModel?>.fromJson(json, (data) => fromJsonObject(data, UserDetailsClockInModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<SearchFilterModel>?> getAllSearchResult(int moduleType) async {
    final restRequest = createGetRequest(UsersRestResources.getAllSearchResult(moduleType));
    final response = await executeRequestAsync<ResponseWrapper<List<SearchFilterModel>?>>(
      restRequest,
          (json) => ResponseWrapper<List<SearchFilterModel>?>.fromJson(
        json,
            (data) => (data as List).map((item) => SearchFilterModel.fromJson(item)).toList(),
      ),
    );
    return response.data;
  }

  @override
  Future<bool?> updateSearch(SearchResponse searchResponseModel) async {
    final restRequest = createPutRequest(UsersRestResources.updateSearch, body: searchResponseModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<LeadratSubscriptionDetailsModel?>? getSubscriptionDetails(String? userId) async{
    final restRequest = createGetRequest(UsersRestResources.getSubscriptionDetails(userId));
    final response = await executeRequestAsync<ResponseWrapper<LeadratSubscriptionDetailsModel?>>(
      restRequest,
          (json) => ResponseWrapper<LeadratSubscriptionDetailsModel?>.fromJson(json, (data) => fromJsonObject(data, LeadratSubscriptionDetailsModel.fromJson)),
    );
    return response.data;
  }
}
