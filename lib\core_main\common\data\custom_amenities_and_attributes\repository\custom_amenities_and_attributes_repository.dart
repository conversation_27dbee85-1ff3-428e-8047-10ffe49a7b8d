import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';

abstract class CustomAmenitiesAndAttributesRepository {
  Future<List<CustomAmenitiesModel?>?> getCustomAmenities({bool restore = false, Duration timeout = const Duration(seconds: 60)});

  Future<List<String?>?> getCustomAmenityCategories({bool restore = false, Duration timeout = const Duration(seconds: 60)});

  Future<List<CustomAttributesModel?>?> getCustomAttributes({bool restore = false,Duration timeout = const Duration(seconds: 60)});
}
