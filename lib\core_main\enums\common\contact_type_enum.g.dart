// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContactTypeAdapter extends TypeAdapter<ContactType> {
  @override
  final int typeId = 16;

  @override
  ContactType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ContactType.whatsApp;
      case 1:
        return ContactType.call;
      case 2:
        return ContactType.email;
      case 3:
        return ContactType.sms;
      case 4:
        return ContactType.pushNotification;
      case 5:
        return ContactType.links;
      default:
        return ContactType.whatsApp;
    }
  }

  @override
  void write(BinaryWriter writer, ContactType obj) {
    switch (obj) {
      case ContactType.whatsApp:
        writer.writeByte(0);
        break;
      case ContactType.call:
        writer.writeByte(1);
        break;
      case ContactType.email:
        writer.writeByte(2);
        break;
      case ContactType.sms:
        writer.writeByte(3);
        break;
      case ContactType.pushNotification:
        writer.writeByte(4);
        break;
      case ContactType.links:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
