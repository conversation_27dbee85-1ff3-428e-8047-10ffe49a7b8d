package com.leadrat.black.mobile.droid.leadrat;

import android.app.Activity;
import android.view.WindowManager;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import android.content.SharedPreferences;
import android.content.Context;

public class ScreenshotHandler {
    private static final String PREFS_NAME = "screenshot_prefs";
    private static final String KEY_SCREENSHOT_ENABLED = "screenshot_enabled";

    private final Activity activity;
    private boolean screenshotEnabled = true;
    private SharedPreferences preferences;

    public ScreenshotHandler(Activity activity) {
        this.activity = activity;
        this.preferences = activity.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.screenshotEnabled = preferences.getBoolean(KEY_SCREENSHOT_ENABLED, true);
        updateScreenshotSecurity();
    }

    public void setScreenshotEnabled(boolean enabled, MethodChannel.Result result) {
        try {
            this.screenshotEnabled = enabled;

            SharedPreferences.Editor editor = preferences.edit();
            editor.putBoolean(KEY_SCREENSHOT_ENABLED, enabled);
            editor.apply();

            updateScreenshotSecurity();

            result.success(true);
        } catch (Exception e) {
            result.error("SCREENSHOT_ERROR", "Failed to set screenshot enabled: " + e.getMessage(), null);
        }
    }

    public void isScreenshotEnabled(MethodChannel.Result result) {
        result.success(screenshotEnabled);
    }

    private void updateScreenshotSecurity() {
        activity.runOnUiThread(() -> {
            try {
                if (screenshotEnabled) {
                    activity.getWindow().setFlags(0, WindowManager.LayoutParams.FLAG_SECURE);
                } else {
                    System.out.println("Disabling screenshots");
                    activity.getWindow().setFlags(
                            WindowManager.LayoutParams.FLAG_SECURE,
                            WindowManager.LayoutParams.FLAG_SECURE
                    );
                }
            } catch (Exception e) {
                System.err.println("Error updating screenshot security: " + e.getMessage());
            }
        });
    }


    public void onResume() {
        updateScreenshotSecurity();
    }

    // Call this method when the app is created to set initial state
    public void onActivityCreated() {
        updateScreenshotSecurity();
    }
}