import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'property_status.g.dart';

@HiveType(typeId: HiveModelConstants.propertyStatusTypeId)
@JsonEnum(valueField: 'value')
enum PropertyStatus {
  @HiveField(0)
  active(0, "Active"),
  @HiveField(1)
  sold(1, "Sold");

  final int value;
  final String description;

  const PropertyStatus(this.value, this.description);
}

@JsonEnum(valueField: 'index')
enum PropertyVisibility {
  all("All listing", "allCount"),
  draft("Draft", "draftCount"),
  approved("Approved", "approvedCount"),
  refused("Refused", "refusedCount"),
  archived("Archived", "archivedCount"),
  sold("Sold", "soldCount");

  final String description;
  final String key;

  const PropertyVisibility(this.description, this.key);

  factory PropertyVisibility.fromString(String key) {
    return PropertyVisibility.values.firstWhere((e) => e.key == key);
  }
}

@JsonEnum(valueField: 'index')
enum OfferingType {
  none("All"),
  ready("Ready"),
  offPlan("Off-Plan"),
  secondary("Secondary");

  final String description;

  const OfferingType(this.description);
}
