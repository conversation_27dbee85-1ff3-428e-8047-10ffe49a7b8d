// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_project_amenitites_map_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterProjectAmenititesMapModelAdapter
    extends TypeAdapter<MasterProjectAmenititesMapModel> {
  @override
  final int typeId = 34;

  @override
  MasterProjectAmenititesMapModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterProjectAmenititesMapModel(
      key: fields[0] as String,
      values: (fields[1] as List).cast<MasterProjectAmenititesModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, MasterProjectAmenititesMapModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.key)
      ..writeByte(1)
      ..write(obj.values);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterProjectAmenititesMapModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
