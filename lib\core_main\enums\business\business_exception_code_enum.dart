enum BusinessExceptionCode {
  unknown(1),
  unauthorized(2),
  oauthVerification(3),
  parameterIsRequired(4),
  restServiceError(5),
  restServiceDeserializationError(6),
  peopleService(7),
  invalidHttpRequestContentType(8),
  invalidDataBaseVersion(9),
  invalidFileSize(10),
  invalidFileExtension(11),
  invalidFileContent(12),
  invalidFileName(13),
  peopleServiceUserNotFound(14),
  openExcelError(15),
  notificationMessageBodyNotFound(16),
  notificationScheduleDateNotFound(17),
  notificationScheduleTimeNotFound(18),
  notificationScheduleTimeZoneNotFound(19),
  notificationScheduledDateSurpassed(20),

  permissionsNotFound(100),
  userContextNotInitialized(101),
  activeUserNotFound(102),

  notificationException(20100),
  userNotFound(20001),
  incorrectOtp(20002),
  userExists(20003),
  userCreationFailed(20004),
  tenantNotFound(20005),

  sqlUnknown(50001),
  entityNotFound(50002),
  entityDetailsNotFound(50003),
  entityAlreadyExists(50004),
  dependedEntityExists(50005),
  invalidParameterValue(50006),
  permissionDenied(50007),
  dependentEntitiesNotExist(50008),

  validationException(50025),
  fileUploadException(50026);

  final int code;

  const BusinessExceptionCode(this.code);

  @override
  String toString() => 'BusinessExceptionCode($code)';
}

const Map<BusinessExceptionCode, String> businessExceptionMessages = {
  BusinessExceptionCode.unknown: "Unknown exception",
  BusinessExceptionCode.unauthorized: "Unauthorized access",
  BusinessExceptionCode.oauthVerification: "OAuth verification failed",
  BusinessExceptionCode.parameterIsRequired: "Parameter is required",
  BusinessExceptionCode.restServiceError: "Bad result of REST service call",
  BusinessExceptionCode.restServiceDeserializationError: "REST service deserialization error",
  BusinessExceptionCode.peopleService: "People service error",
  BusinessExceptionCode.invalidHttpRequestContentType: "Invalid ContentType of http request data",
  BusinessExceptionCode.invalidDataBaseVersion: "Invalid version of database",
  BusinessExceptionCode.invalidFileSize: "Invalid size of file",
  BusinessExceptionCode.invalidFileExtension: "Invalid extension of file",
  BusinessExceptionCode.invalidFileContent: "File contains an invalid content",
  BusinessExceptionCode.sqlUnknown: "Unknown sql exception",
  BusinessExceptionCode.entityNotFound: "Entity not found",
  BusinessExceptionCode.entityDetailsNotFound: "Entity details not found",
  BusinessExceptionCode.entityAlreadyExists: "Entity already exists",
  BusinessExceptionCode.dependedEntityExists: "Depended entity exists",
  BusinessExceptionCode.invalidParameterValue: "Invalid parameter value",
  BusinessExceptionCode.permissionDenied: "Permission denied",
  BusinessExceptionCode.dependentEntitiesNotExist: "Sorry, you cannot make category Published. There should be at least 1 published Topic with 1 published FAQ",
  BusinessExceptionCode.peopleServiceUserNotFound: "People service user not found",
  BusinessExceptionCode.openExcelError: "Incorrect file. Please use the template for import.",
  BusinessExceptionCode.userNotFound: "User not found in current tenant",
  BusinessExceptionCode.incorrectOtp: "Incorrect OTP",
  BusinessExceptionCode.userExists: "User already exists in current tenant",
  BusinessExceptionCode.userCreationFailed: "Error creating user",
  BusinessExceptionCode.tenantNotFound: "Tenant not found",
  BusinessExceptionCode.notificationScheduledDateSurpassed: "Can't delete Notification. Scheduled date is already surpassed",
  BusinessExceptionCode.notificationException: "Notification Exception",
};
