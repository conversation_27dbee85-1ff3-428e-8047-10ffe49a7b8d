{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987efc2b48a648aae297e4648c964ba1a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98834962f0e3cdd98e577946be92586b99", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5d646fc2c23ade2164eb7b9c2312b9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9878808acc5dd718a2ec8083c1b4113021", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5d646fc2c23ade2164eb7b9c2312b9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f4b6fef5067edb040752f99fe858f23", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b1a0604ac1d1fe8120c26b76974501b2", "guid": "bfdfe7dc352907fc980b868725387e98eff4336e648d9bb31c539c3cac990499", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98779b9f91084c832dcb4423aa43f68d09", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a71a1099a057877cc3ee42f7f3700ca3", "guid": "bfdfe7dc352907fc980b868725387e98b63de5142ed74160effdabd2a0869d73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcea0a18fbeec5617cdafc5318d81a28", "guid": "bfdfe7dc352907fc980b868725387e98e5bda07a3bc4f0a7b024abe3a71b0b05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d06c42ef59b82b2f62dac5a444b3d2", "guid": "bfdfe7dc352907fc980b868725387e989104e2223c78198648533a1d48897a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471ba48a96024f1f122dbdf5c5109928", "guid": "bfdfe7dc352907fc980b868725387e98187e66f79233d74e0d187c3b4f846b30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340ac24f911ced2243e7c91736852d2b", "guid": "bfdfe7dc352907fc980b868725387e988101063788eea267ebb07c55a1881098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c691b83eba85ea4479fbd30ca446b0", "guid": "bfdfe7dc352907fc980b868725387e98b1e8ebee358037320a9b95c5509f39e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808dfcc2a4e704d1c656818a0be329b8a", "guid": "bfdfe7dc352907fc980b868725387e98e8e831902b3972173bd5a239e6e6c1ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c567a93a3658d189d94af935be21c4ca", "guid": "bfdfe7dc352907fc980b868725387e9899920aef2c7a29fc70cacbe43fde6d2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e20fca2344bec5aab3b4b4d47c03b975", "guid": "bfdfe7dc352907fc980b868725387e981eaee8b371b0ec6ec51bbb176b635a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3c4596b60844f61c58990e4aab2d8c", "guid": "bfdfe7dc352907fc980b868725387e98950883af966790ca91e09b126dec51d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b679b09538367bc759f886201b314d7", "guid": "bfdfe7dc352907fc980b868725387e986faa8fa53c9bf46dc89c31612f3cb968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988258edfc0ed2755f9c5b007ae0baffa2", "guid": "bfdfe7dc352907fc980b868725387e9870a6a9849d641edf5d3f92c870a6568c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d8307abf42bd16c75ce2fc5cba152f5", "guid": "bfdfe7dc352907fc980b868725387e98833764021a29f49182eda7ba9c803776"}], "guid": "bfdfe7dc352907fc980b868725387e9854724536c00cf388e0391e10b5ede4aa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98db70d9cec1f833faaa0b728c6d4451a8"}], "guid": "bfdfe7dc352907fc980b868725387e98f3bff3fd5385622c7a8f8700173de4a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984ece06e733862754d62031d85d69dc6c", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9839e50e9659ecb720cb4347d008a4c45d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}