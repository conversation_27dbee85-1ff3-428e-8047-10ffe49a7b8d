{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d2eaaa57863cefb280344f353f02bc7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e35931737a0a8419b1b3b9a6e202f82e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e35931737a0a8419b1b3b9a6e202f82e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981591ebcdf09d2040a8971aa3abafe669", "guid": "bfdfe7dc352907fc980b868725387e989d10e7d3a23565665ecad4bb21b006ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f842a231beb93c530fa6b570dfe306", "guid": "bfdfe7dc352907fc980b868725387e98a437d7843677599243314f9796cca63f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b42380bb3f77ae948b83c083fda6547", "guid": "bfdfe7dc352907fc980b868725387e98f7508504560af08492884b5aa74b4a58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d33970d1ea4b68ee3eb27ffa08471c16", "guid": "bfdfe7dc352907fc980b868725387e987e2d7e2d258cc9b76c86040a5462944c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed9b59fd65bf77d1af2c0d44c62bb62", "guid": "bfdfe7dc352907fc980b868725387e982e2bb68a7f5f5c6a2b1070763c0adb8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d751d0ee940c5608e6f5e52874a2301", "guid": "bfdfe7dc352907fc980b868725387e988ee816fad95681358d9e5868ce1be1d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da38cb8f6bdfc3e868cbabbf3993400", "guid": "bfdfe7dc352907fc980b868725387e98717dcd7fc3cd7ad8289651cf0d99dceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845cd5c52e7f112fe43c5ee9121ad39df", "guid": "bfdfe7dc352907fc980b868725387e9813a13c07d683bba187d804fc2328f9e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1211f0e274d0f9d5ba8ef3292efbff0", "guid": "bfdfe7dc352907fc980b868725387e984ca9e2a0dabba19c7d0be9178ea47d87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e361fd5709941c2521567103297316b9", "guid": "bfdfe7dc352907fc980b868725387e988c23767f8f859a5605150a47c93aacfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9b7654df27280871ed5708204c4daf", "guid": "bfdfe7dc352907fc980b868725387e98b104a33f14df2b9ec9fa0fbdfa5b86b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d76c403dc5015c4c5da4f2d00548a8", "guid": "bfdfe7dc352907fc980b868725387e984f899f56f70e275b22b1fec8d3a58ba4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca5ad692d580c0f0ba44f829348812d", "guid": "bfdfe7dc352907fc980b868725387e9806d958b9136febe6b5d320bcb2ed21cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824783f50873c3f9a7ce8ba7cce9ddb88", "guid": "bfdfe7dc352907fc980b868725387e98ca2f0db09652e0d3acaa095d5e93bcae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694ded642a007ef66094799162eb8177", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e71086cd706f6fb86efa0bea809f8701", "guid": "bfdfe7dc352907fc980b868725387e982eb0b3c19d046186551e61f0546b3a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98587c95841a9a9f6d6bfc231ff856dfa4", "guid": "bfdfe7dc352907fc980b868725387e983c7d9bdc7f94f786c11277fd3d9cbbec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b154766808e46c2227d0eec71f456f5", "guid": "bfdfe7dc352907fc980b868725387e98f795b3c0efbed74a1c7eafc8571edb73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986293337f4745512139a8443b8ac3ef27", "guid": "bfdfe7dc352907fc980b868725387e98b55695732aae845f6666bc708d5f8eb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592c408396d20057b2db7408eaaec46a", "guid": "bfdfe7dc352907fc980b868725387e987add46a6da30b48a62a998d4c6b775ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d846c8346003d9bf06ae336f5c07ca08", "guid": "bfdfe7dc352907fc980b868725387e982e89c660bba527ecfb1a557172ade444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c82a26700c7844468b1cee1065073fa", "guid": "bfdfe7dc352907fc980b868725387e9801edc74c43caa165d192a43f00ca9be5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfb71009957e0730b129be2be57c202", "guid": "bfdfe7dc352907fc980b868725387e98938c6e33b823dca4b1e04f77b8133d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da5ae4158bdb49962f03d394ded702b", "guid": "bfdfe7dc352907fc980b868725387e98597c83acd83c2684c9649cc5681a377f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb791e5915cd7ac2aad0d1017781ac4b", "guid": "bfdfe7dc352907fc980b868725387e98731e1d634136e6a2bd0bf1fe18f590cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e2a086d4fb64477aee86646c6157be", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}