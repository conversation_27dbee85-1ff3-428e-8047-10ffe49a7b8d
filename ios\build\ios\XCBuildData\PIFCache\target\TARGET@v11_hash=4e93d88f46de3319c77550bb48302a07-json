{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852dafe4957627fddd699ea3b91466019", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c3c3cf3a6b6ca4595451a798c7e9a8b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e8e5b34700a7354bff0b37dbc56fe4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c5998a327534938584ba0700defc36d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e8e5b34700a7354bff0b37dbc56fe4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859c6463d5f75e1216225dc67f85d867c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6c444366bc14da5b244f6b78029fb11", "guid": "bfdfe7dc352907fc980b868725387e98321f9992229d27e86ffa22640d19488d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0f78efe61c761c6e27646b1acda96a", "guid": "bfdfe7dc352907fc980b868725387e98569d0aaa7630134f979146664dcc2593"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a906d16cb55e507713abdff00afacd4", "guid": "bfdfe7dc352907fc980b868725387e98bc6122123eefc2468dde90f1565016ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710b40326d1434c4cc5ee8235c476056", "guid": "bfdfe7dc352907fc980b868725387e983397e91c0df8a59af26c06899d797bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b586801d2b327a2da2034d047a6031c", "guid": "bfdfe7dc352907fc980b868725387e98a7686069e6640e3455c75e76d5158337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5d239377b882e6a398b2ab95bdd81c", "guid": "bfdfe7dc352907fc980b868725387e98d94bd0f9619185c9ae2571bd79fad288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983350b081854b3fbb116aa75c96384cab", "guid": "bfdfe7dc352907fc980b868725387e9826d27c30a14d8a52c5110822cc73f579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb8e741daead4474ce9765ca2985900", "guid": "bfdfe7dc352907fc980b868725387e98ea48d892dedb9397e49953724e45553a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b516c8a8b0dc3637458542ea281865", "guid": "bfdfe7dc352907fc980b868725387e98f2df8688700c7bf89b82fc13139bfe84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813604eed1d759d6cc8fcbfeeff7bf137", "guid": "bfdfe7dc352907fc980b868725387e98be6adbeb8bb657d8dbadef6b35df415b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe713aa3aaf75d0f9ca091becd46f4fc", "guid": "bfdfe7dc352907fc980b868725387e98aa100955f9b231bef728b09d5edcd61d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e840881df64ecd7cfa2f84df5eb6532e", "guid": "bfdfe7dc352907fc980b868725387e98d9c201654d6c268c69c4eeebe5810a9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23bc4b2cf92d2aa8ffc007338d76610", "guid": "bfdfe7dc352907fc980b868725387e98ea40d3734057c7d84601f17b3dd85754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bda27ea0f48cc6a09a7e6f946e20ae1", "guid": "bfdfe7dc352907fc980b868725387e98902af2b5755d0c9790109c6da256c5e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e19b97dd9e138063678ad40d7fa0bf", "guid": "bfdfe7dc352907fc980b868725387e98ce92fc5f73d5310f7b004c7385c98c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8dc0a58217c7c6747087897cf492db8", "guid": "bfdfe7dc352907fc980b868725387e9849b5e98783b7d657ff34b773ca6b35a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815040d42673c5a7f92145a450cd3c90c", "guid": "bfdfe7dc352907fc980b868725387e98da0e1395c7388e8af2f25d83a4bbcb0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315d23a399935bc55c10643306a521c0", "guid": "bfdfe7dc352907fc980b868725387e9861ed05b443598c27059331d34cdc8bdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f00d7b291bc6e46e10ec1d85517db1", "guid": "bfdfe7dc352907fc980b868725387e9867dfd1b3ab9aed933e5521cde1f7ab51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec51551ad1cbc9b252c760761765de9d", "guid": "bfdfe7dc352907fc980b868725387e98524c05371b435d3789f120c8699de807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a32be3ca1fe9344c8c432b4d7d87a093", "guid": "bfdfe7dc352907fc980b868725387e98c264d2d56df15d60e2f36e5839ed57f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8c6d6287e1ee016796c9a1878a165a", "guid": "bfdfe7dc352907fc980b868725387e9846b3a79b5c71851131a56df9b1615c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc48cbc06720748327f290c147e8a0e", "guid": "bfdfe7dc352907fc980b868725387e98fb73d6fc44f1a0e1bf9f67eda3029806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3d5a892a976933a6daf82ef34c7ac8", "guid": "bfdfe7dc352907fc980b868725387e98b69895eacf7f339eab115dee3bb290c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a11cbe453ff24659163a9dd728f26a9c", "guid": "bfdfe7dc352907fc980b868725387e9813cda77eca2b2a265395cb6f6f092d9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0b1d874bdb0d0f980fc4b23999ed70", "guid": "bfdfe7dc352907fc980b868725387e986d86fd69de478b7b5b2f15f79ffa53cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8d285394283e2ee35198742801c49c", "guid": "bfdfe7dc352907fc980b868725387e98127f9026955a3d4a097f655254ca954c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6461d24fcce2aee1f79d8a12ca306b", "guid": "bfdfe7dc352907fc980b868725387e9892410544cc01082ab685cd42ce6dd19e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981714753256b16eebf507ad54a19fb8de", "guid": "bfdfe7dc352907fc980b868725387e9896f73d5e48921fd4932fa3f50c600889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85adbc3b9457fe4ec9f9b61ddf0532d", "guid": "bfdfe7dc352907fc980b868725387e98da23d1f816a7c273cf96c71647d1a1d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d0118185af03a8d9c3a06c0ac32f79", "guid": "bfdfe7dc352907fc980b868725387e98d111bf94635031872cd018b304fe6abb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7029ab5394d36d143c9b44dfa561c61", "guid": "bfdfe7dc352907fc980b868725387e98683ae53f179459464fdea7e689f21279", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd38048fe06760815218df9416618e0", "guid": "bfdfe7dc352907fc980b868725387e98305c03a878c0438f694131c2faaa2482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960b9919e083b0e598f188270135a944", "guid": "bfdfe7dc352907fc980b868725387e9830b5a7171c1b2d4929daeed501d73432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8f52f9846434a664a9f4bbe4350350", "guid": "bfdfe7dc352907fc980b868725387e98e88fb15099947b6e5b50cc11d4beb411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986167ed39f8d24e918c101f877b27e454", "guid": "bfdfe7dc352907fc980b868725387e98fc79cb855392c5eb741d0391f3ff9443"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4176326e2c5bfe3a0652061806dfbaf", "guid": "bfdfe7dc352907fc980b868725387e98843ef2fcd61ae7de1da5ba41a2cfed42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b86c58292e3c39d37ada95769458fa", "guid": "bfdfe7dc352907fc980b868725387e98765ae526fb7c4e35ac564c04f8fed294"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985166c8fd51743fb34613556ce100f1c2", "guid": "bfdfe7dc352907fc980b868725387e983c83a6104421dd03e6a4c96cde8cea1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d6a37459e39b90cafd44a8ed9a2c48", "guid": "bfdfe7dc352907fc980b868725387e98d7ca26752fea86ff35e0a47912f15186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6349c4409057b1a89b341704f654d8d", "guid": "bfdfe7dc352907fc980b868725387e98ec7b83a1f88ccef5df719f5efd9e1a51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844b440a284667ed03874b9293948bc77", "guid": "bfdfe7dc352907fc980b868725387e98fd61caaabec5218affcdf97d0b065930"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899b11de378a658c8b5eeb0f8dd5cacb5", "guid": "bfdfe7dc352907fc980b868725387e987320bbef837dc55a4455b2645b5e5022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd7b5c2c81fbc1f47b52f279a6344322", "guid": "bfdfe7dc352907fc980b868725387e9875c1ff003d6b4a1b554af507cf8d5162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2320a1f082a78fcf73b4300aa1b7376", "guid": "bfdfe7dc352907fc980b868725387e9873815dd86e5e22d59042ea8fe2cb76fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75fdebbb4cc92f303405265ab724ebf", "guid": "bfdfe7dc352907fc980b868725387e98745532798f31041bf6370743d2f0dcc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22861f724bd65ab436c5baaf044d52c", "guid": "bfdfe7dc352907fc980b868725387e98d033e846dfc959e53da278e082969667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1538ef0c5d7113154636e9c4090d790", "guid": "bfdfe7dc352907fc980b868725387e98ac78429bb4a6756b50921517a3bdc364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de502b39be9a42044a593cb8ee563488", "guid": "bfdfe7dc352907fc980b868725387e98d0c32154c40b9e79907f94990a97d2ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41003fd5fa5dfb3ad7bef0f85e72a06", "guid": "bfdfe7dc352907fc980b868725387e9825c795c9f0550ff34b0908f3f3013433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981042df652291050fdcc6a5337cd87d87", "guid": "bfdfe7dc352907fc980b868725387e983b1e4e8161fc121a4b5efe99a792f9ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855790ac0ecd822cd6330c7e3ad90cbf7", "guid": "bfdfe7dc352907fc980b868725387e983022cae091d989e02f0027c744736d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e764f83551191c813205b7afbe73246", "guid": "bfdfe7dc352907fc980b868725387e98ed5b89326212928463c8cb35e1c84c20"}], "guid": "bfdfe7dc352907fc980b868725387e98483a337e60b127335211e10e126b4236", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988db67991d7442cea690f610776e6af2f", "guid": "bfdfe7dc352907fc980b868725387e987489a8a2f8374b42196800ded59e2821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984441c4e89a88695a45490f1e2b24da70", "guid": "bfdfe7dc352907fc980b868725387e985ef3a102f1991c991e120e5e39e2731b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891485537a34ca717b474cd59b98062fa", "guid": "bfdfe7dc352907fc980b868725387e98b327f75ee745def9ca45e9741a21d471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a005fdf2bb094de49bbeaa54b6b6b67", "guid": "bfdfe7dc352907fc980b868725387e983159c8ee2ff865405d969ba4ffc04b85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff18a6390127a2f6ba4db4cb5166cdb9", "guid": "bfdfe7dc352907fc980b868725387e98d70fda9afaf46a38806b947c43d137ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d592f22501effac4985a5e8d6cb33ed1", "guid": "bfdfe7dc352907fc980b868725387e98651f456adb3ce301545a61bc0875e1a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e97208c097274ea6763ad52dea4c21", "guid": "bfdfe7dc352907fc980b868725387e98e7dc2eb02159815dd243b89b6d856e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b27d1136851e40ae495004057a3f81", "guid": "bfdfe7dc352907fc980b868725387e9812f1c7a1781d5fa0d228b2d1951d2e7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ce7e16e42dc0bd191851660bdd2119", "guid": "bfdfe7dc352907fc980b868725387e981e590a9809371f31acb9ccda439ecbc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed743486cb8c30d5846f3e8cb0911e83", "guid": "bfdfe7dc352907fc980b868725387e98c4825f36a95e2fac109844e254023da7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ccb425b55f60f73898eb31c011b7c72", "guid": "bfdfe7dc352907fc980b868725387e98c9b5d8fd76b608e7ea6f30b288f34511"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f52ee4ed76f3d6fb404df66dbcfc685", "guid": "bfdfe7dc352907fc980b868725387e984f32055ac75cae0496d405d41d895746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac5721044876c6e1912242fa8a23e09", "guid": "bfdfe7dc352907fc980b868725387e98342ad4ad2d72d96928c57b2d37a82d16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6bf3e4839c70ed16532a08c21d0434", "guid": "bfdfe7dc352907fc980b868725387e9839646e60e6b8ebd188073798300d09a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e1995b29a8d49fbda3b915f79e77ad", "guid": "bfdfe7dc352907fc980b868725387e98e8ede6ce308f60563073e3b7b1e44412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1eb7d9f0adafd67d8446816c5f6e335", "guid": "bfdfe7dc352907fc980b868725387e980c86e2ae8589dfb36e4fd24f9b1bdb03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2da0583f95fee20d639a1c7aefe932", "guid": "bfdfe7dc352907fc980b868725387e983e2cf97a926fd16cc45c7a9310692ae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e596c10517d37af2d80cf75c98673ceb", "guid": "bfdfe7dc352907fc980b868725387e98331fde8f11473c19c902c5d6245b1f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99f4e9d48ab28b39797c7e3825185ba", "guid": "bfdfe7dc352907fc980b868725387e986bd62e5bb27b3608309a4a45efa8bb31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cae4abcf5dcfc6b89d1f0fda6554f3", "guid": "bfdfe7dc352907fc980b868725387e98f73628a20ee49db8cd8b7d2db3d5b0e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad349a8cb41f2eeca68c848a88fc990c", "guid": "bfdfe7dc352907fc980b868725387e98cb861076e5eb433e3694aa9f55b33c55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d571810f4850f2e1f916f688b176aa", "guid": "bfdfe7dc352907fc980b868725387e98aa50d87856cc632d1ebc1db81e4a9b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce9f415d2d6a945d038031297f7e9bb", "guid": "bfdfe7dc352907fc980b868725387e98f369bf6adff1507de6477ca60841e35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1655eb82129be9b8cf5bbd2d967d57", "guid": "bfdfe7dc352907fc980b868725387e98415793fadb7693b19fa8982d0abb1027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab989e4db1cae56c881d572b5711a80", "guid": "bfdfe7dc352907fc980b868725387e9883f14920e30c979dcc7c3e507ff8cd26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a40654ec38ac34a5229a61374034a2a", "guid": "bfdfe7dc352907fc980b868725387e988c5e7ad03c718c7db80c9e723dc40a32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ffbdb6d6d4b476a8ee0f38cddf781d", "guid": "bfdfe7dc352907fc980b868725387e9838b00d94f9224a711e9d4108f8a83b23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869438947f97f1c3955c03543e8bfae83", "guid": "bfdfe7dc352907fc980b868725387e98abbb2eb7c2e161c2df18b0380bb9e8c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7805be23a2c08e9bc61d53d0e51ab4", "guid": "bfdfe7dc352907fc980b868725387e981bbc5ff43ff297f4f9f97ccfaee66aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a40ada40d61689e6b263ef8ac0825b2a", "guid": "bfdfe7dc352907fc980b868725387e9805e18c8527db7105e21ae036d7c1fbbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e37fc1db8405e1532bd9de953b95bd", "guid": "bfdfe7dc352907fc980b868725387e9829a83c116d679bada5c416a8c04c4131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d73a03c3177796694539dcdefe1e52", "guid": "bfdfe7dc352907fc980b868725387e98f0a59db76cdd11c452635dffc1f57bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895cc98339a8ddf26373e35bc0cdf77f3", "guid": "bfdfe7dc352907fc980b868725387e988528f8fb1ba06e2c08b9b6a59fcad85c"}], "guid": "bfdfe7dc352907fc980b868725387e98cd24ea53aa88924a6c8ddd67be63e3bc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98576b79b8ae3be182e0692c23a71f4ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e98d41791dc8e677acdea8a64f4d6b3d26b"}], "guid": "bfdfe7dc352907fc980b868725387e9852bffe25c5ffeb4e9f6a9bc881949f84", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982085e63aac83ec4dcf73316040236867", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98fd153d6885d45b9669d49424fe72e32c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}