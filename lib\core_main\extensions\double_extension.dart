extension DoubleFormatting on double? {
  String? toDoubleFormattedString() {
    if (this == null) {
      return null;
    }

    String formattedValue = toString();

    if (formattedValue.endsWith(".0")) {
      return formattedValue.substring(0, formattedValue.length - 2);
    }
    return formattedValue;
  }

  String toNonZeroString() {
    if (this == null) return '--';
    return this! > 0.0 ? this?.toString() ?? '--' : '--';
  }

  String convertCurrencyFormat({String? currency, bool? isInternationalFormat}) {
    if (this == null) {
      return "";
    }

    bool isInternational = false;
    List<String> similarIndianCurrencies = ["INR", "NPR", "BDT", "PKR", "LKR"];

    if (this! < 0) {
      return "Negative values not supported";
    }

    if (this! == 0) {
      return "";
    }

    isInternational = currency == null ? false : (similarIndianCurrencies.contains(currency) ? false : true);

    if (!isInternational) {
      // Indian Format (Crores, Lakhs, Thousands)
      List<double> conversionFactors = [10000000.0, 100000.0, 1000.0];
      List<String> suffixes = ["cr", "lac", "k"];

      for (int i = 0; i < conversionFactors.length; i++) {
        double factor = conversionFactors[i];
        if (this! >= factor) {
          double quotient = this! / factor;
          return currency != null && currency.isNotEmpty ? "$currency ${quotient.toStringAsFixed(1)} ${suffixes[i]}" : "${quotient.toStringAsFixed(1)} ${suffixes[i]}";
        }
      }
    } else {
      // International Format (Billions, Millions, Thousands)
      List<double> conversionFactors = [1000000000.0, 1000000.0, 1000.0];
      List<String> suffixes = ["B", "M", "K"];

      for (int i = 0; i < conversionFactors.length; i++) {
        double factor = conversionFactors[i];
        if (this! >= factor) {
          double quotient = this! / factor;
          return currency.isNotEmpty ? "$currency ${quotient.toStringAsFixed(1)} ${suffixes[i]}" : "${quotient.toStringAsFixed(1)} ${suffixes[i]}";
        }
      }
    }

    return currency != null && currency.isNotEmpty ? "$currency ${this!.toStringAsFixed(1)}" : this!.toStringAsFixed(1);
  }

  String formatBrokerage({String? currency}) {
    if (this == null) {
      return "";
    }

    if (currency != null && currency.trim() == "%") {
      return "${this!.toString().endsWith('.0') ? this!.toString().replaceFirst('.0', '') : this} %";
    }

    String formatted = this!.convertCurrencyFormat(currency: currency);

    if (formatted.endsWith('.0')) {
      formatted = formatted.replaceFirst('.0', '');
    }

    return formatted;
  }
}
