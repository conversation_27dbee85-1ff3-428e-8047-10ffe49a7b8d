import 'package:intl/intl.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';

extension DateTimeExtension on DateTime? {
  String convertDateFormatToString() {
    if (this == null) return "";
    return DateFormat("d MMM, yyyy 'at' h:mm a").format(this!.toUserTimeZone()!);
  }

  String convertDateFormatToPossessionDateFormat() {
    if (this == null) return "";
    return DateFormat("d MMM, yyyy").format(this!.toUserTimeZone()!);
  }

  String customDateFormatter(String format) {
    if (this == null) return "";
    try {
      return DateFormat(format).format(this!.toUserTimeZone()!);
    } catch (ex) {
      return '';
    }
  }

  String toDateTimeToFormattedString() {
    if (this == null) return "";

    DateTime now = DateTime.now().toUserTimeZone()!;
    DateTime today = DateTime(now.year, now.month, now.day).toUserTimeZone()!;
    DateTime yesterday = today.subtract(const Duration(days: 1));
    DateTime targetDate = DateTime(this!.year, this!.month, this!.day).toUserTimeZone()!;

    if (targetDate == today) {
      return "Today";
    } else if (targetDate == yesterday) {
      return "Yesterday";
    } else {
      return DateFormat.yMMMd().format(this!);
    }
  }

  String toTimeToFormattedString() {
    if (this == null) return "";
    return DateFormat('hh:mm a').format(this!.toUserTimeZone()!);
  }

  DateTime? toUniversalTime() {
    if (this == null) return null;
    return DateTime.utc(this!.year, this!.month, this!.day, 12, 0, 0, 0, 0);
  }

  DateTime toUniversalTimeStartOfDay() {
    return DateTime.utc(this!.year, this!.month, this!.day, 0, 0, 0, 0, 0);
  }

  DateTime? toUtcFormat() {
    if (this == null) return null;
    return DateTime.utc(this!.year, this!.month, this!.day, this!.hour, this!.minute, this!.second, this!.millisecond, this!.microsecond);
  }
}
