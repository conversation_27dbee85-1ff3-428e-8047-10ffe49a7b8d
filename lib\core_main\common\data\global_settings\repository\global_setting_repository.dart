import 'package:leadrat/core_main/common/data/global_settings/models/country_info_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';

abstract class GlobalSettingRepository {
  Future<GlobalSettingModel?> getGlobalSettings({bool restore = false, Duration duration = const Duration(seconds: 60)});

  Future<BaseCountryInfoModel?> getCountriesInfo({bool restore = false, Duration duration = const Duration(seconds: 60)});
}
