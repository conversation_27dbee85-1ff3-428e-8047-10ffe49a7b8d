// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceInfoModel _$DeviceInfoModelFromJson(Map<String, dynamic> json) =>
    DeviceInfoModel(
      id: json['id'] as String?,
      userId: json['userId'] as String?,
      operatingSystem: (json['operatingSystem'] as num?)?.toInt(),
      osVersion: json['osVersion'] as String?,
      countryCode: json['countryCode'] as String?,
      languageCode: json['languageCode'] as String?,
      currencyCode: json['currencyCode'] as String?,
      currencySymbol: json['currencySymbol'] as String?,
      deviceTimeZone: json['deviceTimeZone'] as String?,
      timeFormat: (json['timeFormat'] as num?)?.toInt(),
      deviceModel: json['deviceModel'] as String?,
      deviceUDID: json['deviceUDID'] as String?,
      deviceName: json['deviceName'] as String?,
      isDebug: json['isDebug'] as bool? ?? false,
      isDeveloperMode: json['isDeveloperMode'] as bool? ?? false,
      model: json['model'] as String?,
      manufacturer: json['manufacturer'] as String?,
      environment: json['environment'] as String?,
      ipAddress: json['ipAddress'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
    );

Map<String, dynamic> _$DeviceInfoModelToJson(DeviceInfoModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.operatingSystem case final value?) 'operatingSystem': value,
      if (instance.osVersion case final value?) 'osVersion': value,
      if (instance.countryCode case final value?) 'countryCode': value,
      if (instance.languageCode case final value?) 'languageCode': value,
      if (instance.currencyCode case final value?) 'currencyCode': value,
      if (instance.currencySymbol case final value?) 'currencySymbol': value,
      if (instance.deviceTimeZone case final value?) 'deviceTimeZone': value,
      if (instance.timeFormat case final value?) 'timeFormat': value,
      if (instance.deviceModel case final value?) 'deviceModel': value,
      if (instance.deviceUDID case final value?) 'deviceUDID': value,
      if (instance.deviceName case final value?) 'deviceName': value,
      'isDebug': instance.isDebug,
      'isDeveloperMode': instance.isDeveloperMode,
      if (instance.model case final value?) 'model': value,
      if (instance.manufacturer case final value?) 'manufacturer': value,
      if (instance.environment case final value?) 'environment': value,
      if (instance.ipAddress case final value?) 'ipAddress': value,
      if (instance.latitude case final value?) 'latitude': value,
      if (instance.longitude case final value?) 'longitude': value,
    };
