import 'dart:async';

import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_environment.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/token_manager.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/resources/theme/app_theme.dart';
import 'package:leadrat/core_main/utilities/app_life_cycle_observer.dart';
import 'package:leadrat/core_main/utilities/app_utils.dart';
import 'package:leadrat/core_main/utilities/native_call_tracker.dart';
import 'package:leadrat/features/home/<USER>/pages/splash_screen.dart';

import 'firebase_options.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  RestResources.setAppEnvironment(AppEnvironment.qa);
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  final config = ClarityConfig(projectId: StringConstants.microsoftClarityProjectId, logLevel: LogLevel.None);

  await initDependencies();
  await getIt<TokenManager>().loadTokens();
  await getIt<AuthTokenRepository>().getBaseUrl();
  unawaited(getIt<AppAnalysisRepository>().getAppFeatures());

  await NativeCallTracker.initialize();

  if (kReleaseMode) {
    runApp(ClarityWidget(app: const MyApp(), clarityConfig: config));
  } else {
    runApp(const MyApp());
  }
}

class MyApp extends StatefulWidget {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late CallLifecycleObserver _callLifecycleObserver;

  @override
  void initState() {
    super.initState();
    _callLifecycleObserver = CallLifecycleObserver();
    WidgetsBinding.instance.addObserver(_callLifecycleObserver);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_callLifecycleObserver);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final whiteLabeledAppName = AppUtils.getAppName();
    return MultiBlocProvider(
      providers: registerBlocProviders(context),
      child: MaterialApp(
        title: whiteLabeledAppName,
        navigatorKey: MyApp.navigatorKey,
        navigatorObservers: [routeObserver],
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkThemeMode,
        home: const SplashScreen(),
      ),
    );
  }
}
