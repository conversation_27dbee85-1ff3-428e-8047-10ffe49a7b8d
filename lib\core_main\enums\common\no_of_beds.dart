import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

@JsonEnum(valueField: 'index')
enum Beds {
  studio(0, "studio"),
  oneBed(1, "1"),
  twoBed(2, "2"),
  threeBed(3, "3"),
  fourBed(4, "4"),
  fiveBed(5, "5"),
  sixBed(6, "6"),
  sevenBed(7, "7"),
  eightBed(8, "8"),
  nineBed(9, "9"),
  tenBed(10, "10");

  final int value;
  final String description;

  const Beds(this.value, this.description);
}

T? getEnumFromBeds<T extends Enum>(List<T> values, int? noOfBeds) {
  try {
    if (noOfBeds == null) return null;
    return values.firstWhere((e) => (e as dynamic).value == noOfBeds);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}
