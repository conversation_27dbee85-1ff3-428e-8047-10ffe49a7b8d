// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DocumentTypeAdapter extends TypeAdapter<DocumentType> {
  @override
  final int typeId = 104;

  @override
  DocumentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DocumentType.defaultType;
      case 1:
        return DocumentType.identity;
      case 2:
        return DocumentType.experience;
      case 3:
        return DocumentType.signature;
      case 4:
        return DocumentType.jpg;
      case 5:
        return DocumentType.png;
      case 6:
        return DocumentType.pdf;
      case 7:
        return DocumentType.docx;
      case 8:
        return DocumentType.txt;
      case 9:
        return DocumentType.csv;
      default:
        return DocumentType.defaultType;
    }
  }

  @override
  void write(BinaryWriter writer, DocumentType obj) {
    switch (obj) {
      case DocumentType.defaultType:
        writer.writeByte(0);
        break;
      case DocumentType.identity:
        writer.writeByte(1);
        break;
      case DocumentType.experience:
        writer.writeByte(2);
        break;
      case DocumentType.signature:
        writer.writeByte(3);
        break;
      case DocumentType.jpg:
        writer.writeByte(4);
        break;
      case DocumentType.png:
        writer.writeByte(5);
        break;
      case DocumentType.pdf:
        writer.writeByte(6);
        break;
      case DocumentType.docx:
        writer.writeByte(7);
        break;
      case DocumentType.txt:
        writer.writeByte(8);
        break;
      case DocumentType.csv:
        writer.writeByte(9);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DocumentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
