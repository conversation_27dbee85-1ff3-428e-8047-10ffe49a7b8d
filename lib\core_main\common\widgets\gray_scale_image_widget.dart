import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:lottie/lottie.dart';

class GrayScaleImageWidget extends StatelessWidget {
  final String? imageUrl;
  final String? lottieAsset;

  const GrayScaleImageWidget({super.key, this.imageUrl, this.lottieAsset});

  static const grayscaleColorFilter = ColorFilter.matrix(<double>[
    0.2126, 0.7152, 0.0722, 0, 0, // red
    0.2126, 0.7152, 0.0722, 0, 0, // green
    0.2126, 0.7152, 0.0722, 0, 0, // blue
    0, 0, 0, 1, 0,               // alpha
  ]);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (imageUrl != null)
        // Grayscale image
        ColorFiltered(
          colorFilter: grayscaleColorFilter,
          child: CachedNetworkImage(
            imageUrl: imageUrl ?? '',
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        ),

        // Optional grayscale Lottie animation overlay
        if (lottieAsset != null)
          Positioned.fill(
            child: ColorFiltered(
              colorFilter: grayscaleColorFilter,
              child: Lottie.asset(
                lottieAsset!,
                fit: BoxFit.cover,
              ),
            ),
          ),
      ],
    );
  }
}
