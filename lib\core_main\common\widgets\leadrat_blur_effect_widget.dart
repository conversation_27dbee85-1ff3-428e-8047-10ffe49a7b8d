import 'dart:ui';
import 'package:flutter/material.dart';

class LeadratBlurEffectWidget extends StatelessWidget {
  final Widget child;
  final double blurSigma;

  const LeadratBlurEffectWidget({
    super.key,
    required this.child,
    this.blurSigma = 8,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: Stack(
        children: [
          child,
          Positioned.fill(
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: blurSigma,
                sigmaY: blurSigma,
              ),
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
