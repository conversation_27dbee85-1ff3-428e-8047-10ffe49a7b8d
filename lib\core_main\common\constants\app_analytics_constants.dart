class AppAnalyticsConstants {
  static const String mobileDomainButtonConnectClick = 'mobile.domain.button.connect.click';
  static const String mobileLoginButtonLoginClick = 'mobile.login.button.login.click';
  static const String mobileForgotPasswordButtonClick = 'mobile.forgotPassword.button.click';
  static const String mobileForgotPasswordPageForgotPasswordView = 'mobile.forgotPassword.page.forgotPassword.view';
  static const String mobileLoginPageView = 'mobile.login.page.view';
  static const String mobileForgotPasswordGoBackToLoginScreenButtonClick = 'mobile.forgotPassword.goBackToLoginScreen.button.click';
  static const String mobileForgotPasswordButtonSubmitClick = 'mobile.forgotPassword.button.submit.click';
  static const String mobileRecoveringYourAccountButtonGetOTPClick = 'mobile.recoveringYourAccount.button.getOTP.click';
  static const String mobileEnterOTPButtonSubmitClick = 'mobile.enterOTP.button.submit.click';
  static const String mobileManageLeadsPageManageLeadsView = 'mobile.manageLeads.page.manageLeads.view';
  static const String mobileManageLeadsButtonUserProfileClick = 'mobile.manageLeads.button.userProfile.click';
  static const String mobileManageLeadsButtonSearchClick = 'mobile.manageLeads.button.search.click';
  static const String mobileManageLeadsButtonFilterClick = 'mobile.manageLeads.button.filter.click';
  static const String mobileManageLeadsButtonDashboardClick = 'mobile.manageLeads.button.dashboard.click';
  static const String mobileManageLeadsButtonDataClick = 'mobile.manageLeads.button.data.click';
  static const String mobileManageLeadsButtonPropertyClick = 'mobile.manageLeads.button.property.click';
  static const String mobileManageLeadsButtonMenuClick = 'mobile.manageLeads.button.menu.click';
  static const String mobileManageLeadsButtonAddLeadClick = 'mobile.manageLeads.button.addLead.click';
  static const String mobileManageLeadsButtonNotificationClick = 'mobile.manageLeads.button.notification.click';
  static const String mobileLeadFilterPageLeadFilterView = 'mobile.leadFilter.page.leadFilter.view';
  static const String mobileLeadFilterButtonCloseClick = 'mobile.leadFilter.button.close.click';
  static const String mobileLeadFilterButtonApplyClick = 'mobile.leadFilter.button.apply.click';
  static const String mobileAddLeadPageView = 'mobile.addLead.page.view';
  static const String mobileAddLeadButtonAddAlternateNumberClick = 'mobile.addLead.button.addAlternateNumber.click';
  static const String mobileAddLeadButtonCountryCodeClick = 'mobile.addLead.button.countryCode.click';
  static const String mobileAddLeadButtonImportContactsClick = 'mobile.addLead.button.imprtContacts.click';
  static const String mobileAddLeadButtonAddEmailClick = 'mobile.addLead.button.addEmail.click';
  static const String mobileAddLeadButtonAddReferralDetailsClick = 'mobile.addLead.button.addreferalDetails.click';
  static const String mobileAddLeadButtonRemoveAlternateNumberClick = 'mobile.addLead.button.removeAlternateNumber.click';
  static const String mobileAddLeadButtonRemoveEmailClick = 'mobile.addLead.button.removeEmail.click';
  static const String mobileAddLeadButtonRemoveReferralDetailsClick = 'mobile.addLead.button.removeReferalDetails.click';
  static const String mobileAddLeadButtonLeadSourceClick = 'mobile.addLead.button.leadSource.click';
  static const String mobileAddLeadButtonAssignLeadToClick = 'mobile.addLead.button.assignLeadTo.click';
  static const String mobileAddLeadButtonSecondaryAssignToClick = 'mobile.addLead.button.secondaryAssignto.click';
  static const String mobileAddLeadButtonCustomerLocationClick = 'mobile.addLead.button.customerLocation.click';
  static const String mobileAddLeadButtonBudgetCurrencyClick = 'mobile.addLead.button.budgetCurrency.click';
  static const String mobileAddLeadButtonCarpetAreaUnitClick = 'mobile.addLead.button.carpetAreaUnit.click';
  static const String mobileAddLeadButtonSaleableAreaUnitClick = 'mobile.addLead.button.saleableAreaUnit.click';
  static const String mobileAddLeadButtonBuiltUpAreaUnitClick = 'mobile.addLead.button.builtUpAreaUnit.click';
  static const String mobileAddLeadButtonEnquiredForClick = 'mobile.addLead.button.enquiredFor.click';
  static const String mobileAddLeadButtonPropertyTypeClick = 'mobile.addLead.button.propertyType.click';
  static const String mobileAddLeadButtonPropertySubTypeClick = 'mobile.addLead.button.propertySubType.click';
  static const String mobileAddLeadButtonBHKClick = 'mobile.addLead.button.bhk.click';
  static const String mobileAddLeadButtonBRClick = 'mobile.addLead.button.br.click';
  static const String mobileAddLeadButtonBathsClick = 'mobile.addLead.button.baths.click';
  static const String mobileAddLeadButtonBHKTypeClick = 'mobile.addLead.button.bhkType.click';
  static const String mobileAddLeadButtonPreferredFloorClick = 'mobile.addLead.button.preferredFloor.click';
  static const String mobileAddLeadButtonFurnishedClick = 'mobile.addLead.button.furnished.click';
  static const String mobileAddLeadButtonSelectProjectsClick = 'mobile.addLead.button.selectProjects.click';
  static const String mobileAddLeadButtonSelectPropertiesClick = 'mobile.addLead.button.selectProperties.click';
  static const String mobileAddLeadButtonOfferingTypeClick = 'mobile.addLead.button.offeringType.click';
  static const String mobileAddLeadButtonAgencyNameClick = 'mobile.addLead.button.agencyName.click';
  static const String mobileAddLeadButtonChannelPartnerNameClick = 'mobile.addLead.button.channelPartnerName.click';
  static const String mobileAddLeadButtonPossessionDateClick = 'mobile.addLead.button.possessionDate.click';
  static const String mobileAddLeadButtonLocationClick = 'mobile.addLead.button.location.click';
  static const String mobileAddLeadButtonProfessionClick = 'mobile.addLead.button.profession.click';
  static const String mobileAddLeadButtonSourcingManagerClick = 'mobile.addLead.button.sourcingManager.click';
  static const String mobileAddLeadButtonClosingManagerClick = 'mobile.addLead.button.closingManager.click';
  static const String mobileAddLeadButtonBackClick = 'mobile.addLead.button.back.click';
  static const String mobileAddLeadButtonSaveAndFinishClick = 'mobile.addLead.button.saveAndFinish.click';
  static const String mobileMenuButtonDashboardClick = 'mobile.menu.button.dashboard.click';
  static const String mobileMenuButtonLeadsClick = 'mobile.menu.button.leads.click';
  static const String mobileMenuButtonDataClick = 'mobile.menu.button.data.click';
  static const String mobileMenuButtonPropertiesClick = 'mobile.menu.button.properties.click';
  static const String mobileMenuButtonProjectsClick = 'mobile.menu.button.projects.click';
  static const String mobileMenuButtonAttendanceClick = 'mobile.menu.button.attendance.click';
  static const String mobileMenuButtonQrCodeClick = 'mobile.menu.button.qrCode.click';
  static const String mobileMenuButtonSettingsClick = 'mobile.menu.button.settings.click';
  static const String mobileMenuButtonNotificationClick = 'mobile.menu.button.notification.click';
  static const String mobileMenuButtonLogoutClick = 'mobile.menu.button.logout.click';
  static const String mobileMenuButtonCloseClick = 'mobile.menu.button.close.click';
  static const String mobileMenuButtonViewProfileClick = 'mobile.menu.button.viewProfile.click';
  static const String mobileManageLeadsButtonLeadClick = 'mobile.manageLeads.button.lead.click';
  static const String mobileLeadInfoPageView = 'mobile.leadInfo.page.view';
  static const String mobileLeadInfoButtonEditClick = 'mobile.leadInfo.button.edit.click';
  static const String mobileLeadInfoButtonBackClick = 'mobile.leadInfo.button.back.click';
  static const String mobileLeadInfoButtonCallClick = 'mobile.leadInfo.button.call.click';
  static const String mobileLeadInfoButtonSmsClick = 'mobile.leadInfo.button.sms.click';
  static const String mobileLeadInfoButtonWhatsAppClick = 'mobile.leadInfo.button.whatsapp.click';
  static const String mobileLeadInfoButtonEmailClick = 'mobile.leadInfo.button.email.click';
  static const String mobileShareTemplatePageView = 'mobile.shareTemplate.page.view';
  static const String mobileShareTemplateButtonLeadClick = 'mobile.shareTemplate.button.lead.click';
  static const String mobileShareTemplateButtonPropertyClick = 'mobile.shareTemplate.button.property.click';
  static const String mobileShareTemplateButtonProjectClick = 'mobile.shareTemplate.button.project.click';
  static const String mobileShareTemplateButtonLeadSelectTemplateClick = 'mobile.shareTemplate.button.leadSelectTemplate.click';
  static const String mobileShareTemplateButtonPropertySelectTemplateClick = 'mobile.shareTemplate.button.propertySelectTemplate.click';
  static const String mobileShareTemplateButtonProjectSelectTemplateClick = 'mobile.shareTemplate.button.projectSelectTemplate.click';
  static const String mobileShareTemplateButtonSelectPropertyClick = 'mobile.shareTemplate.button.selectProperty.click';
  static const String mobileShareTemplateButtonSelectProjectClick = 'mobile.shareTemplate.button.selectProject.click';
  static const String mobileShareTemplateButtonBackClick = 'mobile.shareTemplate.button.back.click';
  static const String mobileShareTemplateButtonSendMessageClick = 'mobile.shareTemplate.button.sendMessage.click';
  static const String mobileLeadInfoButtonIVRClick = 'mobile.leadinfo.button.ivr.click';
  static const String mobileLeadInfoButtonDialerClick = 'mobile.leadinfo.button.dailer.click';
  static const String mobileLeadInfoButtonChangeStatusClick = 'mobile.leadinfo.button.changeStatus.click';
  static const String mobileUpdateLeadStatusPageView = 'mobile.updateLeadStatus.page.view';
  static const String mobileUpdateLeadStatusButtonSelectStatusClick = 'mobile.updateLeadStatus.button.selectStatus.click';
  static const String mobileUpdateLeadStatusButtonSelectSubStatusClick = 'mobile.updateLeadStatus.button.selectSubStatus.click';
  static const String mobileUpdateLeadStatusButtonScheduleDateClick = 'mobile.updateLeadStatus.button.scheduleDate.click';
  static const String mobileUpdateLeadStatusButtonScheduleTimeClick = 'mobile.updateLeadStatus.button.scheduleTime.click';
  static const String mobileUpdateLeadStatusButtonUnmatchedBudgetClick = 'mobile.updateLeadStatus.buttom.unMatchedBudget.click';
  static const String mobileUpdateLeadStatusButtonPrimaryAssignToClick = 'mobile.updateLeadStatus.button.primaryAssignTo.click';
  static const String mobileUpdateLeadStatusButtonSecondaryAssignToClick = 'mobile.updateLeadStatus.button.secondaryAssignTo.click';
  static const String mobileUpdateLeadStatusButtonCancelClick = 'mobile.updateLeadStatus.button.cancel.click';
  static const String mobileUpdateLeadStatusButtonSaveAndFinishClick = 'mobile.updateLeadStatus.button.saveAndFinish.click';
  static const String mobileLeadInfoButtonUpdateTagsClick = 'mobile.leadInfo.button.updateTags.click';
  static const String mobileLeadInfoButtonShowMoreTagsClick = 'mobile.leadInfo.button.showMoreTags.click';
  static const String mobileLeadInfoButtonReAssignLeadClick = 'mobile.leadInfo.button.reAssignLead.click';
  static const String mobileAssignLeadPageView = 'mobile.assignLead.page.view';
  static const String mobileAssignLeadButtonPrimaryAssignToClick = 'mobile.assignLead.button.primaryAssignTo.click';
  static const String mobileAssignLeadButtonSecondaryAssignToClick = 'mobile.assignLead.button.secondaryAssignTo.click';
  static const String mobileAssignLeadButtonBackClick = 'mobile.assignLead.button.back.click';
  static const String mobileAssignLeadButtonAssignLeadClick = 'mobile.assignLead.button.assignLead.click';
  static const String mobileLeadInfoButtonLeadInfoTabClick = 'mobile.leadInfo.button.leadInfoTab.click';
  static const String mobileLeadInfoButtonEnquiryTabClick = 'mobile.leadInfo.button.enquiryTab.click';
  static const String mobileLeadInfoButtonHistoryTabClick = 'mobile.leadInfo.button.historyTab.click';
  static const String mobileLeadInfoButtonNotesTabClick = 'mobile.leadInfo.button.notesTab.click';
  static const String mobileLeadInfoButtonDocumentsClick = 'mobile.leadInfo.button.documents.click';
  static const String mobileLeadInfoPageEnquiryTabView = 'mobile.leadinfo.page.enquiryTab.view';
  static const String mobileLeadInfoPageHistoryTabView = 'mobile.leadInfo.page.historyTab.view';
  static const String mobileLeadInfoPageNotesTabView = 'mobile.leadInfo.page.notesTab.view';
  static const String mobileLeadInfoPageDocumentsTabView = 'mobile.leadInfo.page.documentsTab.view';
  static const String mobileLeadInfoButtonAddNotesClick = 'mobile.leadInfo.button.addNotes.click';
  static const String mobileLeadInfoPageAddNotesView = 'mobile.leadInfo.page.addNotes.view';
  static const String mobileLeadInfoButtonAddNotesCancelClick = 'mobile.leadInfo.button.addNotesCancel.click';
  static const String mobileLeadInfoButtonAddNotesSaveNoteClick = 'mobile.leadInfo.button.addNotesSaveNote.click';
  static const String mobileLeadInfoButtonUploadFileClick = 'mobile.leadInfo.button.uploadFile.click';
  static const String mobileLeadInfoButtonClickPhotoClick = 'mobile.leadInfo.button.clickPhoto.click';
  static const String mobileLeadInfoButtonUploadFileCancelClick = 'mobile.leadInfo.button.uploadFileCancel.click';
  static const String mobileLeadInfoButtonDownloadFileClick = 'mobile.leadInfo.button.downloadFile.click';
  static const String mobileLeadInfoButtonDeleteFileClick = 'mobile.leadInfo.button.deleteFile.click';
  static const String mobileLeadInfoPageLeadInfoTabSwipe = 'mobile.leadInfo.page.leadInfoTab.swipe';
  static const String mobileLeadInfoPageEnquiryTabSwipe = 'mobile.leadInfo.page.enquiryTab.swipe';
  static const String mobileLeadInfoPageHistoryTabSwipe = 'mobile.leadInfo.page.historyTab.swipe';
  static const String mobileLeadInfoPageNotesTabSwipe = 'mobile.leadInfo.page.notesTab.swipe';
  static const String mobileLeadInfoPageDocumentsTabSwipe = 'mobile.leadInfo.page.documentsTab.swipe';
  static const String mobileLeadInfoPageNextLeadInfoSwipe = 'mobile.leadInfo.page.nextLeadInfo.swipe';
  static const String mobileLeadInfoPagePreviousLeadInfoSwipe = 'mobile.leadInfo.page.previousLeadInfo.swipe';
  static const String mobileManageLeadsPageCategoryUpScrollScroll = 'mobile.manageLeads.page.categoryUpScroll.scroll'; //
  static const String mobileManageLeadsPageCategoryDownScrollScroll = 'mobile.manageLeads.page.categoryDownScroll.scroll';
  static const String mobileManageLeadsPageCategoryRightSwipeSwipe = 'mobile.manageLeads.page.categoryRightSwipe.swipe'; //
  static const String mobileManageLeadsPageCategoryLeftSwipeSwipe = 'mobile.manageLeads.page.categoryLeftSwipe.swipe'; //
  static const String mobileManageLeadsPageCategoryLoadingDotsView = 'mobile.manageLeads.page.categoryLoadingDots.view';
  static const String mobileManageLeadsButtonClickHereToRefreshClick = 'mobile.manageLeads.button.clickHereToRefresh.click';
  static const String mobileManageLeadsPageCategoryRefreshView = 'mobile.manageLeads.page.categoryRefresh.view';
  static const String mobileLeadInfoButtonMatchingPropertiesClick = 'mobile.leadInfo.button.matchingProperties.click';
  static const String mobileLeadInfoButtonMatchingProjectsClick = 'mobile.leadInfo.button.matchingProjects.click';
  static const String mobileLeadInfoButtonIVRCallRecordingsClick = 'mobile.leadInfo.button.ivrCallRecordings.click';
  static const String mobileCallRecordingPageCallRecordingView = 'mobile.callRecording.page.callRecording.view';
  static const String mobileMatchingPropertyPageMatchingPropertyView = 'mobile.matchingProperty.page.matchingProperty.view';
  static const String mobileMatchingPropertyButtonSeeMoreClick = 'mobile.matchingProperty.button.seeMore.click';
  static const String mobileMatchingPropertyButtonSeeLessClick = 'mobile.matchingProperty.button.seeLess.click';
  static const String mobileMatchingPropertyButtonSearchClick = 'mobile.matchingProperty.button.search.click';
  static const String mobileMatchingPropertyButtonMatchingPropertyClick = 'mobile.matchingProperty.button.matchingProperty.click'; //
  static const String mobileMatchingPropertyButtonSelectRadiusClick = 'mobile.matchingProperty.button.selectRadius.click';
  static const String mobileMatchingPropertyButtonShareIndividualPropertyClick = 'mobile.matchingProperty.button.shareIndividualProperty.click';
  static const String mobileMatchingPropertyButtonSelectAllClick = 'mobile.matchingProperty.button.selectAll.click';
  static const String mobileMatchingPropertyButtonMultipleShareClick = 'mobile.matchingProperty.button.multipleShare.click';
  static const String mobileNotificationPageNotificationView = 'mobile.notification.page.notification.view';
  static const String mobileNotificationButtonNotificationClick = 'mobile.notification.button.notification.click';
  static const String mobileLeadInfoButtonCallPrimaryClick = 'mobile.leadInfo.button.callPrimary.click';
  static const String mobileLeadInfoButtonCallSecondaryClick = 'mobile.leadInfo.button.callSecondary.click';
  static const String mobileLeadInfoButtonWhatsappPrimaryClick = 'mobile.leadInfo.button.whatsappPrimary.click';
  static const String mobileLeadInfoButtonWhatsappSecondaryClick = 'mobile.leadInfo.button.whatsappSecondary.click';
  static const String mobileLeadInfoSmsCallPrimaryClick = 'mobile.leadInfo.button.smsPrimary.click';
  static const String mobileLeadInfoButtonSmsSecondaryClick = 'mobile.leadInfo.button.smsSecondary.click';
  static const String mobileManageDataPageManageDataView = 'mobile.manageData.page.manageData.view';
  static const String mobileManageDataButtonSearchClick = 'mobile.manageData.button.search.click';
  static const String mobileManageDataButtonFilterClick = 'mobile.manageData.button.filter.click';
  static const String mobileManageDataButtonAddDataClick = 'mobile.manageData.button.addData.click';
  static const String mobileDataFilterPageDataFilterView = 'mobile.dataFilter.page.dataFilter.view';
  static const String mobileDataFilterButtonCloseClick = 'mobile.dataFilter.button.close.click';
  static const String mobileDataFilterButtonApplyClick = 'mobile.dataFilter.button.apply.click';
  static const String mobileAddDataPageView = 'mobile.addData.page.addData.view';
  static const String mobileAddDataButtonAddAlternateNumberClick = 'mobile.addData.button.addAlternateNumber.click';
  static const String mobileAddDataButtonRemoveAlternateNumberClick = 'mobile.addData.button.removeAlternateNumber.click';
  static const String mobileAddDataButtonCountryCodeClick = 'mobile.addData.button.countryCode.click';
  static const String mobileAddDataButtonImportContactsClick = 'mobile.addData.button.imprtContacts.click';
  static const String mobileAddDataButtonAddEmailClick = 'mobile.addData.button.addEmail.click';
  static const String mobileAddDataButtonRemoveEmailClick = 'mobile.addData.button.removeEmail.click';
  static const String mobileAddDataButtonAddReferralDetailsClick = 'mobile.addData.button.addreferalDetails.click';
  static const String mobileAddDataButtonRemoveReferralDetailsClick = 'mobile.addData.button.removeReferalDetails.click';
  static const String mobileAddDataButtonDataSourceClick = 'mobile.addData.button.dataSource.click';
  static const String mobileAddDataButtonAssignDataToClick = 'mobile.addData.button.assignDataTo.click';
  static const String mobileAddDataButtonCustomerLocationClick = 'mobile.addData.button.customerLocation.click';
  static const String mobileAddDataButtonBudgetCurrencyClick = 'mobile.addData.button.budgetCurrency.click';
  static const String mobileAddDataButtonCarpetAreaUnitClick = 'mobile.addData.button.carpetAreaUnit.click';
  static const String mobileAddDataButtonSaleableAreaUnitClick = 'mobile.addData.button.saleableAreaUnit.click';
  static const String mobileAddDataButtonBuiltUpAreaUnitClick = 'mobile.addData.button.builtUpAreaUnit.click';
  static const String mobileAddDataButtonPropertyAreaUnitClick = 'mobile.addData.button.propertyType.click';
  static const String mobileAddDataButtonNetAreaUnitClick = 'mobile.addData.button.netAreaUnit.click';
  static const String mobileAddDataButtonNationalityClick = 'mobile.addData.button.nationality.click';
  static const String mobileAddDataButtonEnquiredForClick = 'mobile.addData.button.enquiredFor.click';
  static const String mobileAddDataButtonPropertyTypeClick = 'mobile.addData.button.propertyType.click';
  static const String mobileAddDataButtonPropertySubTypeClick = 'mobile.addData.button.propertySubType.click';
  static const String mobileAddDataButtonBHKClick = 'mobile.addData.button.bhk.click';
  static const String mobileAddDataButtonBRClick = 'mobile.addData.button.br.click';
  static const String mobileAddDataButtonBathsClick = 'mobile.addData.button.baths.click';
  static const String mobileAddDataButtonBHKTypeClick = 'mobile.addData.button.bhkType.click';
  static const String mobileAddDataButtonBedsClick = 'mobile.addData.button.beds.click';
  static const String mobileAddDataButtonPreferredFloorClick = 'mobile.addData.button.preferredFloor.click';
  static const String mobileAddDataButtonFurnishedClick = 'mobile.addData.button.furnished.click';
  static const String mobileAddDataButtonSelectProjectsClick = 'mobile.addData.button.selectProjects.click';
  static const String mobileAddDataButtonSelectPropertiesClick = 'mobile.addData.button.selectProperties.click';
  static const String mobileAddDataButtonOfferingTypeClick = 'mobile.addData.button.offeringType.click';
  static const String mobileAddDataButtonAgencyNameClick = 'mobile.addData.button.agencyName.click';
  static const String mobileAddDataButtonChannelPartnerNameClick = 'mobile.addData.button.channelPartnerName.click';
  static const String mobileAddDataButtonPossessionDateClick = 'mobile.addData.button.possessionDate.click';
  static const String mobileAddDataButtonLocationClick = 'mobile.addData.button.location.click';
  static const String mobileAddDataButtonProfessionClick = 'mobile.addData.button.profession.click';
  static const String mobileAddDataButtonSourcingManagerClick = 'mobile.addData.button.sourcingManager.click';
  static const String mobileAddDataButtonClosingManagerClick = 'mobile.addData.button.closingManager.click';
  static const String mobileAddDataButtonBackClick = 'mobile.addData.button.back.click';
  static const String mobileAddDataButtonSaveAndFinishClick = 'mobile.addData.button.saveAndFinish.click';
  static const String mobileDataInfoPageView = 'mobile.dataInfo.page.view';
  static const String mobileDataInfoButtonEditClick = 'mobile.dataInfo.button.edit.click';
  static const String mobileDataInfoButtonBackClick = 'mobile.dataInfo.button.back.click';
  static const String mobileDataInfoButtonCallClick = 'mobile.dataInfo.button.call.click';
  static const String mobileDataInfoButtonSmsClick = 'mobile.dataInfo.button.sms.click';
  static const String mobileDataInfoButtonWhatsAppClick = 'mobile.dataInfo.button.whatsapp.click';
  static const String mobileDataInfoButtonEmailClick = 'mobile.dataInfo.button.email.click';
  static const String mobileManagePropertyPageView = 'mobile.manageProperties.page.manageProperties.view';
  static const String mobileManagePropertyButtonSearchClick = 'mobile.manageProperties.button.search.click';
  static const String mobileManagePropertyButtonFilterClick = 'mobile.manageProperties.button.filter.click';
  static const String mobileManagePropertyButtonShareClick = 'mobile.manageProperties.button.share.click';
  static const String mobileManagePropertyButtonMicroSiteUrl = 'mobile.manageProperties.button.microSiteUrlCopy.click';
  static const String mobileManagePropertyButtonEdit = 'mobile.manageProperties.button.edit.click';
  static const String mobileManagePropertyButtonArchive = 'mobile.manageProperties.button.archive.click';
  static const String mobileManagePropertyButtonArchiveProcessed = 'mobile.manageProperties.button.archiveProceed.click';
  static const String mobilePropertySharePageView = 'mobile.propertyShare.page.propertyShare.page';
  static const String mobilePropertyShareMessageTemplateClick = 'mobile.propertyShare.button.messageTemplate.click';
  static const String mobilePropertyShareSendMessageClick = 'mobile.propertyShare.button.sendMessage.click';
  static const String mobilePropertyShareCancelClick = 'mobile.propertyShare.button.cancel.click';
  static const String mobilePropertyFilterApplyButtonClick = 'mobile.propertyFilter.button.apply.click';
  static const String mobilePropertyFilterCancelButtonClick = 'mobile.propertyFilter.button.close.click';
  static const String mobilePropertyFilterPageView = 'mobile.propertyFilter.page.propertyFilter.view';
  static const String mobilePropertyInfoReassignClick = 'mobile.propertyInfo.button.reassign.click';
  static const String mobilePropertyInfoMatchingLead = 'mobile.propertyInfo.button.matchingLeads.click';
  static const String mobilePropertyInfoView = 'mobile.propertyInfo.page.propertyInfo.view';
  static const String mobileAddPropertyView = 'mobile.addProperty.page.addProperty.view';
  static const String mobilePropertyInfoShareClick = 'mobile.propertyInfo.button.share.click';
  static const String mobilePropertyInfoEditClick = 'mobile.propertyInfo.button.edit.click';
  static const String mobilePropertyInfoArchiveClick = 'mobile.propertyInfo.button.archive.click';
  static const String mobilePropertyInfoCopyClick = 'mobile.propertyInfo.button.copy.click';
  static const String mobileAddPropertyWantToClick = 'mobile.addProperty.button.wantTo.click';
  static const String mobileAddPropertyPropertyTypeClick = 'mobile.addProperty.button.propertyType.click';
  static const String mobileAddPropertyPropertySubTypeClick = 'mobile.addProperty.button.propertySubType.click';
  static const String mobileAddPropertyBhkClick = 'mobile.addProperty.button.bhk.click';
  static const String mobileAddPropertyBhkTypeClick = 'mobile.addProperty.button.bhk.click';
  static const String mobileAddPropertyHighlightProperty = 'mobile.addProperty.button.highlightProperty.click';
  static const String mobileAddPropertyAssignTo = 'mobile.addProperty.button.assignTo.click';
  static const String mobileAddPropertyPropertySizeUnit = 'mobile.addProperty.button.propertySizeUnit.click';
  static const String mobileAddPropertyCarpetAreaUnit = 'mobile.addProperty.button.carpetAreaUnit.click';
  static const String mobileAddPropertyBuiltAreaUnit = 'mobile.addProperty.button.builtUpAreaUnit.click';
  static const String mobileAddPropertySaleableAreaUnit = 'mobile.addProperty.saleableAreaUnit.button..click';
  static const String mobileAddPropertyPossessionAvailability = 'mobile.addProperty.button.possessionAvailability.click';
  static const String mobileAddPropertyNegotiableClick = 'mobile.addProperty.button.negotiable.click';
  static const String mobileAddPropertyMaintenanceCost = 'mobile.addProperty.button.mainTenanceCost.click';
  static const String mobileAddPropertyTaxationMode = 'mobile.addProperty.button.taxationMode.click';
  static const String mobileAddPropertyProjectClick = 'mobile.addProperty.button.selectProject.click';
  static const String mobileAddPropertyBrokerageAmountUnitClick = 'mobile.addProperty.button.brokerageAmountUnit.click';
  static const String mobileAddPropertyLocationClick = 'mobile.addProperty.button.location.click';
  static const String mobileAddPropertyFurnishStatusClick = 'mobile.addProperty.button.furnishStatus.click';
  static const String mobileAddPropertyFacingClick = 'mobile.addProperty.button.facing.click';
  static const String mobileAddPropertyTotalFloorIncrement = 'mobile.addProperty.button.totalFloorsIncrement.click';
  static const String mobileAddPropertyTotalFloorDecrement = 'mobile.addProperty.button.totalFloorNumberDecrement.click';
  static const String mobileAddPropertyTotalFloorNumberIncrement = 'mobile.addProperty.button.totalFloorNumberIncrement.click';
  static const String mobileAddPropertyTotalFloorNumberDecrement = 'mobile.addProperty.button.totalFloorNumberDecrement.click';
  static const String mobileAddPropertyMaximumOccupantsIncrement = 'mobile.addProperty.button.maximumOccupantsIncrement.click';
  static const String mobileAddPropertyMaximumOccupantsDecrement = 'mobile.addProperty.button.maximumOccupantsDecrement.click';
  static const String mobileAddPropertyKitchenIncrement = 'mobile.addProperty.button.kitchenIncrement.click';
  static const String mobileAddPropertyKitchenDecrement = 'mobile.addProperty.button.kitchenDecrement.click';
  static const String mobileAddPropertyDrawingIncrement = 'mobile.addProperty.button.drawingIncrement.click';
  static const String mobileAddPropertyDrawingDecrement = 'mobile.addProperty.button.drawingDecrement.click';
  static const String mobileAddPropertyBathRoomIncrement = 'mobile.addProperty.button.bathRoomIncrement.click';
  static const String mobileAddPropertyBathRoomDecrement = 'mobile.addProperty.button.bathRoomDecrement.click';
  static const String mobileAddPropertyUtilitiesIncrement = 'mobile.addProperty.button.utilitiesIncrement.click';
  static const String mobileAddPropertyUtilitiesDecrement = 'mobile.addProperty.button.utilitiesDecrement.click';
  static const String mobileAddPropertyBalconiesIncrement = 'mobile.addProperty.button.balconiesIncrement.click';
  static const String mobileAddPropertyBalconiesDecrement = 'mobile.addProperty.button.balconiesDecrement.click';
  static const String mobileAddPropertyNoOfParkingIncrement = 'mobile.addProperty.button.noOfParkingIncrement.click';
  static const String mobileAddPropertyNoOfParkingDecrement = 'mobile.addProperty.button.noOfParkingDecrement.click';
  static const String mobileAddPropertyRating = 'mobile.addProperty.button.rating.click';
  static const String mobileAddPropertyAddPhoto = 'mobile.addProperty.button.addPhoto.click';
  static const String mobileAddPropertyAddBrochures = 'mobile.addProperty.button.addBrochures.click';
  static const String mobileSettingPageView = 'mobile.setting.page.setting.view';
  static const String mobileSettingPageSyncButtonClick = 'mobile.setting.button.syncButton.click';
  static const String mobileSettingPageDownloadCallDetection = 'mobile.setting.button.downloadCallDetection.click';
  static const String mobileSettingPageNotificationReLink = 'mobile.setting.button.notificationReLink.click';
  static const String mobileSettingPageLogOut = 'mobile.setting.button.logout.click';
  static const String mobileSettingSyncPageView = 'mobile.settingSyncPage.page.settingSyncpage.view';
  static const String mobileSettingSyncPageSyncAll= 'mobile.settingSyncPage.button.syncAll.click';
  static const String mobileAttendancePageView= 'mobile.attendance.page.attendance.click';
  static const String mobileAttendanceClockIn= 'mobile.attendance.button.clockIn.click';
  static const String mobileAttendancePageAttendanceHistoryButton= 'mobile.attendance.button.attendanceHistory.click';
  static const String mobileManageProjectPageView= 'mobile.manageProject.page.manageProperty.view';
  static const String mobileManageProjectSearchClick= 'mobile.manageProject.button.search.click';
  static const String mobileManageProjectFilterClick= 'mobile.manageProject.button.projectFilter.click';
  static const String mobileManageProjectArchiveClick= 'mobile.manageProject.button.archive.click';
  static const String mobileManageProjectCopyClick= 'mobile.manageProject.button.microSiteUrlCopy.click';
  static const String mobileManageProjectShareClick= 'mobile.manageProject.button.shareProject.click';
  static const String mobileShareProjectView= 'mobile.projectShare.button.projectShare.view';
  static const String mobileShareProjectMessageTemplateClick= 'mobile.projectShare.button.messageTemplate.click';
  static const String mobileShareProjectSendMessageClick= 'mobile.projectShare.button.sendMessage.click';
  static const String mobileProjectInfoShareProject= 'mobile.projectInfo.button.shareProject.click';
  static const String mobileProjectInfoArchiveProject= 'mobile.projectInfo.button.archiveProject.click';
  static const String mobileDashBoardPageView= 'mobile.dashboard.page.dashboard.view';
  static const String mobileDashBoardActiveClick= 'mobile.dashboard.button.active.click';
  static const String mobileDashBoardBookedClick= 'mobile.dashboard.buttton.booked.click';
  static const String mobileDashBoardBookingCancelClick= 'mobile.dashboard.button.bookingCancel.click';
  static const String mobileDashBoardNotInterestedClick= 'mobile.dashboard.button.notInterested.click';
  static const String mobileDashBoardDroppedClick= 'mobile.dashboard.button.dropped.click';
  static const String mobileDashBoardUnassignedClick= 'mobile.dashboard.button.unassigned.click';
  static const String mobileDashBoardNewClick= 'mobile.dashboard.button.new.click';
  static const String mobileDashBoardPendingClick= 'mobile.dashboard.button.pending.click';
  static const String mobileDashBoardOverDueClick= 'mobile.dashboard.button.overdue.click';
  static const String mobileDashBoardCallBackClick= 'mobile.dashboard.button.callbacks.click';
  static const String mobileDashBoardSiteVisitScheduledClick= 'mobile.dashboard.button.siteVisitScheduled.click';
  static const String mobileDashBoardMeetingScheduledClick= 'mobile.dashboard.button.meetingScheduled.click';
  static const String mobileDashBoardExpressionOfInterestClick= 'mobile.dashboard.button.expressionOfInterest.click';
  static const String mobileDashBoardSiteVisitDoneClick= 'mobile.dashboard.button.siteVisitDone.click';
  static const String mobileDashBoardMeetingDoneClick= 'mobile.dashboard.button.meetingDone.click';
  static const String mobileDashBoardGlobalFilterClick= 'mobile.dashboard.button.globalFilters.click';
  static const String mobileDashBoardSiteVisitFilterClick= 'mobile.dashboard.button.siteVisitFilter.click';
  static const String mobileDashBoardMeetingFilterClick= 'mobile.dashboard.button.meetingFilter.click';
  static const String mobileDashBoardCallsFilterClick= 'mobile.dashboard.button.callsFilter.click';
  static const String mobileDashBoardLeadReceivedFilterClick= 'mobile.dashboard.button.leadsReceivedFilter.click';
  static const String mobileDashBoardLeadPipeLineFilterClick= 'mobile.dashboard.button.leadsPipeLineFilter.click';
  static const String mobileDashBoardActivityOnLeadsFilterClick= 'mobile.dashboard.button.activityOnLeadsFilter.click';
  static const String mobileDashBoardLeadsFromSourceFilterClick= 'mobile.dashboard.button.leadsFromSourceFilter.click';

}
