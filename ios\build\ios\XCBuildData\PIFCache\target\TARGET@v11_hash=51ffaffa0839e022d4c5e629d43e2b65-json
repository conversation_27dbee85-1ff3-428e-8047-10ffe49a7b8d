{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890a36b946852a522d8f5b26461465b71", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c65a38d67a03ec083d0a839f470eeb1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc2c71901bc559dcabc4edac47913a76", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc50040fdd6b78be5026664143aa2df0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc2c71901bc559dcabc4edac47913a76", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986bcb6815f1de79a40dc88d2cab272a37", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c36717fc85722a9e67e23a51f6303899", "guid": "bfdfe7dc352907fc980b868725387e9898ce1dc0f59701c24e2f8e7b7578bf8b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9886ffec9cc8ff93299a91a3799380d85c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f971fb321565a216b7c3c6f1c477d00", "guid": "bfdfe7dc352907fc980b868725387e9878143030fde5ea4c4e5086873b9161bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0cdc1670d87e3cdd88804fa9b040496", "guid": "bfdfe7dc352907fc980b868725387e982b7eca47c20f3a73a55175a4608b3d96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edef60ddaab94b55fc24f37d11c209e", "guid": "bfdfe7dc352907fc980b868725387e9841ed7b4142c9a4fb24635569de7e8e33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edea60fc9bba992ec32f0ad0654b4ebd", "guid": "bfdfe7dc352907fc980b868725387e98fa5641e62f0fd5f3b4f754186c61e4d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901adc13875cc439593790bf7e419df2", "guid": "bfdfe7dc352907fc980b868725387e98f7fb01085c25cef83c2c94e6c8ff0ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c25f144ca228661e1947b317de04c42", "guid": "bfdfe7dc352907fc980b868725387e98c7dd154a823747fdf0518e8449f35e34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19add379d76e7a220ff579108ec4e6e", "guid": "bfdfe7dc352907fc980b868725387e98cba4484b7209f330f592f217cf29ac65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7b90d4b5fec8605ee0408e906a09c0", "guid": "bfdfe7dc352907fc980b868725387e98af768a94a1312a4a1e2c5a633a572659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb26bfce3c8f09a7aa5eb74e422b920d", "guid": "bfdfe7dc352907fc980b868725387e98c093ece4c7b3d0f428aca496dad9b393"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839373b9982b392224b0a8a68a7d22448", "guid": "bfdfe7dc352907fc980b868725387e98967097a82cfbd221c440ce98d719bff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d868abc02280ed32b4b34100e3da0151", "guid": "bfdfe7dc352907fc980b868725387e980d19b143ad91e3468468506b31d78927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca01970345810ffe73945479a77fe26", "guid": "bfdfe7dc352907fc980b868725387e987988f8ef32525347c73300e909219ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca9e6540ee78a9e22cc6c4d17e6fbc7", "guid": "bfdfe7dc352907fc980b868725387e98bc695686151211ee4c1f83d5dffe2204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3687f90d8c9fce73d9d0e2bfb1b6fab", "guid": "bfdfe7dc352907fc980b868725387e98b536e95e2c957ef715783f293d44a3fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989784b25188a41e7c6cc06ffa34ee7d42", "guid": "bfdfe7dc352907fc980b868725387e9830263d27af08d1215f9197a7d4ad9a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98582232685a7c46d09bcf663a1d891d15", "guid": "bfdfe7dc352907fc980b868725387e9871b430a92435f8f486b9972898e6953c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5efeba2652a20cc6582f718f1491e1d", "guid": "bfdfe7dc352907fc980b868725387e98d1dfb2fdcb8f6db4cb9d675da14dedc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b62f22b28ce56af96bd7fbda12f5456", "guid": "bfdfe7dc352907fc980b868725387e98a4b15859d017ab9b6fedea63b7b309a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98809948458eab92986c088b88bed934ca", "guid": "bfdfe7dc352907fc980b868725387e98dd11530bfe0d8b7f625d016e409c46f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4319e3f3c17f0c5b05e8d41e8dabc96", "guid": "bfdfe7dc352907fc980b868725387e9827bbf02f7ef6e2bcf7e2b864fc16a1a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d85acfde01f1d6af8c027c9c2d19ab", "guid": "bfdfe7dc352907fc980b868725387e982df71d2b8f5c5e4b126f57e97097aecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6afd62b84380962e7c16c1e6185686", "guid": "bfdfe7dc352907fc980b868725387e9848d5a3df16df20edfafc816e58450eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cfcc78c05cd3ffdb321dd6e02004ebf", "guid": "bfdfe7dc352907fc980b868725387e989dce871eb7b5931ed3be6fe4d6ca93ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ad071b5e16a8b1ec7b2f2eb1780d358", "guid": "bfdfe7dc352907fc980b868725387e980320ba6eb73304c8f04a48c16b356552"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815600f6c3b32d9a93c9ceda3a7fb38d0", "guid": "bfdfe7dc352907fc980b868725387e9858bd99354b3ef77ced73a7b960218165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2754a0eedaa55335848f3423afb4012", "guid": "bfdfe7dc352907fc980b868725387e987d1a6013389d156eb5b96b3e95891ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e9844137f19c78cae72d9f3f0d55c72", "guid": "bfdfe7dc352907fc980b868725387e9801247f8d6d61e68d2f00c644f55528f4"}], "guid": "bfdfe7dc352907fc980b868725387e988c703f8aba3e36fda250a38202da7eec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8f76d148b86e45f5a4fde2844b1b676", "guid": "bfdfe7dc352907fc980b868725387e9868f9d0000739fd47ea1d9d12dde72dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ca0483875414283457834de9e17fdc", "guid": "bfdfe7dc352907fc980b868725387e981c7fb55ae28beaff47c4e30dfe2653a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e980118626562ba9eb3706f895d5061c3ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989febcf50f0594135daa125ad9e14dbc4", "guid": "bfdfe7dc352907fc980b868725387e9807dbde944e725815f688ca68db8b4291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98d6a95bd2156fe0226c9ed580bba8d01a"}], "guid": "bfdfe7dc352907fc980b868725387e98e789738b3443dc83ff883629346b7032", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d33ca55973201bde585e44d01ea86398", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e986b0910062646e9e633a00dec145fd88c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}