import 'dart:io';
import 'dart:ui' as ui;

import 'package:crypto/crypto.dart';

class PropertyUtils {
  // Updated scoring criteria based on new requirements
  static const int _descriptionPoints = 10;
  static const int _imagesPoints = 6;
  static const int _imageDiversityPoints = 5;
  static const int _imageDuplicationPoints = 18;
  static const int _listingCompletionPoints = 2;
  static const int _locationPoints = 19;
  static const int _titleLengthPoints = 10;
  static const int _listingVerificationPoints = 20;
  static const int _imageDimensionsPoints = 10;

  static const int _minDescriptionLength = 750;
  static const int _maxDescriptionLength = 2000;
  static const int _minTitleLength = 30;
  static const int _maxTitleLength = 50;
  static const int _minImages = 10;
  static const int _minWidth = 1920;
  static const int _minHeight = 1080;

  static Future<int> calculatePropertyListingQualityScore({
    String? description,
    String? title,
    String? locationId,
    bool? isShowManualLocation,
    String? enquiredCity,
    String? enquiredState,
    String? enquiredLocality,
    List<File>? imageFiles,
    String? dldPermitNumber,
    String? dtcmPermit,
    bool? isListingComplete,
  }) async {
    int totalScore = 0;

    // 1. Description (750-2000 characters) - 10 points
    final desc = description ?? '';
    final descriptionLength = desc.length;
    if (descriptionLength >= _minDescriptionLength && descriptionLength <= _maxDescriptionLength) {
      totalScore += _descriptionPoints;
    }

    // 2. Images (Minimum 10 photos) - 6 points
    final imageCount = imageFiles?.length ?? 0;
    if (imageCount >= _minImages) {
      totalScore += _imagesPoints;
    }

    // 3. Image Diversity (All photos must be unique) - 5 points
    if (imageFiles != null && imageFiles.isNotEmpty) {
      final uniqueImageHashes = <String>{};
      for (final file in imageFiles) {
        final hash = await _calculateFileHash(file);
        uniqueImageHashes.add(hash);
      }
      final hasUniqueImages = uniqueImageHashes.length == imageCount && imageCount > 0;
      if (hasUniqueImages) {
        totalScore += _imageDiversityPoints;
      }

      // 4. Image Duplicates (No exact duplicate images) - 18 points
      final exactDuplicates = imageCount - uniqueImageHashes.length;
      if (exactDuplicates == 0 && imageCount > 0) {
        totalScore += _imageDuplicationPoints;
      }

      // 5. Image Dimensions (All images must meet 1920x1080 dimensions) - 10 points
      var invalidSizeImages = 0;

      for (final file in imageFiles) {
        final dimensions = await _getImageDimensions(file);
        if (dimensions == null || dimensions['width']! < _minWidth || dimensions['height']! < _minHeight) {
          invalidSizeImages++;
        }
      }

      if (imageCount > 0 && invalidSizeImages == 0) {
        totalScore += _imageDimensionsPoints;
      }
    }

    // 6. Listing Completion (All mandatory fields filled) - 2 points
    if (isListingComplete == true) {
      totalScore += _listingCompletionPoints;
    }

    // 7. Location (Complete and specific location information) - 19 points
    final hasLocationId = locationId != null && locationId.isNotEmpty;
    final hasManualLocation = isShowManualLocation == true && enquiredCity != null && enquiredCity.isNotEmpty && enquiredState != null && enquiredState.isNotEmpty && enquiredLocality != null && enquiredLocality.isNotEmpty;
    final hasCompleteLocation = hasLocationId || hasManualLocation;
    if (hasCompleteLocation) {
      totalScore += _locationPoints;
    }

    // 8. Title Length (30-50 characters) - 10 points
    final titleText = title ?? '';
    final titleLength = titleText.length;
    if (titleLength >= _minTitleLength && titleLength <= _maxTitleLength) {
      totalScore += _titleLengthPoints;
    }

    // 9. Listing Verification (Listing marked as verified) - 20 points
    final hasPermit = (dldPermitNumber != null && dldPermitNumber.isNotEmpty) || (dtcmPermit != null && dtcmPermit.isNotEmpty);
    if (hasPermit) {
      totalScore += _listingVerificationPoints;
    }

    // Return final score (max 100)
    return totalScore;
  }

  /// Calculate file hash for duplicate detection
  static Future<String> _calculateFileHash(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      // If file reading fails, use file path as fallback
      return file.path.hashCode.toString();
    }
  }

  static Future<Map<String, int>?> _getImageDimensions(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      final image = frame.image;

      return {
        'width': image.width,
        'height': image.height,
      };
    } catch (e) {
      return null;
    }
  }
}
