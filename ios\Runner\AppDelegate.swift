import UIKit
import Flutter
import Photos
import MobileCoreServices
import CoreLocation
import MessageUI
import PhotosUI
import AVFoundation
import Call<PERSON><PERSON>

@main
@objc class AppDelegate: FlutterAppDelegate, MFMessageComposeViewControllerDelegate {

    var resultCallback: FlutterResult?
    var locationManager: CLLocationManager!

    private var audioSession = AVAudioSession.sharedInstance()
    private var callTrackingChannel: FlutterMethodChannel?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
        let channel = FlutterMethodChannel(name: "native_actions", binaryMessenger: controller.binaryMessenger)
        let appConfigChannel = FlutterMethodChannel(name: "app_config", binaryMessenger: controller.binaryMessenger)
        let rootChannel = FlutterMethodChannel(name: "root_security", binaryMessenger: controller.binaryMessenger)

        // Setup call tracking channel
        callTrackingChannel = FlutterMethodChannel(name: "call_tracking", binaryMessenger: controller.binaryMessenger)
        if #available(iOS 10.0, *) {
            CallTrackingManager.shared.setupMethodChannel(callTrackingChannel!)
        }

        channel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            self?.resultCallback = result

            if call.method == "openCamera" {
                self?.checkCameraPermissionAndOpenCamera()
            } else if call.method == "openGalleryMultipleImageSelection" {
                self?.checkPhotoLibraryPermissionAndOpenGallery(isMultiSelect: true)
            } else if call.method == "openGallery" {
                 self?.checkPhotoLibraryPermissionAndOpenGallery(isMultiSelect: false)
             } else if call.method == "openDocuments" {
                self?.openDocuments(isMultiSelect: false)
            } else if call.method == "openDocumentsMultipleDocumentSelection" {
                self?.openDocuments(isMultiSelect: true)
            }else if call.method == "requestLocationPermission" {
                self?.checkLocationPermission()
            }else if call.method == "isMicrophoneActive" {
                self?.checkMicrophoneStatus(result: result)
            }
            else if call.method == "launchSMS" {
                guard let args = call.arguments as? [String: Any],
                      let phoneNumber = args["uri"] as? String,
                      let message = args["message"] as? String else {
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments for launchSMS", details: nil))
                    return
                }
                self?.launchSMS(phoneNumber: phoneNumber, message: message)
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        rootChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            if call.method == "isDeviceJailBroken" {
                result(RootDetection.isDeviceJailBroken())
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        // Setup call tracking channel handler
        callTrackingChannel?.setMethodCallHandler { [weak self] (call, result) in
            guard let self = self else { return }

            if #available(iOS 10.0, *) {
                switch call.method {
                case "startCallTracking":
                    CallTrackingManager.shared.startCallTracking()
                    result(true)

                case "endCallTracking":
                    CallTrackingManager.shared.endCallTracking(reason: "manual_flutter")
                    result(true)

                case "isTrackingCall":
                    result(CallTrackingManager.shared.isCurrentlyTracking())

                case "getCurrentCallDuration":
                    if let duration = CallTrackingManager.shared.getCurrentCallDuration() {
                        result(duration)
                    } else {
                        result(nil)
                    }

                case "getPendingCallData":
                    NSLog("AppDelegate: getPendingCallData method called from Flutter")
                    let pendingCalls = CallTrackingManager.shared.getPendingCallDataFromUserDefaults()
                    NSLog("AppDelegate: Returning \(pendingCalls.count) pending calls to Flutter")
                    result(pendingCalls)

                case "clearPendingCallData":
                    NSLog("AppDelegate: clearPendingCallData method called from Flutter")
                    CallTrackingManager.shared.clearPendingCallDataFromUserDefaults()
                    NSLog("AppDelegate: Pending call data cleared from UserDefaults")
                    result(true)

                default:
                    result(FlutterMethodNotImplemented)
                }
            } else {
                result(FlutterError(
                    code: "UNSUPPORTED_VERSION",
                    message: "CallKit requires iOS 10.0 or later",
                    details: nil
                ))
            }
        }

        appConfigChannel.setMethodCallHandler { [weak self] (call, result) in
                    guard let self = self else { return }

                    switch call.method {
                    case "changeAppIcon":
                        if let arguments = call.arguments as? [String: Any],
                           let iconName = arguments["iconName"] as? String {
                            self.changeAppIcon(to: iconName, result: result)
                        } else {
                            result(FlutterError(
                                code: "INVALID_ARGUMENTS",
                                message: "Invalid arguments for changeAppIcon",
                                details: nil
                            ))
                        }

                    default:
                        result(FlutterMethodNotImplemented)
                    }
                }

                self.applyPersistedAppIcon()


        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    // MARK: Launch SMS
    private func launchSMS(phoneNumber: String, message: String) {
        if MFMessageComposeViewController.canSendText() {
            let messageVC = MFMessageComposeViewController()
            messageVC.messageComposeDelegate = self
            messageVC.recipients = [phoneNumber] // Set the recipient's phone number
            messageVC.body = message // Set the SMS body

            self.window?.rootViewController?.present(messageVC, animated: true, completion: nil)
        } else {
            resultCallback?("SMS_NOT_SUPPORTED")
        }
    }


    private func checkMicrophoneStatus(result: FlutterResult?) {
            do {
                let currentRoute = audioSession.currentRoute
                let isOtherAudioPlaying = audioSession.isOtherAudioPlaying
                let micInUse = currentRoute.inputs.contains { $0.portType == AVAudioSession.Port.builtInMic }
                    && isOtherAudioPlaying

                if let result = result {
                    result(micInUse)
                } else {
                    if let controller = window?.rootViewController as? FlutterViewController {
                        let callTrackingChannel = FlutterMethodChannel(name: "native_actions", binaryMessenger: controller.binaryMessenger)
                        callTrackingChannel.invokeMethod("microphoneStateChanged", arguments: ["active": micInUse])
                    }
                }
            } catch {
                print("Audio session error: \(error)")
                result?(false)
            }
        }

    // MARK: MFMessageComposeViewControllerDelegate
    @objc public func messageComposeViewController(_ controller: MFMessageComposeViewController, didFinishWith result: MessageComposeResult) {
        controller.dismiss(animated: true) {
            switch result {
            case .sent:
                self.resultCallback?("SMS_SENT_SUCCESSFULLY")
            case .cancelled:
                self.resultCallback?("SMS_CANCELLED")
            case .failed:
                self.resultCallback?("SMS_FAILED")
            @unknown default:
                self.resultCallback?("SMS_UNKNOWN_ERROR")
            }
        }
    }

    // (Rest of your code remains the same)
    // MARK: Camera
 private func checkCameraPermissionAndOpenCamera() {
     if UIImagePickerController.isSourceTypeAvailable(.camera) {
         let status = AVCaptureDevice.authorizationStatus(for: .video)

         switch status {
         case .authorized:
             // Permission already granted
             openCamera()

         case .notDetermined:
             // Request permission
             AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                 DispatchQueue.main.async {
                     if granted {
                         self?.openCamera()
                     } else {
                         self?.showCameraPermissionAlert()
                     }
                 }
             }

         case .denied, .restricted:
             // Permission denied or restricted, show alert to guide the user to settings
             showCameraPermissionAlert()

         @unknown default:
             resultCallback?("CAMERA_PERMISSION_UNKNOWN_ERROR")
         }
     } else {
         resultCallback?("NO_CAMERA_AVAILABLE")
     }
 }

 private func showCameraPermissionAlert() {
     guard let topViewController = UIApplication.shared.keyWindow?.rootViewController else { return }

     let alert = UIAlertController(
         title: "Camera Permission Needed",
         message: "To use the camera, please allow access in settings.",
         preferredStyle: .alert
     )

     alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))
     alert.addAction(UIAlertAction(title: "Open Settings", style: .default, handler: { _ in
         if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
             UIApplication.shared.open(settingsURL)
         }
     }))

    topViewController.present(alert, animated: true, completion: nil)
 }

    private func openCamera() {
        let imagePicker = UIImagePickerController()
        imagePicker.delegate = self
        imagePicker.sourceType = .camera
        imagePicker.mediaTypes = [kUTTypeImage as String]
        imagePicker.allowsEditing = false
        self.window?.rootViewController?.present(imagePicker, animated: true, completion: nil)
    }

    // MARK: Gallery
    private func checkPhotoLibraryPermissionAndOpenGallery(isMultiSelect: Bool) {
        let status = PHPhotoLibrary.authorizationStatus()
        if status == .authorized {
            openGallery(isMultiSelect: isMultiSelect)
        } else if status == .notDetermined {
            PHPhotoLibrary.requestAuthorization { [weak self] newStatus in
                if newStatus == .authorized {
                    DispatchQueue.main.async {
                        self?.openGallery(isMultiSelect: isMultiSelect)
                    }
                } else {
                    self?.resultCallback?("GALLERY_PERMISSION_DENIED")
                }
            }
        } else {
            resultCallback?("GALLERY_PERMISSION_DENIED")
        }
    }

    private func openGallery(isMultiSelect: Bool) {
    DispatchQueue.main.async {
                   if #available(iOS 14, *) {
                       var config = PHPickerConfiguration()
                       config.selectionLimit = isMultiSelect ? 0 : 1   // 👈 Allows multiple selection (0 = unlimited)
                       config.filter = .images    // 👈 Restricts selection to images only

                       let picker = PHPickerViewController(configuration: config)
                       picker.delegate = self
                       self.window?.rootViewController?.present(picker, animated: true)
                   } else {
                       let imagePicker = UIImagePickerController()
                       imagePicker.sourceType = .photoLibrary
                       imagePicker.mediaTypes = [kUTTypeImage as String]
                       imagePicker.delegate = self
                       imagePicker.allowsEditing = !isMultiSelect // Disable editing for multi-selection
                       self.window?.rootViewController?.present(imagePicker, animated: true)
                   }
               }
    }

    // MARK: Documents
    private func openDocuments(isMultiSelect: Bool) {
        let documentPicker = UIDocumentPickerViewController(documentTypes: [kUTTypeItem as String], in: .import)
        documentPicker.delegate = self
        documentPicker.allowsMultipleSelection = isMultiSelect //will work above ios11+
        self.window?.rootViewController?.present(documentPicker, animated: true, completion: nil)
    }

    // MARK: Location
    private func checkLocationPermission() {
        locationManager = CLLocationManager()
        locationManager.delegate = self

        if CLLocationManager.locationServicesEnabled() {
            let status = CLLocationManager.authorizationStatus()
            switch status {
            case .notDetermined:
                locationManager.requestWhenInUseAuthorization()
            case .restricted, .denied:
                resultCallback?("LOCATION_PERMISSION_DENIED")
            case .authorizedAlways, .authorizedWhenInUse:
                resultCallback?("LOCATION_PERMISSION_GRANTED")
            @unknown default:
                resultCallback?("LOCATION_PERMISSION_UNKNOWN")
            }
        } else {
            resultCallback?("LOCATION_SERVICES_DISABLED")
        }
    }

    private func saveImageToDocuments(image: UIImage) -> URL? {
                let fileManager = FileManager.default
                let paths = fileManager.urls(for: .documentDirectory, in: .userDomainMask)

                let imageName = UUID().uuidString + ".jpg"
                let fileURL = paths[0].appendingPathComponent(imageName)

                if let imageData = image.jpegData(compressionQuality: 1.0) {
                    do {
                        try imageData.write(to: fileURL)
                        return fileURL
                    } catch {
                        print("Error saving image: \(error)")
                        return nil
                    }
                }
                return nil
            }

     private func changeAppIcon(to iconName: String, result: @escaping FlutterResult) {
            if !UIApplication.shared.supportsAlternateIcons {
                result(FlutterError(
                    code: "UNSUPPORTED",
                    message: "Device doesn't support alternate icons",
                    details: nil
                ))
                return
            }

            let iconToUse: String?

            switch iconName {
            case "default":
                iconToUse = nil
            case "prowinIcon":
                iconToUse = iconName
            case "hjProperties":
                iconToUse = iconName
            case "prowinn":
                iconToUse = iconName
                case "realtorsHub":
                 iconToUse = iconName
                 case "realtorsHubIcon":
                 iconToUse = iconName
                 case "kanjau":
                  iconToUse = iconName
                 case "kanjauIcon":
                 iconToUse = iconName
            default:
                iconToUse = nil
            }

            UIApplication.shared.setAlternateIconName(iconToUse) { error in
                if let error = error {
                    result(FlutterError(
                        code: "ICON_CHANGE_FAILED",
                        message: error.localizedDescription,
                        details: nil
                    ))
                } else {
                    UserDefaults.standard.set(iconName, forKey: "currentAppIcon")
                    result(true)
                }
            }
        }

        private func applyPersistedAppIcon() {
            if let savedIconName = UserDefaults.standard.string(forKey: "currentAppIcon"),
               savedIconName != "default",
               UIApplication.shared.supportsAlternateIcons {
                UIApplication.shared.setAlternateIconName(savedIconName, completionHandler: { error in
                    if let error = error {
                        print("Error reapplying saved icon: \(error.localizedDescription)")
                    }
                })
            }
        }
}

@available(iOS 14, *)
extension AppDelegate: PHPickerViewControllerDelegate {
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)
        let group = DispatchGroup()
        var imageUrls: [String] = []
        for result in results {
            group.enter()
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] (object, _) in
                if let image = object as? UIImage,
                   let imageUrl = self?.saveImageToDocuments(image: image) {
                    imageUrls.append(imageUrl.absoluteString)
                }
                group.leave()
            }
        }
        group.notify(queue: .main) { self.resultCallback?(imageUrls) }
    }
}

// MARK: UIImagePickerControllerDelegate & UINavigationControllerDelegate
extension AppDelegate: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        picker.dismiss(animated: true, completion: nil)

        if let image = info[.originalImage] as? UIImage {
            if let imageUrl = saveImageToDocuments(image: image) {
                resultCallback?(imageUrl.absoluteString)
            } else {
                resultCallback?("FAILED_TO_SAVE_IMAGE")
            }
        } else {
            resultCallback?("NO_IMAGE_SELECTED")
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
        resultCallback?("IMAGE_PICKER_CANCELLED")
    }
}

// MARK: UIDocumentPickerDelegate
extension AppDelegate: UIDocumentPickerDelegate {
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        if urls.isEmpty {
            resultCallback?("NO_DOCUMENT_SELECTED")
        } else {
            // 🔧 Get your app's safe Documents directory
            let fileManager = FileManager.default
            let docsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!

            if controller.allowsMultipleSelection {
                var savedURLs: [String] = []

                for url in urls {
                    // 🔧 Create a unique filename to avoid collisions
                    let fileName = UUID().uuidString + "_" + url.lastPathComponent
                    let destURL = docsURL.appendingPathComponent(fileName)

                    do {
                        // 🔧 Remove old file if it exists at destination
                        if fileManager.fileExists(atPath: destURL.path) {
                            try fileManager.removeItem(at: destURL)
                        }

                        // 🔧 Copy the picked document to Documents folder
                        try fileManager.copyItem(at: url, to: destURL)
                        savedURLs.append(destURL.path) // 🔧 Send safe path back to Flutter
                    } catch {
                        print("Error copying document: \(error)")
                    }
                }

                resultCallback?(savedURLs)
            } else {
                guard let url = urls.first else {
                    resultCallback?("NO_DOCUMENT_SELECTED")
                    return
                }

                // 🔧 Same steps for single file: unique filename, copy to Documents
                let fileName = UUID().uuidString + "_" + url.lastPathComponent
                let destURL = docsURL.appendingPathComponent(fileName)

                do {
                    if fileManager.fileExists(atPath: destURL.path) {
                        try fileManager.removeItem(at: destURL)
                    }

                    try fileManager.copyItem(at: url, to: destURL)
                    resultCallback?(destURL.path) // 🔧 Send safe path to Flutter
                } catch {
                    print("Error copying document: \(error)")
                    resultCallback?("DOCUMENT_COPY_FAILED")
                }
            }
        }
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        controller.dismiss(animated: true, completion: nil)
        resultCallback?("DOCUMENT_PICKER_CANCELLED")
    }
}


// MARK: CLLocationManagerDelegate
extension AppDelegate: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            resultCallback?("LOCATION_PERMISSION_GRANTED")
        case .denied, .restricted:
            resultCallback?("LOCATION_PERMISSION_DENIED")
        case .notDetermined:
            break
        @unknown default:
            resultCallback?("LOCATION_PERMISSION_UNKNOWN")
        }
    }
}

// MARK: - Root Detection
class RootDetection {
    static func isDeviceJailBroken() -> Bool {
        // Don't perform jailbreak detection on simulator
        #if targetEnvironment(simulator)
        return false
        #else
        return checkJailbreakMethod1() || checkJailbreakMethod2() || checkJailbreakMethod3() || checkJailbreakMethod4()
        #endif
    }

    // Method 1: Check for common jailbreak files
    private static func checkJailbreakMethod1() -> Bool {
        let jailbreakPaths = [
            "/Applications/Cydia.app",
            "/Library/MobileSubstrate/MobileSubstrate.dylib",
            "/bin/bash",
            "/usr/sbin/sshd",
            "/etc/apt",
            "/usr/bin/ssh",
            "/private/var/lib/apt/",
            "/private/var/lib/cydia",
            "/private/var/mobile/Library/SBSettings/Themes",
            "/Library/MobileSubstrate/DynamicLibraries/Veency.plist",
            "/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist",
            "/System/Library/LaunchDaemons/com.ikey.bbot.plist",
            "/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist",
            "/private/var/tmp/cydia.log",
            "/private/var/lib/cydia",
            "/private/var/stash",
            "/private/var/db/stash",
            "/private/etc/dpkg/origins/debian",
            "/bin/sh",
            "/usr/libexec/ssh-keysign",
            "/etc/ssh/sshd_config",
            "/usr/libexec/sftp-server",
            "/usr/bin/scp"
        ]
        for path in jailbreakPaths {
            if FileManager.default.fileExists(atPath: path) {
                return true
            }
        }
        return false
    }

    // Method 2: Check if we can write to system directories
    private static func checkJailbreakMethod2() -> Bool {
        let testString = "test"
        let testPath = "/private/test_jailbreak.txt"
        do {
            try testString.write(toFile: testPath, atomically: true, encoding: .utf8)
            try FileManager.default.removeItem(atPath: testPath)
            return true
        } catch {
            return false
        }
    }

    // Method 3: Check for suspicious apps
    private static func checkJailbreakMethod3() -> Bool {
        let suspiciousApps = [
            "cydia://",
            "sileo://",
            "zbra://",
            "undecimus://",
            "checkra1n://",
            "filza://",
            "activator://",
            "winterboard://",
            "ifile://",
            "poof://",
            "mxguardian://",
            "intelliscreen://",
            "firewall://",
            "mywi://",
            "kuaidial://",
            "dockware://",
            "spire://",
            "dreamboard://",
            "cardiohop://",
            "intelliscreen://",
            "mytether://",
            "mywi://",
            "kuaidial://",
            "dockware://",
            "spire://",
            "dreamboard://",
            "cardiohop://"
        ]
        for app in suspiciousApps {
            if let url = URL(string: app) {
                if UIApplication.shared.canOpenURL(url) {
                    return true
                }
            }
        }
        return false
    }

    // Method 4: Check for symbolic links (common in jailbroken devices)
    private static func checkJailbreakMethod4() -> Bool {
        let symbolicLinks = [
            "/Applications",
            "/Library/Ringtones",
            "/Library/Wallpaper",
            "/usr/arm-apple-darwin9",
            "/usr/include",
            "/usr/libexec",
            "/usr/share"
        ]
        for link in symbolicLinks {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: link)
                if let fileType = attributes[FileAttributeKey.type] as? FileAttributeType {
                    if fileType == FileAttributeType.typeSymbolicLink {
                        return true
                    }
                }
            } catch {
                continue
            }
        }
        return false
    }
}