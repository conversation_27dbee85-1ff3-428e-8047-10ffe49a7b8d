// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_anaylsis_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppAnalysisModel _$AppAnalysisModelFromJson(Map<String, dynamic> json) =>
    AppAnalysisModel(
      tenantId: json['tenantId'] as String?,
      userId: json['userId'] as String?,
      refId: json['refId'] as String?,
      featureId: json['featureId'] as String?,
      actionId: json['actionId'] as String?,
      source: json['source'] as String?,
      deviceId: json['deviceId'] as String?,
      deviceType: json['deviceType'] as String?,
      osVer: json['osVer'] as String?,
      appVer: json['appVer'] as String?,
      iP: json['iP'] as String?,
    );

Map<String, dynamic> _$AppAnalysisModelToJson(AppAnalysisModel instance) =>
    <String, dynamic>{
      if (instance.tenantId case final value?) 'tenantId': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.refId case final value?) 'refId': value,
      if (instance.featureId case final value?) 'featureId': value,
      if (instance.actionId case final value?) 'actionId': value,
      if (instance.source case final value?) 'source': value,
      if (instance.deviceId case final value?) 'deviceId': value,
      if (instance.deviceType case final value?) 'deviceType': value,
      if (instance.osVer case final value?) 'osVer': value,
      if (instance.appVer case final value?) 'appVer': value,
      if (instance.iP case final value?) 'iP': value,
    };
