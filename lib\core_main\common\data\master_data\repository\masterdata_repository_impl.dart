import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/repository/custom_amenities_and_attributes_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/remote/masterdata_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/enums/app_enum/user_modified_date_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

import '../models/master_user_services_type_model.dart';

class MasterDataRepositoryImpl implements MasterDataRepository {
  final MasterDataLocalDataSource _localDataSource;
  final MasterDataRemoteDataSource _remoteDataSource;

  MasterDataRepositoryImpl(this._localDataSource, this._remoteDataSource);

  @override
  Future<List<MasterAreaUnitsModel?>?> getAreaUnits({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterAreaUnit,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterAreaUnit,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getAreaUnits();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAreaUnits(timeout: duration);
        if (response != null) {
          await _localDataSource.saveAreaUnits(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterProjectTypeModel?>?> getProjectTypes({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    final lastModifiedDateList = _localDataSource.getModifiedDateModels();

    var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
          (element) => element.entityType == EntityTypeEnum.tempProjects,
        ) ??
        ModifiedDateModel(
          entityType: EntityTypeEnum.tempProjects,
          lastModifiedDate: DateTime.now(),
          lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
        );

    final localData = _localDataSource.getProjectTypes();

    if (localData == null || restore || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
      final response = await _remoteDataSource.getProjectTypes(timeout: duration);
      if (response != null) {
        await _localDataSource.saveProjectTypes(response);
        lastModifiedModel = lastModifiedModel.copyWith(
          lastUpdatedLocallyDate: DateTime.now(),
        );
        await _localDataSource.updateModifiedDateModel(lastModifiedModel);
        return response;
      }
    }

    return localData;
  }

  @override
  Future<Map<EntityTypeEnum, DateTime>?> getModifiedDates({Duration duration = const Duration(seconds: 60)}) async {
    try {
      final remoteData = await _remoteDataSource.getModifiedDates(timeout: duration);
      if (remoteData == null) return null;

      final localModels = _localDataSource.getModifiedDateModels();

      final localDataMap = {for (var model in localModels) model.entityType!: model};

      final updatedModels = <ModifiedDateModel>[];

      for (var entry in remoteData.entries) {
        final entityType = entry.key;
        final remoteDate = entry.value;

        Map<UserModifiedDateEnum, ModifiedDateModel>? userModifiedDates = {};
        if (entityType == EntityTypeEnum.userDetails) {
          for (var userModifiedDateEnum in UserModifiedDateEnum.values) {
            userModifiedDates[userModifiedDateEnum] = ModifiedDateModel(
              entityType: entityType,
              lastModifiedDate: remoteDate,
              lastUpdatedLocallyDate: localDataMap[entityType]?.userModifiedDates?[userModifiedDateEnum]?.lastUpdatedLocallyDate,
            );
          }
        }
        if (localDataMap.containsKey(entityType)) {
          final localModel = localDataMap[entityType]!;
          updatedModels.add(
            ModifiedDateModel(
              entityType: entityType,
              lastModifiedDate: remoteDate,
              lastUpdatedLocallyDate: localModel.lastUpdatedLocallyDate,
              userModifiedDates: userModifiedDates,
            ),
          );
        } else {
          updatedModels.add(
            ModifiedDateModel(
              entityType: entityType,
              lastModifiedDate: remoteDate,
              lastUpdatedLocallyDate: null,
              userModifiedDates: userModifiedDates,
            ),
          );
        }
      }
      if (updatedModels.isNotEmpty) {
        await _localDataSource.saveModifiedDateModels(updatedModels);
      }
      return remoteData;
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterAssociatedBankModel>?> getAssociatedBank({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final result = await _remoteDataSource.getAssociatedBank(timeout: duration);
      return result;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterLeadSourceModel>?> getLeadSource({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.source,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.source,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getLeadSource();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getLeadSource(timeout: duration);
        if (response != null) {
          await _localDataSource.saveLeadSource(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterLeadStatusModel>?> getLeadStatuses({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.customMasterLeadStatus,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.customMasterLeadStatus,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getLeadStatuses();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getLeadStatuses(timeout: duration);
        final globalSettings = await getIt<GlobalSettingRepository>().getGlobalSettings();
        if (globalSettings?.shouldRenameSiteVisitColumn ?? false) {
          MasterLeadStatusModel? replaceSiteVisit = response?.where((element) => element.status == 'site_visit_scheduled').toList().firstOrNull;
          response?.remove(replaceSiteVisit);
          replaceSiteVisit = replaceSiteVisit?.copyWith(displayName: 'Referral Scheduled');
          response?.add(replaceSiteVisit!);
        }
        if (response != null) {
          await _localDataSource.saveLeadStatuses(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<Map<String, List<MasterProjectAmenititesModel>>?> getProjectAmenitites({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyAmenity,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyAmenity,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getProjectAmenitites();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getProjectAmenitites(timeout: duration);
        if (response != null) {
          await _localDataSource.saveProjectAmenitites(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterProjectAttributeModel>?> getProjectAttributes({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyAttribute,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyAttribute,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getProjectAttributes();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getProjectAttributes(timeout: duration);
        if (response != null) {
          await _localDataSource.saveProjectAttributes(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyAmenitites({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyAmenity,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyAmenity,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getPropertyAmenitites();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getPropertyAmenitites(timeout: duration);
        if (response != null) {
          await _localDataSource.savePropertyAmenitites(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterPropertyAttributesModel>?> getPropertyAttributes({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyAttribute,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyAttribute,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getPropertyAttributes();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getPropertyAttributes(timeout: duration);
        if (response != null) {
          await _localDataSource.savePropertyAttributes(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterPropertyTypeModel>?> getPropertyTypes({bool restore = false, bool isPropertyListingEnabled = false, bool isCustomFormEnabled = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyType,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyType,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getPropertyTypes();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getPropertyTypes(timeout: duration);
        if (response != null) {
          await _localDataSource.savePropertyTypes(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  // @override
  // Future<List<MasterPropertyTypeModel>?> getCustomPropertyTypes() async {
  //   try {
  //     return await _remoteDataSource.getCustomPropertyTypes();
  //   } catch (e) {
  //     e.logException();
  //     return null;
  //   }
  // }

  @override
  Future<List<MasterUserServicesModel>?> getUserServices({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.userDetails,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getUserServices();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getMasterUserServices(timeout: duration);
        if (response != null) {
          await _localDataSource.saveUserServices(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<ModifiedDateModel>?> getLocalLastModifiedDates({Duration duration = const Duration(seconds: 60)}) async {
    try {
      return _localDataSource.getModifiedDateModels();
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterCustomStatusModel>?> getCustomStatus({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.customMasterLeadStatus,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.customMasterLeadStatus,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getLeadCustomStatus();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getCustomStatus(timeout: duration);
        if (response != null) {
          await _localDataSource.saveCustomStatus(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<String>?> getAgencyNames({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.agencyNames,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.agencyNames,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getAgencyNames();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAgencyNames(timeout: duration);
        if (response != null) {
          await _localDataSource.saveAgencyNames(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<String>?> getAllLeadAddresses({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.leadAddresses,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.leadAddresses,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getLeadAddresses();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAllLeadAddress(timeout: duration);
        if (response != null) {
          await _localDataSource.saveLeadAddresses(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterPropertyTypeModel>?> getListingPropertyTypes({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyType,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyType,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getListingPropertyTypes();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getListingPropertyTypes(timeout: duration);
        if (response != null) {
          await _localDataSource.saveListingPropertyTypes(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<List<MasterPropertyTypeModel>?> getCustomPropertyTypes({bool isPropertyListingEnabled = false, bool isCustomFormEnabled = false, Duration duration = const Duration(seconds: 60)}) async {
    if (isPropertyListingEnabled || isCustomFormEnabled) {
      return await getListingPropertyTypes();
    } else {
      return await getPropertyTypes();
    }
  }

  @override
  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyListingAmenitites({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.masterPropertyAmenity,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.masterPropertyAmenity,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getPropertyListingAmenitites();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getPropertyAmenitites(isPropertyListingEnabled: true, timeout: duration);
        if (response != null) {
          await _localDataSource.savePropertyListingAmenitites(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> initMasterData({Duration timeoutDuration = const Duration(seconds: 30)}) async {
    try {
      final modifiedDates = await getModifiedDates(duration: timeoutDuration);
      if (modifiedDates == null) return;
      final allFutures = [
        getIt<UsersDataRepository>().getUser(duration: timeoutDuration),
        getIt<UsersDataRepository>().getAllUsers(duration: timeoutDuration),
        getIt<UsersDataRepository>().getAdminsAndReportee(duration: timeoutDuration),
        getPropertyTypes(duration: timeoutDuration),
        getListingPropertyTypes(duration: timeoutDuration),
        getLeadSource(duration: timeoutDuration),
        getAreaUnits(duration: timeoutDuration),
        getPropertyListingAmenitites(duration: timeoutDuration),
        getProjectTypes(duration: timeoutDuration),
        getProjectAmenitites(duration: timeoutDuration),
        getIt<GlobalSettingRepository>().getGlobalSettings(duration: timeoutDuration),
        getIt<CustomAmenitiesAndAttributesRepository>().getCustomAmenities(timeout: timeoutDuration),
        getIt<CustomAmenitiesAndAttributesRepository>().getCustomAttributes(timeout: timeoutDuration),
        getIt<CustomAmenitiesAndAttributesRepository>().getCustomAmenityCategories(timeout: timeoutDuration),
      ];

      await Future.wait(allFutures);
    } catch (ex) {
      ex.logException();
    }
  }
}
