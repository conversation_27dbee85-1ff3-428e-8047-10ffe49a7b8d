{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985486d6dece04fb75e04e60f1a0471e63", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf18c06fcc8b3e1b0cd3f158c7280c51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf18c06fcc8b3e1b0cd3f158c7280c51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989dced73919e14391eb63a2dd5dbad1c8", "guid": "bfdfe7dc352907fc980b868725387e985a63d93e93828a5ffb1b1c0bfc20e3b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14733c242b58f27c8cd1fe19d8157a6", "guid": "bfdfe7dc352907fc980b868725387e983578620150ec4cfaced272678fe44d7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f0935a4da290a9027fdc60008f7dfc", "guid": "bfdfe7dc352907fc980b868725387e98c18de3ab5182d84c9c0175c3aba0fd45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613cef3161dd29e7b2c6ece9743c653f", "guid": "bfdfe7dc352907fc980b868725387e984a297d3f9b295183e3167ce8f3e637d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986edd651c63195b78a21d06b72966de46", "guid": "bfdfe7dc352907fc980b868725387e9809e32571848693982a1e49d490acd168", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989248e4ecc0db1538d0d9f95e5a3b236d", "guid": "bfdfe7dc352907fc980b868725387e983177711f1a68140586480fe8ab8e1148", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e7b0878e191eee7060059aecb7c354", "guid": "bfdfe7dc352907fc980b868725387e982b94efcba5bcf3d6f079d46b014c2ba9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa52aa5a722c92e32645aa69c6b5f6b", "guid": "bfdfe7dc352907fc980b868725387e983267a210d5ed3f2377367aac18fb611d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873f81b00999ea9ec33d99e7b003efde3", "guid": "bfdfe7dc352907fc980b868725387e985fb5e30d85c549d0f46d6de6bd67c457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983506d549db497478fedc28d461ccc315", "guid": "bfdfe7dc352907fc980b868725387e986f14500ccefea82da288481b5dddcdb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cece9b15b49ce46d077f40703e6461ac", "guid": "bfdfe7dc352907fc980b868725387e98bf0abbb8ec3561691b61d3b58ceda1cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08909405b78dfb4d1ec942b3bef7baf", "guid": "bfdfe7dc352907fc980b868725387e985b0d38c3b75a6fa0957c0dfece06f84e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f028c7e27a72d47b129bfbf4b95004e9", "guid": "bfdfe7dc352907fc980b868725387e98eaad785e8502dba1ac89989df49e5651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccfbb7f86582dce97a01d13c8170319b", "guid": "bfdfe7dc352907fc980b868725387e980ae95d60b4f1ad46f709cd42b28b9c54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d3f8d6633ff7baeac11ac6016be0702", "guid": "bfdfe7dc352907fc980b868725387e98001edf29f30f3a0d6968f1b9e63b7c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb9cc2456eb320a125bbbede0f875c1", "guid": "bfdfe7dc352907fc980b868725387e98cdb7002555716bf47bd7586b4ac8236d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98238d4b03140c42936516f618afb8b306", "guid": "bfdfe7dc352907fc980b868725387e98afe46c3822400c8579fd6865c4ec06a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819d566d9ed690dde3c0c2a6f7e24c1cb", "guid": "bfdfe7dc352907fc980b868725387e982e652fc935434bcee769772a3371419c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c11472e2dd06222cd48edfa32328bf6", "guid": "bfdfe7dc352907fc980b868725387e988747aaddd72cf806c7f759385ed906c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98153ac09f4714d244e7e76f352d9e6096", "guid": "bfdfe7dc352907fc980b868725387e9863e009fb137902e81297ce1c64bc2c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a2dca66b3d5a62d0728fd456c4275b", "guid": "bfdfe7dc352907fc980b868725387e981738f2fe66b2f1157c6d5bcd6723dcff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b14e83bd33ab1b5022dabff3f59b7d", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04c426bbf213cff1d323dc9ce3f01bc", "guid": "bfdfe7dc352907fc980b868725387e982c00cb8e7484561ce8a52e3103d40d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc8575234d5eaab494d2d446d1c3001", "guid": "bfdfe7dc352907fc980b868725387e984a6c13a334bae14cd96094284d90a4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32726c7b3209912412429f33a5b41ff", "guid": "bfdfe7dc352907fc980b868725387e9892d9842ee650c2ef18ab68a744a8c35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934a3d5eecadf4f27b7a8579b7961989", "guid": "bfdfe7dc352907fc980b868725387e982e1ea7128555c3484de12aa0d21baa58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db75bf48605f9276959fc4146a5bb422", "guid": "bfdfe7dc352907fc980b868725387e986c53e4a9cb33b77a82c428c58f00d6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116b23b3b6272183e466ee24a8606437", "guid": "bfdfe7dc352907fc980b868725387e98f12b35618e3c290a4a26cbd67c9c0700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985427f4d4dd26dd7a064676c353a7c163", "guid": "bfdfe7dc352907fc980b868725387e98939bb9403a0fe28be3dcaf7b38e459ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e16f83f5a204174628e8ac5cfbf0c3d", "guid": "bfdfe7dc352907fc980b868725387e9837a212af2ef8d8299b0af7555e8de3dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a6db65f23bc543f99d595349e8273a", "guid": "bfdfe7dc352907fc980b868725387e98ba539d3d57137bdb21a3b8d3ebb5b664"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}