// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_property_amenitites_map_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterPropertyAmenititesMapModelAdapter
    extends TypeAdapter<MasterPropertyAmenititesMapModel> {
  @override
  final int typeId = 35;

  @override
  MasterPropertyAmenititesMapModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterPropertyAmenititesMapModel(
      key: fields[0] as String,
      values: (fields[1] as List).cast<MasterPropertyAmenitiesModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, MasterPropertyAmenititesMapModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.key)
      ..writeByte(1)
      ..write(obj.values);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterPropertyAmenititesMapModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
