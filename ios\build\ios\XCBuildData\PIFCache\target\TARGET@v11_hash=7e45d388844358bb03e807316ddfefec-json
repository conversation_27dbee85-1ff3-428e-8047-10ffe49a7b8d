{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b7958c019f9cfb1dbdafd0cb485146a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980376258cf776abf8ce6cb4e71cf0e5c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9813a2aa6914f7ccf270016a3fa79eaecb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882724d5ad0240f81686d533b9f06f575", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9813a2aa6914f7ccf270016a3fa79eaecb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822f672670fb4d72497b3a2f5eeb8b914", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871dff1033677f1b5ee8fcaa933f49b5b", "guid": "bfdfe7dc352907fc980b868725387e986c283334b0f78e3ce7f3995e776821e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b6798730135e2a45b7493d65d3b923", "guid": "bfdfe7dc352907fc980b868725387e989118f7791c2917f8c155075204020844", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f317da4f660f6450ce781d01d0da4d7", "guid": "bfdfe7dc352907fc980b868725387e98edd755837502d44416c150d3b1850839", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985370e6a705d2b61bc5761d9aa1530cf0", "guid": "bfdfe7dc352907fc980b868725387e98c1c06d18c4cc9266c1b3785d6953d65e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b576f8149b2f2c52cd43bc0af08ebaf1", "guid": "bfdfe7dc352907fc980b868725387e98815d7e43900e1d49bfa3acf6cf130c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a6b03a0ea7e245d3adaecb42eba1b0", "guid": "bfdfe7dc352907fc980b868725387e9890c9a0c5d198c44843a651b0fa6b7e3e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bcec3892e3fccf55522c44e10b4fb6d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983fdadd11d9d6be4dddca21456506ea01", "guid": "bfdfe7dc352907fc980b868725387e98d19bd8b452c2d89908a483f66d228717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa68a6eabe44c9608c1bc5d8e49f9a3e", "guid": "bfdfe7dc352907fc980b868725387e983440d50d1770549113d8bc94de8edd25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832d4656aedb84f1e071ab5057c07c059", "guid": "bfdfe7dc352907fc980b868725387e983d7985d437801894b0bbf80f5e1f8861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcccfbdffb27f6f5bc5c69a27d51a983", "guid": "bfdfe7dc352907fc980b868725387e98274fb2600683282693736ea4688ae907"}], "guid": "bfdfe7dc352907fc980b868725387e989bfdf2eedd58ad6f23c1b542e8c26b26", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981c7a2244944853703838fa76d2259dfb", "guid": "bfdfe7dc352907fc980b868725387e98ef962ae0db04c0df1232eb871c3b9a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98da5cb7aeacf8fec8deff4d47b3d76817"}], "guid": "bfdfe7dc352907fc980b868725387e98c3b08a8dbb89f44cf4421f62f39fd87e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a3b4fb766144d79e871a4ac6cdcde678", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp"}], "guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a513e2d8d894b8762a51df43e9ec3f83", "name": "SDWebImageWebPCoder.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}