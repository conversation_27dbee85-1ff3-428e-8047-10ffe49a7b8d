{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f1679943766dde685ea0c27f8781ea6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9861fcf487d07c36ec5f69b10c481512eb", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9861fcf487d07c36ec5f69b10c481512eb", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c45bd4d2ddd540b191e97231dd1ae96c", "guid": "bfdfe7dc352907fc980b868725387e9817011278146d850ad8c1800732eb1e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e02284dd0f325abcfd94694f40b2a6b6", "guid": "bfdfe7dc352907fc980b868725387e980ac0e33a793cc19ff4abfb357eae768f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ae440eac96247558a9fc162c0c26e6", "guid": "bfdfe7dc352907fc980b868725387e989d6f395bb26cbecbfd60c1405ceff351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e3044b2659885dcfbee7fecec7ed16", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819a2c4251cbeb380eab305337157cc64", "guid": "bfdfe7dc352907fc980b868725387e984a42ac0fe50661fb4253f6986ea45bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b58f81f2110a070bee6f9812dded0775", "guid": "bfdfe7dc352907fc980b868725387e98de0db65b473b31d0081f4a5269702284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f693ce9dc162f82a4f18d744010ea9", "guid": "bfdfe7dc352907fc980b868725387e9860e7129d76385a521f0b9fd136393b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff9f65870f8d57fba312200d0883835b", "guid": "bfdfe7dc352907fc980b868725387e98c102fd841861eee3b0b8bacef085b917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492a7caae2bd0c0fd78698029ae445b4", "guid": "bfdfe7dc352907fc980b868725387e98f8633a22aa08547c812ac9bec8b70ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a786ebff31e681feb7fee14a74ae85c", "guid": "bfdfe7dc352907fc980b868725387e98daf47c85e530f3405587d1d40166baa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d4941bd7bdcf96febb38d349caaabe7", "guid": "bfdfe7dc352907fc980b868725387e98acadf1494a3e3cd73f3caf0731d75f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed90f54c60232acc3d8a429d67f634e8", "guid": "bfdfe7dc352907fc980b868725387e98cdf6a5c0e94262f9f4ac605bffef3bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1668563a6ef8c6e5e4f8f859227c79", "guid": "bfdfe7dc352907fc980b868725387e98749c0e50cc6ab0c5d2ae99062d78bd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443477eaed1c371c5d62665a8e862627", "guid": "bfdfe7dc352907fc980b868725387e98b60c9e9460d623e196c3542325e313b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98050bb7bf2ac0eb6bfdf95a6e3780bc0d", "guid": "bfdfe7dc352907fc980b868725387e984176d1afb1f259dfdc07efe17ea652cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c3a2205ab128966d58113ed893ed48", "guid": "bfdfe7dc352907fc980b868725387e98bc8f2f12d75589e115d9449c5986f9fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984567814e45912b6b13fc8da401c5868d", "guid": "bfdfe7dc352907fc980b868725387e9818b5f752d3526a5d027411efb16100bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e6b3c6168d4db196ebbec6a59a619a5", "guid": "bfdfe7dc352907fc980b868725387e989de49e9d16c76d6cdb2cd05defe828b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989daccc2d6ba14b1358dd9be17d4fbed6", "guid": "bfdfe7dc352907fc980b868725387e980869c8041a3db4283bedf358c81f931e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986015646684a321d221579143ddb7acf2", "guid": "bfdfe7dc352907fc980b868725387e98c1f1e163902f92d3f444c8eef23fe9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862aa2bb885c73018c41e60365c024e6b", "guid": "bfdfe7dc352907fc980b868725387e98ae14d59b03d674cb5e1bad0f3d076dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3734630b618c0219089ce807b1df0e1", "guid": "bfdfe7dc352907fc980b868725387e98edc2a89fb1a6e200ab1412ec87f9c9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f01bd650f9dff8e256a10266bd8d273", "guid": "bfdfe7dc352907fc980b868725387e98492b5866dd16c57c285b5e79c6867b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aab8b3af7b903193f57ba7b0abe6c62", "guid": "bfdfe7dc352907fc980b868725387e98e484c4f5150fbf8fabdad69f35a52c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980961e99a6cf10fd29c4376612ccdbe76", "guid": "bfdfe7dc352907fc980b868725387e98d15e2dd2c99fea1c18b18e69ffd1b703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51ffc785b0af2827551c3c4a311352d", "guid": "bfdfe7dc352907fc980b868725387e986fee5e9c3343f8d1008352e701bc900e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96babb59dfd09c3491f422cfc2bab47", "guid": "bfdfe7dc352907fc980b868725387e98a3b33d70365c2c21c6e5ce5c403a6767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03fa5b1c2b6758d97c3e4e04f854494", "guid": "bfdfe7dc352907fc980b868725387e98bc874e43a4cb78d8f4d1a0ac40c942bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9805f317799122b2084cb77905a0dc45f8", "guid": "bfdfe7dc352907fc980b868725387e9829d2b0b2b7d95a9ddf7e598d6ffc4db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe59ccef4e82415ee72f6b3893fd0c9", "guid": "bfdfe7dc352907fc980b868725387e988ad79f08695ea6c2cca3c5d7d8b8e33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a75efa7e612d185661030c837b09a158", "guid": "bfdfe7dc352907fc980b868725387e98eb8f6a719f355b17f23d8eb2e9a5d44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fff41ef32c1cd4f1c513fda9751d0ca", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f55c53c634ce85d77bbf0f7d04ed8472", "guid": "bfdfe7dc352907fc980b868725387e98838df245c5366c7ccd43779dddceac8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b405a72bb1b669bdfa92bcf00187ac", "guid": "bfdfe7dc352907fc980b868725387e980cc73214077d885d0ddd9bbb802b0c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814fff02b147eea785387c493100b2079", "guid": "bfdfe7dc352907fc980b868725387e987699d61d47db16d864283d0af14a2a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af1bebc81b993769aeacf51ef2658e61", "guid": "bfdfe7dc352907fc980b868725387e98e97804a71a7eda4782f6d18bbb55dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835787fede786dcb5461cc12ceb37c73a", "guid": "bfdfe7dc352907fc980b868725387e98d47beba43c550f9d5c74eaccd5ff08b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827de73d7070cfebaa3667385597d6fea", "guid": "bfdfe7dc352907fc980b868725387e982130a66579435704115f7e00d09f53d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46ff9834fdafb392f56f66b5c9e503c", "guid": "bfdfe7dc352907fc980b868725387e98163ea20656902ae2aebec0d2b7d2c959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f003442b36fcbeb32e7f26686ee1600d", "guid": "bfdfe7dc352907fc980b868725387e98c065af2abb2799503e76158497948974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3d7beb6e53a554132468195d9374400", "guid": "bfdfe7dc352907fc980b868725387e986c074fcd043656e86edc370b80f2b6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98095ec9387434797a727b38f8f71f9ae8", "guid": "bfdfe7dc352907fc980b868725387e98897022fb7c83452617bb5421eee661b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b9a3b7086a1d49a41bc17bc94e3b78", "guid": "bfdfe7dc352907fc980b868725387e98cfe537ef6fd48aebe0e7b866f6b1142d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c548ce17efec42b77a25b2aa48e978f7", "guid": "bfdfe7dc352907fc980b868725387e988830eb10a635306371e79be09704e0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889df696f5bf7023b1acfaa1c7ae76471", "guid": "bfdfe7dc352907fc980b868725387e989ac43435114fcf14579f257bc3f9c364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df35da05a021bd052265c37411a6460", "guid": "bfdfe7dc352907fc980b868725387e985cf8026ab77fe57445a9e11bc4c4c7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c84c4166f056faf1f43e8aed98feca53", "guid": "bfdfe7dc352907fc980b868725387e98ef071ab9cef1d242de5b1113119d33b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e22861ee67d4448ed283e3e35db83d35", "guid": "bfdfe7dc352907fc980b868725387e9852ddaf4c3962f25802ecf6e4d216c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98accbc7ab60fbe66e1b578d75b10f8031", "guid": "bfdfe7dc352907fc980b868725387e98abfa7315b06c10451197abd58e79d711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edcb90501f16421bbdcc32aa5994b06b", "guid": "bfdfe7dc352907fc980b868725387e987f00ce95bbef4bcae9c2b6d4ab613e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880de1788d36cb3b5b61bbc61b906555f", "guid": "bfdfe7dc352907fc980b868725387e981243c2de66d0689b6cc84852675e392f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f985742080c0493f607e94dd263ad1", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e7eb44ba9c0b257d1bf51c2035a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2872a50353b6530ebcab88814c24d9c", "guid": "bfdfe7dc352907fc980b868725387e987e1026d7e32253d02e2a13c87719a604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a8f61ba1888a98eaab17faaa3362b3", "guid": "bfdfe7dc352907fc980b868725387e98fe57562c583a8a215f5783496b1ef801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989116c73560c80b1e462211f7a55b3d02", "guid": "bfdfe7dc352907fc980b868725387e98aa36efef915c33b7edff59f6a9a0bbfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2aa8ca6fd7b102d20b5e42f02777c1", "guid": "bfdfe7dc352907fc980b868725387e98fd0b1420ef9ecbffd05e08127fc813c3"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}