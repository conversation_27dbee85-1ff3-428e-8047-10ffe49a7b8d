import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class LeadratFormButton extends LeadratStatelessWidget {
  final String buttonText;
  final VoidCallback onPressed;
  final bool isTrailingVisible;
  final double trailingIconSpacing;
  final bool isShadowVisible;
  final bool isEnabled;

  const LeadratFormButton({
    super.key,
    required this.onPressed,
    required this.buttonText,
    this.isTrailingVisible = false,
    this.trailingIconSpacing = 5,
    this.isShadowVisible = true,
    this.isEnabled = true,
  });

  @override
  Widget buildContent(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (isEnabled) onPressed();
      },
      child: Container(
        constraints: BoxConstraints(minWidth: context.width(35)),
        decoration: BoxDecoration(
          color: ColorPalette.primaryDarkColor,
          border: Border.all(color: const Color(0xFF3D3D3D)),
          borderRadius: BorderRadius.circular(100),
          boxShadow: isShadowVisible
              ? const [
                  BoxShadow(
                    color: Color(0xFF1f2020),
                    blurRadius: 8.0,
                    offset: Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Color(0xFF1f2020),
                    blurRadius: 8.0,
                    offset: Offset(-4, -4),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: isTrailingVisible ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
          children: [
            Flexible(
              fit: FlexFit.loose,
              child: Padding(
                padding: EdgeInsets.only(top: 16, bottom: 16, left: 20, right: isTrailingVisible ? 5 : 20),
                child: Text(
                  buttonText,
                  textAlign: TextAlign.center,
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.primary,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            if (isTrailingVisible)
              Padding(
                padding: EdgeInsets.only(right: 5, left: trailingIconSpacing), // Adjust padding as needed
                child: SvgPicture.asset(
                  ImageResources.iconCircularRightArrow,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
