import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'module_name_enum.g.dart';

@HiveType(typeId: HiveModelConstants.moduleNameEnumTypeId)
@JsonEnum(valueField: 'index')
enum ModuleName {
  @HiveField(0)
  lead,
  @HiveField(1)
  todo,
  @HiveField(2)
  integration,
  @HiveField(3)
  user,
  @HiveField(4)
  profile,
  @HiveField(5)
  project,
  @HiveField(6)
  property,
  @HiveField(7)
  team,
  @HiveField(8)
  email,
  @HiveField(9)
  invoice,
  @HiveField(10)
  unit
}
