import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'property_attribute_field_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.propertyAttributeFieldTypeTypeId)
@JsonEnum(valueField: 'index')
enum PropertyAttributeFieldType {
  @HiveField(0)
  bool,
  @HiveField(1)
  eNum,
  @HiveField(2)
  int,
}
