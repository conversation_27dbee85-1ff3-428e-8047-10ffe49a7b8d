class WebSocketConnectionException implements Exception {
  final String message;
  WebSocketConnectionException(this.message);

  @override
  String toString() => 'WebSocketConnectionException: $message';
}

class WebSocketSendMessageException implements Exception {
  final String message;
  WebSocketSendMessageException(this.message);

  @override
  String toString() => 'WebSocketSendMessageException: $message';
}

class WebSocketDisconnectionException implements Exception {
  final String message;
  WebSocketDisconnectionException(this.message);

  @override
  String toString() => 'WebSocketDisconnectionException: $message';
}