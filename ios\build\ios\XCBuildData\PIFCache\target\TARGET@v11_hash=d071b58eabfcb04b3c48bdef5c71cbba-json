{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ffe108ae7eafd8399d41b143802249cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980064cbcec0b4ec286b9be75c61a1f624", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980064cbcec0b4ec286b9be75c61a1f624", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb753900c0d20b66f4260688ac001ad3", "guid": "bfdfe7dc352907fc980b868725387e98fadb486075b285ced26dc074123c7de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de64244cb18e603e04f4da97781c2f5", "guid": "bfdfe7dc352907fc980b868725387e982fe32e357c24218ffd83a8755a5aef4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b217fd6013fc09c6cc24097e051794c", "guid": "bfdfe7dc352907fc980b868725387e984e8ef2d0871966b0e1c3eb4e1dabb288", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ce99694669155fd4cfbe6a43efdfe1", "guid": "bfdfe7dc352907fc980b868725387e98f617d1f5eb7c59ea0dbb9b965cfd32e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c8374244291d6be780c28f69e1350e", "guid": "bfdfe7dc352907fc980b868725387e9845e19ba25df26c3ccc091052c94434e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b56d5bfe1ca8280bcec27acf227b375", "guid": "bfdfe7dc352907fc980b868725387e980f66a8466556e02887e640a18a15d2a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b735d59226ce4f0dfcbc37f2bc9ba3bf", "guid": "bfdfe7dc352907fc980b868725387e98c7c54b07bcddd6ccbf7ab80cf5a1cd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352ca71d409146eaeed9fa5a8bcf3a48", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98293cd5b025713b64cca098c1422b5b18", "guid": "bfdfe7dc352907fc980b868725387e989b10e4f1a2392b46188b46a696fe6f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871bd3ba5e5ed3c04c430da411c510664", "guid": "bfdfe7dc352907fc980b868725387e98dd654257a1fd0c6e921147a42f67b5a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e47506bd977323d13ae132737bd35da", "guid": "bfdfe7dc352907fc980b868725387e98a45519d9b7df12fe88c43fd3734edd28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e2d57dafaafb21209c0df69ef957f13", "guid": "bfdfe7dc352907fc980b868725387e98f682da9a79143524b5f497de034aab22", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9801d395c10945db9266344392e2eebe82", "guid": "bfdfe7dc352907fc980b868725387e982719fbf133fe11908b7646a8732e8c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5cb064b549c8825d6f2ec74c9bf328d", "guid": "bfdfe7dc352907fc980b868725387e98e7f1adcf8bcdd041993e95b76576e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd47dfc82be64bc13c03dae5d6f2e5b5", "guid": "bfdfe7dc352907fc980b868725387e98c8ecff818db0dd67fdeef6fae4daf174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c036c3a74dbad0a3ff418dd6dfc6c7", "guid": "bfdfe7dc352907fc980b868725387e98c4a5745524eda0f2de3c7aafd595192d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c266181710afb3ad39cfba5c78dc7499", "guid": "bfdfe7dc352907fc980b868725387e98028d032725aacbef77923adda72dc897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d57f623b673e1773a5f9e77deeaad7", "guid": "bfdfe7dc352907fc980b868725387e9862752dfe48b852effa95e644f27aa841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ffff48081f8e303af21a10361f16de", "guid": "bfdfe7dc352907fc980b868725387e980d82d6002380c17426352d2b6dcc403d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857bd82fc0c55be7b06134ea1c2c9bd22", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa58f82f1b25204c0d1c6f6cd8a9a7ae", "guid": "bfdfe7dc352907fc980b868725387e981104cbc90d9acbd26870d72ac58f83e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f609dc7b3d2fb04cee91918228823db", "guid": "bfdfe7dc352907fc980b868725387e98228a73a0dea2578b100514b934e774fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982466392ab7f97250e9641ef568546b04", "guid": "bfdfe7dc352907fc980b868725387e98f01d1de579313563442377b603c7f1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b1d4155688a3cbb966f68094199526", "guid": "bfdfe7dc352907fc980b868725387e9898e57582cd28f465ead1f88705177fee"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}