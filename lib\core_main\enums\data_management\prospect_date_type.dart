import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum ProspectDateType {
  all(0, "All"),
  receivedDate(1, "Created Date"),
  modifiedDate(2, "Modified Date"),
  scheduledDate(3, "Scheduled Date"),
  qualifiedDate(4, "Qualified Date"),
  convertedDate(5, "Converted Date"),
  possessionDate(6, "Possession Date");

  final int value;
  final String description;

  const ProspectDateType(this.value, this.description);
}
