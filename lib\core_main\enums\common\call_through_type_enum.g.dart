// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_through_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CallThroughTypeEnumAdapter extends TypeAdapter<CallThroughTypeEnum> {
  @override
  final int typeId = 117;

  @override
  CallThroughTypeEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallThroughTypeEnum.askEveryTime;
      case 1:
        return CallThroughTypeEnum.ivrOnly;
      case 2:
        return CallThroughTypeEnum.dialerOnly;
      default:
        return CallThroughTypeEnum.askEveryTime;
    }
  }

  @override
  void write(BinaryWriter writer, CallThroughTypeEnum obj) {
    switch (obj) {
      case CallThroughTypeEnum.askEveryTime:
        writer.writeByte(0);
        break;
      case CallThroughTypeEnum.ivrOnly:
        writer.writeByte(1);
        break;
      case CallThroughTypeEnum.dialerOnly:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallThroughTypeEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
