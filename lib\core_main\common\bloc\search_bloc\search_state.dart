part of 'search_bloc.dart';

@immutable
class SearchState {
  final PageState pageState;
  final String? errorMessage;
  final List<ItemLeadModel>? leads;
  final List<ItemPropertyModel>? properties;
  final List<ItemListingManagementModel>? propertyListings;
  final List<ItemProjectModel>? projects;
  final ItemLeadCategoryModel? searchedLeadCategory;
  final int totalCount;
  final int pageNo;
  final bool loadMore;
  final AppModule appModule;
  final bool isTagsVisible;
  final List<SearchFilterModel?> labels;
  final List<SearchFilterModel?> selectedItem;
  final List<SearchFilterModel?> recentSearch;
  final List<SearchFilterModel?> originalLevels;
  final bool isMoreThanSevenSelected;
  final bool isRecentSearch;

  const SearchState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.leads,
    this.totalCount = 0,
    this.pageNo = 1,
    this.loadMore = false,
    this.searchedLeadCategory,
    this.projects,
    this.properties,
    this.propertyListings,
    this.appModule = AppModule.lead,
    this.isTagsVisible = false,
    this.labels = const [],
    this.selectedItem = const [],
    this.recentSearch = const [],
    this.isMoreThanSevenSelected = false,
    this.originalLevels = const [],
    this.isRecentSearch = false,
  });

  SearchState copyWith({
    PageState? pageState,
    String? errorMessage,
    List<ItemLeadModel>? leads,
    List<ItemPropertyModel>? properties,
    List<ItemListingManagementModel>? propertyListings,
    List<ItemProjectModel>? projects,
    int? totalCount,
    int? pageNo,
    bool? loadMore,
    ItemLeadCategoryModel? searchedLeadCategory,
    AppModule? appModule,
    bool? isTagsVisible,
    List<SearchFilterModel?>? labels,
    List<SearchFilterModel?>? selectedItem,
    List<SearchFilterModel?>? recentSearch,
    List<SearchFilterModel?>? originalLevels,
    bool? isMoreThanSevenSelected,
    bool? isRecentSearch,
  }) {
    return SearchState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage ?? this.errorMessage,
      leads: leads ?? this.leads,
      totalCount: totalCount ?? this.totalCount,
      pageNo: pageNo ?? this.pageNo,
      loadMore: loadMore ?? this.loadMore,
      projects: projects ?? this.projects,
      searchedLeadCategory: searchedLeadCategory ?? this.searchedLeadCategory,
      properties: properties ?? this.properties,
      propertyListings: propertyListings ?? this.propertyListings,
      appModule: appModule ?? this.appModule,
      isTagsVisible: isTagsVisible ?? this.isTagsVisible,
      labels: labels ?? this.labels,
      selectedItem: selectedItem ?? this.selectedItem,
      recentSearch: recentSearch ?? this.recentSearch,
      originalLevels: originalLevels ?? this.originalLevels,
      isMoreThanSevenSelected: isMoreThanSevenSelected ?? this.isMoreThanSevenSelected,
      isRecentSearch: isRecentSearch ?? this.isRecentSearch,
    );
  }
}
