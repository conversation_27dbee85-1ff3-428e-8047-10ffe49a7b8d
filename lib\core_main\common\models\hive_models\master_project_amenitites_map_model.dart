  import 'package:hive_flutter/hive_flutter.dart';
  import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
  import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
  
  part 'master_project_amenitites_map_model.g.dart';
  
  @HiveType(typeId: HiveModelConstants.masterProjectAmenititesMapModelTypeId)
  class MasterProjectAmenititesMapModel {
    @HiveField(0)
    final String key;
  
    @HiveField(1)
    final List<MasterProjectAmenititesModel> values;
  
    MasterProjectAmenititesMapModel({required this.key, required this.values});
  }
