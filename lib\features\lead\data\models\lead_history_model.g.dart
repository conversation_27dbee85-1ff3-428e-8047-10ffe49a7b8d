// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadHistoryModel _$LeadHistoryModelFromJson(Map<String, dynamic> json) =>
    LeadHistoryModel(
      fieldName: json['fieldName'] as String?,
      filterKey:
          $enumDecodeNullable(_$LeadHistoryFilterKeyEnumMap, json['filterKey']),
      oldValue: json['oldValue'] as String?,
      newValue: json['newValue'] as String?,
      updatedBy: json['updatedBy'] as String?,
      updatedOn: json['updatedOn'] == null
          ? null
          : DateTime.parse(json['updatedOn'] as String),
      auditActionType: json['auditActionType'] as String?,
    );

Map<String, dynamic> _$LeadHistoryModelToJson(LeadHistoryModel instance) =>
    <String, dynamic>{
      'fieldName': instance.fieldName,
      'filterKey': _$LeadHistoryFilterKeyEnumMap[instance.filterKey],
      'oldValue': instance.oldValue,
      'newValue': instance.newValue,
      'updatedBy': instance.updatedBy,
      'updatedOn': instance.updatedOn?.toIso8601String(),
      'auditActionType': instance.auditActionType,
    };

const _$LeadHistoryFilterKeyEnumMap = {
  LeadHistoryFilterKey.none: 0,
  LeadHistoryFilterKey.assignment: 1,
  LeadHistoryFilterKey.notes: 2,
  LeadHistoryFilterKey.status: 3,
};

LeadHistoryResponseModel _$LeadHistoryResponseModelFromJson(
        Map<String, dynamic> json) =>
    LeadHistoryResponseModel(
      succeeded: json['succeeded'] as bool?,
      message: json['message'] as String?,
      errors:
          (json['errors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      data: (json['data'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as Map<String, dynamic>).map(
              (k, e) => MapEntry(
                  k,
                  (e as List<dynamic>)
                      .map((e) =>
                          LeadHistoryModel.fromJson(e as Map<String, dynamic>))
                      .toList()),
            )),
      ),
    );

Map<String, dynamic> _$LeadHistoryResponseModelToJson(
        LeadHistoryResponseModel instance) =>
    <String, dynamic>{
      'succeeded': instance.succeeded,
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };
