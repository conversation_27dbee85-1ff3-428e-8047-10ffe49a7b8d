{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98890a626c82325828e90a9fa8eac53742", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877e08430f83a3f8fc2b5594a101e1dea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e28ecb8cca2d55aca0535e4cc5b933f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98212ea6cf48fa03ac0f20022dacbde00c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e28ecb8cca2d55aca0535e4cc5b933f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985617fc7351751ef0404c1e757406a8ae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c225fb32ad08c98bc43650036641de4", "guid": "bfdfe7dc352907fc980b868725387e986ec37b32485eb77b740da7e1d80521d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d25cd50008df0400275fab8fedef309", "guid": "bfdfe7dc352907fc980b868725387e98a09a8edcde25bfc9379843b45fbc0c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2f6443bdcad05c96b167788670b520", "guid": "bfdfe7dc352907fc980b868725387e98453ac5cade67e023d02b6322d7305740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a84d07fba02adc234f2e0010d194da48", "guid": "bfdfe7dc352907fc980b868725387e986c719c2c9db96268da267f69dec1de82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c4b3eca22413569c116b3662cf80a9", "guid": "bfdfe7dc352907fc980b868725387e98697ee8d08dff9294e703635bfc430bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98750b09918b7390dd520906ea677f9d49", "guid": "bfdfe7dc352907fc980b868725387e984c7fb7b57ebe976bab4e8cb40c8cf418", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b55695847f6f889a3a8ad2b59cdd6fc4", "guid": "bfdfe7dc352907fc980b868725387e9851fc662398584ab6ba3149ca869f7e82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98631e2c334df1815ab2f75abe34f6ff4e", "guid": "bfdfe7dc352907fc980b868725387e98f055d901970c1ec098f5280ea55a769a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800bcfebad490e5d115ca433823e666d1", "guid": "bfdfe7dc352907fc980b868725387e98143da06b9f4f18c9a16fd19e9d5b4fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d85ce958a15135617def25f272f5ac4", "guid": "bfdfe7dc352907fc980b868725387e980ff63f1c718d8e41b32053b96fd276f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980123322dde8ec6f15e99d4b389443504", "guid": "bfdfe7dc352907fc980b868725387e98178b9b06744481cf027be764ddc848f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812759be4f65bf7558e7fe9674153457c", "guid": "bfdfe7dc352907fc980b868725387e98d4ff498d3ad864fc7fb353dc35b10c65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173b75834914ac691f21d356f1c667c9", "guid": "bfdfe7dc352907fc980b868725387e98c78a709a09303cbccb9e2538fbc25c98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209c61594448154439ef1631eb168f4d", "guid": "bfdfe7dc352907fc980b868725387e98bf593c02c24118782403a8b271355bed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe2b7c82eb38dadbdefb3d526c2ac50f", "guid": "bfdfe7dc352907fc980b868725387e98245327c26b4ea0e50968cba533d12a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b72d16b72a4926b0630a158c7909a93", "guid": "bfdfe7dc352907fc980b868725387e98bfc802336babbba656a6acc461391273", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a0388b5d77f463ce5b87c10900a9ef", "guid": "bfdfe7dc352907fc980b868725387e98ef3d75f236dfbd416adbf60369d7e403", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3eba46420419c598ef2680127e7a7e", "guid": "bfdfe7dc352907fc980b868725387e980c7fe76b788ef3006b0b361d1e74c471", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3ad41b196f33ddf2ba936cddc82b690", "guid": "bfdfe7dc352907fc980b868725387e986992c3fe853e5e5fb755e286a7a35b34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bfdd3a23795323a3cb6c052bb826000", "guid": "bfdfe7dc352907fc980b868725387e98f7ddbc3a1f340033e3bc481068970f43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98199e6abc8160e7ec1488af29dc3b4cf9", "guid": "bfdfe7dc352907fc980b868725387e989194f86bfbb7773d3ae0b9fc56cabdff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e710ec1cdc78d52a1bcd358f483e85d7", "guid": "bfdfe7dc352907fc980b868725387e9864aa5ba8eba21b3080239d110505a44a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8ea40810c855674b8f354b4de93f01", "guid": "bfdfe7dc352907fc980b868725387e9889efa66405701a0d76051555351b65e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a5c43034b2257324286bc9e0e2a5bb", "guid": "bfdfe7dc352907fc980b868725387e98081505ed6dbd7014d3ec3e765ba8c88a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637a58e237338f21621739ef139f7a75", "guid": "bfdfe7dc352907fc980b868725387e984914f03f006221a3bb0fa8bb7cbda764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd40629688003e3e9210854fde83344a", "guid": "bfdfe7dc352907fc980b868725387e98be953ac8f04f9eec9eb1427d76a84c6d"}], "guid": "bfdfe7dc352907fc980b868725387e9801b442d390643fb46a7a99b2835462e5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eb7f6d4a903cc6924cea6e9c505e2f85", "guid": "bfdfe7dc352907fc980b868725387e98695bda2aba35ca005f5f39f65aa7e614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc5fed30fec24babc1a6b01abf9adb7", "guid": "bfdfe7dc352907fc980b868725387e98505046839b0887de55301ce1211b4aa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981517e2fafd3b0e9a6b8e01564d052eac", "guid": "bfdfe7dc352907fc980b868725387e98a5267e74c9616afbd02b85bfd44c4e3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98370f94bf83ff836050eb9021600bcc93", "guid": "bfdfe7dc352907fc980b868725387e98b8e3a59af4d38d1109a80dfa4f8b88d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873009079cc220c396cf9cf0ad2ad13a6", "guid": "bfdfe7dc352907fc980b868725387e988700f02296ed855387fe728b665860d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dbbf0ff25274c69e4e15b4c6876a5b4", "guid": "bfdfe7dc352907fc980b868725387e98f1fc2dd11727230c59eba76f81b5c6f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccbb9567f4dd37c19487369abb8fe141", "guid": "bfdfe7dc352907fc980b868725387e98b66677371d734a688b9bc57c73566baf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ec282a80cc32f724868ffb8dc1ad25", "guid": "bfdfe7dc352907fc980b868725387e9877d7616261c1f08f157c2c3b797d831a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e0b1b4610fdb3a2c7866bc09a07482", "guid": "bfdfe7dc352907fc980b868725387e98a551d1da461cfd60039333d43a75f6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9711776fb4f97d8595d05d0714c660a", "guid": "bfdfe7dc352907fc980b868725387e9877e81f5e8271d8788a48c98793a8f67c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a65398d1fa6b155f6814f313062af1", "guid": "bfdfe7dc352907fc980b868725387e987af18be715f2c57d14d86eab1d4f2911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989014334ec210853bc32a36698fa022f0", "guid": "bfdfe7dc352907fc980b868725387e98e2548a623d40539f367f47a0b8dad4db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bc7a2aeb3e184a9aa6d9f3bb535477", "guid": "bfdfe7dc352907fc980b868725387e98fac96b887292f44a5e6fbe64021e95fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fc1c43e178bc17906d968e6742a5f5", "guid": "bfdfe7dc352907fc980b868725387e98a40e597461068740d046679c62df50c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e793b2a44c4331a88fc8eee1217b21", "guid": "bfdfe7dc352907fc980b868725387e98c8f730f667f44c899a2e3b82648d57b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fd06d02c25fa98be136ef261cc6ef96", "guid": "bfdfe7dc352907fc980b868725387e98832cc31fc848a85302cdad24fe209075"}], "guid": "bfdfe7dc352907fc980b868725387e98e16ba7b3e3cbe906ecfe06ba7474ae10", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9863ed298c89ec7c707ab21a4a58c234b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e98260d01f8429840ba1894e64db264f4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e98f2dd495d411a59ff8e51eb0de05030a5"}], "guid": "bfdfe7dc352907fc980b868725387e98aba4a8882c18dd7d05f43ad25899d2d9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a6a088f3eacc7b2a32571be9e0de827a", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98a26bcc9c86839d427aaada23b845f561", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}