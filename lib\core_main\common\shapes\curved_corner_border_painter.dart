import 'package:flutter/material.dart';

class CurvedCornerBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double cornerRadius;

  CurvedCornerBorderPainter({
    required this.color,
    this.strokeWidth = 2,
    this.cornerRadius = 4,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final path = Path()
      ..moveTo(0, size.height - cornerRadius)
      ..quadraticBezierTo(0, size.height, cornerRadius, size.height)
      ..lineTo(size.width - cornerRadius, size.height)
      ..quadraticBezierTo(size.width, size.height, size.width, size.height - cornerRadius);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
