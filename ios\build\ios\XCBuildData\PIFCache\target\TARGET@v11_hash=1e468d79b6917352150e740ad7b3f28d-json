{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c7a25a72fd463987b8f3d7afd607f224", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f6f4c7ccfc0acbbcd115fd38ab9784b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e80129e1698527ba07915b599eeb96d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6647bf33cb808742ba4c3d3a20fec49", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e80129e1698527ba07915b599eeb96d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bc6b555d6bba23852bc6dfb335eb419", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985a94da9cb6d6859c796309fb24fb7679", "guid": "bfdfe7dc352907fc980b868725387e980e444c8b8c7820b99d4c1bcb96ce7501", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989cf094b37cda0afcc2665bf8e42cdd8a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1bf5a6f0fad8420e4c4b526e9e01e30", "guid": "bfdfe7dc352907fc980b868725387e987759a60642965d76ec2ab1416e290ee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989211c3b79472a6a20833dc6fac5f42f3", "guid": "bfdfe7dc352907fc980b868725387e98dd2aafcbef6698da023535ef0b1a0467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e7ab4e8dcdc864f411fb3880be29632", "guid": "bfdfe7dc352907fc980b868725387e98e6a940fd09f5c16f0abcaaa834d6f4b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c30e3696a547d7459845bcd27d65c7", "guid": "bfdfe7dc352907fc980b868725387e98d6250b5bdce28bf1ee35a2072ff371cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c764fbe3dbf9f7dd4e582c0429507aa", "guid": "bfdfe7dc352907fc980b868725387e9858eef3844e0af52eaa07c45d25cfc47d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31eadb3d22456c5cfdba1534102f498", "guid": "bfdfe7dc352907fc980b868725387e988ba1dff961ff430002533b5d7ff48e90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890831ef9391bce257d3631160642517a", "guid": "bfdfe7dc352907fc980b868725387e98a3b1dd732d0bfe799d7d47b26cb5c205"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be036ee7baa1249e20e34a42ee080aa", "guid": "bfdfe7dc352907fc980b868725387e987e7c4fe51821467d1080baaf65298770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad1d11b83b8edba4468c33dd4a5f0db", "guid": "bfdfe7dc352907fc980b868725387e98983f4d32f888b59875d2e0b118cfacb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de5ca65472d8eb66405d14e5d9485234", "guid": "bfdfe7dc352907fc980b868725387e986c29ca70f08bbd2305da0013d87fa3e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98081ceba3f2477031c822d0a521cfaada", "guid": "bfdfe7dc352907fc980b868725387e9888132ce999e380e24adce564d21a0ee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987522801df9fd5a552d4d2ff8f02e226e", "guid": "bfdfe7dc352907fc980b868725387e98dd751eac317079b7802c3dd2776b7b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d2a88030e2da016e0961e092b7d1f5", "guid": "bfdfe7dc352907fc980b868725387e98622974cc54ae10d87e1afefc3ec4ee62"}], "guid": "bfdfe7dc352907fc980b868725387e98873a47e9c85b3d9b269245e1d89e2c3d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9822116264bf8b7c42d77a865360c92a14"}], "guid": "bfdfe7dc352907fc980b868725387e98b38743f258a9481762d81c0b4ab7414a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983271ac5f3f7c051ac68b2e7014d35e67", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e982d1fde68e3d7e9abd92c9ac4015c556a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}