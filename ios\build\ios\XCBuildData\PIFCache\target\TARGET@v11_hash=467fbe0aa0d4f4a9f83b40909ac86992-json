{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5f7c69ebbf8d64422cee6f2455c2225", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5fc32ba29bc802fd6b9dd6b51188d33", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ae0545eec001462ab66cb3461f316a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8832c79b578321c9002a5120f424b56", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ae0545eec001462ab66cb3461f316a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a67679a8c476b7c7164a552424ba1a1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98208cfc18f9ef107872250d4736c84ce4", "guid": "bfdfe7dc352907fc980b868725387e9809a74ac6717898d6a0e1094ad223c9c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823993cbc760f5c7cc8a7785dd43dda30", "guid": "bfdfe7dc352907fc980b868725387e98157f614ea0306066dac79fbb6bd7d5ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98872979ece3a6a7be63fd62d6b9f25d2a", "guid": "bfdfe7dc352907fc980b868725387e982dc0d667b8cd427ffcc365cd1f89328e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112dc6dcef5488365c7ec444224a8f25", "guid": "bfdfe7dc352907fc980b868725387e988c4a1084aff67a492eec6762b2779d36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b9a5c492e7652521661f7c42fd05a9", "guid": "bfdfe7dc352907fc980b868725387e98d237e6dbee2f373d6b1b3104f6474e92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd0c9ec102bb442a3099993420837931", "guid": "bfdfe7dc352907fc980b868725387e986b8629f00d9c3a567b476a12909cb061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a3e360ff5ab4ea141f85b02caf3312", "guid": "bfdfe7dc352907fc980b868725387e984b17c0b828a23ca9df64f4386f01380a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c23afad994cf93195e2780dd6e3ef39c", "guid": "bfdfe7dc352907fc980b868725387e989fd854b895754cd05922b7913a8ae2ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efa1b5aefcc8b7eeb8cfc60574bc8603", "guid": "bfdfe7dc352907fc980b868725387e9875873b072481be5ddd08d9600478fae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbed6775775d47b35209a1f05692173", "guid": "bfdfe7dc352907fc980b868725387e98aae81e88f80dc0d25e2b77d23d0b363c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981504fd0cd16b2d14674e5c19d838db8f", "guid": "bfdfe7dc352907fc980b868725387e98c50521bfa545b60e13e62d42b4843a31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c89a3b8cd6989e797be1c205671f7d98", "guid": "bfdfe7dc352907fc980b868725387e98f95c9307db6784e291d9138cf737d6e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b2e775fd93eeb1f35d27f44b490a124", "guid": "bfdfe7dc352907fc980b868725387e9828c974018bd03b9ef614312ddd9aaac8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9929037c8bbb546e84a43ef11bee1ca", "guid": "bfdfe7dc352907fc980b868725387e98b8dce026eebf6acc66b9a626a414fead", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e30cba46f27e709e430f03f0f1d30e", "guid": "bfdfe7dc352907fc980b868725387e98b0fe97778a63564b6a911ba7f4e01aba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f97d3bb59db0322cd669d8ac2eb8a4d", "guid": "bfdfe7dc352907fc980b868725387e987102cd9867d4895c867befd2975e07d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e603a6267ed61500e8ca9ed0ea4974", "guid": "bfdfe7dc352907fc980b868725387e984e27893a0faf023499cd5c0085f7a51b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e2bd045d17f9956a3c07a844575c696", "guid": "bfdfe7dc352907fc980b868725387e983b4868510194c758a876a65d83263264", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a50aa7b07e7a8d96a34ac13ab2d7cc", "guid": "bfdfe7dc352907fc980b868725387e9856fe71ae0f25680f063fce3b7671fad7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb4839acfa513b7d87d7a6a9022134b", "guid": "bfdfe7dc352907fc980b868725387e9816333129125815b22771f6497ee47723", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d61c6b6405705e18143126b6a4ac3d", "guid": "bfdfe7dc352907fc980b868725387e98d00c74d9f4adddb7ef57a298b8f6a02b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98205c72b7143f34951633913fdbb3e727", "guid": "bfdfe7dc352907fc980b868725387e983faf1d95ee192be02fe1131ea5c5bef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c6b26e94b103939b10e3d722698f33", "guid": "bfdfe7dc352907fc980b868725387e98e17c629219a9d767be127a97cd2a5729", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b056a18bb937c46e04533ff6f7a72188", "guid": "bfdfe7dc352907fc980b868725387e9857a5cdc075c6b1050e01b9d8ec57e574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd6512b7d4ba2cc2fd163a9f9b7fc09", "guid": "bfdfe7dc352907fc980b868725387e98c51f86d0dbb58e1f37e9b852095ab973", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f578157b1fd93762166fdd3f7fbb4c", "guid": "bfdfe7dc352907fc980b868725387e98dc5e88c9132a68f701a5cc3a318a6be3"}], "guid": "bfdfe7dc352907fc980b868725387e9850e6887e8a8ecf59df9c3a3b09af62a8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a1a033ad2154c78cbf0aba462b3e2f6", "guid": "bfdfe7dc352907fc980b868725387e985040bc55a5a5a737abd034e543735752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a797cb8248d442057f54ccd6783cf97", "guid": "bfdfe7dc352907fc980b868725387e9827389ff00d0f3bef07b96bdec98fd49b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a9bfc1263fc94ab96071267c5b2fa2", "guid": "bfdfe7dc352907fc980b868725387e98b834c4f50618387d5ec4869051df13d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c798a7d3d2fac5f11d9683e60746a115", "guid": "bfdfe7dc352907fc980b868725387e9896d9ac32c809055afecb05d5346b9c2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa842d0e8b8ac2ef56de189a62748b9e", "guid": "bfdfe7dc352907fc980b868725387e9833c210e49654023df462749281b8ce50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a212f7b01602e713e8d6fffe6a0a75a", "guid": "bfdfe7dc352907fc980b868725387e9801b87ceedb2c6a277ae91d7d04a10a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6101c896a4eb7ead290cb008de24997", "guid": "bfdfe7dc352907fc980b868725387e984cbce50cde30d80db1a9826373122e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9729eaa59368e7f8655e33cda38b161", "guid": "bfdfe7dc352907fc980b868725387e98471ab1540983c6f8195fa0e22859c420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506e809c481c3fdec8bcea763fd9b8cf", "guid": "bfdfe7dc352907fc980b868725387e988019ca6d1a4cb5d19c7b90a23fc66b63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411562d5347c67547057d5427ad827be", "guid": "bfdfe7dc352907fc980b868725387e982323c5139c9964d856cdc25b88d696c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3a87e2655a5617846f9965de0296720", "guid": "bfdfe7dc352907fc980b868725387e9896d0a9ab786405fd93509bdc8a47b1d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039b5ede0b93eac7344e38505eac2353", "guid": "bfdfe7dc352907fc980b868725387e985c700bde3ba8b96b1c58befe68f8a0fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0bb0fc4d9eab5a6264f338a0512be6", "guid": "bfdfe7dc352907fc980b868725387e983d586fc4a95ebe35c50907edfb985a6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dfaa0019738d497f10722e21b843845", "guid": "bfdfe7dc352907fc980b868725387e9876147c8e58a2451b9a8778bb246ea336"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981477aba11cfc1bcc42d9b4df96649a69", "guid": "bfdfe7dc352907fc980b868725387e98d0b1c720a3121a6035422f36f61f41aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc13c56d472cc9a18a90131908d537c", "guid": "bfdfe7dc352907fc980b868725387e98f0c76a958e720951feabb1b3ab35e1b2"}], "guid": "bfdfe7dc352907fc980b868725387e985e0766b85a6a186ad553f1c1600059b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98b41ff81176f3e7328af664bdabeb6e45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e98bef75b228026c305db7b2434b1c7987e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e982aa538f63f9c0d17dbfb307996d04e53"}], "guid": "bfdfe7dc352907fc980b868725387e9810320d801562dec1ca12ea5a97134248", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9836e41d36dde95caf6225e6cd14e1c5ae", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9891e324d38ef7a3c3650c3e4aff4c3558", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}