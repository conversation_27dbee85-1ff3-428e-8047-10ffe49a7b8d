{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c7a25a72fd463987b8f3d7afd607f224", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc95bee39d27a60c17c93bad720e1d51", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e80129e1698527ba07915b599eeb96d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821833c3f446bc353b8edc67ce78ea3eb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e80129e1698527ba07915b599eeb96d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f0a77e8074392e8d0405905ef5fa427", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985a94da9cb6d6859c796309fb24fb7679", "guid": "bfdfe7dc352907fc980b868725387e98fb050c439307851a12a44a840ff3c1bb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988c45d66db20482bb5fac38501ad7497a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1bf5a6f0fad8420e4c4b526e9e01e30", "guid": "bfdfe7dc352907fc980b868725387e98aaeff6d353bd4eff0edd70ae9e4372e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989211c3b79472a6a20833dc6fac5f42f3", "guid": "bfdfe7dc352907fc980b868725387e988560c108b2255753701b54c6d389bb2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e7ab4e8dcdc864f411fb3880be29632", "guid": "bfdfe7dc352907fc980b868725387e98e594f8b09e9b8e05453115702924775e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c30e3696a547d7459845bcd27d65c7", "guid": "bfdfe7dc352907fc980b868725387e9803faf108072c111189f3b4df599ecd0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c764fbe3dbf9f7dd4e582c0429507aa", "guid": "bfdfe7dc352907fc980b868725387e986485eb2823f09ef6b6fc9e0c43509ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31eadb3d22456c5cfdba1534102f498", "guid": "bfdfe7dc352907fc980b868725387e98066e3d015fdfcbaefc3107ecff96af31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890831ef9391bce257d3631160642517a", "guid": "bfdfe7dc352907fc980b868725387e98d96294471732752791d815db2badad10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be036ee7baa1249e20e34a42ee080aa", "guid": "bfdfe7dc352907fc980b868725387e98e0365ef810025fee6116f9748edc0b18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad1d11b83b8edba4468c33dd4a5f0db", "guid": "bfdfe7dc352907fc980b868725387e98dbb8021239e2da2194e320a53fea4939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de5ca65472d8eb66405d14e5d9485234", "guid": "bfdfe7dc352907fc980b868725387e98e733317e11d8238c552dded916202782"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98081ceba3f2477031c822d0a521cfaada", "guid": "bfdfe7dc352907fc980b868725387e98d8d4d1038dc8fa9c1622f481a6777cdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987522801df9fd5a552d4d2ff8f02e226e", "guid": "bfdfe7dc352907fc980b868725387e98b55d8772bd4d15ba0415c48b8e3ad1f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d2a88030e2da016e0961e092b7d1f5", "guid": "bfdfe7dc352907fc980b868725387e9842d33efc2dd4d6a049b8ee77fc3d6711"}], "guid": "bfdfe7dc352907fc980b868725387e987fcd7c85a32f8facc514c499aa8e5260", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9851c3870ea9a534b4b879b94683443134"}], "guid": "bfdfe7dc352907fc980b868725387e9868c9f2ed7fa3434c0740222fced0c34f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ace3e1065f0f4d380e066e7cf181b42c", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a3bb505911accdcd1823723ec106ee4e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}