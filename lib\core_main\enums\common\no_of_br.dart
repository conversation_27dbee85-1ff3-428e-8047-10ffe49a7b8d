import 'package:leadrat/core_main/extensions/object_extension.dart';

enum NoOfBr {
  oneBr("1", 1),
  twoBr("2", 2),
  threeBr("3", 3),
  fourBr("4", 4),
  fiveBr("5", 5),
  sixBr("6", 6),
  sevenBr("7", 7),
  eightBr("8", 8),
  nineBr("9", 9),
  tenBr("10", 10);

  final String description;
  final double noOfBr;

  const NoOfBr(this.description, this.noOfBr);
}

enum NoOfPropertiesBr {
  studio("Studio", 0.5),
  oneBr("1", 1),
  twoBr("2", 2),
  threeBr("3", 3),
  fourBr("4", 4),
  fiveBr("5", 5),
  sixBr("6", 6),
  sevenBr("7", 7),
  eightBr("8", 8),
  nineBr("9", 9),
  tenBr("10", 10);

  final String description;
  final double noOfBr;

  const NoOfPropertiesBr(this.description, this.noOfBr);
}

T? getEnumFromNoOfBr<T extends Enum>(List<T> values, double? noOfBr) {
  try {
    if (noOfBr == null) return null;
    return values.firstWhere((e) => (e as dynamic).noOfBr == noOfBr);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}
