// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dto_with_name_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DTOWithNameModelAdapter extends TypeAdapter<DTOWithNameModel> {
  @override
  final int typeId = 107;

  @override
  DTOWithNameModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DTOWithNameModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      createdBy: fields[3] as String?,
      createdOn: fields[2] as DateTime?,
      lastModifiedOn: fields[4] as DateTime?,
      lastModifiedBy: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, DTOWithNameModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdBy)
      ..writeByte(4)
      ..write(obj.lastModifiedOn)
      ..writeByte(5)
      ..write(obj.lastModifiedBy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DTOWithNameModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DTOWithNameModel _$DTOWithNameModelFromJson(Map<String, dynamic> json) =>
    DTOWithNameModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$DTOWithNameModelToJson(DTOWithNameModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
    };
