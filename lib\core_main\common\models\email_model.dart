import 'package:json_annotation/json_annotation.dart';

part 'email_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class EmailModel {
  final String? sender;
  final String? subject;
  final String? contentBody;
  final String? toRecipients;
  final String? ccRecipients;
  final String? bccRecipients;

  EmailModel({
    this.sender,
    this.subject,
    this.contentBody,
    this.toRecipients,
    this.ccRecipients,
    this.bccRecipients,
  });

  factory EmailModel.fromJson(Map<String, dynamic> json) => _$EmailModelFromJson(json);

  Map<String, dynamic> toJson() => _$EmailModelToJson(this);
}
