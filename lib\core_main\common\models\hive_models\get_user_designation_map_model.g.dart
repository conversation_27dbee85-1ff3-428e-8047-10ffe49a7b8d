// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_user_designation_map_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GetUserDesignationMapModelAdapter
    extends TypeAdapter<GetUserDesignationMapModel> {
  @override
  final int typeId = 115;

  @override
  GetUserDesignationMapModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GetUserDesignationMapModel(
      key: fields[0] as String,
      values: (fields[1] as List).cast<GetUserDesignationModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, GetUserDesignationMapModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.key)
      ..writeByte(1)
      ..write(obj.values);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetUserDesignationMapModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
