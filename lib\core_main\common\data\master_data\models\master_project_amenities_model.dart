import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';

part 'master_project_amenities_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterProjectAmenititesModelTypeId)
@JsonSerializable()
class MasterProjectAmenititesModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final String? amenityName;
  @HiveField(9)
  final String? amenityDisplayName;
  @HiveField(10)
  final String? imageURL;
  @HiveField(11)
  final String? amenityType;
  @HiveField(12)
  final String? category;
  @HiveField(13)
  final int? orderRank;
  @HiveField(14)
  final String? mobileIcon;
  @HiveField(15)
  final List<PropertyType>? propertyType;
  @HiveField(16)
  final String? fullImageURL;

  MasterProjectAmenititesModel({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.amenityName,
    this.amenityDisplayName,
    this.imageURL,
    this.amenityType,
    this.category,
    this.orderRank,
    this.mobileIcon,
    this.propertyType,
    this.fullImageURL,
  });

  factory MasterProjectAmenititesModel.fromJson(Map<String, dynamic> json) => _$MasterProjectAmenititesModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterProjectAmenititesModelToJson(this);
}
