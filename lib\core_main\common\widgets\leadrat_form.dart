import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';

class LeadratForm extends LeadratStatelessWidget {
  final Widget child;
  final Key? formKey;
  final Widget leadingButton;
  final Widget trailingButton;
  final AppBar? appBar;
  final bool wrapWithScrollView;
  final EdgeInsetsGeometry? padding;

  const LeadratForm({
    required this.leadingButton,
    required this.trailingButton,
    super.key,
    required this.child,
    this.formKey,
    this.appBar,
    this.padding,
    this.wrapWithScrollView = true,
  });

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      bottomNavigationBar: Container(
        padding: const EdgeInsets.only(left: 24, right: 14, top: 32, bottom: 20),
        child: Row(
          children: [
            leadingButton,
            const Sized<PERSON>ox(width: 14),
            Expanded(
              child: trailingButton,
            )
          ],
        ),
      ),
      body: SafeArea(
        child: Container(
          height: context.height(100),
          width: context.width(100),
          padding: padding ?? const EdgeInsets.only(left: 24, right: 14),
          decoration: const BoxDecoration(
            color: ColorPalette.white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
          ),
          child: Form(
            key: formKey,
            child: wrapWithScrollView
                ? SingleChildScrollView(
              child: child,
            )
                : child,
          ),
        ),),);
  }
}
