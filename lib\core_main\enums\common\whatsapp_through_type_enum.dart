import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'whatsapp_through_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.whatsappThroughTypeId)
@JsonEnum(valueField: 'index')
enum WhatsappThroughTypeEnum {
  @HiveField(0)
  askEveryTime,
  @HiveField(1)
  templateShare,
  @HiveField(2)
  openConversation;
}
