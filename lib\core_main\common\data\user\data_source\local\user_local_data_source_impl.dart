import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';
import 'package:leadrat/core_main/common/models/hive_models/get_user_designation_map_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

import '../../../../../services/local_storage_service/local_storage_service.dart';
import '../../../../constants/hive_model_constants.dart';
import '../../models/get_all_users_model.dart';
import '../../models/user_details_model.dart';

class UsersLocalDataSourceImpl implements UsersLocalDataSource {
  final LocalStorageService _localStorageService;

  UsersLocalDataSourceImpl(this._localStorageService);

  @override
  List<GetAllUsersModel?>? getAllUsers() {
    try {
      return _localStorageService.getAllItems<GetAllUsersModel?>(HiveModelConstants.getAllUsersBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveAllUsers(List<GetAllUsersModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.getAllUsersBoxName);
      final validItems = items.whereType<GetAllUsersModel>().toList();
      await _localStorageService.addItems<GetAllUsersModel>(HiveModelConstants.getAllUsersBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  UserDetailsModel? getUser() {
    try {
      return _localStorageService.getAllItems<UserDetailsModel?>(HiveModelConstants.getUserBoxName).firstOrNull;

    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveUser(UserDetailsModel? item) async {
    if (item == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.getUserBoxName);
      await _localStorageService.addItem<UserDetailsModel>(HiveModelConstants.getUserBoxName, item);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Future<void> saveAdminsAndReportee(List<GetAllUsersModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.getAllAdminsAndReporteesBoxName);
      final validItems = items.whereType<GetAllUsersModel>().toList();
      await _localStorageService.addItems<GetAllUsersModel>(HiveModelConstants.getAllAdminsAndReporteesBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<GetAllUsersModel?>? getAdminsAndReportee() {
    try {
      return _localStorageService.getAllItems<GetAllUsersModel?>(HiveModelConstants.getAllAdminsAndReporteesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  List<GetAllUsersModel?>? getAllReportees() {
    try {
      return _localStorageService.getAllItems<GetAllUsersModel?>(HiveModelConstants.getAllReporteesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveAllReportees(List<GetAllUsersModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.getAllReporteesBoxName);
      final validItems = items.whereType<GetAllUsersModel>().toList();
      await _localStorageService.addItems<GetAllUsersModel>(HiveModelConstants.getAllReporteesBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Map<String, List<GetUserDesignationModel>>? getUserDesignations() {
    try {
      final mapItems = _localStorageService.getAllItems<GetUserDesignationMapModel>(HiveModelConstants.getAllUserDesignationBoxName);
      return Map.fromEntries(mapItems.map((item) => MapEntry(item.key, item.values)));
    } catch (ex) {
      ex.logException();
      return null;
    }
  }

  @override
  Future<void> saveUserDesignations(Map<String, List<GetUserDesignationModel>>? items) async {
    if (items == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.getAllUserDesignationBoxName);
      final mapItems = items.entries
          .map(
            (entry) => GetUserDesignationMapModel(key: entry.key, values: entry.value),
          )
          .toList();
      await _localStorageService.addItems<GetUserDesignationMapModel>(HiveModelConstants.getAllUserDesignationBoxName, mapItems);
    } catch (ex) {
      ex.logException();
    }
  }
}
