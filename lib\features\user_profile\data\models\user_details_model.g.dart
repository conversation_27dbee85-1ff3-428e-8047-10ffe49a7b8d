// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDetailsModel _$UserDetailsModelFromJson(Map<String, dynamic> json) =>
    UserDetailsModel(
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      isActive: json['isActive'] as bool?,
      emailConfirmed: json['emailConfirmed'] as bool?,
      imageUrl: json['imageUrl'] as String?,
      altPhoneNumber: json['altPhoneNumber'] as String?,
      altEmail: json['altEmail'] as String?,
      address: json['address'] as String?,
      email: json['email'] as String?,
      bloodGroup:
          $enumDecodeNullable(_$BloodGroupTypeEnumMap, json['bloodGroup']),
      gender: $enumDecodeNullable(_$GenderEnumEnumMap, json['gender']),
      permanentAddress: json['permanentAddress'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      empNo: json['empNo'] as String?,
      officeName: json['officeName'] as String?,
      officeAddress: json['officeAddress'] as String?,
      reportsTo: json['reportsTo'] == null
          ? null
          : BaseUserModel.fromJson(json['reportsTo'] as Map<String, dynamic>),
      department: json['department'] == null
          ? null
          : DTOWithNameModel.fromJson(
              json['department'] as Map<String, dynamic>),
      designation: json['designation'] == null
          ? null
          : DTOWithNameModel.fromJson(
              json['designation'] as Map<String, dynamic>),
      description: json['description'] as String?,
      profileCompletion: (json['profileCompletion'] as num?)?.toDouble(),
      documents: (json['documents'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>?)
                ?.map((e) =>
                    UserDocumentModel.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      leadCount: (json['leadCount'] as num?)?.toInt(),
      rolePermission: (json['rolePermission'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : RolePermissionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserDetailsModelToJson(UserDetailsModel instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'isActive': instance.isActive,
      'emailConfirmed': instance.emailConfirmed,
      'imageUrl': instance.imageUrl,
      'altPhoneNumber': instance.altPhoneNumber,
      'altEmail': instance.altEmail,
      'address': instance.address,
      'email': instance.email,
      'bloodGroup': _$BloodGroupTypeEnumMap[instance.bloodGroup],
      'gender': _$GenderEnumEnumMap[instance.gender],
      'permanentAddress': instance.permanentAddress,
      'phoneNumber': instance.phoneNumber,
      'empNo': instance.empNo,
      'officeName': instance.officeName,
      'officeAddress': instance.officeAddress,
      'reportsTo': instance.reportsTo?.toJson(),
      'department': instance.department?.toJson(),
      'designation': instance.designation?.toJson(),
      'description': instance.description,
      'profileCompletion': instance.profileCompletion,
      'documents': instance.documents
          ?.map((k, e) => MapEntry(k, e?.map((e) => e.toJson()).toList())),
      'leadCount': instance.leadCount,
      'rolePermission':
          instance.rolePermission?.map((e) => e?.toJson()).toList(),
    };

const _$BloodGroupTypeEnumMap = {
  BloodGroupType.none: 0,
  BloodGroupType.aPositive: 1,
  BloodGroupType.aNegative: 2,
  BloodGroupType.bPositive: 3,
  BloodGroupType.bNegative: 4,
  BloodGroupType.oPositive: 5,
  BloodGroupType.oNegative: 6,
  BloodGroupType.abPositive: 7,
  BloodGroupType.abNegative: 8,
};

const _$GenderEnumEnumMap = {
  GenderEnum.notMentioned: 0,
  GenderEnum.male: 1,
  GenderEnum.female: 2,
  GenderEnum.other: 3,
};
