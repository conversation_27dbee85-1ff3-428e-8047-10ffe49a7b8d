{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e4fb363862f0e9d1899c4473fce1d65", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9886d532d58f42870d744f9a7dd6f7505b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f27cce995e019d43b9c3a578dda1f64f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840cdadd123040e736c5b08c8572b2b38", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f27cce995e019d43b9c3a578dda1f64f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98181048e3dda3e05204fa2b86e810292a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f29e0881dbdce13475689c1451a341b", "guid": "bfdfe7dc352907fc980b868725387e9831e719f51358ebb1fb79cd2a920f5761", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b9fe5c9d984a28ac836258b9f169a5", "guid": "bfdfe7dc352907fc980b868725387e9889d1d2a03ab2b847fcc4910794030167", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbe56af29293f8a554eddf97e85f7fbc", "guid": "bfdfe7dc352907fc980b868725387e98c4147bd02a59c69be2472b9ae73573cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f6018b628912785d37dd9f1e372d7a7", "guid": "bfdfe7dc352907fc980b868725387e981c29d3ec959611d5dc8cda0fe46cd348", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980870f54a3968ca40c4032b2213392709", "guid": "bfdfe7dc352907fc980b868725387e98269fac1408f5bdfee79cb5d2a080876e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae85f44ca108cff183ce92258f3a3d13", "guid": "bfdfe7dc352907fc980b868725387e985daae4cfff6a84aaaea77c13e2da2509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb5ce09fba90d9e4006824e800408d2", "guid": "bfdfe7dc352907fc980b868725387e982e5b92c1ffcfce6f4530fa4622ff7c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8bd8b296b979d580c2a59a884c5fc4", "guid": "bfdfe7dc352907fc980b868725387e984ebc4864d9c87a611bc9ca296e08e6b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f2eb239406278842d2a3ecca28162d", "guid": "bfdfe7dc352907fc980b868725387e98b583db66041b3e36ff36e5df6c0b0389", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ab4586bd04b5f3a6cc42655718d16b", "guid": "bfdfe7dc352907fc980b868725387e986b3d1d42e45dae132490c9a5cd4812a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2f291e6bcecb5aaf371a73b4ccc06da", "guid": "bfdfe7dc352907fc980b868725387e98a66a8dc4dd71791bd4897f6fdea0cf95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f15284178368ae98e36cfb561321397", "guid": "bfdfe7dc352907fc980b868725387e98c215ab4fadb003412a3592f81d62c8aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b70c98b83a0a62ca8c1885e4c4b9e38", "guid": "bfdfe7dc352907fc980b868725387e985ba3e927f833d10489157b3862b47367", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bafe4383ce0def49d9796b7adeec6e", "guid": "bfdfe7dc352907fc980b868725387e9825674cb353db7e3e5ceeaf818ba0ca75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a3a58ec8f1e91f12ef0cc87dad19c2", "guid": "bfdfe7dc352907fc980b868725387e9827a6ac1a04b95a7cf2c77667e94b5941", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6fb1f5c0eea6fee248b250bb4f4143f", "guid": "bfdfe7dc352907fc980b868725387e98ebaab5dd39773eaa1a519b0f185d7e31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f2f6cf919b3e9bb242b989df89845e", "guid": "bfdfe7dc352907fc980b868725387e989baf8ad8b33a0258718af96b9aaf9401", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db80c7c236d3133df56b7e8db37b2828", "guid": "bfdfe7dc352907fc980b868725387e9819bda8475d30cb2bb6038ec0ed8c2d7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ea9f6e42717729e68be71088a3c3514", "guid": "bfdfe7dc352907fc980b868725387e98f85de5a9b9e1f5b9bf1afa51aa2081be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986845b0f533e8aa15ae9906bacdaab42f", "guid": "bfdfe7dc352907fc980b868725387e9875884e99f3a42feebf7b0ba02c8185e4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b7549afc8263693654c13228b1b891", "guid": "bfdfe7dc352907fc980b868725387e98cf9ce4a95b7131ed9587d47500f7c6e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811adb53ddadd52ccb57ef2f05db4019a", "guid": "bfdfe7dc352907fc980b868725387e988ca7280ab279b0be4162ef4072e27912", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98337daed14f0a5fa0aca68d115401fd04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982d032652b5e53a340111276c4852ad46", "guid": "bfdfe7dc352907fc980b868725387e98c6da8c735e176f95ca60d2f173068e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba510fa14c6631e2c418dc673017f31e", "guid": "bfdfe7dc352907fc980b868725387e9867398dde18f2a8254aa27c4daa0ec536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b490b9e37d6995cdbc2cac00057ca151", "guid": "bfdfe7dc352907fc980b868725387e98491d59921826f112db37da987669232a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efea0b70dfd0c205768d1266c1930e6", "guid": "bfdfe7dc352907fc980b868725387e981e85a6692dcc9030220ea5d5266de97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239398c2957b25ef27db52fbf675428d", "guid": "bfdfe7dc352907fc980b868725387e987fc1b4ea495131331c20eb8f7a0bee2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98361f145627588dd0517657248e3659dd", "guid": "bfdfe7dc352907fc980b868725387e984fe8a5ccf07addff624fb7b303a35a00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984287486d4b4911282b46c5b20fb9235d", "guid": "bfdfe7dc352907fc980b868725387e98b6774aef5ed8612dc647a5b0a1ac52d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981654299b249f23876d0e49af92afe1a9", "guid": "bfdfe7dc352907fc980b868725387e98eeb246f4357dfb3b642bc73b1c469674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7803f89fc84e202a6b8eea5ef352ac1", "guid": "bfdfe7dc352907fc980b868725387e98d8c4058ee7e9e78ca1237807fd989ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987346542486393a2c1b6776acd155b6df", "guid": "bfdfe7dc352907fc980b868725387e9889cca8eb29d1ad9db479c6f8ff32c3b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98317de9fbfa48e8d19b716c15c22bf6b8", "guid": "bfdfe7dc352907fc980b868725387e98ae18ef4e40502f44a5fd9f67a5ebee22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818fb48a90a8650e95a0de2a269696d5f", "guid": "bfdfe7dc352907fc980b868725387e98cfa86b0554e2449319692c12fd5167fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988399bda5fc65f94d28cd24100047e189", "guid": "bfdfe7dc352907fc980b868725387e981c4d7b172c194ca3a7c4880073ea5143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815defad93c8500c712e9f67e2a67be16", "guid": "bfdfe7dc352907fc980b868725387e987d07382fa99b5e7f9e414ae372d192ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bca00a7099b0dc66c77c8d953bdff9f", "guid": "bfdfe7dc352907fc980b868725387e98f6aa11e748e91722aab927d647881b9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e241e71c0b7a44108d0d9712c62d6fe", "guid": "bfdfe7dc352907fc980b868725387e98b9346e0b23a4b2afd67803117d64e6c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac1b5de6c17c72c232de5e0be6030433", "guid": "bfdfe7dc352907fc980b868725387e984523c52cc72d3e117b8614f7a4be7f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60e9662bab2d22170b88dd4ffcca452", "guid": "bfdfe7dc352907fc980b868725387e98f2c0b25a1f60623fab69a516cdb8da81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a8a211135f8c91e20ba4cf2335132c", "guid": "bfdfe7dc352907fc980b868725387e98620fdc3050faff3b3e99dd6d9924afd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b30254e99d7e020de742c44f4bbc8818", "guid": "bfdfe7dc352907fc980b868725387e988230a25bfc25a8b0b817be1c6b862707"}], "guid": "bfdfe7dc352907fc980b868725387e988e8a9f07469c8b50002956c867f2747c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e981525d52e5e2c5632041ae1c7a87df664"}], "guid": "bfdfe7dc352907fc980b868725387e98eacb55b046fa63d529d0a2816a327a6c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98922468eb6cffab90a2fd4c3de825657e", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c6edb763a34bad26e3d5e9b0822a8118", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}