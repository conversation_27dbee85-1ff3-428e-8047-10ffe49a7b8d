import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'gender_enum.g.dart';

@HiveType(typeId: HiveModelConstants.genderEnumTypeId)
@JsonEnum(valueField: 'value')
enum GenderEnum {
  @HiveField(0)
  notMentioned("prefer not to say", 0),
  @HiveField(1)
  male("male", 1),
  @HiveField(2)
  female("female", 2),
  @HiveField(3)
  other("trans-gender", 3);

  final String description;
  final int value;
  const GenderEnum(this.description, this.value);


}
