import 'dart:ui';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ImagePreviewCubit extends Cubit<bool> {
  ImagePreviewCubit() : super(false);

  void previewImage(WebViewController controller) {
    emit(false);
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {
            emit(true);
          },
          onPageFinished: (String url) {
            emit(false);
          },
          onWebResourceError: (WebResourceError error) {
            emit(false);
          }));
  }
}
