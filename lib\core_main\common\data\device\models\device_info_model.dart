import 'package:json_annotation/json_annotation.dart';

part 'device_info_model.g.dart';

@JsonSerializable(includeIfNull: false)
class DeviceInfoModel {
  final String? id;
  final String? userId;
  final int? operatingSystem;
  final String? osVersion;
  final String? countryCode;
  final String? languageCode;
  final String? currencyCode;
  final String? currencySymbol;
  final String? deviceTimeZone;
  final int? timeFormat;
  final String? deviceModel;
  final String? deviceUDID;
  final String? deviceName;
  final bool isDebug;
  final bool isDeveloperMode;
  final String? model;
  final String? manufacturer;
  final String? environment;
  final String? ipAddress;
  final String? latitude;
  final String? longitude;

  DeviceInfoModel({
    this.id,
      this.userId,
      this.operatingSystem,
      this.osVersion,
      this.countryCode,
      this.languageCode,
      this.currencyCode,
      this.currencySymbol,
      this.deviceTimeZone,
      this.timeFormat,
      this.deviceModel,
      this.deviceUDID,
      this.deviceName,
      this.isDebug = false,
      this.isDeveloperMode = false,
      this.model,
      this.manufacturer,
      this.environment,
    this.ipAddress,
      this.latitude,
      this.longitude});

  factory DeviceInfoModel.fromJson(Map<String, dynamic> json) => _$DeviceInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceInfoModelToJson(this);

  DeviceInfoModel copyWith({
    String? id,
    String? userId,
    int? operatingSystem,
    String? osVersion,
    String? countryCode,
    String? languageCode,
    String? currencyCode,
    String? currencySymbol,
    String? deviceTimeZone,
    int? timeFormat,
    String? deviceModel,
    String? deviceUDID,
    String? deviceName,
    bool? isDebug,
    bool? isDeveloperMode,
    String? model,
    String? manufacturer,
    String? environment,
    String? ipAddress,
    String? latitude,
    String? longitude,
  }) {
    return DeviceInfoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      osVersion: osVersion ?? this.osVersion,
      countryCode: countryCode ?? this.countryCode,
      languageCode: languageCode ?? this.languageCode,
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      deviceTimeZone: deviceTimeZone ?? this.deviceTimeZone,
      timeFormat: timeFormat ?? this.timeFormat,
      deviceModel: deviceModel ?? this.deviceModel,
      deviceUDID: deviceUDID ?? this.deviceUDID,
      deviceName: deviceName ?? this.deviceName,
      isDebug: isDebug ?? this.isDebug,
      isDeveloperMode: isDeveloperMode ?? this.isDeveloperMode,
      model: model ?? this.model,
      manufacturer: manufacturer ?? this.manufacturer,
      environment: environment ?? this.environment,
      ipAddress: ipAddress ?? this.ipAddress,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}