// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whats_app_chat_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WhatsAppChatModel _$WhatsAppChatModelFromJson(Map<String, dynamic> json) =>
    WhatsAppChatModel(
      userId: json['userId'] as String?,
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      leadName: json['leadName'] as String?,
      contactNo: json['contactNo'] as String?,
      modifiedOn: json['modifiedOn'] == null
          ? null
          : DateTime.parse(json['modifiedOn'] as String),
      modifiedBy: json['modifiedBy'] as String?,
      messages: json['messages'] as String?,
      chatedBy: json['chatedBy'] as String?,
      chatedOn: json['chatedOn'] == null
          ? null
          : DateTime.parse(json['chatedOn'] as String),
    );

Map<String, dynamic> _$WhatsAppChatModelToJson(WhatsAppChatModel instance) =>
    <String, dynamic>{
      if (instance.userId case final value?) 'userId': value,
      if (instance.id case final value?) 'id': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.leadName case final value?) 'leadName': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.modifiedOn?.toIso8601String() case final value?)
        'modifiedOn': value,
      if (instance.modifiedBy case final value?) 'modifiedBy': value,
      if (instance.messages case final value?) 'messages': value,
      if (instance.chatedBy case final value?) 'chatedBy': value,
      if (instance.chatedOn?.toIso8601String() case final value?)
        'chatedOn': value,
    };
