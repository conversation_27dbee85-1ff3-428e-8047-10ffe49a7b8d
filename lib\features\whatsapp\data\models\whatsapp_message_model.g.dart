// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whatsapp_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WhatsappMessageModel _$WhatsappMessageModelFromJson(
        Map<String, dynamic> json) =>
    WhatsappMessageModel(
      id: json['id'] as String?,
      templateName: json['templateName'] as String?,
      message: json['message'] as String?,
      messageId: json['messageId'] as String?,
      customerId: json['customerId'] as String?,
      customerNo: json['customerNo'] as String?,
      userId: json['userId'] as String?,
      rawJson: json['rawJson'] as String?,
      waEvent: $enumDecodeNullable(_$WhatsAppEventsEnumMap, json['waEvent'],
          unknownValue: WhatsAppEvents.none),
      mediaUrl: json['mediaUrl'] as String?,
      mediaType: json['mediaType'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      username: json['username'] as String?,
      createdDateLocalTime: json['createdDateLocalTime'] == null
          ? null
          : DateTime.parse(json['createdDateLocalTime'] as String),
    );

Map<String, dynamic> _$WhatsappMessageModelToJson(
        WhatsappMessageModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.templateName case final value?) 'templateName': value,
      if (instance.message case final value?) 'message': value,
      if (instance.messageId case final value?) 'messageId': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.customerNo case final value?) 'customerNo': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.rawJson case final value?) 'rawJson': value,
      if (_$WhatsAppEventsEnumMap[instance.waEvent] case final value?)
        'waEvent': value,
      if (instance.mediaUrl case final value?) 'mediaUrl': value,
      if (instance.mediaType case final value?) 'mediaType': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
      if (instance.username case final value?) 'username': value,
      if (instance.createdDateLocalTime?.toIso8601String() case final value?)
        'createdDateLocalTime': value,
    };

const _$WhatsAppEventsEnumMap = {
  WhatsAppEvents.none: 0,
  WhatsAppEvents.sent: 1,
  WhatsAppEvents.delivered: 2,
  WhatsAppEvents.read: 3,
  WhatsAppEvents.failed: 4,
  WhatsAppEvents.receive: 5,
  WhatsAppEvents.receivedRead: 6,
};
