// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_property_amenitie_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterPropertyAmenitiesModelAdapter
    extends TypeAdapter<MasterPropertyAmenitiesModel> {
  @override
  final int typeId = 26;

  @override
  MasterPropertyAmenitiesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterPropertyAmenitiesModel(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdBy: fields[2] as String?,
      createdOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      lastModifiedOn: fields[5] as DateTime?,
      deletedOn: fields[6] as DateTime?,
      deletedBy: fields[7] as String?,
      amenityName: fields[8] as String?,
      amenityDisplayName: fields[9] as String?,
      imageURL: fields[10] as String?,
      amenityType: fields[11] as String?,
      category: fields[12] as String?,
      propertyType: (fields[13] as List?)?.cast<PropertyType>(),
      mobileIcon: fields[14] as String?,
      orderRank: fields[15] as int?,
      fullImageURL: fields[16] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterPropertyAmenitiesModel obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.createdOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.deletedOn)
      ..writeByte(7)
      ..write(obj.deletedBy)
      ..writeByte(8)
      ..write(obj.amenityName)
      ..writeByte(9)
      ..write(obj.amenityDisplayName)
      ..writeByte(10)
      ..write(obj.imageURL)
      ..writeByte(11)
      ..write(obj.amenityType)
      ..writeByte(12)
      ..write(obj.category)
      ..writeByte(13)
      ..write(obj.propertyType)
      ..writeByte(14)
      ..write(obj.mobileIcon)
      ..writeByte(15)
      ..write(obj.orderRank)
      ..writeByte(16)
      ..write(obj.fullImageURL);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterPropertyAmenitiesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterPropertyAmenitiesModel _$MasterPropertyAmenitiesModelFromJson(
        Map<String, dynamic> json) =>
    MasterPropertyAmenitiesModel(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      amenityName: json['amenityName'] as String?,
      amenityDisplayName: json['amenityDisplayName'] as String?,
      imageURL: json['imageURL'] as String?,
      amenityType: json['amenityType'] as String?,
      category: json['category'] as String?,
      propertyType: (json['propertyType'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$PropertyTypeEnumMap, e))
          .toList(),
      mobileIcon: json['mobileIcon'] as String?,
      orderRank: (json['orderRank'] as num?)?.toInt(),
      fullImageURL: json['fullImageURL'] as String?,
    );

Map<String, dynamic> _$MasterPropertyAmenitiesModelToJson(
        MasterPropertyAmenitiesModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'amenityName': instance.amenityName,
      'amenityDisplayName': instance.amenityDisplayName,
      'imageURL': instance.imageURL,
      'amenityType': instance.amenityType,
      'category': instance.category,
      'propertyType':
          instance.propertyType?.map((e) => _$PropertyTypeEnumMap[e]!).toList(),
      'mobileIcon': instance.mobileIcon,
      'orderRank': instance.orderRank,
      'fullImageURL': instance.fullImageURL,
    };

const _$PropertyTypeEnumMap = {
  PropertyType.residential: 0,
  PropertyType.commercial: 1,
  PropertyType.agricultural: 2,
};
