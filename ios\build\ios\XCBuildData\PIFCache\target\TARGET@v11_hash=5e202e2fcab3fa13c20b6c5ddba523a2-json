{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc1f32b1f3b62bc878476821934e9784", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98900761a7248653218f0be1f23afa62b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98900761a7248653218f0be1f23afa62b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bb296f411e1a633509dfbf91a908a9f", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f98a11c58a0c92a740509d3373bf09f9", "guid": "bfdfe7dc352907fc980b868725387e98969408efbabd6c244fb31026a70b0e67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616210ccbd2819d28cb3fd9895f23447", "guid": "bfdfe7dc352907fc980b868725387e98ccc662fd71b0284b67383206314978f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e099728cdc02508c1d7e8a9f46e4d2d", "guid": "bfdfe7dc352907fc980b868725387e98a1d9f2375a69945874d713b2e9d10914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0f8da6303c80d2605ebc6aa8454f2f", "guid": "bfdfe7dc352907fc980b868725387e9831c4597a13c2a83e1eb4b6365e79c8a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a238641fed44435bfe9ce67714fe4b9", "guid": "bfdfe7dc352907fc980b868725387e98a38a8909fbb968ee8962d0eb3a64c90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca44290161977e21438028e82b85eb2", "guid": "bfdfe7dc352907fc980b868725387e98c0986a0665428367160e1e55ec465718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988341fee5cd99e569641bf8752f6dd636", "guid": "bfdfe7dc352907fc980b868725387e98897de58191c27aa4fe939008a06c809d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9213e29118097dfa4f3b8397f4a48bf", "guid": "bfdfe7dc352907fc980b868725387e9854c5968b776e8718b35f17e0dff8eb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985704e0d0c637f3eeb7584708c7317ff1", "guid": "bfdfe7dc352907fc980b868725387e98ab96995fc8e4a45a40f3c884fe968273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857b15899877050ab152ad1993015385e", "guid": "bfdfe7dc352907fc980b868725387e9899af2dda6527bf8fb092669ac6408cf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae8656520691134b7523f17c13b1692", "guid": "bfdfe7dc352907fc980b868725387e9876349763c5b1cf0979ef72fba08af750"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee851f4936dc54d300ca2b7ef38ab0cc", "guid": "bfdfe7dc352907fc980b868725387e98807a98cc2e15ab353cc80cd5a2911dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443cae799920331857f5321e97300c17", "guid": "bfdfe7dc352907fc980b868725387e98f4fd326e0ca504595dc36191fefcf041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb306e601bba710ba3ec4cd40460e7ef", "guid": "bfdfe7dc352907fc980b868725387e98614e7b84060dfca1532c9261932bedba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982209cd394ca536bb2308cb309ee10430", "guid": "bfdfe7dc352907fc980b868725387e986f8ab02e63746154e009cf16b40b5ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850000025abbdba8677c1fc8f06f4b36d", "guid": "bfdfe7dc352907fc980b868725387e984722d323bbb9bbc17b36d513ba5ca692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8bcd0ee6cd41d8be80cb35af74339e1", "guid": "bfdfe7dc352907fc980b868725387e98023a186b4da6cb3c95409fe9cabdc07f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea570afb54e18999a023c8b5ff46552", "guid": "bfdfe7dc352907fc980b868725387e9829f9bddb91fc2ae033d4ff0390bd4077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98092f0a425ab045c7077722f571eba044", "guid": "bfdfe7dc352907fc980b868725387e9883231659dc0afd4499e758a9f0ac4bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f834bb1121e79720d7d30e5b8d20e50d", "guid": "bfdfe7dc352907fc980b868725387e980578bc68fa4d6001c19f15cea2cf0916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d315e2f5158c3f6bd7ef7f9d7759070c", "guid": "bfdfe7dc352907fc980b868725387e98127007af8599b62b2d07e49be3a3a435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aa26c5f05ff86c16fb5a283749072b9", "guid": "bfdfe7dc352907fc980b868725387e984780857419970627e136821200fd6ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f98f64cbe988e0f2404de7a639c9f245", "guid": "bfdfe7dc352907fc980b868725387e9801dc05cf4d659d6c5ffddde739f677ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4566323d3b11205c9abc76d1a376196", "guid": "bfdfe7dc352907fc980b868725387e98f8e623a8064d5de453d3f5b25a482784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c48cb010454e6745c5dbcbb008d469", "guid": "bfdfe7dc352907fc980b868725387e9896432c7de9753ce65d6c09472c6f77d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d17b632dce330653622f9e83101854", "guid": "bfdfe7dc352907fc980b868725387e9860763840ab26c983d8fa8905475b90b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6ec0704093044017ec72e165081544", "guid": "bfdfe7dc352907fc980b868725387e9896909fd38aa76b2683f8efc4d8ccc7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b69034e188c6c2618a3be15dae6278a", "guid": "bfdfe7dc352907fc980b868725387e98062843da7211295493f6e6943e3833cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb81759483d726612a75b2322c30310", "guid": "bfdfe7dc352907fc980b868725387e983424fea5e879691c8f8c58e5ec913d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814ad477994b82ec149ce51897f39c76b", "guid": "bfdfe7dc352907fc980b868725387e985f2d59c25bbf2bc9057916c51b751229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b0513b8ea5f4cdc3b8a61be3c630f3", "guid": "bfdfe7dc352907fc980b868725387e98e039bbe167efa69be6380f8e93017b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e241b7f423ab3418037775bd950a2ab", "guid": "bfdfe7dc352907fc980b868725387e98ebbe03d7b48c2a6be2ce2affa2b6ae48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eef82e81917e56dea24a558a80c752f", "guid": "bfdfe7dc352907fc980b868725387e98810205a4c17760bb9cdceb27780dc2fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa610124a8411a875135e925678ef4e", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071518bfcd4363bd4d55679d1f8059c4", "guid": "bfdfe7dc352907fc980b868725387e9848c8613314694b7b2103a9a1feff2255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838d65abd7df42569154ce998e85ac874", "guid": "bfdfe7dc352907fc980b868725387e98b081cf1e1109fd8daeefbf5d083c8d20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bdeb1069c15f5d20687257a88c0cdb0", "guid": "bfdfe7dc352907fc980b868725387e9892f7d93ec0427d7daa11e33b8c3504a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4fab24f6fb07df707ccda17a45406af", "guid": "bfdfe7dc352907fc980b868725387e98ec2088e131689a01ad1b2b5d940a7622"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}