<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
		<key>CFBundleAlternateIcons</key>
		<dict>
			<key>prowinIcon</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>prowinIcon</string>
				</array>
				<key>UIPrerenderedIcon</key>
				<false/>
			</dict>
			<key>kanjauIcon</key>
             <dict>
                <key>CFBundleIconFiles</key>
                 <array>
                     <string>kanjauIcon</string>
                 </array>
                 <key>UIPrerenderedIcon</key>
                <false/>
                        	</dict>
			<key>realtorsHubIcon</key>
            			<dict>
            				<key>CFBundleIconFiles</key>
            				<array>
            					<string>realtorsHubIcon</string>
            				</array>
            				<key>UIPrerenderedIcon</key>
            				<false/>
            			</dict>
			<key>hjProperties</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>hjProperties</string>
				</array>
				<key>UIPrerenderedIcon</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Leadrat</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>leadrat</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
		<string>whatsapp</string>
		<string>whatsappbusiness</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<!-- Allows the app to access the camera for capturing profile images and documents -->
	<key>NSCameraUsageDescription</key>
	<string>As a CRM software, the camera permission is requested to enable users to upload and set a profile image by capturing picture through their camera, and easily update their information. With this permission, users can conveniently personalize their profile,  and keep their information up to date.</string>
	<!-- Allows the app to access contacts for importing contact details -->
	<key>NSContactsUsageDescription</key>
	<string>As a CRM software, the contacts permission is requested when the user wants to import the contact details from their phone application.</string>
	<!-- Allows the app to access the user's location for attendance and lead location features -->
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>As a CRM software, the location permission is requested  when the user wants to log in and keep a track of attendance, And while adding the lead location if the user wants to update the current location.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>As a CRM software, the location permission is requested  when the user wants to log in and keep a track of attendance, And while adding the lead location if the user wants to update the current location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>As a CRM software, the location permission is requested  when the user wants to log in and keep a track of attendance, And while adding the lead location if the user wants to update the current location.</string>
	<!-- Allows the app to access the microphone for voice features -->
	<key>NSMicrophoneUsageDescription</key>
	<string>As a CRM software, the microphone permission is requested when the user wants to have a conversation with the lead in our crm</string>
	<!-- Allows the app to access the photo library for uploading and setting profile images -->
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>As a CRM software, the photo permission is requested to enable users to upload and set a profile image by selecting a picture from their photos app, and easily update their information. With this permission, users can conveniently personalize their profile, and keep their information up to date.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>As a CRM software, the photo permission is requested to enable users to upload and set a profile image by selecting a picture from their photos app, and easily update their information. With this permission, users can conveniently personalize their profile, and keep their information up to date.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
     </dict>
</dict>
</plist>
