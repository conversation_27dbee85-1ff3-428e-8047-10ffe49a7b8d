// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_amenity_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomAmenitiesModelAdapter extends TypeAdapter<CustomAmenitiesModel> {
  @override
  final int typeId = 45;

  @override
  CustomAmenitiesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomAmenitiesModel(
      categoryName: fields[0] as String?,
      amenities: (fields[1] as List?)?.cast<CustomAmenityModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, CustomAmenitiesModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.categoryName)
      ..writeByte(1)
      ..write(obj.amenities);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomAmenitiesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomAmenityModelAdapter extends TypeAdapter<CustomAmenityModel> {
  @override
  final int typeId = 46;

  @override
  CustomAmenityModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomAmenityModel(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdBy: fields[2] as String?,
      createdOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      lastModifiedOn: fields[5] as DateTime?,
      deletedOn: fields[6] as DateTime?,
      deletedBy: fields[7] as String?,
      amenityName: fields[8] as String?,
      amenityDisplayName: fields[9] as String?,
      imageURL: fields[10] as String?,
      inActiveImageURL: fields[11] as String?,
      amenityType: fields[12] as String?,
      category: fields[13] as String?,
      propertyType: (fields[14] as List?)?.cast<PropertyType>(),
      orderRank: fields[15] as int?,
      mobileIcon: fields[16] as String?,
      isActive: fields[17] as bool?,
      masterAmenityId: fields[18] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CustomAmenityModel obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.createdOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.deletedOn)
      ..writeByte(7)
      ..write(obj.deletedBy)
      ..writeByte(8)
      ..write(obj.amenityName)
      ..writeByte(9)
      ..write(obj.amenityDisplayName)
      ..writeByte(10)
      ..write(obj.imageURL)
      ..writeByte(11)
      ..write(obj.inActiveImageURL)
      ..writeByte(12)
      ..write(obj.amenityType)
      ..writeByte(13)
      ..write(obj.category)
      ..writeByte(14)
      ..write(obj.propertyType)
      ..writeByte(15)
      ..write(obj.orderRank)
      ..writeByte(16)
      ..write(obj.mobileIcon)
      ..writeByte(17)
      ..write(obj.isActive)
      ..writeByte(18)
      ..write(obj.masterAmenityId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomAmenityModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomAmenitiesModel _$CustomAmenitiesModelFromJson(
        Map<String, dynamic> json) =>
    CustomAmenitiesModel(
      categoryName: json['categoryName'] as String?,
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => CustomAmenityModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CustomAmenitiesModelToJson(
        CustomAmenitiesModel instance) =>
    <String, dynamic>{
      'categoryName': instance.categoryName,
      'amenities': instance.amenities,
    };

CustomAmenityModel _$CustomAmenityModelFromJson(Map<String, dynamic> json) =>
    CustomAmenityModel(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      amenityName: json['amenityName'] as String?,
      amenityDisplayName: json['amenityDisplayName'] as String?,
      imageURL: json['imageURL'] as String?,
      inActiveImageURL: json['inActiveImageURL'] as String?,
      amenityType: json['amenityType'] as String?,
      category: json['category'] as String?,
      propertyType: (json['propertyType'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$PropertyTypeEnumMap, e))
          .toList(),
      orderRank: (json['orderRank'] as num?)?.toInt(),
      mobileIcon: json['mobileIcon'] as String?,
      isActive: json['isActive'] as bool?,
      masterAmenityId: json['masterAmenityId'] as String?,
    );

Map<String, dynamic> _$CustomAmenityModelToJson(CustomAmenityModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'amenityName': instance.amenityName,
      'amenityDisplayName': instance.amenityDisplayName,
      'imageURL': instance.imageURL,
      'inActiveImageURL': instance.inActiveImageURL,
      'amenityType': instance.amenityType,
      'category': instance.category,
      'propertyType':
          instance.propertyType?.map((e) => _$PropertyTypeEnumMap[e]!).toList(),
      'orderRank': instance.orderRank,
      'mobileIcon': instance.mobileIcon,
      'isActive': instance.isActive,
      'masterAmenityId': instance.masterAmenityId,
    };

const _$PropertyTypeEnumMap = {
  PropertyType.residential: 0,
  PropertyType.commercial: 1,
  PropertyType.agricultural: 2,
};
