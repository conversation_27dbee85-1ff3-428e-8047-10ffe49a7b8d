// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class EventAdapter extends TypeAdapter<Event> {
  @override
  final int typeId = 21;

  @override
  Event read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Event.signUp;
      case 1:
        return Event.login;
      case 2:
        return Event.logout;
      case 3:
        return Event.leadStatusToCallback;
      case 4:
        return Event.callbackReminder;
      case 5:
        return Event.leadStatusToScheduleMeeting;
      case 6:
        return Event.scheduleMeetingReminder;
      case 7:
        return Event.leadStatusToScheduleSiteVisit;
      case 8:
        return Event.scheduleSiteVisitReminder;
      case 9:
        return Event.leadStatusToConvert;
      case 10:
        return Event.leadStatusToNotInterest;
      case 11:
        return Event.leadSatusToDropped;
      case 12:
        return Event.leadStatusToPending;
      case 13:
        return Event.singleTaskComplete;
      case 14:
        return Event.multipleTaskComplete;
      case 15:
        return Event.taskCreation;
      case 16:
        return Event.taskUpdation;
      case 17:
        return Event.scheduledTaskReminder;
      case 18:
        return Event.taskOverdue;
      case 19:
        return Event.taskPending;
      case 20:
        return Event.leadFromPropertyMicrositeToBuy;
      case 21:
        return Event.leadFromPropertyMicrositeForRent;
      case 22:
        return Event.leadFromPortfolioMicrosite;
      case 23:
        return Event.integrationAccCreation;
      case 24:
        return Event.leadFromIntegration;
      case 25:
        return Event.propertyCreation;
      case 26:
        return Event.propertyDeletion;
      case 27:
        return Event.propertySoftDeletion;
      case 28:
        return Event.dusKaDumChallengeStarts;
      case 29:
        return Event.supportQuery;
      case 30:
        return Event.leadAssignment;
      case 31:
        return Event.taskAssignment;
      case 32:
        return Event.unAssignedLeadUpdate;
      default:
        return Event.signUp;
    }
  }

  @override
  void write(BinaryWriter writer, Event obj) {
    switch (obj) {
      case Event.signUp:
        writer.writeByte(0);
        break;
      case Event.login:
        writer.writeByte(1);
        break;
      case Event.logout:
        writer.writeByte(2);
        break;
      case Event.leadStatusToCallback:
        writer.writeByte(3);
        break;
      case Event.callbackReminder:
        writer.writeByte(4);
        break;
      case Event.leadStatusToScheduleMeeting:
        writer.writeByte(5);
        break;
      case Event.scheduleMeetingReminder:
        writer.writeByte(6);
        break;
      case Event.leadStatusToScheduleSiteVisit:
        writer.writeByte(7);
        break;
      case Event.scheduleSiteVisitReminder:
        writer.writeByte(8);
        break;
      case Event.leadStatusToConvert:
        writer.writeByte(9);
        break;
      case Event.leadStatusToNotInterest:
        writer.writeByte(10);
        break;
      case Event.leadSatusToDropped:
        writer.writeByte(11);
        break;
      case Event.leadStatusToPending:
        writer.writeByte(12);
        break;
      case Event.singleTaskComplete:
        writer.writeByte(13);
        break;
      case Event.multipleTaskComplete:
        writer.writeByte(14);
        break;
      case Event.taskCreation:
        writer.writeByte(15);
        break;
      case Event.taskUpdation:
        writer.writeByte(16);
        break;
      case Event.scheduledTaskReminder:
        writer.writeByte(17);
        break;
      case Event.taskOverdue:
        writer.writeByte(18);
        break;
      case Event.taskPending:
        writer.writeByte(19);
        break;
      case Event.leadFromPropertyMicrositeToBuy:
        writer.writeByte(20);
        break;
      case Event.leadFromPropertyMicrositeForRent:
        writer.writeByte(21);
        break;
      case Event.leadFromPortfolioMicrosite:
        writer.writeByte(22);
        break;
      case Event.integrationAccCreation:
        writer.writeByte(23);
        break;
      case Event.leadFromIntegration:
        writer.writeByte(24);
        break;
      case Event.propertyCreation:
        writer.writeByte(25);
        break;
      case Event.propertyDeletion:
        writer.writeByte(26);
        break;
      case Event.propertySoftDeletion:
        writer.writeByte(27);
        break;
      case Event.dusKaDumChallengeStarts:
        writer.writeByte(28);
        break;
      case Event.supportQuery:
        writer.writeByte(29);
        break;
      case Event.leadAssignment:
        writer.writeByte(30);
        break;
      case Event.taskAssignment:
        writer.writeByte(31);
        break;
      case Event.unAssignedLeadUpdate:
        writer.writeByte(32);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EventAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
