import 'package:leadrat/core_main/common/data/device/models/device_config_model.dart';
import 'package:leadrat/core_main/common/data/device/data_source/device_data_source.dart';
import 'package:leadrat/core_main/common/data/device/models/device_info_model.dart';
import 'package:leadrat/core_main/common/data/device/models/device_registration_info_model.dart';
import 'package:leadrat/core_main/common/data/device/models/registration_device_response_model.dart';
import '../../../../remote/leadrat_rest_service.dart';
import '../../../../remote/rest_response/rest_response_wrapper.dart';
import '../../../../resources/common/rest_resources.dart';

class DeviceDataSourceImpl extends LeadratRestService implements DeviceDataSource {
  @override
  Future<RegistrationDeviceResponseModel> changeDeviceRegistration(DeviceRegistrationInfoModel deviceRegistrationInfoModel) async {
    final restRequest = createPutRequest(SettingsRestResources.updateDeviceInfoById(deviceRegistrationInfoModel.endpointId ?? ''), body: deviceRegistrationInfoModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<RegistrationDeviceResponseModel?>>(
      restRequest,
          (json) => ResponseWrapper<RegistrationDeviceResponseModel?>.fromJson(json, (data) => fromJsonObject(data, RegistrationDeviceResponseModel.fromJson)),
    );
    return response.data ?? RegistrationDeviceResponseModel();
  }

  @override
  Future<String?> postDeviceConfig(DeviceConfigModel deviceConfigModel) async {
    // final restRequest = createPostRequest(SettingsRestResources.registerDeviceInfo(), body: deviceInfo.toJson() ?? '');
    // final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    // return response.data;
    return null;
  }

  @override
  Future<String?> postDeviceInfo(DeviceInfoModel deviceInfo) async {
    final restRequest = createPostRequest(SettingsRestResources.registerDeviceInfo(), body: deviceInfo.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }
}