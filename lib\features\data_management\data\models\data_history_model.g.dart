// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DataHistoryModel _$DataHistoryModelFromJson(Map<String, dynamic> json) =>
    DataHistoryModel(
      fieldName: json['fieldName'] as String?,
      oldValue: json['oldValue'] as String?,
      newValue: json['newValue'] as String?,
      modifiedBy: json['modifiedBy'] as String?,
      modifiedOn: json['modifiedOn'] == null
          ? null
          : DateTime.parse(json['modifiedOn'] as String),
    );

Map<String, dynamic> _$DataHistoryModelToJson(DataHistoryModel instance) =>
    <String, dynamic>{
      'fieldName': instance.fieldName,
      'oldValue': instance.oldValue,
      'newValue': instance.newValue,
      'modifiedBy': instance.modifiedBy,
      'modifiedOn': instance.modifiedOn?.toIso8601String(),
    };

DataHistoryResponseModel _$DataHistoryResponseModelFromJson(
        Map<String, dynamic> json) =>
    DataHistoryResponseModel(
      succeeded: json['succeeded'] as bool?,
      message: json['message'] as String?,
      errors:
          (json['errors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      data: (json['data'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as Map<String, dynamic>).map(
              (k, e) => MapEntry(
                  k,
                  (e as List<dynamic>)
                      .map((e) =>
                          DataHistoryModel.fromJson(e as Map<String, dynamic>))
                      .toList()),
            )),
      ),
    );

Map<String, dynamic> _$DataHistoryResponseModelToJson(
        DataHistoryResponseModel instance) =>
    <String, dynamic>{
      'succeeded': instance.succeeded,
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };
