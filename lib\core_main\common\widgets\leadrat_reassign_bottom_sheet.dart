import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

void leadratReAssignBottomSheet({
  required BuildContext context,
  required String title,
  required String assignedUser,
  required List<ItemSimpleModel> selectableItems,
  required ItemSimpleModel? selectedItem,
  required Function(ItemSimpleModel) onItemSelected,
  bool isIvr = false,
}) {
  List<ItemSimpleModel> items = List.from(selectableItems);

  List<ItemSimpleModel> filteredItems = List.from(selectableItems);

  List<ItemSimpleModel> tempSelectedItems = selectedItem != null ? [selectedItem] : [];

  final index = items.indexWhere((element) => element.description == selectedItem?.description);
  if (!isIvr && index != -1) {
    items[index] = items[index].copyWith(isSelected: true);
  }
  filteredItems = [...tempSelectedItems, ...items.where((item) => !tempSelectedItems.contains(item))];
  bool isVisible = false;
  TextEditingController searchController = TextEditingController();

  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: StatefulBuilder(builder: (context, setState) {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned(top: -6, left: 0, right: 0, child: CustomPaint(size: Size(context.width(100), 6), painter: TrapeziumPainter())),
              Container(
                width: context.width(100),
                height: context.height(75),
                padding: const EdgeInsets.all(20),
                color: ColorPalette.black,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary)),
                    const SizedBox(width: 30, child: Divider(color: ColorPalette.primary, thickness: 2.0)),
                    const SizedBox(height: 10),
                    isIvr
                        ? SizedBox(
                            width: context.width(100),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text('Agent not registered with IVR', style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.peanutButter)),
                                const SizedBox(height: 20),
                                Text('You can register yourself as an agent on IVR portal', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                                const SizedBox(height: 14),
                                Text('or', style: LexendTextStyles.lexend16SemiBold.copyWith(color: ColorPalette.primary)),
                                const SizedBox(height: 14),
                                Text('You can select the agent from below list', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                              ],
                            ),
                          )
                        : Row(
                            children: [
                              Text('assigned to ', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                              const SizedBox(width: 10),
                              Chip(
                                label: Text(assignedUser, style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                                padding: EdgeInsets.zero,
                                labelStyle: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary),
                                visualDensity: const VisualDensity(horizontal: 0.0, vertical: -4),
                                backgroundColor: ColorPalette.primaryLightColor,
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5), side: const BorderSide(color: ColorPalette.lightBackground)),
                              ),
                            ],
                          ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: ListView(
                        children: [
                          if (!isIvr) Text('assign to', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isVisible = !isVisible;
                                filteredItems = selectedItem != null ? [selectedItem!, ...filteredItems.where((item) => isIvr ? selectedItem?.title != item.title : selectedItem?.description != item.description)] : [...filteredItems];
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                              margin: const EdgeInsets.only(top: 10),
                              decoration: BoxDecoration(
                                border: Border.all(color: ColorPalette.lightBackground),
                                borderRadius: BorderRadius.circular(8),
                                color: ColorPalette.primaryColor,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(selectedItem?.title ?? 'Select Number', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                                  const Icon(Icons.keyboard_arrow_down, color: ColorPalette.primary),
                                ],
                              ),
                            ),
                          ),
                          Visibility(
                            visible: isVisible,
                            child: Container(
                              height: 250,
                              padding: const EdgeInsets.only(right: 5),
                              margin: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: ColorPalette.primary,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                children: [
                                  if (!isIvr)
                                    Container(
                                      height: 40,
                                      decoration: BoxDecoration(
                                        border: const Border(bottom: BorderSide(color: ColorPalette.lightBackground)),
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      alignment: Alignment.center,
                                      child: TextField(
                                        controller: searchController,
                                        onChanged: (value) {
                                          setState(() {
                                            if (value.isEmpty) {
                                              filteredItems = selectedItem != null ? [selectedItem!, ...items.where((item) => isIvr ? selectedItem?.title != item.title : selectedItem?.description != item.description)] : [...items];
                                            } else {
                                              filteredItems = items.where((element) => element.title.toLowerCase().contains(value.toLowerCase())).toList();
                                            }
                                          });
                                        },
                                        keyboardType: TextInputType.text,
                                        decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintText: 'Search Users',
                                          hintStyle: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.lightBackground),
                                          suffixIcon: searchController.text.isNotEmpty
                                              ? IconButton(
                                                  icon: const Icon(
                                                    Icons.clear,
                                                    color: ColorPalette.black,
                                                    size: 20,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      searchController.clear();
                                                      filteredItems = selectedItem != null ? [selectedItem!, ...items.where((item) => isIvr ? selectedItem?.title != item.title : selectedItem?.description != item.description)] : [...items];
                                                    });
                                                  },
                                                )
                                              : null,
                                        ),
                                        style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.black),
                                      ),
                                    ),
                                  Expanded(
                                    child: ListView.builder(
                                      itemCount: filteredItems.length,
                                      itemBuilder: (context, index) {
                                        return RadioListTile(
                                          value: filteredItems[index],
                                          groupValue: filteredItems.firstWhereOrNull((element) => element.isSelected) ?? false,
                                          onChanged: (value) {
                                            setState(() {
                                              filteredItems = filteredItems
                                                  .map((e) => e.copyWith(
                                                        isSelected: isIvr ? e.title == filteredItems[index].title : e.description == filteredItems[index].description,
                                                      ))
                                                  .toList();
                                              selectedItem = filteredItems.firstWhereOrNull((element) => element.isSelected) ?? ItemSimpleModel(title: '');
                                              isVisible = false;
                                            });
                                          },
                                          activeColor: ColorPalette.primaryGreen,
                                          contentPadding: EdgeInsets.zero,
                                          visualDensity: const VisualDensity(vertical: -4),
                                          title: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Expanded(child: Text(filteredItems[index].title, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primaryDarkColor, overflow: TextOverflow.ellipsis))),
                                              if (!(filteredItems[index].isEnabled ?? true)) Text("(disabled)", style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.fadedRed, overflow: TextOverflow.ellipsis)),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          LeadratFormButton(onPressed: () => Navigator.pop(context), buttonText: isIvr ? 'cancel' : 'back'),
                          LeadratFormButton(
                              onPressed: () {
                                onItemSelected(selectedItem ?? ItemSimpleModel(title: ''));
                                Navigator.pop(context);
                              },
                              buttonText: isIvr ? 'connect call' : 'assign data',
                              isTrailingVisible: true),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
      );
    },
  );
}
