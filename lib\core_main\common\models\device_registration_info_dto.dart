class DeviceRegistrationInformationDTO {
  String endpointId;
  int platform;
  String deviceModel;
  String deviceUDID;
  String applicationTokenValue;
  String userId;
  String userName;
  String email;
  String token;
  bool isActive;
  bool isTablet;
  String appVersion;
  List<String> ADSecurityGroups;
  List<String> topics;

  DeviceRegistrationInformationDTO({
    required this.endpointId,
    required this.platform,
    required this.deviceModel,
    required this.deviceUDID,
    required this.applicationTokenValue,
    required this.userId,
    required this.userName,
    required this.email,
    required this.token,
    this.isActive = true,
    this.isTablet = false,
    required this.appVersion,
    this.ADSecurityGroups = const [],
    this.topics = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'EndpointId': endpointId,
      'Platform': platform,
      'DeviceModel': deviceModel,
      'DeviceUDID': deviceUDID,
      'ApplicationTokenValue': applicationTokenValue,
      'UserId': userId,
      'UserName': userName,
      'Email': email,
      'Token': token,
      'IsActive': isActive,
      'IsTablet': isTablet,
      'AppVersion': appVersion,
      'ADSecurityGroups': ADSecurityGroups,
      'Topics': topics,
    };
  }
}
