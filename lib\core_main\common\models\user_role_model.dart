import 'package:json_annotation/json_annotation.dart';

part 'user_role_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class UserRoleModel {
  final String? roleId;
  final String? roleName;
  final String? description;
  final bool? enabled;

  UserRoleModel({
    required this.roleId,
    required this.roleName,
    required this.description,
    required this.enabled,
  });

  factory UserRoleModel.fromJson(Map<String, dynamic> json) =>
      _$UserRoleModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserRoleModelToJson(this);
}
