import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import '../../models/master_user_services_type_model.dart';

abstract class MasterDataLocalDataSource {
  List<MasterAreaUnitsModel>? getAreaUnits();

  Future<void> saveAreaUnits(List<MasterAreaUnitsModel?>? items);

  List<MasterProjectTypeModel>? getProjectTypes();

  Future<void> saveProjectTypes(List<MasterProjectTypeModel?>? items);

  Future<void> saveModifiedDateModels(List<ModifiedDateModel> models);

  List<ModifiedDateModel> getModifiedDateModels();

  Future<void> updateModifiedDateModel(ModifiedDateModel model);

  List<MasterLeadSourceModel>? getLeadSource();

  Future<void> saveLeadSource(List<MasterLeadSourceModel?>? items);

  List<MasterLeadStatusModel>? getLeadStatuses();

  Future<void> saveLeadStatuses(List<MasterLeadStatusModel?>? items);

  Map<String, List<MasterPropertyAmenitiesModel>>? getPropertyAmenitites();

  Map<String, List<MasterPropertyAmenitiesModel>>? getPropertyListingAmenitites();

  Future<void> savePropertyAmenitites(Map<String, List<MasterPropertyAmenitiesModel>>? items);

  Future<void> savePropertyListingAmenitites(Map<String, List<MasterPropertyAmenitiesModel>>? items);

  List<MasterPropertyAttributesModel>? getPropertyAttributes();

  Future<void> savePropertyAttributes(List<MasterPropertyAttributesModel?>? items);

  List<MasterAssociatedBankModel>? getAssociatedBank();

  Future<void> saveAssociatedBank(List<MasterAssociatedBankModel?>? items);

  List<MasterProjectAttributeModel>? getProjectAttributes();

  Future<void> saveProjectAttributes(List<MasterProjectAttributeModel?>? items);

  Map<String, List<MasterProjectAmenititesModel>>? getProjectAmenitites();

  Future<void> saveProjectAmenitites(Map<String, List<MasterProjectAmenititesModel>>? items);

  Future<void> savePropertyTypes(List<MasterPropertyTypeModel?>? items);

  Future<void> saveListingPropertyTypes(List<MasterPropertyTypeModel?>? items);

  List<MasterPropertyTypeModel>? getPropertyTypes();

  List<MasterPropertyTypeModel>? getListingPropertyTypes();

  Future<void> saveUserServices(List<MasterUserServicesModel?>? items);

  List<MasterUserServicesModel>? getUserServices();

  Future<void> saveCustomStatus(List<MasterCustomStatusModel?>? items);

  List<MasterCustomStatusModel>? getLeadCustomStatus();

  Future<void> saveAgencyNames(List<String>? items);

  List<String>? getAgencyNames();

  Future<void> saveLeadAddresses(List<String>? items);

  List<String>? getLeadAddresses();
}
