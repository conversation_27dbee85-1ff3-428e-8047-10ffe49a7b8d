import 'package:leadrat/core_main/common/data/saved_filter/data_source/remote/saved_filter_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/create_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/repository/saved_filter_repository.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';

class SavedFilterRepositoryImpl implements SavedFilterRepository {
  final SavedFilterRemoteDataSource _savedFilterRemoteDataSource;

  SavedFilterRepositoryImpl(this._savedFilterRemoteDataSource);

  @override
  Future<String> createSavedFilter(CreateSavedFilterModel model) async {
    try {
      return await _savedFilterRemoteDataSource.createSavedFilter(model);
    } catch (ex) {
      ex.logException();
      return '';
    }
  }

  @override
  Future<bool> deleteSavedFilter(String id) async {
    try {
      return await _savedFilterRemoteDataSource.deleteSavedFilter(id);
    } catch (ex) {
      ex.logException();
      return false;
    }
  }

  @override
  Future<GetSavedFilterModel?> getSavedFilterById(String id) async {
    try {
      return await _savedFilterRemoteDataSource.getSavedFilterById(id);
    } catch (ex) {
      ex.logException();
      return null;
    }
  }

  @override
  Future<PagedResponse<GetSavedFilterModel, String>?> getSavedFilters({required String module, String? searchText}) async {
    try {
      return await _savedFilterRemoteDataSource.getSavedFilters(module: module, searchText: searchText);
    } catch (ex) {
      ex.logException();
      return null;
    }
  }

  @override
  Future<String> updateSavedFilter(CreateSavedFilterModel model) async {
    try {
      return await _savedFilterRemoteDataSource.updateSavedFilter(model);
    } catch (ex) {
      ex.logException();
      return '';
    }
  }

  @override
  Future<bool> checkIfFilterNameExits({required String filterName, required String module}) async {
    try {
      return await _savedFilterRemoteDataSource.checkIfFilterNameExits(filterName: filterName, module: module);
    } catch (ex) {
      ex.logException();
      return false;
    }
  }
}
