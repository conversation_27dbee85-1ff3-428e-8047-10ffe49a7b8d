import 'package:leadrat/core_main/common/data/auth_tokens/models/get_token_model.dart';
import 'package:leadrat/core_main/common/models/base_url_model.dart';
import 'package:leadrat/core_main/common/models/force_update_model.dart';

abstract interface class AuthTokenDataSource {
  Future<GetTokenModel?> getRefreshToken({required String tenant, required String idToken, required String refreshToken});

  Future<GetTokenModel?> getToken({required String tenant, required String userName, required String password});

  Future<String?> generateMultiFactorAuthOtp({required String userName, required String tenant});

  Future<GetTokenModel?> verifyAuthOtp({required String userName, required String sessionId, required String otp, required String tenant});

  Future<BaseUrlModel?> getBaseUrl();

  Future<ForceUpdateModel?> getUpdateAppVersion();
}
