import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum Facing {
  unknown(0, 'unknown'),
  east(1, "East"),
  west(2, "West"),
  north(3, "North"),
  south(4, "South"),
  northEast(5, "North-East"),
  northWest(6, "North-West"),
  southEast(7, "South-East"),
  southWest(8, "South-West");

  final int value;
  final String description;

  const Facing(this.value, this.description);
}
