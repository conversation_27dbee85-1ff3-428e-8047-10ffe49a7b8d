// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetNotificationModel _$GetNotificationModelFromJson(
        Map<String, dynamic> json) =>
    GetNotificationModel(
      title: json['title'] as String?,
      description: json['description'] as String?,
      isOpened: json['isOpened'] as bool?,
      leadId: json['leadId'] as String?,
      deliveredTime: json['deliveredTime'] as String?,
      fcmDeepLinkUrl: json['fcmDeepLinkUrl'] as String?,
      notificationUniqueid: json['notificationUniqueid'] as String?,
      isDelivered: json['isDelivered'] as bool?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      isLocalNotification: json['isLocalNotification'] as bool?,
    );

Map<String, dynamic> _$GetNotificationModelToJson(
        GetNotificationModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'isOpened': instance.isOpened,
      'leadId': instance.leadId,
      'deliveredTime': instance.deliveredTime,
      'fcmDeepLinkUrl': instance.fcmDeepLinkUrl,
      'notificationUniqueid': instance.notificationUniqueid,
      'isDelivered': instance.isDelivered,
      'createdOn': instance.createdOn?.toIso8601String(),
      'isLocalNotification': instance.isLocalNotification,
    };
