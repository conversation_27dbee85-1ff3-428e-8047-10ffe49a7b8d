// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role_permission_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RolePermissionModelAdapter extends TypeAdapter<RolePermissionModel> {
  @override
  final int typeId = 109;

  @override
  RolePermissionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RolePermissionModel(
      id: fields[0] as String?,
      createdOn: fields[1] as DateTime?,
      createdBy: fields[2] as String?,
      lastModifiedOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      isNew: fields[5] as bool?,
      name: fields[6] as String?,
      description: fields[7] as String?,
      enabled: fields[8] as bool?,
      permissions: (fields[9] as List?)?.cast<String?>(),
      permissionDescription: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, RolePermissionModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdOn)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.lastModifiedOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.isNew)
      ..writeByte(6)
      ..write(obj.name)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.enabled)
      ..writeByte(9)
      ..write(obj.permissions)
      ..writeByte(10)
      ..write(obj.permissionDescription);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RolePermissionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RolePermissionModel _$RolePermissionModelFromJson(Map<String, dynamic> json) =>
    RolePermissionModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      isNew: json['isNew'] as bool?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      enabled: json['enabled'] as bool?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      permissionDescription: json['permissionDescription'] as String?,
    );

Map<String, dynamic> _$RolePermissionModelToJson(
        RolePermissionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'isNew': instance.isNew,
      'name': instance.name,
      'description': instance.description,
      'enabled': instance.enabled,
      'permissions': instance.permissions,
      'permissionDescription': instance.permissionDescription,
    };
