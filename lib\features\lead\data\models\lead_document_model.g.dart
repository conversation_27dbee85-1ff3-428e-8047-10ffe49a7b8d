// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadDocumentModel _$LeadDocumentModelFromJson(Map<String, dynamic> json) =>
    LeadDocumentModel(
      documentType:
          $enumDecodeNullable(_$DocumentTypeEnumMap, json['documentType']),
      documentName: json['documentName'] as String?,
      filePath: json['filePath'] as String?,
      uploadedOn: json['uploadedOn'] == null
          ? null
          : DateTime.parse(json['uploadedOn'] as String),
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$LeadDocumentModelToJson(LeadDocumentModel instance) =>
    <String, dynamic>{
      'documentName': instance.documentName,
      'filePath': instance.filePath,
      'uploadedOn': instance.uploadedOn?.toIso8601String(),
      'documentType': _$DocumentTypeEnumMap[instance.documentType],
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.defaultType: 0,
  DocumentType.identity: 1,
  DocumentType.experience: 2,
  DocumentType.signature: 3,
  DocumentType.jpg: 4,
  DocumentType.png: 5,
  DocumentType.pdf: 6,
  DocumentType.docx: 7,
  DocumentType.txt: 8,
  DocumentType.csv: 9,
};

AddLeadDocumentsModel _$AddLeadDocumentsModelFromJson(
        Map<String, dynamic> json) =>
    AddLeadDocumentsModel(
      leadId: json['leadId'] as String?,
      documents: (json['documents'] as List<dynamic>?)
          ?.map((e) => LeadDocumentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AddLeadDocumentsModelToJson(
        AddLeadDocumentsModel instance) =>
    <String, dynamic>{
      'leadId': instance.leadId,
      'documents': instance.documents,
    };
