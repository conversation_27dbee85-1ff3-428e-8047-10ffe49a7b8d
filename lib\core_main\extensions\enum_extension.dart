import 'package:leadrat/core_main/extensions/object_extension.dart';

extension EnumDescription on Enum {
  String getDescription() {
    return (this as dynamic).description;
  }

  int getValue() {
    return (this as dynamic).value;
  }
}

T? getEnumFromDescription<T extends Enum>(List<T> values, String description) {
  try {
    return values.firstWhere((e) => e.getDescription() == description);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}


T? getEnumFromValue<T extends Enum>(List<T> values, int? value) {
  try {
    if(value == null) return null;
    return values.firstWhere((e) => e.getValue() == value);
  } catch (e,stackTrace) {
    e.logException(stackTrace);
    return null;
  }
}