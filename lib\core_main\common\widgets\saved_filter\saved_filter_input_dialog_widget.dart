import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';

class SavedFilterInputDialogWidget extends LeadratStatelessWidget {
  final Map<String, dynamic>? filterCriteria;
  final void Function()? onSavedOrCanceled;
  final SavedFilterModule module;
  final String? filterId;
  final String? filterName;

  late TextEditingController filterNameController;

  SavedFilterInputDialogWidget({super.key, this.filterId, this.filterName, this.onSavedOrCanceled, required this.filterCriteria, required this.module}) : filterNameController = TextEditingController(text: filterName);

  @override
  Widget buildContent(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      insetPadding: const EdgeInsets.only(left: 24, right: 16),
      backgroundColor: ColorPalette.darkToneInk,
      child: BlocListener<SavedFilterBloc, SavedFilterState>(
        listener: (context, state) {
          if (state.dialogState == PageState.success) {
            DialogManager().hideTransparentProgressDialog();
            if (onSavedOrCanceled != null) onSavedOrCanceled!();
            Navigator.of(context).pop();
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(text: "${filterName == null ? 'Add' : 'Update'} filter name", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryTextColor)),
                TextSpan(text: " *", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.fadedRed)),
              ])),
              const SizedBox(height: 15),
              TextField(
                controller: filterNameController,
                decoration: InputDecoration(
                  fillColor: const Color(0xFF232527),
                  filled: true,
                  border: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(4)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  enabledBorder: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(4)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  focusedBorder: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(4)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  hintText: "type here..",
                  hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray600),
                ),
                cursorColor: ColorPalette.primaryGreen,
                style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.whiteSolid),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      if (onSavedOrCanceled != null) onSavedOrCanceled!();
                    },
                    child: Container(
                      decoration: BoxDecoration(color: ColorPalette.gray800, borderRadius: BorderRadius.circular(30)),
                      padding: const EdgeInsets.only(right: 14, top: 2, bottom: 2, left: 2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(ImageResources.icCancelFilled),
                          SizedBox(width: context.width(2)),
                          Text("cancel", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.white)),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  GestureDetector(
                    onTap: () {
                      if (filterNameController.text.isNullOrEmpty()) {
                        LeadratCustomSnackbar.show(message: "Filter name cant be empty", context: context, type: SnackbarType.error);
                        return;
                      }
                      if (filterName == null) {
                        context.read<SavedFilterBloc>().add(CreateSavedFilterEvent(
                              module: module,
                              filterCriteria: filterCriteria,
                              filterName: filterNameController.text,
                            ));
                      } else if (filterId.isNotNullOrEmpty()) {
                        context.read<SavedFilterBloc>().add(UpdateSavedFilterEvent(
                              filterId: filterId!,
                              module: module,
                              filterCriteria: filterCriteria,
                              filterName: filterNameController.text,
                              checkFilterExits: filterNameController.text != filterName,
                            ));
                      }
                      DialogManager().showTransparentProgressDialog(context, message: filterName == null ? "saving filter" : "updating filter");
                    },
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(color: ColorPalette.leadratBgGreen, borderRadius: BorderRadius.circular(30)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(ImageResources.icCheckFilled),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Text(filterName == null ? "save filter" : "update filter", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.white)),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
