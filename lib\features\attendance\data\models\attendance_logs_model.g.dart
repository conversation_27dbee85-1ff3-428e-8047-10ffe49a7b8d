// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_logs_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceLogModel _$AttendanceLogModelFromJson(Map<String, dynamic> json) =>
    AttendanceLogModel(
      id: json['id'] as String?,
      clockInTime: json['clockInTime'] == null
          ? null
          : DateTime.parse(json['clockInTime'] as String),
      clockOutTime: json['clockOutTime'] == null
          ? null
          : DateTime.parse(json['clockOutTime'] as String),
      clockInLatitude: (json['clockInLatitude'] as num?)?.toDouble(),
      clockInLongitude: (json['clockInLongitude'] as num?)?.toDouble(),
      clockInLocation: json['clockInLocation'] as String?,
      clockOutLatitude: (json['clockOutLatitude'] as num?)?.toDouble(),
      clockOutLongitude: (json['clockOutLongitude'] as num?)?.toDouble(),
      clockOutLocation: json['clockOutLocation'] as String?,
      isClosed: json['isClosed'] as bool?,
      clockInImageUrl: json['clockInImageUrl'] as String?,
      clockOutImageUrl: json['clockOutImageUrl'] as String?,
      logInDuration: json['logInDuration'] as String?,
    );

Map<String, dynamic> _$AttendanceLogModelToJson(AttendanceLogModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'clockInTime': instance.clockInTime?.toIso8601String(),
      'clockOutTime': instance.clockOutTime?.toIso8601String(),
      'clockInLatitude': instance.clockInLatitude,
      'clockInLongitude': instance.clockInLongitude,
      'clockInLocation': instance.clockInLocation,
      'clockOutLatitude': instance.clockOutLatitude,
      'clockOutLongitude': instance.clockOutLongitude,
      'clockOutLocation': instance.clockOutLocation,
      'isClosed': instance.isClosed,
      'clockInImageUrl': instance.clockInImageUrl,
      'clockOutImageUrl': instance.clockOutImageUrl,
      'logInDuration': instance.logInDuration,
    };
