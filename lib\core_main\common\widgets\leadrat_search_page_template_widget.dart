import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class LeadratSearchPageTemplateWidget extends StatelessWidget {
  final Widget child;
  final String? text;
  final TextEditingController textEditingController;

  final Function(String text) onSearch;

  const LeadratSearchPageTemplateWidget(
      {super.key, required this.text, required this.child, required this.textEditingController, required this.onSearch});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  height: 120,
                  width: double.infinity,
                  padding: const EdgeInsets.only(left: 20, right: 175),
                  decoration: const BoxDecoration(
                      color: ColorPalette.darkToneInk,
                      image: DecorationImage(
                        image: AssetImage(
                          ImageResources.imageTopBarBottomLeftPatternSearchPage,
                        ),
                        alignment: FractionalOffset.centerLeft,
                      )),
                  child: Row(
                    children: [
                      IconButton(onPressed: () =>Navigator.pop(context) ,
                        icon:SvgPicture.asset(
                          ImageResources.iconBack,
                        ),
                      ),
                      const Spacer(),
                      Text('search', style: LexendTextStyles.lexend18Regular.copyWith(color: ColorPalette.primary))
                    ],
                  ),
                ),
                Container(
                  height: 45,
                  margin: const EdgeInsets.only(left: 24, right: 12, top: 10),
                  child: TextField(
                    controller: textEditingController,
                    decoration: InputDecoration(
                        fillColor: const Color.fromRGBO(73, 79, 86, 0.4),
                        filled: true,
                        hintText: text,
                        hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray600),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0), // Rounded corners
                          borderSide: const BorderSide(
                            color: ColorPalette.primaryLightColor, // Border color
                            width: 1.0,
                          ),
                        ),
                        suffixIcon: UnconstrainedBox(
                            child: SvgPicture.asset(
                          ImageResources.iconSearch,
                          height: 36,
                        )),
                        enabled: true),
                    onChanged: (value) {
                      onSearch(value);
                    },
                    style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.white),
                  ),
                ),
                Expanded(
                    child: Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: child,
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
