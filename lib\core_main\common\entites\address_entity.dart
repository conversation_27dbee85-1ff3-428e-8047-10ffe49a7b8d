import 'package:leadrat/core_main/common/models/address_model.dart';

class AddressEntity {
  final String? id;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final String? placeId;
  final String? subLocality;
  final String? locality;
  final String? district;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? longitude;
  final String? latitude;
  final bool? isGoogleMapLocation;
  final String? locationId;
  final bool isManual;
  final String? community;
  final String? subCommunity;
  final String? towerName;

  AddressEntity({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.placeId,
    this.subLocality,
    this.locality,
    this.district,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.longitude,
    this.latitude,
    this.isGoogleMapLocation,
    this.locationId,
    this.isManual = false,
    this.community,
    this.subCommunity,
    this.towerName,
  });

  AddressModel toModel() {
    return AddressModel(
      createdOn: createdOn,
      createdBy: createdBy,
      lastModifiedOn: lastModifiedOn,
      lastModifiedBy: lastModifiedBy,
      placeId: placeId,
      subLocality: subLocality,
      locality: locality,
      district: district,
      city: city,
      state: state,
      country: country,
      postalCode: postalCode,
      longitude: longitude,
      latitude: latitude,
      isGoogleMapLocation: isGoogleMapLocation ?? false,
      locationId: locationId,
      isManual: false,
      community: community,
      subCommunity: subCommunity,
      towerName: towerName,
    );
  }
}

extension AddressEntityExtension on AddressEntity {
  String addressEntityToLocation() {
    var locality = subLocality ?? this.locality ?? '';
    var state_ = state ?? '';
    var city = this.city ?? '';
    String title = [locality, city, state_].where((element) => element.isNotEmpty).join(', ');
    return title;
  }
}
