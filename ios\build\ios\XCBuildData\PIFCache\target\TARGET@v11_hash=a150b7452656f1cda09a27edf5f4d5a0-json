{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be83b9c373a70b8f3c325e3f14f64944", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3cf2c287c353e641e178001fbd4946f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ad7c7d9e3ede671e80bbaedd5450331", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a95f17d25edd09b3f0a0210454c321f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ad7c7d9e3ede671e80bbaedd5450331", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d4e9ba8e985d17859c3cd8429b49a12", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ddc4062da1b5d96a96a0adda0437e787", "guid": "bfdfe7dc352907fc980b868725387e987b0df7cde029ac76fb297ba0f9a089cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b79cd0b496db822aaa4f322a38ab77d", "guid": "bfdfe7dc352907fc980b868725387e9834efffb02b9e215cfc2aed2c2e300ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983062ea4be4fdd9f6f1a14ea7d4c9237c", "guid": "bfdfe7dc352907fc980b868725387e98ec15976bccca0bd02247c35052b31e51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980447b1a780b8168f7e086459f4acbe68", "guid": "bfdfe7dc352907fc980b868725387e98153ab10198da5acc3ebf78644c990cf5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70315f5941b41280abfb6259e8732d4", "guid": "bfdfe7dc352907fc980b868725387e9840d58881bf8207bcb05df1e3d64cad78", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a6ccf405c09128d5275b128f65eff73d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98860a1839049fdf8bef96bde1345b9305", "guid": "bfdfe7dc352907fc980b868725387e98dda8783d33248c877931d962b1b316fd"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f362c29933a5831430dc4d72716b8d5c", "guid": "bfdfe7dc352907fc980b868725387e98a82324dae2b116ece7920b64f606a05b"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9845d1d9292c1a52db7d8f4a07bb6d3c91", "guid": "bfdfe7dc352907fc980b868725387e98832ca75f382943731eb384c032f1106a"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989e3eb025b1fb3c10111730d6102db3ca", "guid": "bfdfe7dc352907fc980b868725387e9800ebe3f3cd583d7ffb866ef6dadf7e86"}], "guid": "bfdfe7dc352907fc980b868725387e988879554615952b03814caa9bdda4321d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e980ab2c9e0c01ef51359eb2cc0596a5756"}], "guid": "bfdfe7dc352907fc980b868725387e98d9c55aa6f1da12321a64beeb88d14a90", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981169aa0e84ca64bdb576a6a3cc6efb9f", "targetReference": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6"}], "guid": "bfdfe7dc352907fc980b868725387e98fd56e0d3a1afc5bdffcaa7b3630ce51d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6", "name": "nanopb-nanopb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98edeb236a6bea2a184984d344e4936f7f", "name": "nanopb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}