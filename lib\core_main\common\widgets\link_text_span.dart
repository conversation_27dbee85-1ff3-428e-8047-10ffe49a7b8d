import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

TextSpan buildLinkTextSpan({String? text, TextStyle? style}) {
  if (text == null || text.isEmpty) return const TextSpan();
  final urlRegExp = RegExp(r'(http|https)://[^\s<>"]+|www\.[^\s<>"]+', caseSensitive: false);
  final spans = <TextSpan>[];
  var start = 0;

  for (final match in urlRegExp.allMatches(text)) {
    if (match.start > start) {
      spans.add(TextSpan(
        text: text.substring(start, match.start),
        style: style,
      ));
    }

    final url = text.substring(match.start, match.end);
    spans.add(TextSpan(
      text: url,
      style: style?.copyWith(
        color: Colors.blue,
        decoration: TextDecoration.underline,
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () async {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri);
          }
        },
    ));

    start = match.end;
  }

  if (start < text.length) {
    spans.add(TextSpan(
      text: text.substring(start),
      style: style,
    ));
  }

  return TextSpan(children: spans);
}
