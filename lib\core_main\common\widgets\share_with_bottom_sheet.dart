import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/message_utility.dart';
import 'package:leadrat/main.dart';
import 'package:share_plus/share_plus.dart';

Future<ShareToResult> shareWithBottomSheet({
  required String message,
  String? contactNumber,
  String? subject,
  String? emailAddress,
}) async {
  final context = MyApp.navigatorKey.currentState?.context;
  if (context == null) {
    throw Exception("Context can't be null");
  }

  final List<SelectShareOption> options = [
    const SelectShareOption(label: 'whatsapp', icon: ImageResources.iconWhatsappLogo, shareTo: ShareToResult.whatsapp),
    const SelectShareOption(label: 'email', icon: ImageResources.imageEmail, shareTo: ShareToResult.email),
    const SelectShareOption(label: 'others', icon: ImageResources.imageOthers, shareTo: ShareToResult.others),
  ];

  ShareToResult getSharedWith(ShareResult shareResult) {
    switch (shareResult.status) {
      case ShareResultStatus.success:
        if (shareResult.raw.contains('com.google.android.apps.messaging/com.google.android.apps.messaging')) {
          return ShareToResult.sms;
        } else if (shareResult.raw.contains('com.whatsapp/com.whatsapp.contact.picker.ContactPicker')) {
          return ShareToResult.whatsapp;
        } else if (shareResult.raw.contains('com.google.android.gm/com.google.android.gm.ComposeActivityGmailExternal')) {
          return ShareToResult.email;
        }
        return ShareToResult.others;
      case ShareResultStatus.dismissed:
      case ShareResultStatus.unavailable:
        return ShareToResult.failed;
    }
  }

  Future<ShareToResult> handleOnOptionSelect(BuildContext context, ShareToResult option) async {
    try {
      switch (option) {
        case ShareToResult.whatsapp:
          await MessageUtility.sendWhatsappMessage(
            phoneNumber: contactNumber ?? '',
            message: message,
          );
          return ShareToResult.whatsapp;
        case ShareToResult.email:
          await MessageUtility.sendEmail(
            email: emailAddress ?? '',
            body: message,
            subject: subject ?? '',
          );
          return ShareToResult.email;
        case ShareToResult.others:
          try {
            final result = await Share.share(message);
            return getSharedWith(result);
          } catch (ex) {
            return ShareToResult.failed;
          }
        default:
          return ShareToResult.failed;
      }
    } catch (e) {
      return ShareToResult.failed;
    }
  }

  return await showModalBottomSheet<ShareToResult>(
        context: context,
        builder: (BuildContext context) {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned(
                top: -6,
                left: 0,
                right: 0,
                child: CustomPaint(
                  size: Size(context.width(100), 6),
                  painter: TrapeziumPainter(),
                ),
              ),
              Container(
                width: context.width(100),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                color: ColorPalette.primaryDarkColor,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "share with",
                          style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary),
                        ),
                        const SizedBox(
                          width: 40,
                          child: Divider(
                            color: ColorPalette.primary,
                            thickness: 2.0,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    Row(
                      children: options.map((SelectShareOption option) {
                        return Expanded(
                          child: GestureDetector(
                            onTap: () async {
                              final result = await handleOnOptionSelect(context, option.shareTo);
                              if (context.mounted) {
                                Navigator.pop(context, result);
                              }
                            },
                            child: Column(
                              children: [
                                SvgPicture.asset(
                                  option.icon,
                                  height: 50,
                                  width: 50,
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  option.label,
                                  style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          );
        },
      ) ??
      ShareToResult.failed;
}

class SelectShareOption {
  final String label;
  final String icon;
  final ShareToResult shareTo;

  const SelectShareOption({
    required this.label,
    required this.icon,
    required this.shareTo,
  });
}

enum ShareToResult { whatsapp, email, others, failed, sms }
