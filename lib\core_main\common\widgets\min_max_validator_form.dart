import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class MinMaxValidatorForm extends LeadratStatefulWidget {
  final TextEditingController? minCountController;
  final TextEditingController? maxCountController;

  const MinMaxValidatorForm({
    this.minCountController,
    this.maxCountController,
    Key? key,
  }) : super(key: key);

  @override
  _MinMaxValidatorFormState createState() => _MinMaxValidatorFormState();
}

class _MinMaxValidatorFormState extends State<MinMaxValidatorForm> {
  String? minCountMessage;
  String? maxCountMessage;

  changeMinCountMessage() {
    //validation for minCountController
    if (widget.minCountController?.text.isNullOrEmpty() ?? false) {
      minCountMessage = null;
    } else if (!RegExp(r'^[0-9]+$').hasMatch(widget.minCountController?.text ?? '')) {
      minCountMessage = 'Enter numbers only';
    } else {
      minCountMessage = null;
    }

//validation for maxCountController
    if (widget.maxCountController?.text.isNullOrEmpty() ?? false) {
      maxCountMessage = null;
    } else if (!RegExp(r'^[0-9]+$').hasMatch(widget.maxCountController?.text ?? '')) {
      maxCountMessage = 'Enter numbers only';
    } else {
      double minValue = double.tryParse(widget.minCountController?.text ?? '0') ?? 0;
      double maxValue = double.tryParse(widget.maxCountController?.text ?? '0') ?? 0;
      if (maxValue < minValue) {
        maxCountMessage = 'Max should be greater than Min';
      } else {
        maxCountMessage = null;
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 20),
        Column(
          children: [
            Row(
              children: [
                Text('Min Count: ', style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500)),
                Expanded(
                  child: TextFormField(
                    controller: widget.minCountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: "ex 12345",
                      hintStyle: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                      isCollapsed: true,
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: minCountMessage != null && (widget.minCountController?.text.isNotNullOrEmpty() ?? false) ? ColorPalette.red : ColorPalette.white, // Change border color based on validation
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: minCountMessage != null && (widget.minCountController?.text.isNotNullOrEmpty() ?? false) ? ColorPalette.red : ColorPalette.white, // Change border color based on validation
                        ),
                      ),
                    ),
                    style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.white),
                    onChanged: (value) => changeMinCountMessage(),
                  ),
                ),
              ],
            ),
            if (minCountMessage != null && (widget.minCountController?.text.isNotNullOrEmpty() ?? false))
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(minCountMessage ?? '', style: LexendTextStyles.lexend8SemiBold.copyWith(color: ColorPalette.red)),
                ],
              ),
          ],
        ),
        const SizedBox(height: 20),
        Column(
          children: [
            Row(
              children: [
                Text('Max Count: ', style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500)),
                Expanded(
                  child: TextFormField(
                    controller: widget.maxCountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: "ex 12345",
                      hintStyle: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                      isCollapsed: true,
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: maxCountMessage != null && (widget.maxCountController?.text.isNotNullOrEmpty() ?? false) ? ColorPalette.red : ColorPalette.white,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: maxCountMessage != null && (widget.maxCountController?.text.isNotNullOrEmpty() ?? false) ? ColorPalette.red : ColorPalette.white,
                        ),
                      ),
                    ),
                    style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.white),
                    onChanged: (value) => changeMinCountMessage(),
                  ),
                ),
              ],
            ),
            if (maxCountMessage != null && (widget.maxCountController?.text.isNotNullOrEmpty() ?? false))
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(maxCountMessage ?? '', style: LexendTextStyles.lexend8SemiBold.copyWith(color: ColorPalette.red)),
                ],
              ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
