{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ff6a789a7f685cf123808ef7e04c274", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98140b31d17c56d97028788a98d13e6361", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98140b31d17c56d97028788a98d13e6361", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98261510b76384bb39dc4af1715d7ab76c", "guid": "bfdfe7dc352907fc980b868725387e98cd8b4d470239292b5a89fc07b33e7795", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd57fcce987ce78d71632bfaa418909", "guid": "bfdfe7dc352907fc980b868725387e98cde643e8ac672c53ec3c51cd2812783f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828315efe0bc7453dd501895921999cf3", "guid": "bfdfe7dc352907fc980b868725387e987ded1524a5bace32a99ab89069d66f3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f447fb4dc67dccb0b11d68fd1f0a8d2", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fdf003ea661ad32b9837daa77c27ece", "guid": "bfdfe7dc352907fc980b868725387e984c1ed1795bae8ed60b03a4f3eca7857d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367f7a5a7c0cc2e47d396266fc1f3d32", "guid": "bfdfe7dc352907fc980b868725387e98afb2de80d4e8ea2f3ad8a755ea6ed5ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806ae805edd53937b2c796bec574cecfa", "guid": "bfdfe7dc352907fc980b868725387e9836ed1731b2912dbbe00b661acecde8f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de646080138b3f6335a50a52e6c27640", "guid": "bfdfe7dc352907fc980b868725387e981019666468b33fa8e21aed3788f39353", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e263e19a1990e9e0edf0e7fcb09a54e", "guid": "bfdfe7dc352907fc980b868725387e98b8ff1e65ef5bfab230cfe574def89c35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875bfaf43f4e75ad8a3dfb626babc36f7", "guid": "bfdfe7dc352907fc980b868725387e98a826faaa8d8ead74a177d2537db9c050", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986211211ae7211c506f4e1b7e4ea08f02", "guid": "bfdfe7dc352907fc980b868725387e98daa5fb44bb7ff18010b4bebacc237570", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5980c410fd34b177a7c99e92ecaa097", "guid": "bfdfe7dc352907fc980b868725387e98f1d3671b43eb8d48cfa9fa13768631ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869c251a3a09408ad9c14a4643d390f50", "guid": "bfdfe7dc352907fc980b868725387e98903f1c6599612940b8658e372f663244", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d3cd024c9000d07e8b433a232ad7d2", "guid": "bfdfe7dc352907fc980b868725387e983b1ed378e908c5ebc9db9ba53783df77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987faa78bd6afeeb39b833806104b79fad", "guid": "bfdfe7dc352907fc980b868725387e98dbbfa8bf33620be1770652d87ebcd43c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98087a6e6c5e6fc2392b22e2f2f303c62c", "guid": "bfdfe7dc352907fc980b868725387e980db838ff199d254682e05765e94616ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cf2144ad664ddb56a43c841ce7b643b", "guid": "bfdfe7dc352907fc980b868725387e982bc4cabfa2fa59295b804f11004e2082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829cb29ecd4ff23a0e94d0b3096ae4bce", "guid": "bfdfe7dc352907fc980b868725387e9850093f5fe27f3302766e3fd201577fcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894730a068962ab920a615515c6304c66", "guid": "bfdfe7dc352907fc980b868725387e988820fc9f9023c761a828954fc9ebc795", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137fd089f23febc4fa0d3b29204139d2", "guid": "bfdfe7dc352907fc980b868725387e987ee86a7f66d3eeda49de273f915dc021", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3dfb4a9c5a44b255d2227ba7e8b3a33", "guid": "bfdfe7dc352907fc980b868725387e98e8d128b16a37d2e2d0b54f2230ad1c96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98448cd7b28226556d18545948413ceeb9", "guid": "bfdfe7dc352907fc980b868725387e989bd5baf7b4a615c08d64d2d5cc48ea0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a02e20ed35fd414954beb02b896bb5", "guid": "bfdfe7dc352907fc980b868725387e98e5e4b80076d7df97f331b11f5278120e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98047681d92e2bb23a013b659960c1f200", "guid": "bfdfe7dc352907fc980b868725387e981de146d7bf230c13103c2b842c4f0374", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d6efc23828e1aadd92bc3b4eede5b9", "guid": "bfdfe7dc352907fc980b868725387e9871a271ad2988042d98167cc32f7e4121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af4862dae0d65f42599d758232099e6", "guid": "bfdfe7dc352907fc980b868725387e9866a35755ed35b831c562b8e38d9a0c95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989010ca7193eb957a63c6011ad88ee661", "guid": "bfdfe7dc352907fc980b868725387e98dcba36f1cf3dd276b9812e44dcc7f59b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a544d98ceef41fb79b57c599d9c87e8", "guid": "bfdfe7dc352907fc980b868725387e981578266810e35991182ef4faf658c1a7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df0faa286411fffff2d5a6f22caa4867", "guid": "bfdfe7dc352907fc980b868725387e98f8cdc9b3187b28a3fb952ba61204b520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a8016dba102ab470b88d604d61a1b1", "guid": "bfdfe7dc352907fc980b868725387e98aa08ecbcf3ad8c655e2fca2c402d05ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb9b0f5d616d4b65f071357d52ffbd80", "guid": "bfdfe7dc352907fc980b868725387e98f2cabc4140c4b5bd1d6c24b6eb586a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c0636d28a782b6e279020b9b9daec5", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b2637480e47e732dc0819b63699e20", "guid": "bfdfe7dc352907fc980b868725387e98f470fff906d2124328296d2455635fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f28b0f349781e6b4cc5fc7e0a0d774d", "guid": "bfdfe7dc352907fc980b868725387e98df3fc452410765844d0aadde596bfa6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874c11a29b4edbc447fc08fb534e4346a", "guid": "bfdfe7dc352907fc980b868725387e986f2041b8d09744d29f653d88297859d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986657e67f459b4785dc99c1a8023dfcf5", "guid": "bfdfe7dc352907fc980b868725387e9831a558245be96b65ad055c2f55196bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecee1236d3440faa5e3231299c9a495c", "guid": "bfdfe7dc352907fc980b868725387e98dd03d255ba20940be4cd8eed8dd82d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f596343eace9668f2141801f784d06", "guid": "bfdfe7dc352907fc980b868725387e989e92413d9d5d59bd14b19b9d4aac3e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7a3618fb5308b57d3d670817cfe5ad", "guid": "bfdfe7dc352907fc980b868725387e98ff4d69ab1cbdb62506dcf191f0eda33d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ee5060f03a9f092afecfd556f8929c3", "guid": "bfdfe7dc352907fc980b868725387e987eeb3407014ab37e60ed3cfd3a71bb7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98480ce187e5c90aae0ed64574a5447113", "guid": "bfdfe7dc352907fc980b868725387e98acdad2537fd1c5a403685e5edcfb3be5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014be5a6369b14bd0a92cda6890ea8fc", "guid": "bfdfe7dc352907fc980b868725387e980f818d2345f1a0fdb50ebbcc10c92041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809d21b4c090f06747fe45c77c136e31e", "guid": "bfdfe7dc352907fc980b868725387e98978e3a08a87bfba92ceb65784d987452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c27fde773dc35d777df3798d5c29fb", "guid": "bfdfe7dc352907fc980b868725387e987044f24a902173d05db90b91fe8bbcc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893bdcdffdd11bc933a96ccfac7a10f6b", "guid": "bfdfe7dc352907fc980b868725387e98444c86ec5de6753b6afe3f65242e4917"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d290ca1c54f06b6f4d6a2fe6b35c20", "guid": "bfdfe7dc352907fc980b868725387e9864952c3dd734bae9eb3ca2a4502d2a07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989135a0ca7c978c7e8df72d32bd6b6d80", "guid": "bfdfe7dc352907fc980b868725387e98a5f211f0ee5db2294446673e9b03ba65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890edfab38af45cc1cf2f158780e5e85d", "guid": "bfdfe7dc352907fc980b868725387e988c3b8f46340ec4ede88ac97c96c7f654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981842a9aeec61e0abc18293036bc7f134", "guid": "bfdfe7dc352907fc980b868725387e9894c735995ab5ea54e200c4551ece7791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3b7e45f3b4ab93936793c60fb9bed31", "guid": "bfdfe7dc352907fc980b868725387e98c2352ad7b71fb42d7b672cc168b06f48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5477b4a7122db1d826d7072e91ebd3", "guid": "bfdfe7dc352907fc980b868725387e983eaa9bab3ca202e14a02a1f812a2d341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f649a9a87d7d5d955767689cd7a49f3", "guid": "bfdfe7dc352907fc980b868725387e98d99fad5b278a52f4f20c4363c771fcda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756231c63e94e80bcec7aa886581d6b6", "guid": "bfdfe7dc352907fc980b868725387e98813628afd6e98519061d38833bd1d55f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61ca14df3334f5ddacd59f19716646d", "guid": "bfdfe7dc352907fc980b868725387e985ca9e5112d222f562f701478a9daf115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e16384026975866d9ef5ccb7a2b63d34", "guid": "bfdfe7dc352907fc980b868725387e988b82cc5f95b06817535990dc3b2a4923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b700cf69f7c52da0bae4b381ba4f45ce", "guid": "bfdfe7dc352907fc980b868725387e985a49fe31eafc8ea6b5c44881b79f8d5f"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}