// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meeting_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MeetingModel _$MeetingModelFromJson(Map<String, dynamic> json) => MeetingModel(
      meetingDoneUniqueCount: (json['meetingDoneUniqueCount'] as num?)?.toInt(),
      meetingDoneEventCount: (json['meetingDoneEventCount'] as num?)?.toInt(),
      meetingNotDoneUniqueCount:
          (json['meetingNotDoneUniqueCount'] as num?)?.toInt(),
      meetingNotDoneEventCount:
          (json['meetingNotDoneEventCount'] as num?)?.toInt(),
      meetingOverdue: (json['meetingOverdue'] as num?)?.toInt(),
      upcomingMeetings: (json['upcomingMeetings'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MeetingModelToJson(MeetingModel instance) =>
    <String, dynamic>{
      if (instance.meetingDoneUniqueCount case final value?)
        'meetingDoneUniqueCount': value,
      if (instance.meetingDoneEventCount case final value?)
        'meetingDoneEventCount': value,
      if (instance.meetingNotDoneUniqueCount case final value?)
        'meetingNotDoneUniqueCount': value,
      if (instance.meetingNotDoneEventCount case final value?)
        'meetingNotDoneEventCount': value,
      if (instance.meetingOverdue case final value?) 'meetingOverdue': value,
      if (instance.upcomingMeetings case final value?)
        'upcomingMeetings': value,
    };
