// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blood_group_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BloodGroupTypeAdapter extends TypeAdapter<BloodGroupType> {
  @override
  final int typeId = 102;

  @override
  BloodGroupType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BloodGroupType.none;
      case 1:
        return BloodGroupType.aPositive;
      case 2:
        return BloodGroupType.aNegative;
      case 3:
        return BloodGroupType.bPositive;
      case 4:
        return BloodGroupType.bNegative;
      case 5:
        return BloodGroupType.oPositive;
      case 6:
        return BloodGroupType.oNegative;
      case 7:
        return BloodGroupType.abPositive;
      case 8:
        return BloodGroupType.abNegative;
      default:
        return BloodGroupType.none;
    }
  }

  @override
  void write(BinaryWriter writer, BloodGroupType obj) {
    switch (obj) {
      case BloodGroupType.none:
        writer.writeByte(0);
        break;
      case BloodGroupType.aPositive:
        writer.writeByte(1);
        break;
      case BloodGroupType.aNegative:
        writer.writeByte(2);
        break;
      case BloodGroupType.bPositive:
        writer.writeByte(3);
        break;
      case BloodGroupType.bNegative:
        writer.writeByte(4);
        break;
      case BloodGroupType.oPositive:
        writer.writeByte(5);
        break;
      case BloodGroupType.oNegative:
        writer.writeByte(6);
        break;
      case BloodGroupType.abPositive:
        writer.writeByte(7);
        break;
      case BloodGroupType.abNegative:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BloodGroupTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
