import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/data/device/data_source/device_data_source.dart';
import 'package:leadrat/core_main/common/data/device/models/device_info_model.dart';
import 'package:leadrat/core_main/common/data/device/repository/device_repository.dart';
import 'package:leadrat/core_main/enums/app_enum/operating_system_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/shared_preference_manager.dart';
import 'package:leadrat/core_main/remote/identity_rest_service.dart';
import 'package:leadrat/core_main/services/native_implementation_service/native_implementation_service.dart';
import 'package:leadrat/core_main/utilities/PreferencesHelper.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';

import '../../../../di/injection_container.dart';
import '../../../../enums/app_enum/world_time_format.dart';
import '../../user/repository/users_repository.dart';
import '../models/device_registration_info_model.dart';

class DeviceRepositoryImpl extends IdentityRestService implements DeviceRepository {
  final DeviceDataSource _deviceDataSource;
  final NativeImplementationService _nativeImplementationService;
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  DeviceRepositoryImpl(this._deviceDataSource, this._nativeImplementationService);

  @override
  Future<bool?> postDeviceInfo() async {
    var user = getIt<UsersDataRepository>().getLoggedInUser();
    var deviceInfo = DeviceInfoModel(
      id: '00000000-0000-0000-0000-000000000000',
      userId: user?.userId,
      operatingSystem: Platform.isAndroid ? OperatingSystemEnum.android.value : OperatingSystemEnum.ios.value,
      osVersion: await getOSVersion(),
      countryCode: getCountryFromLocale(),
      languageCode: getLanguageUsingIntl(),
      currencyCode: getCurrencyCode(),
      currencySymbol: getCurrencySymbol(),
      deviceTimeZone: getTimeZoneInfo(),
      timeFormat: checkTimeFormat().value,
      deviceModel: await getDeviceModel(),
      deviceUDID: await getDeviceId(),
      deviceName: await getDeviceName(),
      isDebug: false,
      isDeveloperMode: false,
      model: await getDeviceModel(),
      manufacturer: await getDeviceManufacturer(),
      environment: 'production',
      ipAddress: '************',
    );
    var location = await _nativeImplementationService.getCurrentLocation();
    deviceInfo = deviceInfo.copyWith(latitude: location?.latitude.toString() ?? '', longitude: location?.longitude.toString() ?? '');
    final response = await _deviceDataSource.postDeviceInfo(deviceInfo);
    return response != null;
  }

  @override
  Future<bool> registerDeviceToken() async {
    var deviceNotificationToken = await getFCMToken();
    if (await isTokenChanged(deviceNotificationToken)) {
      var registerInfo = await getDeviceRegistrationInfo(deviceNotificationToken, true, []);
      var response = await _deviceDataSource.changeDeviceRegistration(registerInfo);
      if (response.isSuccess ?? false) {
        await PreferencesHelper.saveString('DeviceToken', deviceNotificationToken ?? '');
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  Future<String?> getFCMToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Request permission (iOS only)
    if (Platform.isIOS) {
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get the token
        return await messaging.getToken();
        // Use the token as needed
      } else {
        // error
        return null;
      }
    } else {
      return await messaging.getToken();
    }
  }

  Future<bool> isTokenChanged(String? currentNotificationToken) async {
    var oldNotificationToken = await PreferencesHelper.getString('DeviceToken');
    if (oldNotificationToken != null && oldNotificationToken != '') {
      if (oldNotificationToken == currentNotificationToken) {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<DeviceRegistrationInfoModel> getDeviceRegistrationInfo(String? notificationToken, bool isActive, List<String>? topics) async {
    var endPointId = await getEndpointId();
    var user = getIt<UsersDataRepository>().getLoggedInUser();
    var registerInfo = DeviceRegistrationInfoModel(
      endpointId: endPointId,
      platform: Platform.isAndroid ? OperatingSystemEnum.android : OperatingSystemEnum.ios,
      deviceModel: await getDeviceModel(),
      deviceUDID: await getDeviceId(),
      applicationTokenValue: 'leadrat',
      userId: user?.userId,
      userName: user?.userName,
      email: user?.email,
      token: notificationToken,
      isActive: isActive,
      isTablet: false,
      appVersion: await getAppVersion(),
      topics: topics ?? [],
      aDSecurityGroups: [],
    );

    return registerInfo;
  }

  Future<String?> getEndpointId() async {
    var oldEndpointId = await getIt<SharedPreferenceManager>().getString('endpointId');
    if (oldEndpointId != null && oldEndpointId != '') {
      return oldEndpointId;
    } else {
      var uuid = const Uuid();
      var newEndpointId = uuid.v4();
      await getIt<SharedPreferenceManager>().setString('endpointId', newEndpointId);
      return newEndpointId;
    }
  }

  Future<String?> getDeviceModel() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.model;
    } else {
      return null;
    }
  }

  Future<String?> getDeviceName() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.product;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.name;
    } else {
      return null;
    }
  }

  Future<String?> getDeviceManufacturer() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.manufacturer;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.systemName;
    } else {
      return null;
    }
  }

  Future<String?> getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor;
    } else {
      return null;
    }
  }

  Future<String?> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    String version = packageInfo.version; // e.g. "1.0.0"
    String buildNumber = packageInfo.buildNumber; // e.g. "1"

    return '($version)$buildNumber';
  }

  Future<String?> getOSVersion() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.release;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.systemVersion;
    }
    return null;
  }

  String getCountryFromLocale() {
    var locale = Intl.getCurrentLocale(); // Get the current locale
    return locale.split('_').last; // Extract the country code
  }

  String getLanguageUsingIntl() {
    String currentLocale = Intl.getCurrentLocale(); // Get the current locale
    String languageCode = currentLocale.split('_').first; // Get the language part (e.g., "en")

    return languageCode;
  }

  String getCurrencyCode() {
    // Get the current locale of the device
    String locale = Intl.getCurrentLocale();

    // Create a NumberFormat for currency based on the locale
    NumberFormat currencyFormat = NumberFormat.simpleCurrency(locale: locale);

    // Get the currency code (e.g., "USD", "EUR")
    String currencyCode = currencyFormat.currencyName ?? '';

    return currencyCode;
  }

  String getCurrencySymbol() {
    // Get the current locale of the device
    String locale = Intl.getCurrentLocale();

    // Create a NumberFormat for currency based on the locale
    NumberFormat currencyFormat = NumberFormat.simpleCurrency(locale: locale);

    // Get the currency code (e.g., "USD", "EUR")
    String currencySymbol = currencyFormat.currencySymbol;

    return currencySymbol;
  }

  String getTimeZoneInfo() {
    // Get the current time zone name
    String timeZoneName = DateTime.now().timeZoneName; // e.g., "GMT+5:30"
    // int timeZoneOffset = DateTime.now().timeZoneOffset.inHours; // e.g., 5

    return timeZoneName;
  }

  WorldTimeFormatEnum checkTimeFormat() {
    // Get the current locale
    String locale = Intl.getCurrentLocale();

    // Format the current time using the locale
    DateFormat dateFormat = DateFormat.jm(locale); // 'jm' for short time format

    // Get the formatted time
    String formattedTime = dateFormat.format(DateTime.now());

    // Check if formatted time contains 'AM' or 'PM'
    bool is12HourFormat = formattedTime.contains('AM') || formattedTime.contains('PM');

    if (is12HourFormat) {
      return WorldTimeFormatEnum.twelveHourClock;
    } else {
      return WorldTimeFormatEnum.twentyFourHourClock;
    }
  }

  @override
  Future<DeviceInfoModel?> getDeviceInfo() async {
    DeviceInfoModel deviceInfoModel = DeviceInfoModel();
    if (Platform.isAndroid) {
      var androidInfo = await _deviceInfoPlugin.androidInfo;
      deviceInfoModel = deviceInfoModel.copyWith(
        deviceUDID: androidInfo.id,
        ipAddress: await getIPAddress(),
        osVersion: androidInfo.version.release,
        deviceName: 'Android',
      );
    } else if (Platform.isIOS) {
      var iosInfo = await _deviceInfoPlugin.iosInfo;
      deviceInfoModel = deviceInfoModel.copyWith(
        deviceUDID: iosInfo.identifierForVendor,
        ipAddress: await getIPAddress(),
        osVersion: iosInfo.systemVersion,
        deviceName: 'IOS',
      );
    }
    return deviceInfoModel;
  }

  Future<String?> getIPAddress() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (!connectivityResult.contains(ConnectivityResult.none)) {
      return await _getLocalIPAddress();
    }
    return null;
  }

  Future<String?> _getLocalIPAddress() async {
    try {
      final List<NetworkInterface> interfaces = await NetworkInterface.list();
      for (var interface in interfaces) {
        for (var adr in interface.addresses) {
          if (adr.address != '127.0.0.1' && adr.address.contains('.')) {
            return adr.address;
          }
        }
      }
    } catch (e) {
      'Error getting IP address: $e'.printInConsole();
    }
    return null;
  }
}
