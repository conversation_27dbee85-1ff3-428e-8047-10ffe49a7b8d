{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890638ab2fb9b30c36cab4dbdaeaa2506", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ca18addb0eeff21275927a57e5b769b0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818cac62638ad19561e2bbe3915a57c6e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b8665ec318d3b554897b7f20ef090879", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818cac62638ad19561e2bbe3915a57c6e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980b8fcf4bc3bee13b6a6c7752e7ad1cf9", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fd404c1138f665c6f2cf34ffdcb1a626", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ba768b372ed862980244636b1b78e252", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881dc9916bb586d746523a66573ba0eed", "guid": "bfdfe7dc352907fc980b868725387e98a5b03143d5f5232b85cbc72a47e3e7ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a4fd3a88d5f23a84e98113527d689d", "guid": "bfdfe7dc352907fc980b868725387e98525581d6299ecf65c866bfc9793ea22c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418d3523af89da8c287725f1c84973df", "guid": "bfdfe7dc352907fc980b868725387e983e27e29a1d458bd5e8085b3a65b6026b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a7499f84d6804e7d5c99a91e118d7fa", "guid": "bfdfe7dc352907fc980b868725387e988caeab4edf0c96accbcae08cf3010074"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d12b645fda34ee8967c38ec1079d000", "guid": "bfdfe7dc352907fc980b868725387e98652c94cc4fd37acae1ae2914adeb9412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55e97a50273479566c4bc5ce7028d25", "guid": "bfdfe7dc352907fc980b868725387e980e2b411f8c99eb664ed02407b86be659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9edaba7dc244188a6feaf532224656", "guid": "bfdfe7dc352907fc980b868725387e9815b9b3ac818a5c03a08969e3c2a45318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79d5f5b556dd3ba5cc7455a95cca226", "guid": "bfdfe7dc352907fc980b868725387e98eda620f816db87cc2685a50df6939cb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9138cc30daa250d6051986b5a507790", "guid": "bfdfe7dc352907fc980b868725387e9815030665ac559bc7f1e472daefe09200"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13facf65dbc728bf4981719b668bb67", "guid": "bfdfe7dc352907fc980b868725387e98e935608377ccb04f5fcd9440e69ff4d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0eb194a5734f6f92a2bbd58d4874f5", "guid": "bfdfe7dc352907fc980b868725387e9896a41812376fc1c33ac557e2ec4482a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f28587a6826ebee004a32e9c2a4d6ee", "guid": "bfdfe7dc352907fc980b868725387e984bab986418e6b630db5325d133a76bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aae40151a732e366c4694831c6f499f", "guid": "bfdfe7dc352907fc980b868725387e981152fe14818fa66402788f03625ff733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c4a91a2d20d133d70f88b7e2652cdfd", "guid": "bfdfe7dc352907fc980b868725387e983ef3bca80d112022b57d368c784ccac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c452ccda1b7a4b6b3d6fa3946fdeca6", "guid": "bfdfe7dc352907fc980b868725387e98906797fc003030dbd94c1ae3b0eebda7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932b07cc1785591524b83bef05d8c537", "guid": "bfdfe7dc352907fc980b868725387e98914de531e7775e0b3b80a475f19586b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f85ceb778a1c4022e1154ff288a5a7", "guid": "bfdfe7dc352907fc980b868725387e98ff98d311550a2d7487c34e1257f99530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43cd2dc16d6450f5cad920f56d2e801", "guid": "bfdfe7dc352907fc980b868725387e98a1d0772c60032cad40418b58d29d8b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4cbdfc1e5ec7c542461327abfa2c75e", "guid": "bfdfe7dc352907fc980b868725387e98d6aa4c650e9129dc6076d1f15c30492a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892e1f7ba4c4dfb80f9a97011148efaf", "guid": "bfdfe7dc352907fc980b868725387e98a32fb6966f3f1b524eb13d2e4c707faf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817809610a77e3c04f7865027ce5bc90b", "guid": "bfdfe7dc352907fc980b868725387e989674dde8ffbca4bb5c63e64c9bc6bf02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9cde2672dda03a5769817c92a83d23b", "guid": "bfdfe7dc352907fc980b868725387e98fa8f9d55ec909ba0e8561a42268416f6"}], "guid": "bfdfe7dc352907fc980b868725387e984450510762d7395ca701e1becb1c4dd5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}