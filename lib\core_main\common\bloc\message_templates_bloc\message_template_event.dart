part of 'message_template_bloc.dart';

@immutable
sealed class MessageTemplateEvent {}

final class MessageTemplateInitialEvent extends MessageTemplateEvent {
  final List<GetLeadEntity?>? leadEntities;
  final List<ProspectEntity?>? getProspectEntities;

  MessageTemplateInitialEvent({
    this.getProspectEntities,
    this.leadEntities,
  });
}

final class GetProjectsEvent extends MessageTemplateEvent {
  GetProjectsEvent();
}

final class SelectCategoryEvent extends MessageTemplateEvent {
  final AppModuleName selectedCategory;

  SelectCategoryEvent({required this.selectedCategory});
}

final class SelectPropertyOrProjectEvent extends MessageTemplateEvent {
  final List<SelectableItem<String?>> selectedProjectOrProperties;

  SelectPropertyOrProjectEvent({required this.selectedProjectOrProperties});
}

final class GetPropertiesEvent extends MessageTemplateEvent {
  GetPropertiesEvent();
}

final class SelectTemplateEvent extends MessageTemplateEvent {
  final SelectableItem<TemplateEntity>? selectedTemplate;
  final List<GetLeadEntity?>? leadEntity;
  final List<ProspectEntity?>? prospectEntities;

  SelectTemplateEvent({this.prospectEntities, this.selectedTemplate, this.leadEntity});
}

final class SendTemplateEvent extends MessageTemplateEvent {
  final ContactType? contactType;
  final String id;
  final String? templateTitle;
  final String? phoneNumber;
  final String? email;

  SendTemplateEvent({
    required this.contactType,
    required this.id,
    this.templateTitle,
    this.phoneNumber,
    this.email,
  });
}

final class GetAllTemplateEvent extends MessageTemplateEvent {}

final class SearchTemplateEvent extends MessageTemplateEvent {
  final String searchText;

  SearchTemplateEvent({required this.searchText});
}
