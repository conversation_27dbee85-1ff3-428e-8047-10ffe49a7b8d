import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'index')
enum BHKType {
  none(0, "None"),
  simplex(1, "Simplex"),
  duplex(2, "Duplex"),
  pentHouse(3, "Pent House"),
  others(4, "Others");

  final int value;
  final String description;

  const BHKType(this.value, this.description);

  static BHKType getBhkType(int? value) {
    return BHKType.values.firstWhere(
      (bhk) => bhk.value == value,
      orElse: () => BHKType.none,
    );
  }
}
