import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'lead_source_enum.g.dart';

@JsonEnum(valueField: 'value')
@HiveType(typeId: HiveModelConstants.leadSourceEnumTypeId)
enum LeadSource {
  @HiveField(0)
  any(-1, 'Any'),
  @HiveField(1)
  direct(0, 'Direct'),
  @HiveField(2)
  iVR(1, 'IVR'),
  @HiveField(3)
  facebook(2, 'Facebook'),
  @HiveField(4)
  linkedIn(3, 'LinkedIn'),
  @HiveField(5)
  googleAds(4, 'Google Ads'),
  @HiveField(6)
  magicBricks(5, 'Magic Bricks'),
  @HiveField(7)
  ninetyNineAcres(6, '99 Acres'),
  @HiveField(8)
  housing(7, 'Housing.com'),
  @HiveField(9)
  gharOffice(8, 'GharOffice'),
  @HiveField(10)
  referral(9, 'Referral'),
  @HiveField(11)
  walkIn(10, 'Walk In'),
  @HiveField(12)
  website(11, 'Website'),
  @HiveField(13)
  gmail(12, 'Gmail'),
  @HiveField(14)
  propertyMicrosite(13, 'Microsite'),
  @HiveField(15)
  portfolioMicrosite(14, 'Portfolio Microsite'),
  @HiveField(16)
  phonebook(15, 'Phonebook'),
  @HiveField(17)
  callLogs(16, 'Call Logs'),
  @HiveField(18)
  leadPool(17, 'Lead Pool'),
  @HiveField(19)
  squareYards(18, 'SquareYards'),
  @HiveField(20)
  quikrHomes(19, 'QuikrHomes'),
  @HiveField(21)
  justLead(20, 'JustLead'),
  @HiveField(22)
  whatsApp(21, 'WhatsApp'),
  @HiveField(23)
  youTube(22, 'YouTube'),
  @HiveField(24)
  qRCode(23, 'QR Code'),
  @HiveField(25)
  instagram(24, 'Instagram'),
  @HiveField(26)
  oLX(25, 'OLX'),
  @HiveField(27)
  estateDekho(26, 'Estate Dekho'),
  @HiveField(28)
  googleSheet(27, 'Google Sheets'),
  @HiveField(29)
  channelPartner(28, 'Channel Partner'),
  @HiveField(30)
  realEstateIndia(29, 'Real Estate India'),
  @HiveField(31)
  commonFloor(30, 'Common Floor'),
  @HiveField(32)
  data(31, 'Data'),
  @HiveField(33)
  roofAndFloor(32, 'Roof & Floor'),
  @HiveField(34)
  microsoftAds(33, 'Microsoft Ads'),
  @HiveField(35)
  propertyWala(34, 'PropertyWala'),
  @HiveField(36)
  projectMicrosite(35, 'Project Microsite'),
  @HiveField(37)
  myGate(36, 'MyGate'),
  @HiveField(38)
  flipkart(37, 'Flipkart'),
  @HiveField(39)
  propertyFinder(38, 'Property Finder'),
  @HiveField(40)
  bayut(39, 'Bayut'),
  @HiveField(41)
  dubizzle(40, 'Dubizzle'),
  @HiveField(42)
  webhook(41, 'Webhook'),
  @HiveField(43)
  tikTok(42,"TikTok"),
  @HiveField(44)
  snapchat(43,"SnapChat"),
  @HiveField(45)
  googleAdsCampaign(44, 'Google Ads Campaign');

  final int value;
  final String description;

  const LeadSource(this.value, this.description);
}
