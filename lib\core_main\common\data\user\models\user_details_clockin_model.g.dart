// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details_clockin_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDetailsClockInModel _$UserDetailsClockInModelFromJson(
        Map<String, dynamic> json) =>
    UserDetailsClockInModel(
      id: json['id'] as String?,
      geoFenceRadius: (json['geoFenceRadius'] as num?)?.toInt(),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => UserProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      properties: (json['properties'] as List<dynamic>?)
          ?.map((e) => UserPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      radiusUnit: $enumDecodeNullable(_$RadiusUnitEnumMap, json['radiusUnit']),
    );

Map<String, dynamic> _$UserDetailsClockInModelToJson(
        UserDetailsClockInModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.projects case final value?) 'projects': value,
      if (instance.properties case final value?) 'properties': value,
      if (instance.geoFenceRadius case final value?) 'geoFenceRadius': value,
      if (_$RadiusUnitEnumMap[instance.radiusUnit] case final value?)
        'radiusUnit': value,
    };

const _$RadiusUnitEnumMap = {
  RadiusUnit.none: 0,
  RadiusUnit.meter: 1,
  RadiusUnit.kiloMeter: 2,
};

UserProjectModel _$UserProjectModelFromJson(Map<String, dynamic> json) =>
    UserProjectModel(
      projectIds: json['projectIds'] as String?,
      projectName: json['projectName'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$UserProjectModelToJson(UserProjectModel instance) =>
    <String, dynamic>{
      if (instance.projectIds case final value?) 'projectIds': value,
      if (instance.projectName case final value?) 'projectName': value,
      if (instance.longitude case final value?) 'longitude': value,
      if (instance.latitude case final value?) 'latitude': value,
    };

UserPropertyModel _$UserPropertyModelFromJson(Map<String, dynamic> json) =>
    UserPropertyModel(
      propertyId: json['propertyId'] as String?,
      propertyName: json['propertyName'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$UserPropertyModelToJson(UserPropertyModel instance) =>
    <String, dynamic>{
      if (instance.propertyId case final value?) 'propertyId': value,
      if (instance.propertyName case final value?) 'propertyName': value,
      if (instance.longitude case final value?) 'longitude': value,
      if (instance.latitude case final value?) 'latitude': value,
    };
