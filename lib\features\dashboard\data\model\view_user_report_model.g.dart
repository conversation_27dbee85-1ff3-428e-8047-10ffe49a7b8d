// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'view_user_report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ViewUserReportModel _$ViewUserReportModelFromJson(Map<String, dynamic> json) =>
    ViewUserReportModel(
      id: json['id'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      status: (json['status'] as List<dynamic>?)
          ?.map((e) => ViewStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      generalManager: json['generalManager'] as String?,
      reportingManager: json['reportingManager'] as String?,
    );

Map<String, dynamic> _$ViewUserReportModelToJson(
        ViewUserReportModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.reportingManager case final value?)
        'reportingManager': value,
      if (instance.generalManager case final value?) 'generalManager': value,
      if (instance.status case final value?) 'status': value,
    };

ViewStatusModel _$ViewStatusModelFromJson(Map<String, dynamic> json) =>
    ViewStatusModel(
      statusId: json['statusId'] as String?,
      baseStatusId: json['baseStatusId'] as String?,
      statusDisplayName: json['statusDisplayName'] as String?,
      baseStatusDisplayName: json['baseStatusDisplayName'] as String?,
      count: json['count'] as String?,
      percentage: json['percentage'] as String?,
    );

Map<String, dynamic> _$ViewStatusModelToJson(ViewStatusModel instance) =>
    <String, dynamic>{
      'statusId': instance.statusId,
      'baseStatusId': instance.baseStatusId,
      'statusDisplayName': instance.statusDisplayName,
      'baseStatusDisplayName': instance.baseStatusDisplayName,
      'count': instance.count,
      'percentage': instance.percentage,
    };
