import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/module_name_enum.dart';

part 'template_model.g.dart';

@HiveType(typeId: HiveModelConstants.templateModelTypeId)
@JsonSerializable()
class Template {
  @HiveField(0)
  final String? title;
  @HiveField(1)
  final String? description;
  @HiveField(2)
  final String? header;
  @HiveField(3)
  final String? footer;
  @HiveField(4)
  final ModuleName? moduleName;

  Template({
    this.title,
    this.description,
    this.header,
    this.footer,
    this.moduleName,
  });

  factory Template.fromJson(Map<String, dynamic> json) => _$TemplateFromJson(json);

  Map<String, dynamic> toJson() => _$TemplateToJson(this);
}
