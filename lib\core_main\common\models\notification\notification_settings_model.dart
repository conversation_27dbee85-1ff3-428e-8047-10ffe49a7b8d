import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/models/notification/base_notification_settings.dart';

part 'notification_settings_model.g.dart';

@HiveType(typeId: HiveModelConstants.notificationSettingsModelTypeId)
@JsonSerializable()
class NotificationSettingsModel{
  @HiveField(0)
  final List<BaseNotificationSettings>? moduleSettings;

  NotificationSettingsModel({this.moduleSettings});

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) => _$NotificationSettingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsModelToJson(this);
}