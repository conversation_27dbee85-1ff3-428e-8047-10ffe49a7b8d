import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'contact_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.contactTypeEnumTypeId)
@JsonEnum(valueField: 'index')
enum ContactType {
  @HiveField(0)
  whatsApp("WhatsApp"),
  @HiveField(1)
  call("Call"),
  @HiveField(2)
  email("Email"),
  @HiveField(3)
  sms("SMS"),
  @HiveField(4)
  pushNotification("Push Notification"),
  @HiveField(5)
  links("Links");


final String description;

const ContactType(this.description);
}
