import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class LrRadioOptions<T> extends LeadratStatefulWidget {
  final List<RadioOptionItem<T>> items;
  final T? initialValue;
  final Function(T) onChanged;
  final Color? selectedColor;
  final Color? unselectedColor;
  final double? radioSize;
  final RadioLayout layout;
  final RadioSelectionStyle selectionStyle;
  final TextStyle? labelStyle;
  final TextStyle? subLabelStyle;
  final EdgeInsetsGeometry? itemPadding;
  final double? spacing;
  final double? runSpacing;
  final WrapAlignment wrapAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  const LrRadioOptions({
    Key? key,
    required this.items,
    required this.onChanged,
    this.initialValue,
    this.selectedColor,
    this.unselectedColor,
    this.radioSize = 20.0,
    this.layout = RadioLayout.vertical,
    this.selectionStyle = RadioSelectionStyle.dot,
    this.labelStyle,
    this.subLabelStyle,
    this.itemPadding,
    this.spacing = 8.0,
    this.runSpacing = 8.0,
    this.wrapAlignment = WrapAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  }) : super(key: key);

  @override
  State<LrRadioOptions<T>> createState() => _LrRadioOptionsState<T>();
}

class _LrRadioOptionsState<T> extends LeadratState<LrRadioOptions<T>> {
  T? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.initialValue;
  }

  @override
  void didUpdateWidget(LrRadioOptions<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialValue != widget.initialValue) {
      setState(() {
        selectedValue = widget.initialValue;
      });
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final radioItems = widget.items.map((item) => _buildRadioOption(item)).toList();

    switch (widget.layout) {
      case RadioLayout.horizontal:
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _addSpacingBetween(radioItems, widget.spacing ?? 8.0),
          ),
        );
      case RadioLayout.vertical:
        return Column(
          crossAxisAlignment: widget.crossAxisAlignment,
          children: _addSpacingBetween(radioItems, widget.spacing ?? 8.0),
        );
      case RadioLayout.wrap:
        return Wrap(
          spacing: widget.spacing ?? 8.0,
          runSpacing: widget.runSpacing ?? 8.0,
          alignment: widget.wrapAlignment,
          children: radioItems,
        );
    }
  }

  List<Widget> _addSpacingBetween(List<Widget> items, double spacing) {
    return items.asMap().entries.map((entry) {
      final isLast = entry.key == items.length - 1;
      if (isLast) return entry.value;

      return widget.layout == RadioLayout.horizontal
          ? Row(
              children: [
                entry.value,
                SizedBox(width: spacing),
              ],
            )
          : Column(
              children: [
                entry.value,
                SizedBox(height: spacing),
              ],
            );
    }).toList();
  }

  Widget _buildSelectionIndicator(bool isSelected, Color selectedCol, Color unselectedCol) {
    switch (widget.selectionStyle) {
      case RadioSelectionStyle.dot:
        return Container(
          width: widget.radioSize,
          height: widget.radioSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isSelected ? selectedCol : unselectedCol,
              width: 2,
            ),
          ),
          child: Center(
            child: isSelected
                ? Container(
                    width: (widget.radioSize ?? 20) * 0.5,
                    height: (widget.radioSize ?? 20) * 0.5,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: selectedCol,
                    ),
                  )
                : null,
          ),
        );
      case RadioSelectionStyle.check:
        return Container(
          width: widget.radioSize,
          height: widget.radioSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isSelected ? selectedCol : unselectedCol,
              width: 2,
            ),
            color: isSelected ? selectedCol : Colors.transparent,
          ),
          child: isSelected
              ? Icon(
                  Icons.check,
                  size: (widget.radioSize ?? 20) * 0.6,
                  color: Colors.white,
                )
              : null,
        );
      case RadioSelectionStyle.fill:
        return Container(
          width: widget.radioSize,
          height: widget.radioSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isSelected ? selectedCol : unselectedCol,
              width: 2,
            ),
            color: isSelected ? selectedCol : Colors.transparent,
          ),
        );
    }
  }

  Widget _buildRadioOption(RadioOptionItem<T> item) {
    final isSelected = selectedValue == item.value;
    final selectedCol = widget.selectedColor ?? ColorPalette.primaryGreen;
    final unselectedCol = widget.unselectedColor ?? ColorPalette.gray600;

    return InkWell(
      onTap: () {
        setState(() {
          selectedValue = item.value;
        });
        widget.onChanged(item.value);
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: widget.itemPadding ?? const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSelectionIndicator(isSelected, selectedCol, unselectedCol),
            const SizedBox(width: 8),
            if (item.icon != null) ...[item.icon!, const SizedBox(width: 8)],
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    item.label,
                    style: (widget.labelStyle ?? LexendTextStyles.lexend10Medium).copyWith(
                      color: isSelected ? ColorPalette.corbeau : unselectedCol,
                    ),
                  ),
                  if (item.subLabel != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      item.subLabel!,
                      style: widget.subLabelStyle ??
                          TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum RadioLayout { horizontal, vertical, wrap }

enum RadioSelectionStyle { dot, check, fill }

class RadioOptionItem<T> {
  final T value;
  final String label;
  final String? subLabel;
  final Widget? icon;

  RadioOptionItem({
    required this.value,
    required this.label,
    this.subLabel,
    this.icon,
  });
}
