// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_property_type_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterPropertyTypeModelAdapter
    extends TypeAdapter<MasterPropertyTypeModel> {
  @override
  final int typeId = 22;

  @override
  MasterPropertyTypeModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterPropertyTypeModel(
      id: fields[0] as String?,
      baseId: fields[1] as String?,
      level: fields[2] as int?,
      type: fields[3] as String?,
      displayName: fields[4] as String?,
      childTypes: (fields[5] as List?)?.cast<MasterPropertyTypeModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, MasterPropertyTypeModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.baseId)
      ..writeByte(2)
      ..write(obj.level)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.displayName)
      ..writeByte(5)
      ..write(obj.childTypes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterPropertyTypeModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterPropertyTypeModel _$MasterPropertyTypeModelFromJson(
        Map<String, dynamic> json) =>
    MasterPropertyTypeModel(
      id: json['id'] as String?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      type: json['type'] as String?,
      displayName: json['displayName'] as String?,
      childTypes: (json['childTypes'] as List<dynamic>?)
          ?.map((e) =>
              MasterPropertyTypeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MasterPropertyTypeModelToJson(
        MasterPropertyTypeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'baseId': instance.baseId,
      'level': instance.level,
      'type': instance.type,
      'displayName': instance.displayName,
      'childTypes': instance.childTypes,
    };
