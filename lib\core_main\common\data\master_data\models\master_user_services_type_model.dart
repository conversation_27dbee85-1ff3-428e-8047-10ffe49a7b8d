import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../constants/hive_model_constants.dart';

part 'master_user_services_type_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterUserServicesTypeId)
@JsonSerializable()
class MasterUserServicesModel {
  @HiveField(0)
  String? displayName;
  @HiveField(1)
  String? description;
  @HiveField(2)
  String? imageUrl;
  @HiveField(3)
  int? orderRank;
  @HiveField(4)
  String? createdBy;
  @HiveField(5)
  DateTime? createdOn;
  @HiveField(6)
  String? lastModifiedBy;
  @HiveField(7)
  DateTime? lastModifiedOn;
  @HiveField(8)
  DateTime? deletedOn;
  @HiveField(9)
  String? deletedBy;
  @HiveField(10)
  String? id;
  @HiveField(11)
  bool? isDeleted;

  MasterUserServicesModel({this.displayName, this.description, this.imageUrl, this.orderRank, this.createdBy, this.createdOn, this.lastModifiedBy, this.lastModifiedOn, this.deletedOn, this.deletedBy, this.id, this.isDeleted});
  factory MasterUserServicesModel.fromJson(Map<String, dynamic> json) => _$MasterUserServicesModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterUserServicesModelToJson(this);
}