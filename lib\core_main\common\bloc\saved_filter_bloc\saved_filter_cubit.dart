import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';

class SaveFilterCubit extends Cubit<SaveFilterState> {
  SaveFilterCubit(bool isUpdateFilter) : super(SaveFilterState(isUpdate: false));

  void toggleSaveFilter({
    required bool isUpdate,
    ItemSimpleModel<GetSavedFilterModel>? filter,
  }) {
    emit(state.copyWith(isUpdate: isUpdate, filter: filter));
  }
}

class SaveFilterState {
  final ItemSimpleModel<GetSavedFilterModel>? filter;
  final bool isUpdate;

  SaveFilterState({this.filter, this.isUpdate = false});

  SaveFilterState copyWith({
    ItemSimpleModel<GetSavedFilterModel>? filter,
    bool? isUpdate,
  }) {
    return SaveFilterState(
      filter: filter ?? this.filter,
      isUpdate: isUpdate ?? this.isUpdate,
    );
  }
}
