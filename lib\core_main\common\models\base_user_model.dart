import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../constants/hive_model_constants.dart';

part 'base_user_model.g.dart';

@HiveType(typeId: HiveModelConstants.baseUserModelTypeId)
@JsonSerializable(explicitToJson: true, includeIfNull: true)
class BaseUserModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final String? contactNo;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? createdBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final String? lastModifiedBy;
  @HiveField(7)
  final String? firstName;
  @HiveField(8)
  final String? lastName;
  @HiveField(9)
  final String? userName;
  @HiveField(10)
  final String? imageUrl;
  @HiveField(11)
  final bool? isActive;
  @HiveField(12)
  final bool? isMFAEnabled;
  @HiveField(13)
  final String? licenseNo;
  @HiveField(14)
  final bool? isGeoFenceActive;

  BaseUserModel({
    this.id,
    this.name,
    this.contactNo,
    this.createdBy,
    this.createdOn,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.firstName,
    this.lastName,
    this.userName,
    this.imageUrl,
    this.isActive,
    this.isMFAEnabled,
    this.licenseNo,
    this.isGeoFenceActive,
  });

  factory BaseUserModel.fromJson(Map<String, dynamic> json) => _$BaseUserModelFromJson(json);

  Map<String, dynamic> toJson() => _$BaseUserModelToJson(this);

  String get fullName => "${firstName ?? ""} ${lastName ?? ""}";
}
