enum ErrorActionCode {
  noOp(100, 'No operation'),
  stayOn(101, 'Stay on'),
  changeRoute(102, 'Change route'),
  fallBack(103, 'Fall back'),
  returnToHome(104, 'Return to home'),
  refresh(105, 'Refresh'),
  logout(106, 'Logout'),
  showToast(107, 'Show toast'),
  unknown(0, 'Unknown');

  final int code;
  final String description;

  const ErrorActionCode(this.code, this.description);

  static ErrorActionCode fromCode(int code) {
    return ErrorActionCode.values.firstWhere(
      (e) => e.code == code,
      orElse: () => ErrorActionCode.unknown,
    );
  }

  @override
  String toString() => description;
}
