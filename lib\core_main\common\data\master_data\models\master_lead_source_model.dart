import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/entites/lead_source_entity.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';

part 'master_lead_source_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterLeadSourceModelTypeId)
@JsonSerializable()
class MasterLeadSourceModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final DateTime? createdOn;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? lastModifiedOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? deletedOn;
  @HiveField(6)
  final String? deletedBy;
  @HiveField(7)
  final String? displayName;
  @HiveField(8)
  final int? value;
  @HiveField(9)
  final int? orderRank;
  @HiveField(10)
  final String? imageURL;
  @HiveField(11)
  final String? progressColor;
  @HiveField(12)
  final String? backgroundColor;
  @HiveField(13)
  final LeadSource? leadSourceType;
  @HiveField(14)
  final bool? isEnabled;
  @HiveField(15)
  final bool? isDefault;

  MasterLeadSourceModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.deletedOn,
    this.deletedBy,
    this.displayName,
    this.value,
    this.orderRank,
    this.imageURL,
    this.progressColor,
    this.backgroundColor,
    this.leadSourceType,
    this.isEnabled,
    this.isDefault,
  });

  factory MasterLeadSourceModel.fromJson(Map<String, dynamic> json) => _$MasterLeadSourceModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterLeadSourceModelToJson(this);

  LeadSourceEntity toEntity() {
    return LeadSourceEntity(
      id: id,
      createdOn: createdOn,
      createdBy: createdBy,
      lastModifiedOn: lastModifiedOn,
      lastModifiedBy: lastModifiedBy,
      deletedOn: deletedOn,
      deletedBy: deletedBy,
      displayName: displayName,
      value: value,
      orderRank: orderRank,
      imageURL: imageURL,
      progressColor: progressColor,
      backgroundColor: backgroundColor,
      leadSourceType: leadSourceType,
      isEnabled: isEnabled,
      isDefault: isDefault,
    );
  }
}
