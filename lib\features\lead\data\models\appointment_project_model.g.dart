// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_project_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentProjectModel _$AppointmentProjectModelFromJson(
        Map<String, dynamic> json) =>
    AppointmentProjectModel(
      id: json['id'] as String?,
      projectName: json['projectName'] as String?,
      isDone: json['isDone'] as bool?,
    );

Map<String, dynamic> _$AppointmentProjectModelToJson(
        AppointmentProjectModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.projectName case final value?) 'projectName': value,
      if (instance.isDone case final value?) 'isDone': value,
    };
