import 'package:flutter/material.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/resources/theme/app_theme.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';

abstract class LeadratStatelessWidget extends StatelessWidget {
  const LeadratStatelessWidget({Key? key}) : super(key: key);

  @protected
  void onInit(BuildContext context) {}

  @protected
  void onDispose(BuildContext context) {}

  @protected
  void onVisible(BuildContext context) {}

  @protected
  void onInvisible(BuildContext context) {}

  @protected
  void logError(Object error, [StackTrace? stackTrace]) {
    'Error: $error'.printInConsole();
    if (stackTrace != null) {
      'Stack Trace: $stackTrace'.printInConsole();
    }
  }

  @override
  Widget build(BuildContext context) {
    onInit(context);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onVisible(context);
    });

    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Theme(data: AppTheme.lightThemeMode, child: buildContent(context)),
    );
  }

  @protected
  Widget buildContent(BuildContext context);

  void dispose(BuildContext context) {
    FocusScope.of(context).unfocus();
    DialogManager().hideTransparentProgressDialog();
    onDispose(context);
  }
}
