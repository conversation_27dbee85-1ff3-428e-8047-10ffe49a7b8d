enum BasicAttributesValueTypeEnum {
  one("1", 1),
  two("2", 2),
  three("3", 3),
  four("4", 4),
  five("5", 5),
  six("6",6),
  seven("7",7),
  eight("8",8),
  nine("9",9),
  ten("10",10);


  final String description;
  final int value;

  const BasicAttributesValueTypeEnum(this.description, this.value);

  static BasicAttributesValueTypeEnum? fromValue(int value) {
    if (value <= 0) return null;
    // if (value > 5) return BasicAttributesValueTypeEnum.five;

    for (var type in BasicAttributesValueTypeEnum.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }
}
