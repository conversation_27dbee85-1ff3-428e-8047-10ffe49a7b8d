import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';

import '../../models/master_user_services_type_model.dart';

abstract class MasterDataRemoteDataSource {
  Future<List<MasterPropertyTypeModel>?> getPropertyTypes({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterPropertyTypeModel>?> getListingPropertyTypes({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterLeadSourceModel>?> getLeadSource({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterAreaUnitsModel?>?> getAreaUnits({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterLeadStatusModel>?> getLeadStatuses({Duration timeout = const Duration(seconds: 60)});

  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyAmenitites({bool isPropertyListingEnabled = false, Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterPropertyAttributesModel>?> getPropertyAttributes({Duration timeout = const Duration(seconds: 60)});

  Future<Map<EntityTypeEnum, DateTime>?> getModifiedDates({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterProjectTypeModel?>?> getProjectTypes({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterProjectAttributeModel>?> getProjectAttributes({Duration timeout = const Duration(seconds: 60)});

  Future<Map<String, List<MasterProjectAmenititesModel>>?> getProjectAmenitites({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterAssociatedBankModel>?> getAssociatedBank({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterUserServicesModel>?> getMasterUserServices({Duration timeout = const Duration(seconds: 60)});

  Future<List<MasterCustomStatusModel>?> getCustomStatus({Duration timeout = const Duration(seconds: 60)});

  Future<List<String>?> getAgencyNames({Duration timeout = const Duration(seconds: 60)});

  Future<List<String>?> getAllLeadAddress({Duration timeout = const Duration(seconds: 60)});
}
