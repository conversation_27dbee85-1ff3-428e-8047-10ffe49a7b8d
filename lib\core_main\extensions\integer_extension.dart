import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';

import '../di/injection_container.dart';
import 'string_extension.dart';

extension IntegerExtension on int {
  String convertCurrencyFormat({String? currency, bool? isInternationalFormat}) {
    bool isInternational = false;
    List<String> similarIndianCurrencies = ["INR", "NPR", "BDT", "PKR", "LKR"];
    if (this < 0) {
      return "Negative values not supported";
    }

    if (this == 0) {
      return "--";
    }
    isInternational = currency == null ? false : (similarIndianCurrencies.contains(currency) ? false : true);

    if (!isInternational) {
      // Indian Format (Crores, Lakhs, Thousands)
      List<int> conversionFactors = [10000000, 100000, 1000];
      List<String> suffixes = ["cr", "lac", "k"];

      for (int i = 0; i < conversionFactors.length; i++) {
        int factor = conversionFactors[i];
        if (this >= factor) {
          double quotient = this / factor;
          return currency != null && currency.isNotEmpty ? "$currency ${quotient.toStringAsFixed(1)} ${suffixes[i]}" : "${quotient.toStringAsFixed(1)} ${suffixes[i]}";
        }
      }
    } else {
      // International Format (Billions, Millions, Thousands)
      List<int> conversionFactors = [1000000000, 1000000, 1000];
      List<String> suffixes = ["B", "M", "K"];

      for (int i = 0; i < conversionFactors.length; i++) {
        int factor = conversionFactors[i];
        if (this >= factor) {
          double quotient = this / factor;
          return currency != null && currency.isNotEmpty ? "$currency ${quotient.toStringAsFixed(1)} ${suffixes[i]}" : "${quotient.toStringAsFixed(1)} ${suffixes[i]}";
        }
      }
    }

    return currency != null && currency.isNotEmpty ? "$currency $this" : toString();
  }

  String getMonthNameByMonthNumber() {
    switch (this) {
      case 1:
        return 'Jan';
      case 2:
        return 'Feb';
      case 3:
        return 'Mar';
      case 4:
        return 'Apr';
      case 5:
        return 'May';
      case 6:
        return 'Jun';
      case 7:
        return 'Jul';
      case 8:
        return 'Aug';
      case 9:
        return 'Sep';
      case 10:
        return 'Oct';
      case 11:
        return 'Nov';
      case 12:
        return 'Dec';
      default:
        return 'Jan';
    }
  }
}
