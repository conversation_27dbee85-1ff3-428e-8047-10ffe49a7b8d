import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_button.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

void selectChoiceBottomModal({
  required BuildContext context,
  required String title,
  required String leadingText,
  required String trailingText,
  required Function() onLeadingSelect,
  required Function() onTrailingSelect,
}) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -6,
            left: 0,
            right: 0,
            child: CustomPaint(
              size: Size(context.width(100), 6),
              painter: TrapeziumPainter(),
            ),
          ),
          Container(
            width: context.width(100),
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
            color: ColorPalette.white,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: LexendTextStyles.lexend18Regular.copyWith(color: ColorPalette.black),
                ),
                const SizedBox(height: 30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: LeadratButton(
                        buttonText: leadingText,
                        buttonTextStyle: LexendTextStyles.lexend14Regular.copyWith(color: ColorPalette.white),
                        onPressed: onLeadingSelect,
                        height: 42,
                        buttonColor: ColorPalette.primaryDarkColor,
                        borderRadius: 6,
                        border: Border.all(color: ColorPalette.white.withOpacity(0.2), width: 2),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: LeadratButton(
                        buttonText: trailingText,
                        buttonTextStyle: LexendTextStyles.lexend14Regular.copyWith(color: ColorPalette.black),
                        onPressed: onTrailingSelect,
                        height: 42,
                        buttonColor: ColorPalette.gray200,
                        borderRadius: 6,
                        border: Border.all(color: ColorPalette.white.withOpacity(0.2), width: 2),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      );
    },
  );
}
