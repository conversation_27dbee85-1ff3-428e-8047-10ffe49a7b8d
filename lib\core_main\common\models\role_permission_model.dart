import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../constants/hive_model_constants.dart';

part 'role_permission_model.g.dart';
@HiveType(typeId: HiveModelConstants.rolesAndPermissionsTypeId)
@JsonSerializable(explicitToJson: true)
class RolePermissionModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final DateTime? createdOn;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? lastModifiedOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final bool? isNew;
  @HiveField(6)
  final String? name;
  @HiveField(7)
  final String? description;
  @HiveField(8)
  final bool? enabled;
  @HiveField(9)
  final List<String?>? permissions;
  @HiveField(10)
  final String? permissionDescription;

  RolePermissionModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.isNew,
    this.name,
    this.description,
    this.enabled,
    this.permissions,
    this.permissionDescription,
  });

  factory RolePermissionModel.fromJson(Map<String, dynamic> json) => _$RolePermissionModelFromJson(json);
  Map<String, dynamic> toJson() => _$RolePermissionModelToJson(this);
}
