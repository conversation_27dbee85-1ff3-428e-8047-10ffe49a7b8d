import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';

class LeadSourceEntity {
  final String? id;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final DateTime? deletedOn;
  final String? deletedBy;
  final String? displayName;
  final int? value;
  final int? orderRank;
  final String? imageURL;
  final String? progressColor;
  final String? backgroundColor;
  final LeadSource? leadSourceType;
  final bool? isEnabled;
  final bool? isDefault;

  LeadSourceEntity({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.deletedOn,
    this.deletedBy,
    this.displayName,
    this.value,
    this.orderRank,
    this.imageURL,
    this.progressColor,
    this.backgroundColor,
    this.leadSourceType,
    this.isEnabled,
    this.isDefault,
  });
}
