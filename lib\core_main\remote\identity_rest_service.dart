import 'package:leadrat/core_main/extensions/rest_request_extension.dart';
import 'package:leadrat/core_main/remote/rest_client.dart';
import 'package:leadrat/core_main/remote/rest_service_base.dart';

class IdentityRestService extends RestServiceBase {
  IdentityRestService() : super(restClientCode: "identity");

  @override
  RestRequest createGetRequest(String resource, {body, Duration? timeout}) {
    final request = super.createGetRequest(resource, body: body, timeout: timeout);
    request.addTenantHeader();
    return request;
  }

  @override
  RestRequest createPostRequest(String resource, {body, Duration? timeout}) {
    final request = super.createPostRequest(resource, body: body, timeout: timeout);
    request.addTenantHeader();
    return request;
  }

  @override
  RestRequest createPutRequest(String resource, {body, Duration? timeout}) {
    final request = super.createPutRequest(resource, body: body, timeout: timeout);
    request.addTenantHeader();
    return request;
  }

  @override
  RestRequest createDeleteRequest(String resource, {body, Duration? timeout}) {
    final request = super.createDeleteRequest(resource, body: body, timeout: timeout);
    request.addTenantHeader();
    return request;
  }
}
