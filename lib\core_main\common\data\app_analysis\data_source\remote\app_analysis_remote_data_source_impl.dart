import 'package:dio/dio.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/remote/app_analysis_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/models/app_anaylsis_model.dart';
import 'package:leadrat/core_main/common/data/app_analysis/models/app_feature_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class AppAnalysisDataSourceImpl implements AppAnalysisDataSource {
  final Dio _dio = Dio(BaseOptions(
    baseUrl: AppAnalysisResources.baseUrl,
    headers: {
      'accept': '*/*',
      'Content-Type': 'application/json',
    },
  ));

  @override
  Future<bool?> sendAppAnalysis(AppAnalysisModel appAnalysisModel) async {
    try {
      final response = await _dio.post(AppAnalysisResources.tracks, data: appAnalysisModel.toJson());
      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      e.logException();
    }
    return null;
  }

  @override
  Future<List<AppFeatureModel>> getAppFeatures() async {
    try {
      final response = await _dio.get(AppAnalysisResources.getFeatures);
      if (response.statusCode == 200) {
        List<dynamic> data = response.data['data'];
        List<AppFeatureModel> features = data.map((featureJson) => AppFeatureModel.fromJson(featureJson)).toList();
        return features;
      } else {
        throw Exception('Failed to load features: ${response.statusCode}');
      }
    } catch (ex) {
      ex.logException();
      return [];
    }
  }
}
