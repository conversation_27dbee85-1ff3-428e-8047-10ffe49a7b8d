{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5f7c69ebbf8d64422cee6f2455c2225", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988708a0e75aad5181685a414d42e72f94", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ae0545eec001462ab66cb3461f316a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989810737d011046a9dcfeecabd93df967", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ae0545eec001462ab66cb3461f316a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a07ab812a1032c0e7de7d2e82981c8f2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98208cfc18f9ef107872250d4736c84ce4", "guid": "bfdfe7dc352907fc980b868725387e98c16f2659a30cd927dcd4d3a67acabcd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823993cbc760f5c7cc8a7785dd43dda30", "guid": "bfdfe7dc352907fc980b868725387e981d76175b953474d8f25b3201c2deae68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98872979ece3a6a7be63fd62d6b9f25d2a", "guid": "bfdfe7dc352907fc980b868725387e98fdf119fe6b3dc3477570c73ffe1b1a15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112dc6dcef5488365c7ec444224a8f25", "guid": "bfdfe7dc352907fc980b868725387e98067ba4e9909e4d26a1b7e96913d3ed95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b9a5c492e7652521661f7c42fd05a9", "guid": "bfdfe7dc352907fc980b868725387e989aa9f5fdfc9d12babbc694ca28e8de4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd0c9ec102bb442a3099993420837931", "guid": "bfdfe7dc352907fc980b868725387e98fe2e5ecbae8b02d95b31c22a13d6dc12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a3e360ff5ab4ea141f85b02caf3312", "guid": "bfdfe7dc352907fc980b868725387e98315d209fe7098da8e1c19520bdd1caeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c23afad994cf93195e2780dd6e3ef39c", "guid": "bfdfe7dc352907fc980b868725387e98f8ff9078458555757b73e24b313417e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efa1b5aefcc8b7eeb8cfc60574bc8603", "guid": "bfdfe7dc352907fc980b868725387e98caf78be11b47dfa9f2160fb413ab224f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbed6775775d47b35209a1f05692173", "guid": "bfdfe7dc352907fc980b868725387e98513f356d379071cc0bdc20c6ebf02cf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981504fd0cd16b2d14674e5c19d838db8f", "guid": "bfdfe7dc352907fc980b868725387e9889b14b1dbaf5c7b4487efbc7a9c53bca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c89a3b8cd6989e797be1c205671f7d98", "guid": "bfdfe7dc352907fc980b868725387e98c9c4412b108bfb8d74fc1be8f1edcbb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b2e775fd93eeb1f35d27f44b490a124", "guid": "bfdfe7dc352907fc980b868725387e9806b160281359e885128814c0dec009b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9929037c8bbb546e84a43ef11bee1ca", "guid": "bfdfe7dc352907fc980b868725387e98acc006270bcb621d558c401b7eff635f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e30cba46f27e709e430f03f0f1d30e", "guid": "bfdfe7dc352907fc980b868725387e98baae2f3ea15e94f9754fda6a37648b97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f97d3bb59db0322cd669d8ac2eb8a4d", "guid": "bfdfe7dc352907fc980b868725387e9811c2f04ccfdd1f249076fc53182672e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e603a6267ed61500e8ca9ed0ea4974", "guid": "bfdfe7dc352907fc980b868725387e98f90a5c0daa3288a302cc0dfbae6a37a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e2bd045d17f9956a3c07a844575c696", "guid": "bfdfe7dc352907fc980b868725387e98871296749df5f5254dfed7451b866260", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a50aa7b07e7a8d96a34ac13ab2d7cc", "guid": "bfdfe7dc352907fc980b868725387e9805a5697a71321c39c98e732612fd812e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb4839acfa513b7d87d7a6a9022134b", "guid": "bfdfe7dc352907fc980b868725387e98ba94f2d8adec03361ee2a3ef8a97108e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d61c6b6405705e18143126b6a4ac3d", "guid": "bfdfe7dc352907fc980b868725387e98664f4af579e7ff517f94e4e8e2be91c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98205c72b7143f34951633913fdbb3e727", "guid": "bfdfe7dc352907fc980b868725387e98e2037d790f90223ed58bf609e0cf14e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c6b26e94b103939b10e3d722698f33", "guid": "bfdfe7dc352907fc980b868725387e984365decd1b1876fd7057cef59981e65f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b056a18bb937c46e04533ff6f7a72188", "guid": "bfdfe7dc352907fc980b868725387e98190c4da8762d85576d30c2e9d8e492a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd6512b7d4ba2cc2fd163a9f9b7fc09", "guid": "bfdfe7dc352907fc980b868725387e98f5f15640b9d8f0bc2032c835d7dab645", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f578157b1fd93762166fdd3f7fbb4c", "guid": "bfdfe7dc352907fc980b868725387e98a13dc1c1c82b9470b2dbbe4f073201a1"}], "guid": "bfdfe7dc352907fc980b868725387e98eb5525226d33123bb077dcfbc533e916", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a1a033ad2154c78cbf0aba462b3e2f6", "guid": "bfdfe7dc352907fc980b868725387e988f3df00014a2c8a253e4964456d2d6a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a797cb8248d442057f54ccd6783cf97", "guid": "bfdfe7dc352907fc980b868725387e984c26cdbf51dfb1dc5809208f78733943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a9bfc1263fc94ab96071267c5b2fa2", "guid": "bfdfe7dc352907fc980b868725387e98b9fd49712c7eddc5ae3b45474c703e5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c798a7d3d2fac5f11d9683e60746a115", "guid": "bfdfe7dc352907fc980b868725387e985be98fd707270cb682556eb90e177448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa842d0e8b8ac2ef56de189a62748b9e", "guid": "bfdfe7dc352907fc980b868725387e9856d9ffb71487bcee56455d76e505becc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a212f7b01602e713e8d6fffe6a0a75a", "guid": "bfdfe7dc352907fc980b868725387e981e634317f9d72655b9dd6bc354529a82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6101c896a4eb7ead290cb008de24997", "guid": "bfdfe7dc352907fc980b868725387e98266607422f412039b6cfaa96f7f4a7c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9729eaa59368e7f8655e33cda38b161", "guid": "bfdfe7dc352907fc980b868725387e989a476b19110677f7175b36a271d70df9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506e809c481c3fdec8bcea763fd9b8cf", "guid": "bfdfe7dc352907fc980b868725387e986186eb88c19e7d146a5cdcb6977f0c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411562d5347c67547057d5427ad827be", "guid": "bfdfe7dc352907fc980b868725387e9849ff337b897f46d42b8fee22e5e8024f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3a87e2655a5617846f9965de0296720", "guid": "bfdfe7dc352907fc980b868725387e98977a1c5b7ed3fe16639b134b28563f62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039b5ede0b93eac7344e38505eac2353", "guid": "bfdfe7dc352907fc980b868725387e984b33a52f3a08d2cafe9b6ab95a317408"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0bb0fc4d9eab5a6264f338a0512be6", "guid": "bfdfe7dc352907fc980b868725387e98b4b5071e159517f48bd3ab81039665fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dfaa0019738d497f10722e21b843845", "guid": "bfdfe7dc352907fc980b868725387e98e3e08419ca410dc8cf808712857ab499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981477aba11cfc1bcc42d9b4df96649a69", "guid": "bfdfe7dc352907fc980b868725387e9889705ebab884764ede2a52c3aaf03e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc13c56d472cc9a18a90131908d537c", "guid": "bfdfe7dc352907fc980b868725387e98b81c9b7ade64d264a0393be045c86bba"}], "guid": "bfdfe7dc352907fc980b868725387e9859e28089ea9c3c809d2ed93f4c3ea308", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9844594c9df52b854ac1657b536947a9a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e989ba0e6f48a93c769b8d92a72e3eb13f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e984a451bc6a17fcdf5cd4138e1fe01b64a"}], "guid": "bfdfe7dc352907fc980b868725387e98dc4109c580d2206eed3bde320ace70f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bdb6ebca22ae689d64aaaf171a4e0577", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98fd3ad22b806513a82f7ec56152c0f1c9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}