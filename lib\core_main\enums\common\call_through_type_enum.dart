import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'call_through_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.callThroughTypeId)
@JsonEnum(valueField: 'index')
enum CallThroughTypeEnum {
  @HiveField(0)
  askEveryTime,
  @HiveField(1)
  ivrOnly,
  @HiveField(2)
  dialerOnly;
}
