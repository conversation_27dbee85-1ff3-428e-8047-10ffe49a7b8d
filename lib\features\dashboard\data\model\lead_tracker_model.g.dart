// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_tracker_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadTrackerModel _$LeadTrackerModelFromJson(Map<String, dynamic> json) =>
    LeadTrackerModel(
      whatsAppLead: (json['whatsAppLead'] as num?)?.toInt(),
      callLead: (json['callLead'] as num?)?.toInt(),
      emailLead: (json['emailLead'] as num?)?.toInt(),
      smsLead: (json['smsLead'] as num?)?.toInt(),
      totalEvent: (json['totalEvent'] as num?)?.toInt(),
      whatsAppEvent: (json['whatsAppEvent'] as num?)?.toInt(),
      callEvent: (json['callEvent'] as num?)?.toInt(),
      emailEvent: (json['emailEvent'] as num?)?.toInt(),
      smsEvent: (json['smsEvent'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LeadTrackerModelToJson(LeadTrackerModel instance) =>
    <String, dynamic>{
      if (instance.whatsAppLead case final value?) 'whatsAppLead': value,
      if (instance.callLead case final value?) 'callLead': value,
      if (instance.emailLead case final value?) 'emailLead': value,
      if (instance.smsLead case final value?) 'smsLead': value,
      if (instance.totalEvent case final value?) 'totalEvent': value,
      if (instance.whatsAppEvent case final value?) 'whatsAppEvent': value,
      if (instance.callEvent case final value?) 'callEvent': value,
      if (instance.emailEvent case final value?) 'emailEvent': value,
      if (instance.smsEvent case final value?) 'smsEvent': value,
    };
