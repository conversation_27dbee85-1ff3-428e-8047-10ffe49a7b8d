import 'package:hive/hive.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'entity_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.entityTypeEnumTypeId)
enum EntityTypeEnum {
  @HiveField(0)
  none(0, 'None'),
  @HiveField(1)
  lead(1, 'Lead'),
  @HiveField(2)
  userDetails(2, 'UserDetails'),
  @HiveField(3)
  masterAreaUnit(3, 'MasterAreaUnit'),
  @HiveField(4)
  masterLeadSource(4, 'MasterLeadSource'),
  @HiveField(5)
  masterLeadStatus(5, 'MasterLeadStatus'),
  @HiveField(6)
  masterPropertyAmenity(6, 'MasterPropertyAmenity'),
  @HiveField(7)
  masterPropertyAttribute(7, 'MasterPropertyAttribute'),
  @HiveField(8)
  masterPropertyType(8, 'MasterPropertyType'),
  @HiveField(9)
  profile(9, 'Profile'),
  @HiveField(10)
  roles(10, 'Roles'),
  @HiveField(11)
  currentUser(11, 'CurrentUser'),
  @HiveField(12)
  agencyNames(12, 'AgencyNames'),
  @HiveField(13)
  masterWhatsAppTemplate(13, 'MasterWhatsAppTemplate'),
  @HiveField(14)
  whatsAppTemplate(14, 'WhatsAppTemplate'),
  @HiveField(15)
  template(15, 'Template'),
  @HiveField(16)
  tempProjects(16, 'TempProjects'),
  @HiveField(17)
  property(17, 'Property'),
  @HiveField(18)
  leadAddresses(18, 'LeadAddresses'),
  @HiveField(19)
  userSettings(19, 'UserSettings'),
  @HiveField(20)
  customMasterLeadStatus(20, 'CustomMasterLeadStatus'),
  @HiveField(21)
  globalSettings(21, 'GlobalSettings'),
  @HiveField(22)
  flag(22, 'Flag'),
  @HiveField(23)
  mobileCatalogDetail(23, 'MobileCatalogDetail'),
  @HiveField(24)
  customMasterAttribute(24, 'CustomMasterAttribute'),
  @HiveField(25)
  customMasterAmenity(25, 'CustomMasterAmenity'),
  @HiveField(26)
  customFilters(26, 'CustomFilters'),
  @HiveField(27)
  source(27, 'Source'),
  @HiveField(28)
  customMasterDataStatus(28, 'CustomMasterDataStatus'),
  @HiveField(29)
  project(29, 'Project');

  final int value;
  final String key;

  const EntityTypeEnum(this.value, this.key);

  factory EntityTypeEnum.fromString(String key) {
    return EntityTypeEnum.values.firstWhere(
          (e) => e.key == key,
      orElse: () => EntityTypeEnum.none,
    );
  }
}
