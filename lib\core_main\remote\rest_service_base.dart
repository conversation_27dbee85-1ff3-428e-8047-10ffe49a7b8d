import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/business/error_action_code_enum.dart';
import 'package:leadrat/core_main/enums/remote_enum/request_methods.dart';
import 'package:leadrat/core_main/errors/business_exception/argument_null_exception.dart';
import 'package:leadrat/core_main/errors/business_exception/rest_token_invalid_exception.dart';
import 'package:leadrat/core_main/errors/business_exception/user_permission_denied_exception.dart';
import 'package:leadrat/core_main/errors/remote_exception/rest_response_exception.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/rest_request_extension.dart';
import 'package:leadrat/core_main/remote/rest_client.dart';
import 'package:leadrat/features/auth/presentation/pages/auth_domain_page.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/main.dart';

abstract class RestServiceBase extends RestClient {
  static const int _maxRetryAttempts = 3;

  RestServiceBase({required String restClientCode}) : super(restClientCode: restClientCode);

  Future<T> executeRequestAsync<T>(RestRequest restRequest, T Function(Map<String, dynamic>) fromJson, {int retryCount = 0}) async {
    try {
      Response<dynamic> response = await _sendRequest(restRequest);
      _handleResponse(response);
      final jsonResponse = response.data;

      if (jsonResponse is T) {
        return jsonResponse;
      } else if (jsonResponse is Map<String, dynamic>) {
        return fromJson(jsonResponse);
      } else {
        throw ArgumentError('Invalid response format for type $T');
      }
    } on RestTokenInvalidException catch (error) {
      error.logException();
      _logout();
      rethrow;
    } on DioException catch (error, stackTrace) {
      logException(stackTrace);
      final content = error.response?.data?.toString() ?? '';
      final actionCode = _getActionCode(content);
      if ((error.response?.statusCode == 401 && retryCount < _maxRetryAttempts && ErrorActionCode.refresh.code == actionCode)) {
        return await _retryWithRefreshToken(restRequest, fromJson, retryCount + 1);
      } else {
        _handleDioError(error);
        rethrow;
      }
    } catch (error, stackTrace) {
      error.logException(stackTrace);
      rethrow;
    }
  }

  Future<Response<dynamic>> _sendRequest(RestRequest restRequest) {
    return dio.request<dynamic>(
      restRequest.path,
      data: restRequest.body,
      options: Options(
        method: _mapRequestMethodToString(restRequest.requestMethod),
        headers: restRequest.headers,
        receiveTimeout: restRequest.timeout ?? const Duration(seconds: 60),
      ),
    );
  }

  String _mapRequestMethodToString(RequestMethod? method) {
    switch (method) {
      case RequestMethod.get:
        return 'GET';
      case RequestMethod.post:
        return 'POST';
      case RequestMethod.put:
        return 'PUT';
      case RequestMethod.delete:
        return 'DELETE';
      default:
        throw Exception("Unknown Method");
    }
  }

  Future<T> _retryWithRefreshToken<T>(RestRequest originalRequest, T Function(Map<String, dynamic>) fromJson, int retryCount) async {
    try {
      final newToken = await _refreshToken();
      if (newToken != null) {
        var request = originalRequest;
        request.updateAuthorizationHeader();
        return await executeRequestAsync<T>(request, fromJson, retryCount: retryCount);
      } else {
        throw RestTokenInvalidException();
      }
    } catch (e) {
      throw RestTokenInvalidException();
    }
  }

  Future<String?> _refreshToken() async {
    final authTokenRepository = getIt<AuthTokenRepository>();
    final result = await authTokenRepository.getRefreshToken();
    return result?.idToken;
  }

  void _handleResponse(Response<dynamic> response) {
    final statusCode = response.statusCode;
    if (statusCode == null) {
      throw ArgumentNullException('No status code found');
    }

    if (statusCode == 200) return;
    if (statusCode == 401) return _handleUnauthorized(response);
    if (statusCode == 403) throw UserPermissionDeniedException();
    if (statusCode == 404) {
      throw RestResponseException(message: 'Resource not found');
    }
    if (statusCode == 204) throw ArgumentNullException('No content returned');
    if (statusCode >= 500) throw RestResponseException(message: 'Server error');
  }

  void _handleUnauthorized(Response<dynamic> response) {
    final content = response.data?.toString() ?? '';
    final actionCode = _getActionCode(content);

    if (content.contains("User Not Active")) {
      _logout();
      throw "Your account is inactive. Please contact admin for assistance.";
    } else if (actionCode == ErrorActionCode.refresh.code) {
      throw RestTokenInvalidException();
    } else if ((actionCode == ErrorActionCode.logout.code || content.contains("Password has been changed")) && response.requestOptions.path != "api/mobile/tokens") {
      _logout();
    } else {
      throw response.data?['exception'] ?? "something went wrong";
    }
  }

  void _handleDioError(DioException error) {
    final statusCode = error.response?.statusCode;
    if (statusCode == null) {
      throw ArgumentNullException('No status code found');
    }
    if (statusCode == 401) return _handleUnauthorized(error.response!);
    if (statusCode == 403) throw UserPermissionDeniedException();
    if (statusCode == 404) throw RestResponseException(message: 'Resource not found');
    if (statusCode == 204) throw ArgumentNullException('No content returned');
    if (statusCode >= 500) {
      final errorMessage = error.response?.data['messages'][0] ?? error.message;
      if (errorMessage.toString().toLowerCase().trim() == "refresh token has expired") {
        throw RestTokenInvalidException();
      } else {
        throw errorMessage;
      }
    }
    throw RestResponseException(message: error.message);
  }

  int _getActionCode(String content) {
    try {
      final actionCodeRegex = RegExp(r'actionCode\s*:\s*(\d+)');
      final match = actionCodeRegex.firstMatch(content);
      if (match != null) {
        return int.parse(match.group(1)!);
      }
      return 100;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return 100;
    }
  }

  void _logout() {
    getIt<LeadratHomeBloc>().add(LogoutEvent());
    MyApp.navigatorKey.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const AuthDomainPage()),
      (route) => false,
    );
  }

  Map<String, dynamic> decodeJwt(String token) {
    final jwt = JWT.decode(token);
    return jwt.payload;
  }

  RestRequest createGetRequest(String resource, {dynamic body, Duration? timeout}) => RestRequest(resource, body, method: RequestMethod.get, timeout: timeout);

  RestRequest createPostRequest(String resource, {dynamic body, Duration? timeout}) => RestRequest(resource, body, method: RequestMethod.post, timeout: timeout);

  RestRequest createPutRequest(String resource, {dynamic body, Duration? timeout}) => RestRequest(resource, body, method: RequestMethod.put, timeout: timeout);

  RestRequest createDeleteRequest(String resource, {dynamic body, Duration? timeout}) => RestRequest(resource, body, method: RequestMethod.delete, timeout: timeout);
}
