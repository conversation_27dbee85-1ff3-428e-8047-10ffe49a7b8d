{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987cffd8a615d5caf58a4bdc4acd7c0420", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee60b74d83f4e38e0bbfc6fb36158906", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981267b8dc8e994a1cda2a0204d6ec7a94", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bcb24302b0ffaea56a2b0982224a092a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981267b8dc8e994a1cda2a0204d6ec7a94", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f10634ad6beea79d473b6b177580456e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8fb98292bb2ed1e3aec56b3369c915e", "guid": "bfdfe7dc352907fc980b868725387e987f3f95fcb88abafbb86280edbcb36ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98691fff7855ca6b47d18fd2ee9a24a0e0", "guid": "bfdfe7dc352907fc980b868725387e9804009612ab39cbb7b0da57dae4c91d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1659d008524d293f4df9b7af3141c14", "guid": "bfdfe7dc352907fc980b868725387e98f2e4c2c88cd914dc7bc0d2646748a029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d437bd72553018035a57ef4341b048", "guid": "bfdfe7dc352907fc980b868725387e98af6c96377a71fb8b308dac40b4cf03da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a4a77e58e4f783d57db995683752ff", "guid": "bfdfe7dc352907fc980b868725387e98c9237e44fe25830bb127093d69cfde58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b6d1e2fcb059dc6d3ede399c23664f", "guid": "bfdfe7dc352907fc980b868725387e9888136115411a804b0beef0eab761177d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e303a02cc046f90229e3dc23bd86f684", "guid": "bfdfe7dc352907fc980b868725387e98f89a263c64323d4dff21e914fbab974b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856be72f3b1e8296ae86a012242ba3f3f", "guid": "bfdfe7dc352907fc980b868725387e9889b456f9ee7088f520fbf3ff4476dfab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d535415ecf4691517f32f92f67f3d0", "guid": "bfdfe7dc352907fc980b868725387e98a4ede0c59ce37c354d322a77133913e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984782b1edf8cf7a7d62049378a518449d", "guid": "bfdfe7dc352907fc980b868725387e988e4b0bfd57217e5223c6449ce897c0b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985937860e4a368c38d03a31256ee09a79", "guid": "bfdfe7dc352907fc980b868725387e98e4ac0e6c17fad9150a52eba07c3145ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc7103dae5595611f0fb81b37a328d9", "guid": "bfdfe7dc352907fc980b868725387e98311bb015bc33faab9e30a9827cb9f531", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dde684d0cb08adefcfd25c86a3dd01f", "guid": "bfdfe7dc352907fc980b868725387e98a7f78608d036b218842d156a25a33996", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e04e6ee7ff1d3139384714ab3add3f0", "guid": "bfdfe7dc352907fc980b868725387e98926813e86373d4b4cce9e2f980c887a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6dd3b773484885771036186a5c47b7", "guid": "bfdfe7dc352907fc980b868725387e98cfba8e205a3469b4d2ca676eb152ab2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cd8a63737cc3cd74641c29e57dbc0b", "guid": "bfdfe7dc352907fc980b868725387e989e7d62c439e6e14070f6c265cc22ee7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd1c827865242155b5cc6a220b0a4be", "guid": "bfdfe7dc352907fc980b868725387e98388a58c2c1a497a9d10207a554ef54ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d07d3aba5b4d238d9ee6140e9e5478", "guid": "bfdfe7dc352907fc980b868725387e988d76098c9168798eb7033ffa50e3a230", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b3ade1c04441178ea077541ba627b7", "guid": "bfdfe7dc352907fc980b868725387e98818c7fbf047e2c51f510cfb9c88df353", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d780a45ce7d26b56d4cb8a7533f4811c", "guid": "bfdfe7dc352907fc980b868725387e982d38477fa2bd240563a0891d96c02236", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5b220cc86af7d0747eb65cc49e2e96", "guid": "bfdfe7dc352907fc980b868725387e98eb16b1026445050882e1a8327848a341", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb76e7626fa9ab1d3d0e0c3e7a9833a8", "guid": "bfdfe7dc352907fc980b868725387e98e77763a0b04993b70e385e823951100e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981ae75dd107749c75551c908abbc21d66", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b144dd47e4279b2d7980b669123aa0e", "guid": "bfdfe7dc352907fc980b868725387e98de575a345d00589de29d28a0a2cdac09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405b0517ff10cddf17a1d5e85a0c9b2f", "guid": "bfdfe7dc352907fc980b868725387e987177e0af29b669d191623d5ae4ba12e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802328065911c3dc688018225572a9a6", "guid": "bfdfe7dc352907fc980b868725387e9831eaf7fcf5268ba6c001a6316d6361aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10c4490b42996353db44e2f2b928a38", "guid": "bfdfe7dc352907fc980b868725387e9862a72c7d4488ab538a123e1d6b9bf3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb1d7b93d1087ac12691fadbee24126", "guid": "bfdfe7dc352907fc980b868725387e98a321b81b07e673c0327a9c44dcf93653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e471fc3f0c1e7770062fae11eb7280", "guid": "bfdfe7dc352907fc980b868725387e985ae524db04843446cdcc2a1d2b329410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834cf4937e1675e10863089a6d3634bcb", "guid": "bfdfe7dc352907fc980b868725387e98bd802a4d14ba377e33a7d2778326cd2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed69139fb20a79d7218f6af5d1e6f67", "guid": "bfdfe7dc352907fc980b868725387e98fc0ffbf38a67f7d40611bf31d7a2a5aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5897a888b646fd24d0b20cdf1c9308b", "guid": "bfdfe7dc352907fc980b868725387e98b82462046fdbec66a56ae625bb77d57e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652b32ce8846ec18d9dec92f4d7ac504", "guid": "bfdfe7dc352907fc980b868725387e98d5e0258b6dc6d8aeffc32a97a1be459c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3bba9ef33d43a0adbf0d72014d3420f", "guid": "bfdfe7dc352907fc980b868725387e98969104254cad6ba38496b3c28b1faf84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c956fb215279d9b756ce86c987ab602", "guid": "bfdfe7dc352907fc980b868725387e982759b975d849fa692b5a810a7934a4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729e656715379453a2e3c499627833ff", "guid": "bfdfe7dc352907fc980b868725387e98824a12e70ba4b825713158ef3f69165b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865358eebfd6284281a8fe48b0719827e", "guid": "bfdfe7dc352907fc980b868725387e986f0362360c788bfbd6e98998d057e0d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0523ffc6e6792df1451ba6f0893ae7", "guid": "bfdfe7dc352907fc980b868725387e9835125e61e98214eb12c413e683b06615"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d7c15eee280740fcf144a9acfd0495", "guid": "bfdfe7dc352907fc980b868725387e98b707a13669af48ae0d920829a3616297"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f292f811e3fcfcb49b697908291109ed", "guid": "bfdfe7dc352907fc980b868725387e98af3c70f8622e70208178935d8730d97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813eddd292ec3f4edb01c993216196867", "guid": "bfdfe7dc352907fc980b868725387e982dde7722519900ddca0416e451ee69c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838085ab3432531f32e42fe2c8947fa65", "guid": "bfdfe7dc352907fc980b868725387e980ff9b26c6f3688c0fb17ebacebf933cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2ddf3c08a0f330284793879740da35", "guid": "bfdfe7dc352907fc980b868725387e980d25ba49ca980de850558fd884ee6c84"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c50ad43bc80ef24ea4dd1fd930a6a6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98f428970327b2b193dbf8ba449c5422e1"}], "guid": "bfdfe7dc352907fc980b868725387e980fdabc76e117107c11f4a12e7268bca9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a83d68b8ec21ad39c1495f724a10b236", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98fb78a81e55b74987cb6a2c952a495c3c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}