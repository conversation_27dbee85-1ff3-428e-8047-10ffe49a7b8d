import 'package:flutter/material.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

void removeDocumentBottomModal(BuildContext context, Function(bool) onSelect) {
  showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(10.0),
      ),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 35, vertical: 30),
        width: context.width(100),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(10.0),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              ImageResources.imageRemoveDocument,
              height: 70,
            ),
            const SizedBox(height: 40),
            Text(
              "Are you sure you want to remove the uploaded document?",
              textAlign: TextAlign.center,
              style: LexendTextStyles.lexend12Bold.copyWith(
                color: const Color(0xFF595667),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => onSelect(false),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: ColorPalette.primaryDarkColor,
                      ),
                      height: 38,
                      alignment: Alignment.center,
                      child: Text(
                        "No",
                        style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.white),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: GestureDetector(
                    onTap: () => onSelect(true),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: ColorPalette.veryLightGray,
                      ),
                      height: 38,
                      alignment: Alignment.center,
                      child: Text(
                        "Yes",
                        style: LexendTextStyles.lexend14Medium.copyWith(color: const Color(0xFF595667)),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      );
    },
  );
}
