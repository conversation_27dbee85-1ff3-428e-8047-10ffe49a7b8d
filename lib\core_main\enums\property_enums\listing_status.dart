import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'index')
enum ListingStatus {
  none("All"),
  approved("Approved"),
  draft("Draft"),
  refused("Refused"),
  sold("Sold"),
  archived("Archived");

  final String description;

  const ListingStatus(this.description);
}

@JsonEnum(valueField: 'index')
enum ListingLevel {
  none("None"),
  standard("Standard"),
  featured("Featured"),
  premium("Premium");

  final String description;

  const ListingLevel(this.description);
}
