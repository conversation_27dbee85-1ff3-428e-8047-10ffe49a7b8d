import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';

part 'address_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class AddressModel {
  final DateTime? createdOn;
  final String? id;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final String? placeId;
  final String? subLocality;
  final String? locality;
  final String? district;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? longitude;
  final String? latitude;
  final bool isGoogleMapLocation;
  final String? locationId;
  final bool isManual;
  final String? community;
  final String? subCommunity;
  final String? towerName;

  AddressModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.placeId,
    this.subLocality,
    this.locality,
    this.district,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.longitude,
    this.latitude,
    this.isGoogleMapLocation = false,
    this.locationId,
    this.isManual = false,
    this.community,
    this.subCommunity,
    this.towerName,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => _$AddressModelFromJson(json);

  Map<String, dynamic> toJson() => _$AddressModelToJson(this);

  AddressEntity toEntity() {
    return AddressEntity(
      state: state,
      subLocality: subLocality,
      postalCode: postalCode,
      placeId: placeId,
      longitude: longitude,
      locationId: locationId,
      locality: locality,
      latitude: latitude,
      isGoogleMapLocation: isGoogleMapLocation,
      district: district,
      country: country,
      city: city,
      lastModifiedOn: lastModifiedOn,
      lastModifiedBy: lastModifiedBy,
      createdBy: createdBy,
      createdOn: createdOn,
      isManual: isManual,
      community: community,
      subCommunity: subCommunity,
      towerName: towerName,
    );
  }
}
