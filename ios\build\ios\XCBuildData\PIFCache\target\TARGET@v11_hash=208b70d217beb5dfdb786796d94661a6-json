{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852dafe4957627fddd699ea3b91466019", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f5e415349343e3d7e82955e963638479", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e8e5b34700a7354bff0b37dbc56fe4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d038ed2fe0fe39a1d859600d23f51fc0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e8e5b34700a7354bff0b37dbc56fe4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9833694214ca50bda4b1ef6884bde53e41", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6c444366bc14da5b244f6b78029fb11", "guid": "bfdfe7dc352907fc980b868725387e98da4d401113a9f23dfad82bbc37f76449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0f78efe61c761c6e27646b1acda96a", "guid": "bfdfe7dc352907fc980b868725387e98937de782229e1d4766b8b4d40171422b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a906d16cb55e507713abdff00afacd4", "guid": "bfdfe7dc352907fc980b868725387e9886ff2feedeaf0050fda5988bbe41e266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710b40326d1434c4cc5ee8235c476056", "guid": "bfdfe7dc352907fc980b868725387e98346235154473686b6a7dfd7c5b8391d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b586801d2b327a2da2034d047a6031c", "guid": "bfdfe7dc352907fc980b868725387e98e785a92fddacf13a452225217a6cf1a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5d239377b882e6a398b2ab95bdd81c", "guid": "bfdfe7dc352907fc980b868725387e9857a8d4362bed29dd2386df5e3283fe51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983350b081854b3fbb116aa75c96384cab", "guid": "bfdfe7dc352907fc980b868725387e989c398a17bb66cf1d437fa52390d9fcbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb8e741daead4474ce9765ca2985900", "guid": "bfdfe7dc352907fc980b868725387e98e1128b074b4ffc14806b817c3fdfe68d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b516c8a8b0dc3637458542ea281865", "guid": "bfdfe7dc352907fc980b868725387e98b413741c590c059587221ea7c5ae34e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813604eed1d759d6cc8fcbfeeff7bf137", "guid": "bfdfe7dc352907fc980b868725387e9874a22893e951d00dffc4a2cbeb99e4bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe713aa3aaf75d0f9ca091becd46f4fc", "guid": "bfdfe7dc352907fc980b868725387e983d0a58ef5cece9a325fa0d0c27b0a456", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e840881df64ecd7cfa2f84df5eb6532e", "guid": "bfdfe7dc352907fc980b868725387e9846e707b71ae4178ee9fbec44f2b71db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23bc4b2cf92d2aa8ffc007338d76610", "guid": "bfdfe7dc352907fc980b868725387e988e6b3092df6d5aad4d2980cb1ba98027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bda27ea0f48cc6a09a7e6f946e20ae1", "guid": "bfdfe7dc352907fc980b868725387e98d2567c398b361e38d8666dd0aed52d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e19b97dd9e138063678ad40d7fa0bf", "guid": "bfdfe7dc352907fc980b868725387e981909d90a8a09ea2d56c04ba3531452c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8dc0a58217c7c6747087897cf492db8", "guid": "bfdfe7dc352907fc980b868725387e98bc181ace8551d2f79140dbd8d67e599a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815040d42673c5a7f92145a450cd3c90c", "guid": "bfdfe7dc352907fc980b868725387e98a649ea23634089a747843013ffe4d5a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315d23a399935bc55c10643306a521c0", "guid": "bfdfe7dc352907fc980b868725387e98d2364d817017cddee7a902d666f1de26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f00d7b291bc6e46e10ec1d85517db1", "guid": "bfdfe7dc352907fc980b868725387e9870026aaba1fe5115140c07e8f8248ef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec51551ad1cbc9b252c760761765de9d", "guid": "bfdfe7dc352907fc980b868725387e98f08e57fbbb1f769cd130fcd320d41664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a32be3ca1fe9344c8c432b4d7d87a093", "guid": "bfdfe7dc352907fc980b868725387e98d22cc9ff9d261d21ebb4d55204db9b0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8c6d6287e1ee016796c9a1878a165a", "guid": "bfdfe7dc352907fc980b868725387e98f51312b0855796b06f1c67ccc9466141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc48cbc06720748327f290c147e8a0e", "guid": "bfdfe7dc352907fc980b868725387e98d9659c7a7d6affd79dfccb64782d92c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3d5a892a976933a6daf82ef34c7ac8", "guid": "bfdfe7dc352907fc980b868725387e98cc853b0d7b7005f348469f65b4a3383b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a11cbe453ff24659163a9dd728f26a9c", "guid": "bfdfe7dc352907fc980b868725387e98974ffd90bd52a2d53e000bd839677df4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0b1d874bdb0d0f980fc4b23999ed70", "guid": "bfdfe7dc352907fc980b868725387e98b56f8ee1c89fcbb69c05796704b4f633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8d285394283e2ee35198742801c49c", "guid": "bfdfe7dc352907fc980b868725387e9802c44c5e3a574047d3c160eb6af263b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6461d24fcce2aee1f79d8a12ca306b", "guid": "bfdfe7dc352907fc980b868725387e9859ba9796cc81ccf9143ea685dfbe07ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981714753256b16eebf507ad54a19fb8de", "guid": "bfdfe7dc352907fc980b868725387e9801cc1a95e27586a34b38bf200674ecc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85adbc3b9457fe4ec9f9b61ddf0532d", "guid": "bfdfe7dc352907fc980b868725387e982a62e1bf41e8a4a275c889a050f4fc1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d0118185af03a8d9c3a06c0ac32f79", "guid": "bfdfe7dc352907fc980b868725387e988329a8c4442cb4b9bbe9da5dafd9c3bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7029ab5394d36d143c9b44dfa561c61", "guid": "bfdfe7dc352907fc980b868725387e9869f93149b7b91a9f88894c0771c07fff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd38048fe06760815218df9416618e0", "guid": "bfdfe7dc352907fc980b868725387e9889dc9844fa780a6d5a96cc7cd18e31a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960b9919e083b0e598f188270135a944", "guid": "bfdfe7dc352907fc980b868725387e9823d0f5415931864bc9b6e0b4c226909e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8f52f9846434a664a9f4bbe4350350", "guid": "bfdfe7dc352907fc980b868725387e983bd2fdc3cd52f355d461b90cb1300ab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986167ed39f8d24e918c101f877b27e454", "guid": "bfdfe7dc352907fc980b868725387e9866bdf6bd2a78ce30c1d2612570ea3d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4176326e2c5bfe3a0652061806dfbaf", "guid": "bfdfe7dc352907fc980b868725387e98b2a856aa590763c95dee63b9e4748002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b86c58292e3c39d37ada95769458fa", "guid": "bfdfe7dc352907fc980b868725387e98ae2cda4df7fefcda3688d664659bfce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985166c8fd51743fb34613556ce100f1c2", "guid": "bfdfe7dc352907fc980b868725387e98307f15ed079ffeb8c420cea5b777fca6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d6a37459e39b90cafd44a8ed9a2c48", "guid": "bfdfe7dc352907fc980b868725387e9868205d1a91da0cd5a695b741406f0dfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6349c4409057b1a89b341704f654d8d", "guid": "bfdfe7dc352907fc980b868725387e980ca0064944fac5e3ec1904a0e7b5543b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844b440a284667ed03874b9293948bc77", "guid": "bfdfe7dc352907fc980b868725387e98f4e4875ec2f3266a1336d85f5b008989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899b11de378a658c8b5eeb0f8dd5cacb5", "guid": "bfdfe7dc352907fc980b868725387e98e7bbf4ddc72cc80e5978740c448b5f4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd7b5c2c81fbc1f47b52f279a6344322", "guid": "bfdfe7dc352907fc980b868725387e98c91702a8b293795b71e93bad17153542"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2320a1f082a78fcf73b4300aa1b7376", "guid": "bfdfe7dc352907fc980b868725387e98750035ed8d577c93b2bb086cd61b13ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75fdebbb4cc92f303405265ab724ebf", "guid": "bfdfe7dc352907fc980b868725387e98f73601c0baa252f17395b338187b14dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22861f724bd65ab436c5baaf044d52c", "guid": "bfdfe7dc352907fc980b868725387e98ac8bc81f1aa70c188002b72077972c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1538ef0c5d7113154636e9c4090d790", "guid": "bfdfe7dc352907fc980b868725387e9894b4985a86c351329d883e5980b24b50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de502b39be9a42044a593cb8ee563488", "guid": "bfdfe7dc352907fc980b868725387e98fdb81ccf80538c846c076869bbc8dfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41003fd5fa5dfb3ad7bef0f85e72a06", "guid": "bfdfe7dc352907fc980b868725387e98f8774e403b1aa888d873446916db1518"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981042df652291050fdcc6a5337cd87d87", "guid": "bfdfe7dc352907fc980b868725387e988dd97d90d164dfc8e28cada27be1a9af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855790ac0ecd822cd6330c7e3ad90cbf7", "guid": "bfdfe7dc352907fc980b868725387e9857a3533ab2eb7ca777ddb50289801ceb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e764f83551191c813205b7afbe73246", "guid": "bfdfe7dc352907fc980b868725387e986dabfc87256e71cb3117fa443fa73a80"}], "guid": "bfdfe7dc352907fc980b868725387e98dcc8efee0c9db0ec500208c2872158e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988db67991d7442cea690f610776e6af2f", "guid": "bfdfe7dc352907fc980b868725387e98b20ecb1be1bc1ba10ff8a48aaaba79e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984441c4e89a88695a45490f1e2b24da70", "guid": "bfdfe7dc352907fc980b868725387e98229efd75752431cd0f9798d5734d71a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891485537a34ca717b474cd59b98062fa", "guid": "bfdfe7dc352907fc980b868725387e98cb4761749ca75753e935bc08c79bc8c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a005fdf2bb094de49bbeaa54b6b6b67", "guid": "bfdfe7dc352907fc980b868725387e9892202e0d526b4201b0260298c1512be3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff18a6390127a2f6ba4db4cb5166cdb9", "guid": "bfdfe7dc352907fc980b868725387e98083f77861ea3baa307ba20790b51145f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d592f22501effac4985a5e8d6cb33ed1", "guid": "bfdfe7dc352907fc980b868725387e98e57a4819ad8a8fe9b8abd81c10592bb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e97208c097274ea6763ad52dea4c21", "guid": "bfdfe7dc352907fc980b868725387e984f15b433b6b0ac1b3d29394fd1158efb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b27d1136851e40ae495004057a3f81", "guid": "bfdfe7dc352907fc980b868725387e981bb787d8e79704f45b60e87149e8dacc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ce7e16e42dc0bd191851660bdd2119", "guid": "bfdfe7dc352907fc980b868725387e984f6ca6d4cfcd1495fa3237a8daf4cf2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed743486cb8c30d5846f3e8cb0911e83", "guid": "bfdfe7dc352907fc980b868725387e986619e3978bbe9fd0344b1577217a6061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ccb425b55f60f73898eb31c011b7c72", "guid": "bfdfe7dc352907fc980b868725387e98b7863cb010b9a7e9c43f87cd1c9c2dc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f52ee4ed76f3d6fb404df66dbcfc685", "guid": "bfdfe7dc352907fc980b868725387e985a8197e4dcefdb863973e5ce5f1cd48e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac5721044876c6e1912242fa8a23e09", "guid": "bfdfe7dc352907fc980b868725387e983575c9e4b587a1633ca9c865a9e4838e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6bf3e4839c70ed16532a08c21d0434", "guid": "bfdfe7dc352907fc980b868725387e987e18310be02d3774129e981eea9de0cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e1995b29a8d49fbda3b915f79e77ad", "guid": "bfdfe7dc352907fc980b868725387e98ac443e212912946831c7bfc61e30d507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1eb7d9f0adafd67d8446816c5f6e335", "guid": "bfdfe7dc352907fc980b868725387e98f8fda3e7b74a3cb5b41f9a1b9efc7942"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2da0583f95fee20d639a1c7aefe932", "guid": "bfdfe7dc352907fc980b868725387e985f195ca9fa792b501a4750d2724178b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e596c10517d37af2d80cf75c98673ceb", "guid": "bfdfe7dc352907fc980b868725387e9857175fbedc0b447e03a2227e878a00ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99f4e9d48ab28b39797c7e3825185ba", "guid": "bfdfe7dc352907fc980b868725387e98e20abba607e1d35b2ebda5489bad9420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cae4abcf5dcfc6b89d1f0fda6554f3", "guid": "bfdfe7dc352907fc980b868725387e98bdbce704a0257eda1bfe9589e632be1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad349a8cb41f2eeca68c848a88fc990c", "guid": "bfdfe7dc352907fc980b868725387e98ef0dbfb07a847a72b39c738da1258704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d571810f4850f2e1f916f688b176aa", "guid": "bfdfe7dc352907fc980b868725387e98ecffcf1dd3563984920fb08ce4fcd5e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce9f415d2d6a945d038031297f7e9bb", "guid": "bfdfe7dc352907fc980b868725387e9859233a716c29485da88a346754e71bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1655eb82129be9b8cf5bbd2d967d57", "guid": "bfdfe7dc352907fc980b868725387e98e4362e24e7e0037a50c9d4a221597901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab989e4db1cae56c881d572b5711a80", "guid": "bfdfe7dc352907fc980b868725387e9881875a4b127e58e27d0a1722f84cef34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a40654ec38ac34a5229a61374034a2a", "guid": "bfdfe7dc352907fc980b868725387e983c85b7a4241711ad5784396c938eaab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ffbdb6d6d4b476a8ee0f38cddf781d", "guid": "bfdfe7dc352907fc980b868725387e98da6bd4d0eea7a8575e4f6bf79c13e057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869438947f97f1c3955c03543e8bfae83", "guid": "bfdfe7dc352907fc980b868725387e981687de710efd5333dccb93e5bbe6a687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7805be23a2c08e9bc61d53d0e51ab4", "guid": "bfdfe7dc352907fc980b868725387e98e38f564c4e6a12517e5bd968621fd230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a40ada40d61689e6b263ef8ac0825b2a", "guid": "bfdfe7dc352907fc980b868725387e9811ad54b295fa080cd71e0333fe7fad91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e37fc1db8405e1532bd9de953b95bd", "guid": "bfdfe7dc352907fc980b868725387e983a1445b5f8d87f065f09a0b80400bde3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d73a03c3177796694539dcdefe1e52", "guid": "bfdfe7dc352907fc980b868725387e98472be0759f01f64a7b7fc8d83daca44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895cc98339a8ddf26373e35bc0cdf77f3", "guid": "bfdfe7dc352907fc980b868725387e982917ba90debd649d58fe6c0f66d23763"}], "guid": "bfdfe7dc352907fc980b868725387e985d8ec1f1fe5e43a2043dbb26e0e338c3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98314e10bf66b1655bec3add0b3c81e2cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e98e18d25f9f74c66cb97805c2f07a4b7a5"}], "guid": "bfdfe7dc352907fc980b868725387e98afd5463b93e3bddff69e2c26d310d149", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d51e63d8156ecd5345626fe566abe49d", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9823e5f1c96c40f2594e4a08b4877d5e4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}