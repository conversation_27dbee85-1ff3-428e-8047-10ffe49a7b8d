// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_property_share_count_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdatePropertyShareCountModel _$UpdatePropertyShareCountModelFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyShareCountModel(
      ids: (json['ids'] as List<dynamic>?)?.map((e) => e as String).toList(),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
    );

Map<String, dynamic> _$UpdatePropertyShareCountModelToJson(
        UpdatePropertyShareCountModel instance) =>
    <String, dynamic>{
      'ids': instance.ids,
      'lastModifiedBy': instance.lastModifiedBy,
      'contactType': _$ContactTypeEnumMap[instance.contactType],
    };

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};
