// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_user_designation_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GetUserDesignationModelAdapter
    extends TypeAdapter<GetUserDesignationModel> {
  @override
  final int typeId = 113;

  @override
  GetUserDesignationModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GetUserDesignationModel(
      id: fields[0] as String?,
      designation: fields[3] as UserDesignationModel?,
      firstName: fields[1] as String?,
      lastName: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, GetUserDesignationModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.firstName)
      ..writeByte(2)
      ..write(obj.lastName)
      ..writeByte(3)
      ..write(obj.designation);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetUserDesignationModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserDesignationModelAdapter extends TypeAdapter<UserDesignationModel> {
  @override
  final int typeId = 114;

  @override
  UserDesignationModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserDesignationModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, UserDesignationModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDesignationModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetUserDesignationModel _$GetUserDesignationModelFromJson(
        Map<String, dynamic> json) =>
    GetUserDesignationModel(
      id: json['id'] as String?,
      designation: json['designation'] == null
          ? null
          : UserDesignationModel.fromJson(
              json['designation'] as Map<String, dynamic>),
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$GetUserDesignationModelToJson(
        GetUserDesignationModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.designation case final value?) 'designation': value,
    };

UserDesignationModel _$UserDesignationModelFromJson(
        Map<String, dynamic> json) =>
    UserDesignationModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$UserDesignationModelToJson(
        UserDesignationModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
    };
