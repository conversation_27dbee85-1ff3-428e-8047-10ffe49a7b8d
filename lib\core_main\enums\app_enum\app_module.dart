enum AppModule {
  dashboard("Dashboard"),
  lead("Leads"),
  task("Todos"),
  property("Properties"),
  integration("Integration"),
  global("Global"),
  profile("Profile"),
  tenants("Tenants"),
  users("Users"),
  userRoles("UserRoles"),
  userLocation("UserLocation"),
  roles("Roles"),
  roleClaims("RoleClaims"),
  masterData("MasterData"),
  teams("Teams"),
  userProfile("UserProfile"),
  project("Projects"),
  prospect("Prospects"),
  invoice("Invoice"),
  attendance("Attendance"),
  listingIntegration("ListingIntegration");

  final String description;

  const AppModule(this.description);
}
