// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_tag_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadTagModel _$LeadTagModelFromJson(Map<String, dynamic> json) => LeadTagModel(
      isHighlighted: json['isHighlighted'] as bool?,
      isEscalated: json['isEscalated'] as bool?,
      isAboutToConvert: json['isAboutToConvert'] as bool?,
      isHotLead: json['isHotLead'] as bool?,
      isIntegrationLead: json['isIntegrationLead'] as bool?,
      isWarmLead: json['isWarmLead'] as bool?,
      isColdLead: json['isColdLead'] as bool?,
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$LeadTagModelToJson(LeadTagModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'isHighlighted': instance.isHighlighted,
      'isEscalated': instance.isEscalated,
      'isAboutToConvert': instance.isAboutToConvert,
      'isHotLead': instance.isHotLead,
      'isIntegrationLead': instance.isIntegrationLead,
      'isWarmLead': instance.isWarmLead,
      'isColdLead': instance.isColdLead,
    };
