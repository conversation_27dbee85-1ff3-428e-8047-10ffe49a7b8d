{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985108752ed0ac4b9426d93aa584f4984c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986a7b061a1739311cdffae0fdfd3a4dca", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987aee8116d097a9978e31285a0f44a48c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9825bff0d11de617e1da9b60ed572485e0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987aee8116d097a9978e31285a0f44a48c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984b10bb8833231624903c466868e01c56", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985069a05e6c0211856d3c96f4e061ac2e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985f2c5eeba69a8a37e643fc2d4f981520", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f42fca67cdf364507f07a31cee01310c", "guid": "bfdfe7dc352907fc980b868725387e9874e72e4110355a4c9a1a6c354c85591d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a851b6245afbccdd37535c4c317a44a", "guid": "bfdfe7dc352907fc980b868725387e98be4786d39a56d24422083c248973bb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810aa2e6bae60562c1a2845d8425bd6ac", "guid": "bfdfe7dc352907fc980b868725387e98d789626eb41680503c54c1834498f6a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6113f064ea02364a9ed18336acf05d1", "guid": "bfdfe7dc352907fc980b868725387e98830491f07d57f6189d1467f1290d66f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45ae3ec1ebefb77d2986cc346135037", "guid": "bfdfe7dc352907fc980b868725387e98a4e7c2239dbe09468d4adf6e5e6420aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df10686c86d1cbe7e091b948a977a803", "guid": "bfdfe7dc352907fc980b868725387e98db0186fd32df47e40a53f1ef26d4f282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252f4491a077e0777159a559357d5563", "guid": "bfdfe7dc352907fc980b868725387e981b228846a4d10fd4285cd94229e9aad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6eefcd00ee0a6ef3e0b5ba36ff8ab48", "guid": "bfdfe7dc352907fc980b868725387e982cc48d11182f46879479db26eced8e94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a0a8c5bb2fefb1613194da267d7e7c", "guid": "bfdfe7dc352907fc980b868725387e986d733ae9c4d1b4fef9a2d1091d59d108"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da9eda5e2a9b492a3b9309f49afb2d5", "guid": "bfdfe7dc352907fc980b868725387e98cd732196782ecdee043b257fff71b3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f279c84cdafa90e78adb68b5b94bd9b", "guid": "bfdfe7dc352907fc980b868725387e98180d0ab27cfdc89eb7c568437944101a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d729c1cd017334923f9cc0d5d1a4af9", "guid": "bfdfe7dc352907fc980b868725387e982134b361f43e4f9c30b39b351bd2d7a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dd5d1cc975409fa15aa96968037cc33", "guid": "bfdfe7dc352907fc980b868725387e98ccace772ac54e251c21ea3a22292bb10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e4fb6948ca5c9637e2713f3b07789ec", "guid": "bfdfe7dc352907fc980b868725387e983d253c843b39428f3323a93543deb3fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f707b2c4889249bab03461ad3a19dbff", "guid": "bfdfe7dc352907fc980b868725387e98f8f7c10c402823abac32560c5ec58505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ae80c4884e50438b4620e6bb8f499d", "guid": "bfdfe7dc352907fc980b868725387e98a7a639cadf63eee228ae97ada05aa19d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd517fca97766682ad75827789f33ea3", "guid": "bfdfe7dc352907fc980b868725387e98cc9f062b062cdceeae10a69384298fa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980927ff78d41b7e0a7dc0cbaf1f0b3b0c", "guid": "bfdfe7dc352907fc980b868725387e98210ebd146036bc1ba61625f4fc87d210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65c6bd0e432720a9315776deb023d53", "guid": "bfdfe7dc352907fc980b868725387e984f1684a7f7039a2f437cfa01dcab6570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86482c7d95e709e97bf778451f22626", "guid": "bfdfe7dc352907fc980b868725387e98a865f713042a624d2da9ebda8406a2b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d3b665c94f742c158e34015009c45b0", "guid": "bfdfe7dc352907fc980b868725387e98a23432d59dd908e0b810b5eaa3946acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c2e0e323973cacd90ae55ed4bb3cb0", "guid": "bfdfe7dc352907fc980b868725387e9810ba37c4266b48bf2686628b5f0ba6d9"}], "guid": "bfdfe7dc352907fc980b868725387e98e35064b3faa308e42008549979a84b58", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}