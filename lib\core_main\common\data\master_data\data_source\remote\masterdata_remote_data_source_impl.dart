import 'package:leadrat/core_main/common/data/master_data/data_source/remote/masterdata_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

import '../../models/master_user_services_type_model.dart';

class MasterDataRemoteDataSourceImpl extends LeadratRestService implements MasterDataRemoteDataSource {
  @override
  Future<List<MasterAreaUnitsModel?>?> getAreaUnits({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getAreaUnits, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterAreaUnitsModel?, String>>(
      restRequest,
      (json) => PagedResponse<MasterAreaUnitsModel?, String>.fromJson(json, (data) => fromJsonObject(data, MasterAreaUnitsModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<List<MasterProjectTypeModel?>?> getProjectTypes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getProjectType, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterProjectTypeModel?, String>>(
      restRequest,
      (json) => PagedResponse<MasterProjectTypeModel?, String>.fromJson(json, (data) => fromJsonObject(data, MasterProjectTypeModel.fromJson), (json) => json as String),
    );
    return response.items;
  }

  @override
  Future<Map<EntityTypeEnum, DateTime>?> getModifiedDates({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getModifiedDates, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<Map<String, String>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, String>>.fromJson(json, (data) => Map<String, String>.from(data)),
    );

    return mapResponseToEnum<DateTime, EntityTypeEnum>(
      response.data,
      EntityTypeEnum.fromString,
      (value) => DateTime.parse(value as String),
    );
  }

  @override
  Future<List<MasterAssociatedBankModel>?> getAssociatedBank({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getAssociatedBank, timeout: timeout);

    final response = await executeRequestAsync<ResponseWrapper<List<MasterAssociatedBankModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<MasterAssociatedBankModel>?>.fromJson(
        json,
        (data) => fromJsonList<MasterAssociatedBankModel>(data, MasterAssociatedBankModel.fromJson),
      ),
    );

    return response.data;
  }

  @override
  Future<List<MasterLeadSourceModel>?> getLeadSource({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(SourceRestResource.source, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<List<MasterLeadSourceModel>?>>(
        restRequest,
        (json) => ResponseWrapper<List<MasterLeadSourceModel>?>.fromJson(
              json,
              (data) => fromJsonList<MasterLeadSourceModel>(data, MasterLeadSourceModel.fromJson),
            ));
    return response.data;
  }

  @override
  Future<List<MasterLeadStatusModel>?> getLeadStatuses({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getLeadStatuses, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterLeadStatusModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterLeadStatusModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterLeadStatusModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<Map<String, List<MasterProjectAmenititesModel>>?> getProjectAmenitites({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getProjectAmenities, timeout: timeout);

    final response = await executeRequestAsync<ResponseWrapper<Map<String, List<MasterProjectAmenititesModel>>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, List<MasterProjectAmenititesModel>>>.fromJson(
        json,
        (data) => data.map<String, List<MasterProjectAmenititesModel>>(
          (key, value) => MapEntry(
            key as String, // Cast the key to String
            (value as List<dynamic>).map((item) => MasterProjectAmenititesModel.fromJson(item as Map<String, dynamic>)).toList(),
          ),
        ),
      ),
    );

    return response.data;
  }

  @override
  Future<List<MasterProjectAttributeModel>?> getProjectAttributes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getProjectAttributes, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterProjectAttributeModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterProjectAttributeModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterProjectAttributeModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyAmenitites({bool isPropertyListingEnabled = false, Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(isPropertyListingEnabled ? MasterDataRestResources.getPropertyListingAmenities : MasterDataRestResources.getPropertyAmenities, timeout: timeout);

    final response = await executeRequestAsync<ResponseWrapper<Map<String, List<MasterPropertyAmenitiesModel>>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, List<MasterPropertyAmenitiesModel>>>.fromJson(
        json,
        (data) => data.map<String, List<MasterPropertyAmenitiesModel>>(
          (key, value) => MapEntry(
            key as String,
            (value as List<dynamic>).map((item) => MasterPropertyAmenitiesModel.fromJson(item as Map<String, dynamic>)).toList(),
          ),
        ),
      ),
    );

    return response.data;
  }

  @override
  Future<List<MasterPropertyAttributesModel>?> getPropertyAttributes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getPropertyAttributes, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterPropertyAttributesModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterPropertyAttributesModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterPropertyAttributesModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<List<MasterPropertyTypeModel>?> getPropertyTypes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getPropertyTypes, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterPropertyTypeModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterPropertyTypeModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterPropertyTypeModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<List<MasterPropertyTypeModel>?> getListingPropertyTypes({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getListingPropertyTypes, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterPropertyTypeModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterPropertyTypeModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterPropertyTypeModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<List<MasterUserServicesModel>?> getMasterUserServices({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(MasterDataRestResources.getUserServices, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterUserServicesModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterUserServicesModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterUserServicesModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<List<MasterCustomStatusModel>?> getCustomStatus({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(StatusRestResources.getStatus, timeout: timeout);
    final response = await executeRequestAsync<PagedResponse<MasterCustomStatusModel, String>>(
      restRequest,
      (json) => PagedResponse<MasterCustomStatusModel, String>.fromJson(json, (item) => fromJsonObject(item, MasterCustomStatusModel.fromJson), (data) => data as String),
    );
    return response.items;
  }

  @override
  Future<List<String>?> getAgencyNames({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(LeadRestResources.agencyNames, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getAllLeadAddress({Duration timeout = const Duration(seconds: 60)}) async {
    final restRequest = createGetRequest(LeadRestResources.leadAddresses, timeout: timeout);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }
}
