import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/features/lead/domain/entities/lead_status_custom_entity.dart';

part 'master_custom_status_model.g.dart';

@JsonSerializable()
@HiveType(typeId: HiveModelConstants.masterCustomStatusModelTypeId)
class MasterCustomStatusModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final DateTime? createdOn;
  @HiveField(3)
  final String? createdBy;
  @HiveField(4)
  final DateTime? lastModifiedOn;
  @HiveField(5)
  final String? lastModifiedBy;
  @HiveField(6)
  final List<CustomFieldModel>? customFields;
  @HiveField(7)
  final bool? isLrbStatus;
  @HiveField(8)
  final String? baseId;
  @HiveField(9)
  final int? level;
  @HiveField(10)
  final String? status;
  @HiveField(11)
  final int? orderRank;
  @HiveField(12)
  final String? displayName;
  @HiveField(13)
  final String? actionName;
  @HiveField(14)
  final String? masterLeadStatusId;
  @HiveField(15)
  final bool? idDefaultChild;
  @HiveField(16)
  final bool? shouldUseForBooking;
  @HiveField(17)
  final bool? shouldUseForBookingCancel;
  @HiveField(18)
  final bool? shouldBeHidden;
  @HiveField(19)
  final bool? shouldOpenAppointmentPage;
  @HiveField(20)
  final bool? shouldUseForMeeting;
  @HiveField(21)
  final List<MasterCustomStatusModel>? childTypes;

  MasterCustomStatusModel({
    this.id,
    this.isDeleted,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.customFields,
    this.isLrbStatus,
    this.baseId,
    this.level,
    this.status,
    this.orderRank,
    this.displayName,
    this.actionName,
    this.masterLeadStatusId,
    this.idDefaultChild,
    this.shouldUseForBooking,
    this.shouldUseForBookingCancel,
    this.shouldBeHidden,
    this.shouldOpenAppointmentPage,
    this.shouldUseForMeeting,
    this.childTypes,
  });

  factory MasterCustomStatusModel.fromJson(Map<String, dynamic> json) => _$MasterCustomStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterCustomStatusModelToJson(this);

  LeadStatusCustomEntity toEntity() {
    return LeadStatusCustomEntity(
      level: level,
      status: status,
      displayName: displayName,
      actionName: actionName,
      childType: childTypes?.firstOrNull?.toEntity(),
      baseId: baseId,
      shouldUseForBooking: shouldUseForBooking,
      shouldUseForBookingCancel: shouldUseForBookingCancel,
      shouldOpenAppointmentPage: shouldOpenAppointmentPage,
      shouldUseForMeeting: shouldUseForMeeting,
    );
  }
}

@JsonSerializable()
@HiveType(typeId: HiveModelConstants.customFieldModelTypeId)
class CustomFieldModel {
  @HiveField(0)
  final bool? isRequired;
  @HiveField(1)
  final String? value;
  @HiveField(2)
  final List<String>? validators;
  @HiveField(3)
  final ViewFieldModel? field;

  CustomFieldModel({
    this.isRequired,
    this.value,
    this.validators,
    this.field,
  });

  factory CustomFieldModel.fromJson(Map<String, dynamic> json) => _$CustomFieldModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomFieldModelToJson(this);
}

@JsonSerializable()
@HiveType(typeId: HiveModelConstants.viewFieldModelTypeId)
class ViewFieldModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final int? orderRank;
  @HiveField(3)
  final String? module;
  @HiveField(4)
  final String? notes;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? createdOn;
  @HiveField(7)
  final String? createdBy;
  @HiveField(8)
  final String? lastModifiedBy;

  ViewFieldModel({
    this.id,
    this.name,
    this.orderRank,
    this.module,
    this.notes,
    this.lastModifiedOn,
    this.createdOn,
    this.createdBy,
    this.lastModifiedBy,
  });

  factory ViewFieldModel.fromJson(Map<String, dynamic> json) => _$ViewFieldModelFromJson(json);

  Map<String, dynamic> toJson() => _$ViewFieldModelToJson(this);
}
