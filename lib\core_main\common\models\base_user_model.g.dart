// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BaseUserModelAdapter extends TypeAdapter<BaseUserModel> {
  @override
  final int typeId = 106;

  @override
  BaseUserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BaseUserModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      contactNo: fields[2] as String?,
      createdBy: fields[4] as String?,
      createdOn: fields[3] as DateTime?,
      lastModifiedOn: fields[5] as DateTime?,
      lastModifiedBy: fields[6] as String?,
      firstName: fields[7] as String?,
      lastName: fields[8] as String?,
      userName: fields[9] as String?,
      imageUrl: fields[10] as String?,
      isActive: fields[11] as bool?,
      isMFAEnabled: fields[12] as bool?,
      licenseNo: fields[13] as String?,
      isGeoFenceActive: fields[14] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, BaseUserModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.contactNo)
      ..writeByte(3)
      ..write(obj.createdOn)
      ..writeByte(4)
      ..write(obj.createdBy)
      ..writeByte(5)
      ..write(obj.lastModifiedOn)
      ..writeByte(6)
      ..write(obj.lastModifiedBy)
      ..writeByte(7)
      ..write(obj.firstName)
      ..writeByte(8)
      ..write(obj.lastName)
      ..writeByte(9)
      ..write(obj.userName)
      ..writeByte(10)
      ..write(obj.imageUrl)
      ..writeByte(11)
      ..write(obj.isActive)
      ..writeByte(12)
      ..write(obj.isMFAEnabled)
      ..writeByte(13)
      ..write(obj.licenseNo)
      ..writeByte(14)
      ..write(obj.isGeoFenceActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BaseUserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseUserModel _$BaseUserModelFromJson(Map<String, dynamic> json) =>
    BaseUserModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      userName: json['userName'] as String?,
      imageUrl: json['imageUrl'] as String?,
      isActive: json['isActive'] as bool?,
      isMFAEnabled: json['isMFAEnabled'] as bool?,
      licenseNo: json['licenseNo'] as String?,
      isGeoFenceActive: json['isGeoFenceActive'] as bool?,
    );

Map<String, dynamic> _$BaseUserModelToJson(BaseUserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'contactNo': instance.contactNo,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'userName': instance.userName,
      'imageUrl': instance.imageUrl,
      'isActive': instance.isActive,
      'isMFAEnabled': instance.isMFAEnabled,
      'licenseNo': instance.licenseNo,
      'isGeoFenceActive': instance.isGeoFenceActive,
    };
