import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';

import '../../models/get_all_users_model.dart';
import '../../models/user_details_model.dart';

abstract class UsersLocalDataSource {
  List<GetAllUsersModel?>? getAllUsers();

  Future<void> saveAllUsers(List<GetAllUsersModel?>? items);

  UserDetailsModel? getUser();

  Future<void> saveUser(UserDetailsModel? item);

  Future<void> saveAdminsAndReportee(List<GetAllUsersModel?>? items);

  Future<void> saveAllReportees(List<GetAllUsersModel?>? items);

  List<GetAllUsersModel?>? getAdminsAndReportee();

  List<GetAllUsersModel?>? getAllReportees();

  Map<String, List<GetUserDesignationModel>>? getUserDesignations();

  Future<void> saveUserDesignations(Map<String, List<GetUserDesignationModel>>? items);
}
