import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';

part 'get_all_users_model.g.dart';

@HiveType(typeId: HiveModelConstants.getAllUsersTypeId)
@JsonSerializable()
class GetAllUsersModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? firstName;
  @HiveField(2)
  final String? lastName;
  @HiveField(3)
  final String? userName;
  @HiveField(4)
  final String? imageUrl;
  @HiveField(5)
  final bool? isActive;
  @HiveField(6)
  final bool? isMFAEnabled;
  @HiveField(7)
  final String? licenseNo;
  @HiveField(8)
  final bool? isGeoFenceActive;

  String get fullName => "${firstName ?? ""} ${lastName ?? ""}";

  GetAllUsersModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.userName,
    required this.imageUrl,
    required this.isActive,
    required this.isMFAEnabled,
    this.licenseNo,
    this.isGeoFenceActive,
  });

  factory GetAllUsersModel.fromJson(Map<String, dynamic> json) => _$GetAllUsersModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetAllUsersModelToJson(this);

  BaseUserModel toBaseUserModel() {
    return BaseUserModel(
      id: id,
      firstName: firstName,
      lastName: lastName,
      userName: userName,
      imageUrl: imageUrl,
      isActive: isActive,
      isMFAEnabled: isMFAEnabled,
      licenseNo: licenseNo,
      isGeoFenceActive: isGeoFenceActive,
    );
  }
}
