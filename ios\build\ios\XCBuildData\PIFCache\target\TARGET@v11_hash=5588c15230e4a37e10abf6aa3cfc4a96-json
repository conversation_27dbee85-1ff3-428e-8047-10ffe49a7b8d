{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e77fe72f1be5354d628923aa42f11982", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985260f8519d8cbc0a6759fccc9c49bab7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831e403d13c225b739c0ea5e2ce36a8fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c4a653cb67f9b4de6452f67c26471fc3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831e403d13c225b739c0ea5e2ce36a8fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d099f7a8bd46e20eda903323c7178a0e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b92c1db8ac32abe975e6976e0b31119", "guid": "bfdfe7dc352907fc980b868725387e98e83e9b7c6cd3616fb73a9c47ef06b573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667934a4c6301fb64c202600dfdef344", "guid": "bfdfe7dc352907fc980b868725387e984f66c6a4c5dac810196b035ca5377fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8c38faa76c6fd1a294485c99c34e4b", "guid": "bfdfe7dc352907fc980b868725387e98718343353d2f3096d728d1d4edb4a7ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98457f1485cca580a32cac62afdff3c8a8", "guid": "bfdfe7dc352907fc980b868725387e981195b0a6f446df64b4160806d0cd78d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bccfdbcab6e1fb1a42490067d14d5f9a", "guid": "bfdfe7dc352907fc980b868725387e98192121b0e694673cb67c32d587abcb27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb16ca770dfb9ce3a616923e5ae461b", "guid": "bfdfe7dc352907fc980b868725387e98109c795aae4339e28fbd34c4928e318a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea059dd573709dcc210de509c5f465a4", "guid": "bfdfe7dc352907fc980b868725387e98506e814b00a34227d8b1aaddaedd4f19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558f27c2fc782b0cab30c42a9012983b", "guid": "bfdfe7dc352907fc980b868725387e981ca0b5eab2e8c7e278c8894868413772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3cdd34c036432215a336d9af615738", "guid": "bfdfe7dc352907fc980b868725387e98c247353cee3b4d6799edd810b12dbd29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f31ce89d36196afa2ea4efc07bc22cb", "guid": "bfdfe7dc352907fc980b868725387e984cc569cc61abae6bf292dc151906bfc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee47d7af400ca2d0976d0c0f4a14e57", "guid": "bfdfe7dc352907fc980b868725387e9899e9760e12ed6c92b0fe6e0d8aeee5b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2860f112a3098a5138e05ecf90e041", "guid": "bfdfe7dc352907fc980b868725387e98e91dd5c49577bf2c36c83e559bec9335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e519132f11c1f0c23c680cc8fd46819", "guid": "bfdfe7dc352907fc980b868725387e9870581cbec392a5054414abdebffa99e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec364a3b82c10fa7d544b93a11ebd4c", "guid": "bfdfe7dc352907fc980b868725387e9806ab98f6e2c069925368ca79c1b483f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e5272617f26603dd1d60f677e015c6", "guid": "bfdfe7dc352907fc980b868725387e980eadf777607244b3d481b3a16daf4729"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3bd7d2eb3a53c0a7fa303db21b20f3d", "guid": "bfdfe7dc352907fc980b868725387e986b64385904db1ef389e021f44dc2a654", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b04a435db05b27340cee878b6a49075", "guid": "bfdfe7dc352907fc980b868725387e9837c9f64c178b94dbc055ce8204b96ef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6f485649b9f5d74dedf659465ec22e", "guid": "bfdfe7dc352907fc980b868725387e98428db7be449c46faf064d6123546f14b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d142aa951bb7f24741d0648807f6224", "guid": "bfdfe7dc352907fc980b868725387e98a5e285d2ee1db4eba4ecb0c9e5a8d8cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab968ed38cf838b2ffbff5d805124b5", "guid": "bfdfe7dc352907fc980b868725387e980b7e529b17cf163f5ceaf96d3f05e6d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98783bd012a0ae1715a4c0caefef1f7bd8", "guid": "bfdfe7dc352907fc980b868725387e9897c3228ee28c8a3f84265ed554e1a2ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a39eb5529685c9f01699ca78bd76739", "guid": "bfdfe7dc352907fc980b868725387e9875657be2b954316f4b9aedd6ce7aa853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9a0501d33a4222c028fe3b4a3d81e0", "guid": "bfdfe7dc352907fc980b868725387e988d2d28b064225f91c11d4122c225976e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e858388139ce76ab8374e76e2d223805", "guid": "bfdfe7dc352907fc980b868725387e98d51464aacfe492d31755450ca26705d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5bb43bbbb58703dc9956c299036de2", "guid": "bfdfe7dc352907fc980b868725387e98c349420d0a4c0b3af597640a688cdea8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7a6b9f291951f5e8bc89214b750851", "guid": "bfdfe7dc352907fc980b868725387e98f40dabc165edb7d4e4795acb5a458801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544d33a7683d7013a7ee5727254753b0", "guid": "bfdfe7dc352907fc980b868725387e984fa92f4e752edd133f25fc016d3ff9c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c7de717084cb1d76f2074ea08db8fb", "guid": "bfdfe7dc352907fc980b868725387e98de7c3284421c053be8e27bbc247e66e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07b265b2a5564466a9dc0a38b45f2e3", "guid": "bfdfe7dc352907fc980b868725387e98fc273ec3c18b09b5845c52c734ed03da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595070815d6fde9937953b98cbd62485", "guid": "bfdfe7dc352907fc980b868725387e98d5c9179b029511814f0c59a1a564483d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fced325eec624e0880443891ebbea1", "guid": "bfdfe7dc352907fc980b868725387e98876d50d32f2fc93922dd41022ca93d0a"}], "guid": "bfdfe7dc352907fc980b868725387e98f6c69bb6f45a1cef9aab31650184202b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838d61aab8ae4cc7acd9bbdbf9ef3b61c", "guid": "bfdfe7dc352907fc980b868725387e98e677f0ffb7a4f3fddcdbb777130b03ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf13e80ceed2512e079c8f246d8f63ca", "guid": "bfdfe7dc352907fc980b868725387e98c52510430844177dc0169e4b6bf96ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed9ac295e92f4a8ba367a936ef93af94", "guid": "bfdfe7dc352907fc980b868725387e98d7278d9f9a12a9de09e30893c616bb48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d09f4ef615d704ad74a34df43fb26ec", "guid": "bfdfe7dc352907fc980b868725387e9863b987035d17e4dc32680d58da7e0ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ac4fd11f09478367185d1d346db680", "guid": "bfdfe7dc352907fc980b868725387e982f6ec9cea286c2d06f2387781fc90cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a21b6fc7a8775176e824bbf6403721", "guid": "bfdfe7dc352907fc980b868725387e983738f4907a2223ab96d307633e93b9bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0cd11ef7e2c472ad54a2759fb220da0", "guid": "bfdfe7dc352907fc980b868725387e98f3b0678ff430288c48c2e7a5014fbf95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886a2c34df717e820e71c8c123f14997f", "guid": "bfdfe7dc352907fc980b868725387e986a1b3e462dd03e4c11a8cdef5d38d1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1c529ab2a0d395f195b6d63b889b53", "guid": "bfdfe7dc352907fc980b868725387e98ebd01ffd1348f82e182707ea9d9c81c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d21b265286fb44b9bca44fd6c2181c", "guid": "bfdfe7dc352907fc980b868725387e983f0b89a1d812c933b485dfcc40aa7828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae67ca1005fe3865dfb9ad3f3d994cb2", "guid": "bfdfe7dc352907fc980b868725387e980361d339a8a3cc37775bb05daf13b4e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2713aee289b679cfcce4e79d6c06a6", "guid": "bfdfe7dc352907fc980b868725387e98244c49fd5d75d089bf28b4e699bf79bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ed0ee8172eb897080a5e31e9a245840", "guid": "bfdfe7dc352907fc980b868725387e989b091454974ea0ac55a92081d0ea3c5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f32313cb91b24c5a361c4411ab3b58c", "guid": "bfdfe7dc352907fc980b868725387e9879d7a6bd67d464c75da6266b08f11986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2be361aba3180ff30ee1d0fc24503f4", "guid": "bfdfe7dc352907fc980b868725387e984fcf0c1933e63266166cb158a8351695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa0c5dfa1ef68bf0d6c213da5b1b554", "guid": "bfdfe7dc352907fc980b868725387e9828945039a98d5fd1bec1cb8c90d2ee54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82282897cd3c47262e1d34d17b4d9fa", "guid": "bfdfe7dc352907fc980b868725387e987ba5fe2139576d449bce219dfbe5df9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849962555c07f50d0c8ffa2be983e8f31", "guid": "bfdfe7dc352907fc980b868725387e98f6bdb1be85b7ed0de6bf5e0dc80c200b"}], "guid": "bfdfe7dc352907fc980b868725387e98a2704ea8b6ea017016b103a3b3b5f891", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98d176d2ca9b2baa7042dd9407090d53a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e983d8ac959bf34937fcd15d68d43fbc1dd"}], "guid": "bfdfe7dc352907fc980b868725387e98526be1f9cdd498eb9dbda277f46a6f8a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9868425de842a5f0aa0448c0ece6a5a02c", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9849b23ffd85e9beb475a01372fab63520", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}