// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'template_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TemplateAdapter extends TypeAdapter<Template> {
  @override
  final int typeId = 13;

  @override
  Template read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Template(
      title: fields[0] as String?,
      description: fields[1] as String?,
      header: fields[2] as String?,
      footer: fields[3] as String?,
      moduleName: fields[4] as ModuleName?,
    );
  }

  @override
  void write(BinaryWriter writer, Template obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.title)
      ..writeByte(1)
      ..write(obj.description)
      ..writeByte(2)
      ..write(obj.header)
      ..writeByte(3)
      ..write(obj.footer)
      ..writeByte(4)
      ..write(obj.moduleName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Template _$TemplateFromJson(Map<String, dynamic> json) => Template(
      title: json['title'] as String?,
      description: json['description'] as String?,
      header: json['header'] as String?,
      footer: json['footer'] as String?,
      moduleName: $enumDecodeNullable(_$ModuleNameEnumMap, json['moduleName']),
    );

Map<String, dynamic> _$TemplateToJson(Template instance) => <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'header': instance.header,
      'footer': instance.footer,
      'moduleName': _$ModuleNameEnumMap[instance.moduleName],
    };

const _$ModuleNameEnumMap = {
  ModuleName.lead: 0,
  ModuleName.todo: 1,
  ModuleName.integration: 2,
  ModuleName.user: 3,
  ModuleName.profile: 4,
  ModuleName.project: 5,
  ModuleName.property: 6,
  ModuleName.team: 7,
  ModuleName.email: 8,
  ModuleName.invoice: 9,
  ModuleName.unit: 10,
};
