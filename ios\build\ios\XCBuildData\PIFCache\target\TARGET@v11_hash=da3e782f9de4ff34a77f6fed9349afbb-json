{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824958cc077c90146b2ba8ee16f3e3560", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a57bbcca3c83d7d52823ea94c0aa3fd", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a57bbcca3c83d7d52823ea94c0aa3fd", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a10367ae63370b620e952deedf949504", "guid": "bfdfe7dc352907fc980b868725387e98fbbe543b71214f4509d8c92248a60d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487a668b4afc1372191a5a8ecedf7438", "guid": "bfdfe7dc352907fc980b868725387e98471ff3f9f1b2efe128b7c2d23d239746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867aca9039f69972d60e729d2bed172ae", "guid": "bfdfe7dc352907fc980b868725387e98b76c0130342994e0dd30f1d68cb8abfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f058d3246f7f44f6a3445914ed6b3f7", "guid": "bfdfe7dc352907fc980b868725387e98b026e65d04846ce716b6283013c9b796", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa5627a79834f673b7f4d882e6b8a5f", "guid": "bfdfe7dc352907fc980b868725387e985e0782c485d14f7513f47a5628a6b51b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca159a0c332c323b15b2be596367191a", "guid": "bfdfe7dc352907fc980b868725387e980dc402cd31c13f836903d7124fb9cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06df8917e425b1833a415f657a4c0fa", "guid": "bfdfe7dc352907fc980b868725387e989c0fc48a74ff41a0a9254c97ece8daaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c293b0b3e64127b23823eba01eadf486", "guid": "bfdfe7dc352907fc980b868725387e9861fa11d4d6cf54947eab5534b60f0d93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2a8a274b59d0da26d9403c4798545d", "guid": "bfdfe7dc352907fc980b868725387e985abce06578652c4d8dc3d24c702bd3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98cccbed73414413c1a46a8fce43e07", "guid": "bfdfe7dc352907fc980b868725387e98b8c7585471bcf10789da28bb2a96c405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a8f1811fc4d932ccf5875887263a6e", "guid": "bfdfe7dc352907fc980b868725387e98e293850950354b35a2786639c3e3939e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6be0b0c05309b1fcdb6e31171aa47c9", "guid": "bfdfe7dc352907fc980b868725387e980ef10060e448131985daa2036af5ae3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987005a60ebbe88a2d328804d3ccd5cd83", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982628200d4bfe7d778d2478099a0ff645", "guid": "bfdfe7dc352907fc980b868725387e98c1277031b6540d3602e8c79a5b369c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8826b9cfb4fee44569fe2e42e58d15e", "guid": "bfdfe7dc352907fc980b868725387e98b129ca47461bd3877af22b5d56ec22fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5490c023b5475e06ab2c521f9e5c16d", "guid": "bfdfe7dc352907fc980b868725387e98808ab512e47ea5c54aad6b2807ee7bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b2e535bf19194cac150a6100af4ec1", "guid": "bfdfe7dc352907fc980b868725387e98c191f1ff744a395685e3770c58c824f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d1843f0edccf1c157c743a178075d84", "guid": "bfdfe7dc352907fc980b868725387e982bc1b5cee954a27e7861f672675db061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f954e2ca8c5aa26d7e7c757c5ba09c", "guid": "bfdfe7dc352907fc980b868725387e984e7a5baa4ea128e81cc7b93ea895a0bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c68a50027b07ccb09bcaadf9d046c396", "guid": "bfdfe7dc352907fc980b868725387e986f2e44c4deb448e91b7f9dcbd9877023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820620b71571f79b0e0a383f9b0bc5f5c", "guid": "bfdfe7dc352907fc980b868725387e9878438d158bdc6c70e3cc6f1422a1fd5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b0e6c6f937643b471345efac55fc89", "guid": "bfdfe7dc352907fc980b868725387e9896c5db8f09f591e14135d056fe937d39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec59bb03c3b57b95f346bdf725ed75b4", "guid": "bfdfe7dc352907fc980b868725387e98ed733eeeb4cf442e79221b43ec6ab543", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4052d5779b50b36b2311a81aaad6a7b", "guid": "bfdfe7dc352907fc980b868725387e9863f74156f992cef9610ea0027b158797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803985d446719486f2d033aeab2df531f", "guid": "bfdfe7dc352907fc980b868725387e9817dcb77d7f539f0ba7cf6bba23ab025a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7121a4a42f0dcea8823ad6f54b114d5", "guid": "bfdfe7dc352907fc980b868725387e98430e4d66334ef552e35896edf0800fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b23c530882e9041c7f49caa8c7dc4e", "guid": "bfdfe7dc352907fc980b868725387e98ac1c4a6aecb934ba3f7ce5f4b7a5ce10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c44a31de98e306e6f244f9635f67fd", "guid": "bfdfe7dc352907fc980b868725387e980d88881e8f1864ad44addeb4b546f957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e831c3e1272c1551b06a95665ec10bb", "guid": "bfdfe7dc352907fc980b868725387e981d2fe54f8f024aea0393135c6622c36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e243d994d5b6a447c59465cb0f66e09", "guid": "bfdfe7dc352907fc980b868725387e9875a7bbe49ed36b7b4958102bd487c186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba701d4b94769b00da34e244d3935148", "guid": "bfdfe7dc352907fc980b868725387e98c2fd66d30ed9c469a6f9e66511104826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988df6e7ab322b77f0b78bab34e5485f1b", "guid": "bfdfe7dc352907fc980b868725387e986898536a32f1ba314b25df11fafa9a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989795509a3e0270aa36d4c27ce63fed18", "guid": "bfdfe7dc352907fc980b868725387e989f1c87cce3ba04c5b99d6bc86b996ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c01f696e706f715f81a9e1552289aa8d", "guid": "bfdfe7dc352907fc980b868725387e98892efd917ae976f6c02ccd36be495e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f1bb7a00f385b8443116a8106e6350", "guid": "bfdfe7dc352907fc980b868725387e988f6dd6984240ec7b3e112a512bcbd51f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d686ede3a1c7732c2687d3bdea998c0", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eed40077cf38ddfc3f51a7a688874a5", "guid": "bfdfe7dc352907fc980b868725387e98111a96713a87f9cb39e8152dd6dde744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb21d063215910827561b12961abd616", "guid": "bfdfe7dc352907fc980b868725387e985b7f8093353d96741cf9ecdce14f0a8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0affcd330c2552b7ba260735cb4883", "guid": "bfdfe7dc352907fc980b868725387e98a735fa885496d6e8c24d59f0cb2548ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb1ebbbb2ea39a2778d45e7615264455", "guid": "bfdfe7dc352907fc980b868725387e9837a1e7be82ce3385de98f18fd5990f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98234561b13a9dcd63f97a1b5b989674ac", "guid": "bfdfe7dc352907fc980b868725387e9892af032a9b1b9cd1201f3d2ed313dfed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98736c96ea38cc12f35ca0ee27f2d2c8bf", "guid": "bfdfe7dc352907fc980b868725387e9826eaae4dc2c7871e7fbd53b8b7eb1b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805570ea53fc0ba1019d0c0d523551c0a", "guid": "bfdfe7dc352907fc980b868725387e98241a83cbc6157ce812894eb73152a9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578f814f55aed8fa8eed7aaf6f859ef9", "guid": "bfdfe7dc352907fc980b868725387e98160b038fa4d62a21aeddf4fe7e39ba83"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}