// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_appointments_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateAppointmentsModel _$UpdateAppointmentsModelFromJson(
        Map<String, dynamic> json) =>
    UpdateAppointmentsModel(
      leadId: json['leadId'] as String?,
      meetingOrSiteVisit: $enumDecodeNullable(
          _$MeetingOrSiteVisitEnumEnumMap, json['meetingOrSiteVisit']),
      isDone: json['isDone'] as bool?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      projectName: json['projectName'] as String?,
      executiveName: json['executiveName'] as String?,
      executiveContactNo: json['executiveContactNo'] as String?,
      image: json['image'] as String?,
      imagesWithName: (json['imagesWithName'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ImagesWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isManual: json['isManual'] as bool?,
      notes: json['notes'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      userId: json['userId'] as String?,
      appointmentDoneOn: json['appointmentDoneOn'] == null
          ? null
          : DateTime.parse(json['appointmentDoneOn'] as String),
    );

Map<String, dynamic> _$UpdateAppointmentsModelToJson(
        UpdateAppointmentsModel instance) =>
    <String, dynamic>{
      if (instance.leadId case final value?) 'leadId': value,
      if (_$MeetingOrSiteVisitEnumEnumMap[instance.meetingOrSiteVisit]
          case final value?)
        'meetingOrSiteVisit': value,
      if (instance.isDone case final value?) 'isDone': value,
      if (instance.latitude case final value?) 'latitude': value,
      if (instance.longitude case final value?) 'longitude': value,
      if (instance.projectName case final value?) 'projectName': value,
      if (instance.executiveName case final value?) 'executiveName': value,
      if (instance.executiveContactNo case final value?)
        'executiveContactNo': value,
      if (instance.image case final value?) 'image': value,
      if (instance.imagesWithName?.map((e) => e?.toJson()).toList()
          case final value?)
        'imagesWithName': value,
      if (instance.isManual case final value?) 'isManual': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.appointmentDoneOn?.toIso8601String() case final value?)
        'appointmentDoneOn': value,
    };

const _$MeetingOrSiteVisitEnumEnumMap = {
  MeetingOrSiteVisitEnum.none: 0,
  MeetingOrSiteVisitEnum.meeting: 1,
  MeetingOrSiteVisitEnum.siteVisit: 2,
};
