// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_booked_lead_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetBookedLeadModel _$GetBookedLeadModelFromJson(Map<String, dynamic> json) =>
    GetBookedLeadModel(
      leadId: json['leadId'] as String?,
      bookedDate: json['bookedDate'] == null
          ? null
          : DateTime.parse(json['bookedDate'] as String),
      bookedBy: json['bookedBy'] as String?,
      bookedByUser: json['bookedByUser'] as String?,
      bookedUnderName: json['bookedUnderName'] as String?,
      secondaryOwner: json['secondaryOwner'] as String?,
      userId: json['userId'] as String?,
      soldPrice: json['soldPrice'] as String?,
      notes: json['notes'] as String?,
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      projectIds: (json['projectIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertyIds: (json['propertyIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      teamHead: json['teamHead'] as String?,
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      documents: (json['documents'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Document.fromJson(e as Map<String, dynamic>))
          .toList(),
      carParkingCharges: (json['carParkingCharges'] as num?)?.toDouble(),
      additionalCharges: (json['additionalCharges'] as num?)?.toDouble(),
      tokenAmount: (json['tokenAmount'] as num?)?.toDouble(),
      paymentMode: (json['paymentMode'] as num?)?.toInt(),
      discount: (json['discount'] as num?)?.toInt(),
      discountUnit: json['discountUnit'] as String?,
      discountMode: (json['discountMode'] as num?)?.toInt(),
      remainingAmount: (json['remainingAmount'] as num?)?.toInt(),
      leadBrokerageInfoId: json['leadBrokerageInfoId'] as String?,
      paymentType: (json['paymentType'] as num?)?.toInt(),
      brokerageInfo: json['brokerageInfo'] == null
          ? null
          : BrokerageInfo.fromJson(
              json['brokerageInfo'] as Map<String, dynamic>),
      unitType: json['unitType'] == null
          ? null
          : UnitType.fromJson(json['unitType'] as Map<String, dynamic>),
      isBookingCompleted: json['isBookingCompleted'] as bool?,
      currency: json['currency'] as String?,
      properties: (json['properties'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Property.fromJson(e as Map<String, dynamic>))
          .toList(),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Project.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GetBookedLeadModelToJson(GetBookedLeadModel instance) =>
    <String, dynamic>{
      if (instance.leadId case final value?) 'leadId': value,
      if (instance.bookedDate?.toIso8601String() case final value?)
        'bookedDate': value,
      if (instance.bookedBy case final value?) 'bookedBy': value,
      if (instance.bookedByUser case final value?) 'bookedByUser': value,
      if (instance.bookedUnderName case final value?) 'bookedUnderName': value,
      if (instance.secondaryOwner case final value?) 'secondaryOwner': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.soldPrice case final value?) 'soldPrice': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
      if (instance.projectIds case final value?) 'projectIds': value,
      if (instance.propertyIds case final value?) 'propertyIds': value,
      if (instance.teamHead case final value?) 'teamHead': value,
      if (instance.agreementValue case final value?) 'agreementValue': value,
      if (instance.documents case final value?) 'documents': value,
      if (instance.carParkingCharges case final value?)
        'carParkingCharges': value,
      if (instance.additionalCharges case final value?)
        'additionalCharges': value,
      if (instance.tokenAmount case final value?) 'tokenAmount': value,
      if (instance.paymentMode case final value?) 'paymentMode': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.discountUnit case final value?) 'discountUnit': value,
      if (instance.discountMode case final value?) 'discountMode': value,
      if (instance.remainingAmount case final value?) 'remainingAmount': value,
      if (instance.leadBrokerageInfoId case final value?)
        'leadBrokerageInfoId': value,
      if (instance.paymentType case final value?) 'paymentType': value,
      if (instance.brokerageInfo case final value?) 'brokerageInfo': value,
      if (instance.unitType case final value?) 'unitType': value,
      if (instance.isBookingCompleted case final value?)
        'isBookingCompleted': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.properties case final value?) 'properties': value,
      if (instance.projects case final value?) 'projects': value,
    };

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
      id: json['id'] as String?,
      documentName: json['documentName'] as String?,
      filePath: json['filePath'] as String?,
      type: (json['type'] as num?)?.toInt(),
      bookedDocumentType: (json['bookedDocumentType'] as num?)?.toInt(),
      uploadedOn: json['uploadedOn'] == null
          ? null
          : DateTime.parse(json['uploadedOn'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedByUser: json['lastModifiedByUser'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.documentName case final value?) 'documentName': value,
      if (instance.filePath case final value?) 'filePath': value,
      if (instance.type case final value?) 'type': value,
      if (instance.bookedDocumentType case final value?)
        'bookedDocumentType': value,
      if (instance.uploadedOn?.toIso8601String() case final value?)
        'uploadedOn': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedByUser case final value?)
        'lastModifiedByUser': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
    };

BrokerageInfo _$BrokerageInfoFromJson(Map<String, dynamic> json) =>
    BrokerageInfo(
      soldPrice: (json['soldPrice'] as num?)?.toInt(),
      agreementValue: (json['agreementValue'] as num?)?.toInt(),
      brokerageCharges: (json['brokerageCharges'] as num?)?.toInt(),
      netBrokerageAmount: (json['netBrokerageAmount'] as num?)?.toInt(),
      gst: (json['gst'] as num?)?.toInt(),
      totalBrokerage: (json['totalBrokerage'] as num?)?.toInt(),
      referralNumber: json['referralNumber'] as String?,
      referredBy: json['referredBy'] as String?,
      referralName: json['referralName'] as String?,
      commission: (json['commission'] as num?)?.toInt(),
      commissionUnit: json['commissionUnit'] as String?,
      earnedBrokerage: (json['earnedBrokerage'] as num?)?.toInt(),
      brokerageType: (json['brokerageType'] as num?)?.toInt(),
      gstUnit: json['gstUnit'] as String?,
      brokerageUnit: json['brokerageUnit'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedByUser: json['lastModifiedByUser'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$BrokerageInfoToJson(BrokerageInfo instance) =>
    <String, dynamic>{
      if (instance.soldPrice case final value?) 'soldPrice': value,
      if (instance.agreementValue case final value?) 'agreementValue': value,
      if (instance.brokerageCharges case final value?)
        'brokerageCharges': value,
      if (instance.netBrokerageAmount case final value?)
        'netBrokerageAmount': value,
      if (instance.gst case final value?) 'gst': value,
      if (instance.totalBrokerage case final value?) 'totalBrokerage': value,
      if (instance.referralNumber case final value?) 'referralNumber': value,
      if (instance.referredBy case final value?) 'referredBy': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.commission case final value?) 'commission': value,
      if (instance.commissionUnit case final value?) 'commissionUnit': value,
      if (instance.earnedBrokerage case final value?) 'earnedBrokerage': value,
      if (instance.brokerageType case final value?) 'brokerageType': value,
      if (instance.gstUnit case final value?) 'gstUnit': value,
      if (instance.brokerageUnit case final value?) 'brokerageUnit': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedByUser case final value?)
        'lastModifiedByUser': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
    };

UnitType _$UnitTypeFromJson(Map<String, dynamic> json) => UnitType(
      id: json['id'] as String?,
      name: json['name'] as String?,
      carpetArea: (json['carpetArea'] as num?)?.toInt(),
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      superBuildUpArea: (json['superBuildUpArea'] as num?)?.toInt(),
      superBuildUpAreaUnit: json['superBuildUpAreaUnit'] as String?,
      lastModifiedByUser: json['lastModifiedByUser'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$UnitTypeToJson(UnitType instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.carpetArea case final value?) 'carpetArea': value,
      if (instance.carpetAreaUnitId case final value?)
        'carpetAreaUnitId': value,
      if (instance.superBuildUpArea case final value?)
        'superBuildUpArea': value,
      if (instance.superBuildUpAreaUnit case final value?)
        'superBuildUpAreaUnit': value,
      if (instance.lastModifiedByUser case final value?)
        'lastModifiedByUser': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
    };

Property _$PropertyFromJson(Map<String, dynamic> json) => Property(
      id: json['id'] as String?,
      title: json['title'] as String?,
      enquiredFor: (json['enquiredFor'] as num?)?.toInt(),
      propertyTypeId: json['propertyTypeId'] as String?,
      noOfBHK: (json['noOfBHK'] as num?)?.toInt(),
      noOfBHKs: (json['noOfBHKs'] as num?)?.toInt(),
      bhkType: (json['bhkType'] as num?)?.toInt(),
      dimension: json['dimension'] == null
          ? null
          : Dimension.fromJson(json['dimension'] as Map<String, dynamic>),
      aboutProperty: json['aboutProperty'] as String?,
      project: json['project'] as String?,
      ownerDetails: json['ownerDetails'] == null
          ? null
          : OwnerDetails.fromJson(json['ownerDetails'] as Map<String, dynamic>),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : MonetaryInfo.fromJson(json['monetaryInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyToJson(Property instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.enquiredFor case final value?) 'enquiredFor': value,
      if (instance.propertyTypeId case final value?) 'propertyTypeId': value,
      if (instance.noOfBHK case final value?) 'noOfBHK': value,
      if (instance.noOfBHKs case final value?) 'noOfBHKs': value,
      if (instance.bhkType case final value?) 'bhkType': value,
      if (instance.dimension case final value?) 'dimension': value,
      if (instance.aboutProperty case final value?) 'aboutProperty': value,
      if (instance.project case final value?) 'project': value,
      if (instance.ownerDetails case final value?) 'ownerDetails': value,
      if (instance.monetaryInfo case final value?) 'monetaryInfo': value,
    };

Dimension _$DimensionFromJson(Map<String, dynamic> json) => Dimension(
      id: json['id'] as String?,
      area: (json['area'] as num?)?.toInt(),
      areaUnitId: json['areaUnitId'] as String?,
      unit: json['unit'] as String?,
      conversionFactor: (json['conversionFactor'] as num?)?.toInt(),
      length: (json['length'] as num?)?.toInt(),
      breadth: (json['breadth'] as num?)?.toInt(),
      carpetArea: (json['carpetArea'] as num?)?.toInt(),
      carpetAreaId: json['carpetAreaId'] as String?,
      carpetAreaConversionFactor:
          (json['carpetAreaConversionFactor'] as num?)?.toInt(),
      buildUpArea: (json['buildUpArea'] as num?)?.toInt(),
      buildUpAreaId: json['buildUpAreaId'] as String?,
      buildUpConversionFactor:
          (json['buildUpConversionFactor'] as num?)?.toInt(),
      saleableArea: (json['saleableArea'] as num?)?.toInt(),
      saleableAreaId: json['saleableAreaId'] as String?,
      saleableAreaConversionFactor:
          (json['saleableAreaConversionFactor'] as num?)?.toInt(),
      commonAreaCharges: (json['commonAreaCharges'] as num?)?.toInt(),
      commonAreaChargesId: json['commonAreaChargesId'] as String?,
      commonAreaChargesInSqMtr:
          (json['commonAreaChargesInSqMtr'] as num?)?.toInt(),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$DimensionToJson(Dimension instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.area case final value?) 'area': value,
      if (instance.areaUnitId case final value?) 'areaUnitId': value,
      if (instance.unit case final value?) 'unit': value,
      if (instance.conversionFactor case final value?)
        'conversionFactor': value,
      if (instance.length case final value?) 'length': value,
      if (instance.breadth case final value?) 'breadth': value,
      if (instance.carpetArea case final value?) 'carpetArea': value,
      if (instance.carpetAreaId case final value?) 'carpetAreaId': value,
      if (instance.carpetAreaConversionFactor case final value?)
        'carpetAreaConversionFactor': value,
      if (instance.buildUpArea case final value?) 'buildUpArea': value,
      if (instance.buildUpAreaId case final value?) 'buildUpAreaId': value,
      if (instance.buildUpConversionFactor case final value?)
        'buildUpConversionFactor': value,
      if (instance.saleableArea case final value?) 'saleableArea': value,
      if (instance.saleableAreaId case final value?) 'saleableAreaId': value,
      if (instance.saleableAreaConversionFactor case final value?)
        'saleableAreaConversionFactor': value,
      if (instance.commonAreaCharges case final value?)
        'commonAreaCharges': value,
      if (instance.commonAreaChargesId case final value?)
        'commonAreaChargesId': value,
      if (instance.commonAreaChargesInSqMtr case final value?)
        'commonAreaChargesInSqMtr': value,
      if (instance.currency case final value?) 'currency': value,
    };

OwnerDetails _$OwnerDetailsFromJson(Map<String, dynamic> json) => OwnerDetails(
      id: json['id'] as String?,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$OwnerDetailsToJson(OwnerDetails instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.email case final value?) 'email': value,
    };

MonetaryInfo _$MonetaryInfoFromJson(Map<String, dynamic> json) => MonetaryInfo(
      id: json['id'] as String?,
      expectedPrice: (json['expectedPrice'] as num?)?.toInt(),
      isNegotiable: json['isNegotiable'] as bool?,
      brokerage: (json['brokerage'] as num?)?.toInt(),
      brokerageUnit: (json['brokerageUnit'] as num?)?.toInt(),
      currency: json['currency'] as String?,
      brokerageCurrency: json['brokerageCurrency'] as String?,
      depositAmount: (json['depositAmount'] as num?)?.toInt(),
      maintenanceCost: (json['maintenanceCost'] as num?)?.toInt(),
      isPriceVissible: json['isPriceVissible'] as bool?,
      noOfChequesAllowed: (json['noOfChequesAllowed'] as num?)?.toInt(),
      monthlyRentAmount: (json['monthlyRentAmount'] as num?)?.toInt(),
      escalationPercentage: (json['escalationPercentage'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MonetaryInfoToJson(MonetaryInfo instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.expectedPrice case final value?) 'expectedPrice': value,
      if (instance.isNegotiable case final value?) 'isNegotiable': value,
      if (instance.brokerage case final value?) 'brokerage': value,
      if (instance.brokerageUnit case final value?) 'brokerageUnit': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.brokerageCurrency case final value?)
        'brokerageCurrency': value,
      if (instance.depositAmount case final value?) 'depositAmount': value,
      if (instance.maintenanceCost case final value?) 'maintenanceCost': value,
      if (instance.isPriceVissible case final value?) 'isPriceVissible': value,
      if (instance.noOfChequesAllowed case final value?)
        'noOfChequesAllowed': value,
      if (instance.monthlyRentAmount case final value?)
        'monthlyRentAmount': value,
      if (instance.escalationPercentage case final value?)
        'escalationPercentage': value,
    };

Project _$ProjectFromJson(Map<String, dynamic> json) => Project(
      id: json['id'] as String?,
      name: json['name'] as String?,
      builderDetail: json['builderDetail'] == null
          ? null
          : BuilderDetail.fromJson(
              json['builderDetail'] as Map<String, dynamic>),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : MonetaryInfo.fromJson(json['monetaryInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProjectToJson(Project instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.builderDetail case final value?) 'builderDetail': value,
      if (instance.monetaryInfo case final value?) 'monetaryInfo': value,
    };

BuilderDetail _$BuilderDetailFromJson(Map<String, dynamic> json) =>
    BuilderDetail(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$BuilderDetailToJson(BuilderDetail instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
    };
