import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum PaymentType {
  none(0, "None"),
  bankLoan(1, "Bank Loan"),
  pendingLoanApproval(2, "Pending Loan Approval"),
  partialLoanCash(3, "Partial loan - Cash"),
  loanApplied(4, "Loan Applied"),
  onlineTransfer(5, "Online Transfer"),
  cash(6, "Cash"),
  cheque(7, "Cheque"),
  dD(8, "DD");

  final int value;
  final String description;

  const PaymentType(this.value, this.description);
}
