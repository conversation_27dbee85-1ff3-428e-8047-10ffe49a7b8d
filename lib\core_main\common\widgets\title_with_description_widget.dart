import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class TitleWithDescriptionWidget extends LeadratStatelessWidget {
  final double widthPercentage;
  final String title;
  final String? description;
  final String? subDescription;
  const TitleWithDescriptionWidget({
    super.key,
    required this.widthPercentage,
    required this.title,
    required this.description,
    this.subDescription,
  });

  @override
  Widget buildContent(BuildContext context) {
    return SizedBox(
      width: context.width(widthPercentage),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: '$title\n',
              style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor),
            ),
            TextSpan(
              text: description.isNotNullOrEmpty() ? description : '--',
              style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary),
            ),
            if(subDescription.isNotNullOrEmpty())
              TextSpan(
                text: "\n${subDescription ?? ''}",
                style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primary),
              ),
          ],
        ),
      ),
    );
  }
}
