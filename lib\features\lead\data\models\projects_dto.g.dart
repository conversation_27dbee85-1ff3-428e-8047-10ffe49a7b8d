// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'projects_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectDto _$ProjectDtoFromJson(Map<String, dynamic> json) => ProjectDto(
      leadId: json['leadId'] as String?,
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$ProjectDtoToJson(ProjectDto instance) =>
    <String, dynamic>{
      if (instance.leadId case final value?) 'leadId': value,
      if (instance.projects case final value?) 'projects': value,
    };
