// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_source_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadSourceAdapter extends TypeAdapter<LeadSource> {
  @override
  final int typeId = 23;

  @override
  LeadSource read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LeadSource.any;
      case 1:
        return LeadSource.direct;
      case 2:
        return LeadSource.iVR;
      case 3:
        return LeadSource.facebook;
      case 4:
        return LeadSource.linkedIn;
      case 5:
        return LeadSource.googleAds;
      case 6:
        return LeadSource.magicBricks;
      case 7:
        return LeadSource.ninetyNineAcres;
      case 8:
        return LeadSource.housing;
      case 9:
        return LeadSource.gharOffice;
      case 10:
        return LeadSource.referral;
      case 11:
        return LeadSource.walkIn;
      case 12:
        return LeadSource.website;
      case 13:
        return LeadSource.gmail;
      case 14:
        return LeadSource.propertyMicrosite;
      case 15:
        return LeadSource.portfolioMicrosite;
      case 16:
        return LeadSource.phonebook;
      case 17:
        return LeadSource.callLogs;
      case 18:
        return LeadSource.leadPool;
      case 19:
        return LeadSource.squareYards;
      case 20:
        return LeadSource.quikrHomes;
      case 21:
        return LeadSource.justLead;
      case 22:
        return LeadSource.whatsApp;
      case 23:
        return LeadSource.youTube;
      case 24:
        return LeadSource.qRCode;
      case 25:
        return LeadSource.instagram;
      case 26:
        return LeadSource.oLX;
      case 27:
        return LeadSource.estateDekho;
      case 28:
        return LeadSource.googleSheet;
      case 29:
        return LeadSource.channelPartner;
      case 30:
        return LeadSource.realEstateIndia;
      case 31:
        return LeadSource.commonFloor;
      case 32:
        return LeadSource.data;
      case 33:
        return LeadSource.roofAndFloor;
      case 34:
        return LeadSource.microsoftAds;
      case 35:
        return LeadSource.propertyWala;
      case 36:
        return LeadSource.projectMicrosite;
      case 37:
        return LeadSource.myGate;
      case 38:
        return LeadSource.flipkart;
      case 39:
        return LeadSource.propertyFinder;
      case 40:
        return LeadSource.bayut;
      case 41:
        return LeadSource.dubizzle;
      case 42:
        return LeadSource.webhook;
      case 43:
        return LeadSource.tikTok;
      case 44:
        return LeadSource.snapchat;
      case 45:
        return LeadSource.googleAdsCampaign;
      default:
        return LeadSource.any;
    }
  }

  @override
  void write(BinaryWriter writer, LeadSource obj) {
    switch (obj) {
      case LeadSource.any:
        writer.writeByte(0);
        break;
      case LeadSource.direct:
        writer.writeByte(1);
        break;
      case LeadSource.iVR:
        writer.writeByte(2);
        break;
      case LeadSource.facebook:
        writer.writeByte(3);
        break;
      case LeadSource.linkedIn:
        writer.writeByte(4);
        break;
      case LeadSource.googleAds:
        writer.writeByte(5);
        break;
      case LeadSource.magicBricks:
        writer.writeByte(6);
        break;
      case LeadSource.ninetyNineAcres:
        writer.writeByte(7);
        break;
      case LeadSource.housing:
        writer.writeByte(8);
        break;
      case LeadSource.gharOffice:
        writer.writeByte(9);
        break;
      case LeadSource.referral:
        writer.writeByte(10);
        break;
      case LeadSource.walkIn:
        writer.writeByte(11);
        break;
      case LeadSource.website:
        writer.writeByte(12);
        break;
      case LeadSource.gmail:
        writer.writeByte(13);
        break;
      case LeadSource.propertyMicrosite:
        writer.writeByte(14);
        break;
      case LeadSource.portfolioMicrosite:
        writer.writeByte(15);
        break;
      case LeadSource.phonebook:
        writer.writeByte(16);
        break;
      case LeadSource.callLogs:
        writer.writeByte(17);
        break;
      case LeadSource.leadPool:
        writer.writeByte(18);
        break;
      case LeadSource.squareYards:
        writer.writeByte(19);
        break;
      case LeadSource.quikrHomes:
        writer.writeByte(20);
        break;
      case LeadSource.justLead:
        writer.writeByte(21);
        break;
      case LeadSource.whatsApp:
        writer.writeByte(22);
        break;
      case LeadSource.youTube:
        writer.writeByte(23);
        break;
      case LeadSource.qRCode:
        writer.writeByte(24);
        break;
      case LeadSource.instagram:
        writer.writeByte(25);
        break;
      case LeadSource.oLX:
        writer.writeByte(26);
        break;
      case LeadSource.estateDekho:
        writer.writeByte(27);
        break;
      case LeadSource.googleSheet:
        writer.writeByte(28);
        break;
      case LeadSource.channelPartner:
        writer.writeByte(29);
        break;
      case LeadSource.realEstateIndia:
        writer.writeByte(30);
        break;
      case LeadSource.commonFloor:
        writer.writeByte(31);
        break;
      case LeadSource.data:
        writer.writeByte(32);
        break;
      case LeadSource.roofAndFloor:
        writer.writeByte(33);
        break;
      case LeadSource.microsoftAds:
        writer.writeByte(34);
        break;
      case LeadSource.propertyWala:
        writer.writeByte(35);
        break;
      case LeadSource.projectMicrosite:
        writer.writeByte(36);
        break;
      case LeadSource.myGate:
        writer.writeByte(37);
        break;
      case LeadSource.flipkart:
        writer.writeByte(38);
        break;
      case LeadSource.propertyFinder:
        writer.writeByte(39);
        break;
      case LeadSource.bayut:
        writer.writeByte(40);
        break;
      case LeadSource.dubizzle:
        writer.writeByte(41);
        break;
      case LeadSource.webhook:
        writer.writeByte(42);
        break;
      case LeadSource.tikTok:
        writer.writeByte(43);
        break;
      case LeadSource.snapchat:
        writer.writeByte(44);
        break;
      case LeadSource.googleAdsCampaign:
        writer.writeByte(45);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadSourceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
