import 'package:json_annotation/json_annotation.dart';
part'device_info_model.g.dart';
@JsonSerializable()
class PostDeviceInfoModel {
  final String userId;
  final int operatingSystem;
  final String osVersion;
  final String countryCode;
  final String languageCode;
  final String currencyCode;
  final String currencySymbol;
  final String deviceTimeZone;
  final int timeFormat;
  final String deviceModel;
  final String deviceUDID;
  final String deviceName;
  final bool isDebug;
  final bool isDeveloperMode;
  final String model;
  final String manufacturer;
  final String environment;
  final String ipAddress;
  final String latitude;
  final String longitude;

  PostDeviceInfoModel({
    required this.userId,
    required this.operatingSystem,
    required this.osVersion,
    required this.countryCode,
    required this.languageCode,
    required this.currencyCode,
    required this.currencySymbol,
    required this.deviceTimeZone,
    required this.timeFormat,
    required this.deviceModel,
    required this.deviceUDID,
    required this.deviceName,
    required this.isDebug,
    required this.isDeveloperMode,
    required this.model,
    required this.manufacturer,
    required this.environment,
    required this.ipAddress,
    required this.latitude,
    required this.longitude,
  });
  Map<String, dynamic> toJson() => _$PostDeviceInfoModelToJson(this);

}


