import 'package:leadrat/core_main/remote/rest_client.dart';

import '../di/injection_container.dart';
import '../managers/shared_preference_manager/token_manager.dart';

extension RestRequestExtension on RestRequest {
  Map<String, dynamic> addAuthorizationHeader() {
    final tokenManager = getIt<TokenManager>();
    final idToken = tokenManager.idToken;
    final tenant = tokenManager.tenant;

    return headers = {
      'Authorization': 'Bearer $idToken',
      'tenant': tenant,
    };
  }

  RestRequest updateAuthorizationHeader() {
    final tokenManager = getIt<TokenManager>();
    final idToken = tokenManager.idToken;
    headers?['Authorization'] = 'Bearer $idToken';
    return this;
  }

  Map<String, dynamic> addTenantHeader() {
    final tokenManager = getIt<TokenManager>();
    final tenant = tokenManager.tenant;
    return headers = {
      'tenant': tenant,
    };
  }
}
