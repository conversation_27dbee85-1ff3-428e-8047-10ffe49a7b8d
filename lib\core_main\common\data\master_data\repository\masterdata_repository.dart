import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';

import '../models/master_user_services_type_model.dart';
import '../models/modified_date_model.dart';

abstract class MasterDataRepository {
  Future<List<MasterPropertyTypeModel>?> getPropertyTypes({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterPropertyTypeModel>?> getListingPropertyTypes({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterLeadSourceModel>?> getLeadSource({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterAreaUnitsModel?>?> getAreaUnits({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterLeadStatusModel>?> getLeadStatuses({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyAmenitites({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<Map<String, List<MasterPropertyAmenitiesModel>>?> getPropertyListingAmenitites({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterPropertyAttributesModel>?> getPropertyAttributes({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<Map<EntityTypeEnum, DateTime>?> getModifiedDates({Duration duration = const Duration(seconds: 60)});

  Future<List<MasterProjectTypeModel?>?> getProjectTypes({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterProjectAttributeModel>?> getProjectAttributes({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<Map<String, List<MasterProjectAmenititesModel>>?> getProjectAmenitites({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterAssociatedBankModel>?> getAssociatedBank({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterUserServicesModel>?> getUserServices({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<ModifiedDateModel>?> getLocalLastModifiedDates({Duration duration = const Duration(seconds: 60)});

  Future<List<MasterCustomStatusModel>?> getCustomStatus({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<String>?> getAgencyNames({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<String>?> getAllLeadAddresses({bool restore = false,Duration duration = const Duration(seconds: 60)});

  Future<List<MasterPropertyTypeModel>?> getCustomPropertyTypes({bool isPropertyListingEnabled = false,bool isCustomFormEnabled = false,Duration duration = const Duration(seconds: 60)});

  Future<void> initMasterData({Duration timeoutDuration = const Duration(seconds: 30)});
}
