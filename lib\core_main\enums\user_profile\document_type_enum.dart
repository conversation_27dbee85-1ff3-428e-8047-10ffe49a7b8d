import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'document_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.documentEnumTypeId)
@JsonEnum(valueField: 'value')
enum DocumentType {
  @HiveField(0)
  defaultType("Default",0),
  @HiveField(1)
  identity("Identity",1),
  @HiveField(2)
  experience("Experience",2),
  @HiveField(3)
  signature("Signature",3),
  @HiveField(4)
  jpg("JPG",4),
  @HiveField(5)
  png("PNG",5),
  @HiveField(6)
  pdf("PDF",6),
  @HiveField(7)
  docx("DOCX",7),
  @HiveField(8)
  txt("TXT",8),
  @HiveField(9)
  csv("CSV",9);

  final int value;
  final String description;
  const DocumentType(this.description,this.value);



}
