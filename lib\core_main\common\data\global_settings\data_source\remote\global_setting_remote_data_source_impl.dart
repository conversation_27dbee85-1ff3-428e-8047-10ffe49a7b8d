import 'package:leadrat/core_main/common/data/global_settings/data_source/remote/global_setting_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/country_info_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class GlobalSettingRemoteDataSourceImpl extends LeadratRestService implements GlobalSettingRemoteDataSource {
  @override
  Future<GlobalSettingModel?> getGlobalSettingModel({Duration timeout = const Duration(seconds: 60)}) async {
    try {
      final restRequest = createGetRequest(GlobalSettings.getGlobalSettings, timeout: timeout);
      final response = await executeRequestAsync<ResponseWrapper<GlobalSettingModel?>>(
        restRequest,
        (json) => ResponseWrapper<GlobalSettingModel?>.fromJson(
          json,
          (data) => fromJsonObject(data, GlobalSettingModel.fromJson),
        ),
      );

      return response.data;
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      rethrow;
    }
  }

  @override
  Future<BaseCountryInfoModel?> getCountriesInfo({Duration timeout = const Duration(seconds: 60)}) async {
    try {
      final restRequest = createGetRequest(GlobalSettings.getCountries, timeout: timeout);
      final response = await executeRequestAsync<ResponseWrapper<BaseCountryInfoModel?>>(
        restRequest,
        (json) => ResponseWrapper<BaseCountryInfoModel?>.fromJson(json, (data) => fromJsonObject(data, BaseCountryInfoModel.fromJson)),
      );

      return response.data;
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      rethrow;
    }
  }
}
