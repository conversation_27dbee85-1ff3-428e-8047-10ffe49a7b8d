import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fpdart/src/either.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/models/copy_withvalue_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/app_feature.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/module_name.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/message_utility.dart';
import 'package:leadrat/features/auth/data/data_source/local/auth_local_data_source.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/data_management/domain/entities/template_entity.dart';
import 'package:leadrat/features/data_management/domain/repository/prospect_repository.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_template_usecase.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_details_bloc/data_details_bloc.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/features/projects/domain/entities/get_project_entity.dart';
import 'package:leadrat/features/projects/domain/repository/project_repository.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';
import 'package:leadrat/features/properties/domain/repository/properties_repository.dart';
import 'package:leadrat/main.dart';
import 'package:share_plus/share_plus.dart';

part 'message_template_event.dart';
part 'message_template_state.dart';

class MessageTemplateBloc extends Bloc<MessageTemplateEvent, MessageTemplateState> {
  final UsersDataRepository _usersDataRepository;
  final ProspectRepository _prospectRepository;
  final LeadsRepository _leadsRepository;
  final GetProspectTemplateUseCase _getProspectTemplateUseCase;
  final TextEditingController messageTextEditingController = TextEditingController();
  final TextEditingController searchTemplateTextEditingController = TextEditingController();
  late UserDetailsModel? userDetails;
  final ProjectRepository _projectRepository;
  final PropertiesRepository _propertiesRepository;
  AppModuleName category = AppModuleName.lead;
  String? domain;
  AppFeature _fromPage = AppFeature.leadInfo;

  MessageTemplateBloc(this._usersDataRepository, this._getProspectTemplateUseCase, this._prospectRepository, this._leadsRepository, this._projectRepository, this._propertiesRepository) : super(const MessageTemplateState()) {
    on<MessageTemplateInitialEvent>(_onLeadMessageTemplateInitial);
    on<SelectTemplateEvent>(_onSelectTemplate);
    on<SendTemplateEvent>(_onSendTemplate);
    on<GetPropertiesEvent>(_onGetPropertiesEvent);
    on<GetProjectsEvent>(_onGetProjectsEvent);
    on<SelectCategoryEvent>(_onSelectCategoryEvent);
    on<SelectPropertyOrProjectEvent>(_onSelectPropertyOrProjectEvent);
    on<SearchTemplateEvent>(_onSearchTemplateEvent);
    on<GetAllTemplateEvent>(_onGetAllTemplateEvent);
  }

  Future<FutureOr<void>> _onLeadMessageTemplateInitial(MessageTemplateInitialEvent event, Emitter<MessageTemplateState> emit) async {
    domain = getIt<UsersDataRepository>().getLoggedInUser()?.organizationName;
    add(GetPropertiesEvent());
    add(GetProjectsEvent());
    userDetails = _usersDataRepository.getLoggedInUser();
    messageTextEditingController.text = '';
    String leadNames = ' ';
    if (event.leadEntities != null && event.leadEntities!.isNotEmpty) {
      leadNames = event.leadEntities?.map((element) => element?.name ?? '').join(', ') ?? " ";
      _fromPage = AppFeature.leadInfo;
    } else if (event.getProspectEntities != null && event.getProspectEntities!.isNotEmpty) {
      event.getProspectEntities?.forEach((element) => leadNames = '$leadNames ${element?.name} ,');
      _fromPage = AppFeature.data;
    }
    add(SelectCategoryEvent(selectedCategory: AppModuleName.lead));
    List<SelectableItem<AppModuleName>> templateCategories = [
      SelectableItem<AppModuleName>(title: AppModuleName.lead.description, value: AppModuleName.lead),
      SelectableItem<AppModuleName>(title: AppModuleName.property.description, value: AppModuleName.property),
      SelectableItem<AppModuleName>(
        title: AppModuleName.project.description,
        value: AppModuleName.project,
      )
    ];

    emit(state.copyWith(
      pageState: PageState.loading,
      errorMessage: null,
      leadNames: leadNames,
      templatesCategories: templateCategories,
      selectedTemplate: null,
    ));
  }

  FutureOr<void> _onSelectPropertyOrProjectEvent(SelectPropertyOrProjectEvent event, Emitter<MessageTemplateState> emit) {
    emit(state.copyWith(selectedPropertyOrProjects: event.selectedProjectOrProperties, selectedTemplate: CopyWithValue(value: null, canUpdateValue: true)));
    messageTextEditingController.clear();
  }

  FutureOr<void> _onSelectCategoryEvent(SelectCategoryEvent event, Emitter<MessageTemplateState> emit) async {
    category = event.selectedCategory;
    messageTextEditingController.clear();
    searchTemplateTextEditingController.clear();
    emit(state.copyWith(
      selectedPropertyOrProjects: [],
      selectedTemplate: CopyWithValue(value: null, canUpdateValue: true),
      templatesCategories: state.templatesCategories.map((e) => e.value == event.selectedCategory ? e.copyWith(isSelected: true) : e.copyWith(isSelected: false)).toList(),
    ));
    add(GetAllTemplateEvent());
  }

  Future<FutureOr<void>> _onGetProjectsEvent(GetProjectsEvent event, Emitter<MessageTemplateState> emit) async {
    List<SelectableItem<String?>>? projectsList = [];
    var response = await _projectRepository.getProjectsWithId();
    response.fold((failure) => emit(state.copyWith(pageState: PageState.failure, propertiesList: [], errorMessage: "unable to get projects")), (result) {
      projectsList = result?.map((project) => SelectableItem(title: project.name ?? '', value: project.id ?? '')).toList();
      emit(state.copyWith(pageState: PageState.success, projectsList: projectsList));
    });
  }

  Future<FutureOr<void>> _onGetPropertiesEvent(GetPropertiesEvent event, Emitter<MessageTemplateState> emit) async {
    List<SelectableItem<String?>>? propertiesList = [];
    var response = await _propertiesRepository.getPropertyNameWithId();
    response.fold(
      (failure) => emit(state.copyWith(pageState: PageState.failure, propertiesList: [], errorMessage: "unable to get properties")),
      (result) {
        propertiesList = result?.map((property) => SelectableItem(title: property.title ?? '', value: property.id ?? '')).toList();
        emit(state.copyWith(pageState: PageState.success, propertiesList: propertiesList));
      },
    );
  }

  Future<FutureOr<void>> _onSelectTemplate(SelectTemplateEvent event, Emitter<MessageTemplateState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, errorMessage: 'fetching ${category.description} template messages'));
    Either<Failure, String>? messageStatus;
    if (category == AppModuleName.lead) {
      if (event.leadEntity != null && event.leadEntity!.isNotEmpty) {
        messageStatus = await _leadsRepository.getTemplateMessageByLead(
          footer: event.selectedTemplate?.value?.footer,
          message: event.selectedTemplate?.value?.message,
          header: event.selectedTemplate?.value?.header,
          leadEntity: event.leadEntity,
          domain: domain,
          userDetails: userDetails,
        );
      } else if (event.prospectEntities != null && event.prospectEntities!.isNotEmpty) {
        messageStatus = await _prospectRepository.getTemplateMessageByData(
          footer: event.selectedTemplate?.value?.footer,
          message: event.selectedTemplate?.value?.message,
          header: event.selectedTemplate?.value?.header,
          prospectEntities: event.prospectEntities,
          domain: domain,
          userDetails: userDetails,
        );
      }
    }
    if (category == AppModuleName.property) {
      messageStatus = await _propertiesRepository.getTemplateMessageByProperty(
        footer: event.selectedTemplate?.value?.footer,
        message: event.selectedTemplate?.value?.message,
        header: event.selectedTemplate?.value?.header,
        propertyEntity: state.selectedPropertyOrProjects.map((element) => GetPropertyEntity(id: element.value ?? '')).toList(),
        domain: domain,
        userDetails: userDetails,
        leadName: state.leadNames,
      );
    }
    if (category == AppModuleName.project) {
      messageStatus = await _projectRepository.getTemplateMessageByProject(
        footer: event.selectedTemplate?.value?.footer,
        message: event.selectedTemplate?.value?.message,
        header: event.selectedTemplate?.value?.header,
        projectEntity: state.selectedPropertyOrProjects.map((element) => GetProjectEntity(id: element.value ?? '')).toList(),
        domain: domain,
        userDetails: userDetails,
        leadName: state.leadNames
      );
    }

    if (messageStatus != null) {
      messageStatus.fold(
        (failure) {
          emit(state.copyWith(
              selectedTemplate: CopyWithValue(
                canUpdateValue: true,
                value: event.selectedTemplate,
              ),
              pageState: PageState.failure,
              errorMessage: 'unable to get  ${category.name} template message'));
        },
        (success) {
          messageTextEditingController.text = success;
          emit(state.copyWith(selectedTemplate: CopyWithValue(canUpdateValue: true, value: event.selectedTemplate), pageState: PageState.success, errorMessage: 'successfully get ${category.name} template message'));
        },
      );
    }
  }

  FutureOr<void> _onGetAllTemplateEvent(GetAllTemplateEvent event, Emitter<MessageTemplateState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, errorMessage: 'fetching ${category.name} templates'));
    final result = await _getProspectTemplateUseCase.call(category);
    result.fold(
      (failure) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'unable to get templates '));
      },
      (success) {
        List<SelectableItem<TemplateEntity>> templates = [];
        success?.forEach((template) {
          templates.add(SelectableItem<TemplateEntity>(title: template?.title ?? '', value: template));
        });
        if (templates.isNotEmpty) {
          emit(state.copyWith(templates: templates, pageState: PageState.success, errorMessage: "success"));
        } else {
          emit(state.copyWith(templates: [], pageState: PageState.success, errorMessage: "success"));
          LeadratCustomSnackbar.show(message: "${category.name} templates not found", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
        }
      },
    );
  }

  FutureOr<void> _onSearchTemplateEvent(SearchTemplateEvent event, Emitter<MessageTemplateState> emit) async {
    AppModuleName? selectedCategoryTemplate = state.templatesCategories.where((element) => element.isSelected).firstOrNull?.value;
    if (selectedCategoryTemplate != null) {
      var templates = state.templates?.map((element) => element.value?.moduleName == selectedCategoryTemplate && element.title.toLowerCase().contains(event.searchText.toLowerCase()) ? element.copyWith(isEnabled: true) : element.copyWith(isEnabled: false)).toList();
      emit(state.copyWith(templates: templates));
    }
  }

  FutureOr<void> _onSendTemplate(SendTemplateEvent event, Emitter<MessageTemplateState> emit) async {
    if (event.contactType != null) {
      switch (event.contactType) {
        case ContactType.sms:
          if (event.phoneNumber == null) return;
          await MessageUtility.sendMessage(message: messageTextEditingController.text, phoneNumber: event.phoneNumber!);
          break;
        case ContactType.whatsApp:
          if (event.phoneNumber != null) {
            await MessageUtility.sendWhatsappMessage(phoneNumber: event.phoneNumber!, message: messageTextEditingController.text);
          }
          break;
        case ContactType.email:
          await MessageUtility.sendEmail(body: messageTextEditingController.text, subject: event.templateTitle ?? '', email: event.email ?? '');
          break;
        default:
          break;
      }

      if (event.contactType != null) {
        if (_fromPage == AppFeature.leadInfo) {
          getIt<LeadInfoBloc>().add(UpdateContactCountEvent(event.contactType!, messageTextEditingController.text));
          getIt<ManageLeadsBloc>().add(ResetManageLeadsBlocEvent());
        } else if (_fromPage == AppFeature.data) {
          getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, event.contactType!, messageTextEditingController.text));
        }
      }
    } else {
      try {
        await Share.share(messageTextEditingController.text);
      } catch (e) {
        emit(state.copyWith(errorMessage: ''));
      }
    }
  }

  String getValues(Map<String, String> item) {
    String res = '';
    item.forEach((key, value) {
      if (res != '') {
        res = '$res,$value';
      } else {
        res = value;
      }
    });
    return res;
  }

  String getListOfNamesInOneString(List<String> list) {
    String res = '';
    for (var value in list) {
      if (res != '') {
        res = '$res,$value';
      } else {
        res = value;
      }
    }
    return res;
  }

  String getLocation(AddressEntity address) {
    String res = '';
    res = address.subLocality ?? '';
    res = '$res,${address.locality ?? ''}';
    res = '$res,${address.city ?? ''}';
    return res;
  }

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries.map((MapEntry<String, String> e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}').join('&');
  }

  String processMessage(String message) {
    var placeholderRegex = RegExp(r'#([a-zA-Z\s]+)#');
    String processedMessage = message.replaceAllMapped(placeholderRegex, (match) {
      String placeholder = match.group(1)!;
      String transformed = placeholder.toLowerCase().replaceAll(' ', '');
      return '#$transformed#';
    });
    return processedMessage;
  }
}
