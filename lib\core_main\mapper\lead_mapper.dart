import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/features/lead/domain/entities/lead_status_custom_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_status_entity.dart';

class LeadEntityMapper {
  final MasterDataLocalDataSource _masterDataRepository;
  final UsersLocalDataSource _usersLocalDataSource;

  LeadEntityMapper(this._masterDataRepository, this._usersLocalDataSource);

  PropertyTypeEntity? getProperty(String? subTypeId) {
    if (subTypeId.isNullOrEmpty()) return null;
    final localPropertySubTypes = _masterDataRepository.getPropertyTypes();
    for (MasterPropertyTypeModel propertyType in localPropertySubTypes ?? []) {
      final matchingChildType = propertyType.childTypes?.firstWhereOrNull((childType) => childType.id == subTypeId);
      if (matchingChildType != null) {
        return matchingChildType.toEntity();
      }
    }
    return null;
  }

  LeadStatusEntity? getLeadStatus(String? subStatusId) {
    if (subStatusId.isNullOrEmpty()) return null;

    final localLeadStatuses = _masterDataRepository.getLeadStatuses();
    for (MasterLeadStatusModel leadStatus in localLeadStatuses ?? []) {
      if (leadStatus.id == subStatusId) return leadStatus.toEntity();
      final matchingChildType = leadStatus.childTypes?.firstWhereOrNull((childType) => childType.id == subStatusId);
      if (matchingChildType != null) return leadStatus.toEntity();
    }
    return null;
  }

  BaseUserModel? getUser(String? userId) {
    if (userId.isNullOrEmpty()) return null;
    final localUsersDetails = _usersLocalDataSource.getAllUsers();
    final matchingUser = localUsersDetails?.firstWhereOrNull((getAllUserModel) => getAllUserModel?.id == userId);
    return matchingUser?.toBaseUserModel();
  }

  LeadStatusCustomEntity? getLeadCustomStatus(String? subStatusId) {
    if (subStatusId.isNullOrEmpty()) return null;

    final localLeadCustomStatus = _masterDataRepository.getLeadCustomStatus();
    for (MasterCustomStatusModel leadStatus in localLeadCustomStatus ?? []) {
      if (leadStatus.id == subStatusId) return leadStatus.toEntity();
      final matchingChildType = leadStatus.childTypes?.firstWhereOrNull((childType) => childType.id == subStatusId);
      if (matchingChildType != null) return leadStatus.toEntity();
    }
    return null;
  }

  String? getAreaUnit(String? areaUnitID) {
    if (areaUnitID.isNullOrEmpty()) return null;

    final localAreaUnits = _masterDataRepository.getAreaUnits();
    for (MasterAreaUnitsModel areaUnit in localAreaUnits ?? []) {
      if (areaUnit.id == areaUnitID) return areaUnit.unit;
    }
    return null;
  }
}
