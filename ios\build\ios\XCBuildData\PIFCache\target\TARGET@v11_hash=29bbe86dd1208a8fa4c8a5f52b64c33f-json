{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818af82847c901c363795f4876bc0f75c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fd13f0aa363483aaf0accac0730a880", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faae051753d5408f8658766108628cbf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c86fb4a2310d5de73934a17de89d81e3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faae051753d5408f8658766108628cbf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb78993751c9cb085b28dc43b32b45b8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e0db59dbfebc1acc5fe22212f092de6e", "guid": "bfdfe7dc352907fc980b868725387e988cabf01d7329080c03f067af5d4b9dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2f5a35842096a9a0fdd02fc00172e89", "guid": "bfdfe7dc352907fc980b868725387e98b1a6b0fe62264adf9bc029f16f4ac4ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c2a91119e53f471128778bde51ff27", "guid": "bfdfe7dc352907fc980b868725387e98d0ab85e5311aaf54b772087032cb019d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a34c1f314602602d75cb0d7615afa9", "guid": "bfdfe7dc352907fc980b868725387e989ce22bfa48d8422a0ea4cfc019f56e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7e32e9f2f1a34946babb23b77e3ba6", "guid": "bfdfe7dc352907fc980b868725387e98e707599ce6a73075d044c58d4e64e7f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c65e2ca651192477cf1626f32b2cad", "guid": "bfdfe7dc352907fc980b868725387e98b9805c5c7544ddb800a14b719981a0db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23a1134aca1c9dc1a6a38936157c2a0", "guid": "bfdfe7dc352907fc980b868725387e98a80278096a0b6a8619325be12502f632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826558b0a7c539f36b848237a0a74064d", "guid": "bfdfe7dc352907fc980b868725387e986e27ca6bf3af9257f10ecfbd44d2ea78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a98d15c332d8efbce0caaa103f67eed", "guid": "bfdfe7dc352907fc980b868725387e9888781791444b638cf50e331bcf351ee1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f89210ce2dc90cd668a16881e4b4198b", "guid": "bfdfe7dc352907fc980b868725387e98318a1d9e89784b14722c90cef41b3e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec7869dd2a6c24545c4386b359f735e", "guid": "bfdfe7dc352907fc980b868725387e988d713cf2bf98481c49d6dad83aabe4ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2e78a73e59145b2ad10c8faf318fee", "guid": "bfdfe7dc352907fc980b868725387e98e33078c9de942c0603f36200620e5aef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cfddfdf2b1ad5826ad79484e48e8398", "guid": "bfdfe7dc352907fc980b868725387e98375adb77f71643eb59620e784d4757a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800dbea63b200fdccf018569cf6448735", "guid": "bfdfe7dc352907fc980b868725387e985a4d791b61bc64121de61c08a4a58c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aea6d2a384a4c25da29a0afd594259b", "guid": "bfdfe7dc352907fc980b868725387e98cddd9008c88e2a9584032ddd5f7797ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414f268dbba74474a6334eabc819cb71", "guid": "bfdfe7dc352907fc980b868725387e980fecb28a971cde4c3193f379750a10a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dba0cdd3bc1f9baf72a417f51ab4217", "guid": "bfdfe7dc352907fc980b868725387e983743880ceff1b88e807a0a27fb7df6fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190ab902f67e3aed403c059cb1896ce8", "guid": "bfdfe7dc352907fc980b868725387e982cd83524d8c743f53831bc8d16886081", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a5a484cd9855895ebc6dba55e63db09", "guid": "bfdfe7dc352907fc980b868725387e98f9eef7f038fd796737e2dede2e1fd051", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346853f8a63564835d5fa4698293e253", "guid": "bfdfe7dc352907fc980b868725387e98107ffa96c0f5cc5e6bd222f6d0834ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823c313ce70078e3f5e46249b245d6b49", "guid": "bfdfe7dc352907fc980b868725387e98ef10245562c7e2d7c59e22bb828372a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac23dbc36807dac362b5eac24a317723", "guid": "bfdfe7dc352907fc980b868725387e98eaec9de2520ee54c8ac1d43009418fb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db8f8e9af291538a2cc99f0b675fa98", "guid": "bfdfe7dc352907fc980b868725387e9882540b91f910a281c9fdf4b3fbc5096a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ddec746b1b7efef5504ef351faa00488", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986247edf3cd261ec8390eea1abe9dfc55", "guid": "bfdfe7dc352907fc980b868725387e98ecec4e668f9be5b87aa68622360a8561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d54ae9a25c45a2f8169a41fabc0c480", "guid": "bfdfe7dc352907fc980b868725387e98d5ccf2f8fce9b5e12dbad33c72744eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aaa6843c766cc2d6404c109181a328b", "guid": "bfdfe7dc352907fc980b868725387e98b94b61f1b0bb38a1448a915f960e1703"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986890b3666e10280380897f5eeb938168", "guid": "bfdfe7dc352907fc980b868725387e98b2cffa32ec1df997c54f4041bcf20d8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb39a4257cdab3a62d33c89dbb91077", "guid": "bfdfe7dc352907fc980b868725387e98f77f96504d9850e65aa406a4f5e155d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5267373bbccefb5cb8844af6103911", "guid": "bfdfe7dc352907fc980b868725387e988e34e5acf422f4bb116560f09fe4ba68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f75aec96ed09d82a6dce431f68499e9", "guid": "bfdfe7dc352907fc980b868725387e98b1642ee84f3214e195a4aacb5d39f478"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e387f1ff30c1408dc6bb948c0e097b7", "guid": "bfdfe7dc352907fc980b868725387e98570bb4c7607bd54e3eeda02e148c160a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64563a0955ea4c007274a3794cbc2c4", "guid": "bfdfe7dc352907fc980b868725387e98b7dd5ee0247b9d4c3600287e1b1d080e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5fe897676c2bfabe273f7b247351ee", "guid": "bfdfe7dc352907fc980b868725387e98e2ed38368dbfd1f3ddc0eb7ec739ddd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981493963a686ee5f8b3e606a7b3a7964a", "guid": "bfdfe7dc352907fc980b868725387e981afccee6d50f954cddb9b0d33aa8e6da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d79f07bc6efbac9108d0effb5ffbbd", "guid": "bfdfe7dc352907fc980b868725387e988e7f32de9db3992456aaff6190ddc298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673c2d6eaff0b85accd5f74c19813779", "guid": "bfdfe7dc352907fc980b868725387e98b8a5a494ae8095a51993cff85114ad95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b469bd8bd1a9452e3e873efb93e0363", "guid": "bfdfe7dc352907fc980b868725387e986b0c59714d2f0a5ff002a21829d52358"}], "guid": "bfdfe7dc352907fc980b868725387e98f8e17fe74a9c9bf07367ea7017f9d0c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98e6b4fe4b3dbbee88ec34bb5a637ea1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e988e7e6c9e3a3f53b4a909d8bf9f2bf72c"}], "guid": "bfdfe7dc352907fc980b868725387e98c5eb0f53d08bfe63b50cc1e2c64a0696", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984e2656afe84d66ebf45a6bc14374d280", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e985a14bbd924a1cc427dba3944c8500b1e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}