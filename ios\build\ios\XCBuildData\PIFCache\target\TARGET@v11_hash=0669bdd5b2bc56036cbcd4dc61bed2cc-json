{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987cffd8a615d5caf58a4bdc4acd7c0420", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888b35da22cfcc35f3cc22ef9076132f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981267b8dc8e994a1cda2a0204d6ec7a94", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815924bbd9a6f09efeb055d689f4d5dbe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981267b8dc8e994a1cda2a0204d6ec7a94", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9872485abd1773092d9dd555eec0845e31", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8fb98292bb2ed1e3aec56b3369c915e", "guid": "bfdfe7dc352907fc980b868725387e98901c50aa617ba6f42648f24a5af1ef80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98691fff7855ca6b47d18fd2ee9a24a0e0", "guid": "bfdfe7dc352907fc980b868725387e988b154a9e28cdc7fe052f26040d230b1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1659d008524d293f4df9b7af3141c14", "guid": "bfdfe7dc352907fc980b868725387e9899dc8c042b40774a2ca0e0ffaed26693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d437bd72553018035a57ef4341b048", "guid": "bfdfe7dc352907fc980b868725387e983144df7079aff7e9026a8183614c47f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a4a77e58e4f783d57db995683752ff", "guid": "bfdfe7dc352907fc980b868725387e9844a87207fc388589693539610e198c35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b6d1e2fcb059dc6d3ede399c23664f", "guid": "bfdfe7dc352907fc980b868725387e98989b91c75e7afe20ada242e799d40d49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e303a02cc046f90229e3dc23bd86f684", "guid": "bfdfe7dc352907fc980b868725387e98eed607c98f57659b7d91ff92b049b5fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856be72f3b1e8296ae86a012242ba3f3f", "guid": "bfdfe7dc352907fc980b868725387e9820690b1e7273b693d955c04d3ae29630", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d535415ecf4691517f32f92f67f3d0", "guid": "bfdfe7dc352907fc980b868725387e983fa5e80b469546c06c2bbafaf6e204e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984782b1edf8cf7a7d62049378a518449d", "guid": "bfdfe7dc352907fc980b868725387e984dbba76ed1b7623289dc0d47899aadd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985937860e4a368c38d03a31256ee09a79", "guid": "bfdfe7dc352907fc980b868725387e988eeb99fef5980e77a34d719914bc9ca7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc7103dae5595611f0fb81b37a328d9", "guid": "bfdfe7dc352907fc980b868725387e98ed081cd59fc3a5d3986b4ac0ae1e1122", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dde684d0cb08adefcfd25c86a3dd01f", "guid": "bfdfe7dc352907fc980b868725387e98033fa66e8a67ee45286c4f5b1ddf0507", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e04e6ee7ff1d3139384714ab3add3f0", "guid": "bfdfe7dc352907fc980b868725387e9860d0105db2f354f2c446e650db432d54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6dd3b773484885771036186a5c47b7", "guid": "bfdfe7dc352907fc980b868725387e98c9a9fca55e9f31c787312c4678963e9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cd8a63737cc3cd74641c29e57dbc0b", "guid": "bfdfe7dc352907fc980b868725387e98fb1d24abeeb042b71098efe9ddb75322", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd1c827865242155b5cc6a220b0a4be", "guid": "bfdfe7dc352907fc980b868725387e98cd6bd59bbc86f9e850e128d1af4fe62b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d07d3aba5b4d238d9ee6140e9e5478", "guid": "bfdfe7dc352907fc980b868725387e98d3dc8c038e38e7eeb2f415a2a9dcc5d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b3ade1c04441178ea077541ba627b7", "guid": "bfdfe7dc352907fc980b868725387e987181fea9f1ef479429773ee5e9fb42de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d780a45ce7d26b56d4cb8a7533f4811c", "guid": "bfdfe7dc352907fc980b868725387e9871d3f392c6cdbbf3cd9337308280079b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5b220cc86af7d0747eb65cc49e2e96", "guid": "bfdfe7dc352907fc980b868725387e9864a23047508f567f74f96685e6097ce8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb76e7626fa9ab1d3d0e0c3e7a9833a8", "guid": "bfdfe7dc352907fc980b868725387e983b693526f96dec1ece9c5df891e12015", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cb5ca7803105e756128a8609eaeb0d3a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b144dd47e4279b2d7980b669123aa0e", "guid": "bfdfe7dc352907fc980b868725387e9887662842cfe51dbd69d7b61b11880bd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405b0517ff10cddf17a1d5e85a0c9b2f", "guid": "bfdfe7dc352907fc980b868725387e981eda63f57d2d40edffc0488928a67286"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802328065911c3dc688018225572a9a6", "guid": "bfdfe7dc352907fc980b868725387e98f8e85b1328b7a8b35de1b8b601602dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10c4490b42996353db44e2f2b928a38", "guid": "bfdfe7dc352907fc980b868725387e98da6a6eab2e60618cca2152a63cb0e829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb1d7b93d1087ac12691fadbee24126", "guid": "bfdfe7dc352907fc980b868725387e98fd1e8eef79bcd4fb594fa47e64834330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e471fc3f0c1e7770062fae11eb7280", "guid": "bfdfe7dc352907fc980b868725387e982e08e4d5eba06710d304914c13d95210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834cf4937e1675e10863089a6d3634bcb", "guid": "bfdfe7dc352907fc980b868725387e98226e0fcf57ced453acea9bc8278113d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed69139fb20a79d7218f6af5d1e6f67", "guid": "bfdfe7dc352907fc980b868725387e9822af7de4544397a3dfe529e44a28cc22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5897a888b646fd24d0b20cdf1c9308b", "guid": "bfdfe7dc352907fc980b868725387e985d9a492d36251651cd114ca340a06cb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652b32ce8846ec18d9dec92f4d7ac504", "guid": "bfdfe7dc352907fc980b868725387e983bf914d0782dccd619f8a3b136c8d77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3bba9ef33d43a0adbf0d72014d3420f", "guid": "bfdfe7dc352907fc980b868725387e9821d5501e642060ebec9d2cb68d6f4dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c956fb215279d9b756ce86c987ab602", "guid": "bfdfe7dc352907fc980b868725387e982e33a79aa39332d2e0f969bdc6eb7b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729e656715379453a2e3c499627833ff", "guid": "bfdfe7dc352907fc980b868725387e98e08d3f1c5481b715258299c513b064e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865358eebfd6284281a8fe48b0719827e", "guid": "bfdfe7dc352907fc980b868725387e980e756c8750949ac951be4bf22a42f7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0523ffc6e6792df1451ba6f0893ae7", "guid": "bfdfe7dc352907fc980b868725387e98fc3e9ea5c81622bfdfab25796f50f596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d7c15eee280740fcf144a9acfd0495", "guid": "bfdfe7dc352907fc980b868725387e98f9b3f84a65db2168e83b36c7b128b569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f292f811e3fcfcb49b697908291109ed", "guid": "bfdfe7dc352907fc980b868725387e981bd09432ca4ddc68816150368c8abe50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813eddd292ec3f4edb01c993216196867", "guid": "bfdfe7dc352907fc980b868725387e985d62ea3fdea4155d825fa2821cab6b7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838085ab3432531f32e42fe2c8947fa65", "guid": "bfdfe7dc352907fc980b868725387e98b18a23a6c28da340a150c6ab5fc3d422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2ddf3c08a0f330284793879740da35", "guid": "bfdfe7dc352907fc980b868725387e98f1c6a2ca84d095b3c01f12142f7f1822"}], "guid": "bfdfe7dc352907fc980b868725387e98007fb2bff604b82a9376cbe2668faffb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e981208b1b28430bcf5af3cd06ae038ba78"}], "guid": "bfdfe7dc352907fc980b868725387e986953d2b5e7df2072767bf7ed910f6cbc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980d66250ed8d9f906189ae1dbcdfd549f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98a8db6a82201cdd61de8c251fc59a1390", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}