import 'package:leadrat/core_main/common/data/auth_tokens/models/get_token_model.dart';
import 'package:leadrat/core_main/common/models/base_url_model.dart';
import 'package:leadrat/core_main/common/models/force_update_model.dart';

abstract class AuthTokenRepository {
  Future<GetTokenModel?> getRefreshToken();

  Future<GetTokenModel?> getToken({required String tenant, required String userName, required String password});

  Future<String?> generateMultiFactorAuthOtp({required String userName});

  Future<GetTokenModel?> verifyAuthOtp({required String userName, required String sessionId, required String otp});

  Future<BaseUrlModel?> getBaseUrl();

  Future<ForceUpdateModel?> getUpdateAppVersion();
}
