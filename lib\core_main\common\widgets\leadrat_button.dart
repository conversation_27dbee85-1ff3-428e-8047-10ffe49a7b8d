import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';

class LeadratButton extends LeadratStatelessWidget {
  final String buttonText;
  final TextStyle buttonTextStyle;
  final VoidCallback onPressed;
  final Color buttonColor;
  final Widget? trailingIcon;
  final Widget? leadingIcon;
  final double? spacing;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final BoxBorder? border;
  final double? height;
  const LeadratButton({
    super.key,
    required this.buttonText,
    required this.buttonTextStyle,
    required this.onPressed,
    this.buttonColor = ColorPalette.primaryGreen,
    this.trailingIcon,
    this.leadingIcon,
    this.spacing = 5,
    this.padding = const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
    this.borderRadius = 50,
    this.border,
    this.height,
    this.margin,
  });

  @override
  Widget buildContent(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: height,
        padding: padding,
        margin: margin,
        decoration: BoxDecoration(color: buttonColor, borderRadius: BorderRadius.circular(borderRadius), border: border),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (leadingIcon != null) leadingIcon!,
            if (leadingIcon != null) SizedBox(width: spacing),
            Text(buttonText, style: buttonTextStyle),
            if (trailingIcon != null) SizedBox(width: spacing),
            if (trailingIcon != null) trailingIcon!,
          ],
        ),
      ),
    );
  }
}
