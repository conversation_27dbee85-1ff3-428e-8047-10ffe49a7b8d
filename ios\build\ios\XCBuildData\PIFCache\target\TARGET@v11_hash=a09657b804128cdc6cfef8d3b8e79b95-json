{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5b08756c8cb3fc0f233079e6c4e722d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820c57fc23e0657146725372d33e8d62c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51ff14d0c09696e45089caf50e3267b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a401751eb835a1c2d431ba7a266ac6c2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51ff14d0c09696e45089caf50e3267b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f946ed97e5d5db46a8f906c8a0925c68", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d4aa0d0b72ac5cd8c00a56037447587", "guid": "bfdfe7dc352907fc980b868725387e9893689fa8c32ed94147a3f0768058fc56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989210ef55e3158d148d70515a3d283b04", "guid": "bfdfe7dc352907fc980b868725387e98b55c3fd49112c89fc0b49b072219c763", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa051c084515587675b9bf02a2713630", "guid": "bfdfe7dc352907fc980b868725387e98c9be6d347dab0f6a03ec9530ec19d5b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831abe1044d2d5cca073b644a228632eb", "guid": "bfdfe7dc352907fc980b868725387e98029f1593bd0f6f2e8664a11d86707c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef1f6610bf9dc1c98b9e92fc07af5f9", "guid": "bfdfe7dc352907fc980b868725387e9830fea05951d9bc28e6cd811c34594e86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803bb286d628090a26dc829de52557b1a", "guid": "bfdfe7dc352907fc980b868725387e986efb40889f185dfebc90f351741d0503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be565e0f51624af467300db8b46025d0", "guid": "bfdfe7dc352907fc980b868725387e987f40ac4846ff04c22ffcf56b17506afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ddafc95171afad3d540d5e2f0a2ac5", "guid": "bfdfe7dc352907fc980b868725387e98bd32fb41d752741e36161901edb1a825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984575dac2c60ac78abe6ee97c732f9584", "guid": "bfdfe7dc352907fc980b868725387e983fe8650bc759295d1b6e781d53ab8acc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a672ba8a0e8f712653c9ecc86378b3a5", "guid": "bfdfe7dc352907fc980b868725387e98bcdc6663cdb57bd84f6747e55a85ef23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fce32c6ae4b430eab8a4933e7fc8f6d", "guid": "bfdfe7dc352907fc980b868725387e98066050c54cbb0170da5808da04a6a985", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b959578bb4c9f44ad127485bc41e0c", "guid": "bfdfe7dc352907fc980b868725387e98721c4eb65f2c0d2098f26bf67a515e7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8f1bc7f9eab93c259df16ca8f62cff", "guid": "bfdfe7dc352907fc980b868725387e9834a591f63db9d3d5d28fd53922556479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c84547d7ebb2211c46810790c1a1d3f", "guid": "bfdfe7dc352907fc980b868725387e98547d49c87beb88207c866d6b460b9d12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533df0d1c898594b0b839b89c4f2cf59", "guid": "bfdfe7dc352907fc980b868725387e985d5d1ba19ec7e8ad2ec11d4facb10ac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc678042d8b709cedcd9558fae1f17e8", "guid": "bfdfe7dc352907fc980b868725387e98a50390ba5feaf415cd4492ccfb81b449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a7952719d3d1052b563174d0c63dd2", "guid": "bfdfe7dc352907fc980b868725387e980077cc9a6cd6963d09ad1702e3339ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aabe1c022b76aca8bd78206c749bad64", "guid": "bfdfe7dc352907fc980b868725387e989d52df183d2abc6df2eba647ecc12ea7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2cd62cc136405bddd51d1498ee5dab", "guid": "bfdfe7dc352907fc980b868725387e98e0e98b581f2afd65519ea7cae1e6eace", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca58cc7512cff07d30249036c394123", "guid": "bfdfe7dc352907fc980b868725387e987c442eb0a40d4c24e20ff931bd6fa8b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa563de7c1c20f42818456d87ef1af74", "guid": "bfdfe7dc352907fc980b868725387e98c26ed337712ec686569d9663824e54db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0030e3aaf1b6c8b0fec3a12538d79c2", "guid": "bfdfe7dc352907fc980b868725387e98f0bd0abfa93b67bf9dd99194c5c6e7b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf6da9d225cb948e188ebf4d32b8069", "guid": "bfdfe7dc352907fc980b868725387e983523a395a04b2e7fa34e85d75b2adbf4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980699bb3e8f5870b3502e92eed3b16ceb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881a798283b8d3278ddfd491226d56327", "guid": "bfdfe7dc352907fc980b868725387e98f05a160210e3b1aa5da192b42f5a6b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab14d9c80abbfaed962451883dc45d7", "guid": "bfdfe7dc352907fc980b868725387e9840722f2ed2e1681ed1cd3eefc029f421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afedc01b26ebb2dcf38205406e4ec0cd", "guid": "bfdfe7dc352907fc980b868725387e98ffefd5258a22eb1900468e696be0cf23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2cdade4aff8fa9b335629764a4a46b9", "guid": "bfdfe7dc352907fc980b868725387e9874e4d14a93c0baf0a31b44408d5c4fa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd102daff80eb2b0d4db817e5aabf554", "guid": "bfdfe7dc352907fc980b868725387e986270d2ec6b338f55bf607c2d37a1940a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c752584024744cf77b9b31069a579dea", "guid": "bfdfe7dc352907fc980b868725387e983c0b6248df8a35558140cfe036070627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7eca3fc09a6567b82306d7dc19973c7", "guid": "bfdfe7dc352907fc980b868725387e98fe736815bb78a8dc9c37f73a265158f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52c2c036876b0d49c73df1f79d6347e", "guid": "bfdfe7dc352907fc980b868725387e989f9f7eba5dd01268c398f1a7eca574f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a2fb16fbf971dbb2411967bd35f8dd", "guid": "bfdfe7dc352907fc980b868725387e9896d9d96a69aa4b11d10c9ed1b51a012e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fa774a36c7bba83a4706feb1e6ff403", "guid": "bfdfe7dc352907fc980b868725387e980e8db261a96dcde52381ab8a64dfa674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ce26e549a9ec37fc36534b731bc9c57", "guid": "bfdfe7dc352907fc980b868725387e980913478f23ac1ada013c08f935cb37b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371dab724a1c11c71c1d850125aab313", "guid": "bfdfe7dc352907fc980b868725387e98561b54ec8adb24d87ab3f90e348e4b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603c353dbc3db60d416680061fccec8c", "guid": "bfdfe7dc352907fc980b868725387e98eb850c90d9f07a8d42e439ed2d7ca2cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986597bf074d2f3ea013e4c671c9b30731", "guid": "bfdfe7dc352907fc980b868725387e98639b7d985f2c1c8162f27074a81c7155"}], "guid": "bfdfe7dc352907fc980b868725387e98ff727cb754da3253c0985614bb944d84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98cc4f3144fc17687513d156344c8098db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98fdad3c02e1f9dee7c26978e78a22efd8"}], "guid": "bfdfe7dc352907fc980b868725387e9813ab334ddfc771860d0046b5efc152af", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98829c8b0a6498603fb2a2979b49d5c4fd", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98c7ae5e201ee51f0e56a47e5bd8c18011", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}