import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/local/auth_token_local_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/models/get_token_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/local_storage_service/local_storage_service.dart';

class AuthTokenLocalDataSourceImpl implements AuthTokenLocalDataSource {
  final LocalStorageService _localStorageService;

  AuthTokenLocalDataSourceImpl(this._localStorageService);

  @override
  GetTokenModel? getTokenDetails() {
    try {
      final items = _localStorageService.getAllItems<GetTokenModel>(HiveModelConstants.getTokenBoxName);
      return items.isNotEmpty ? items.first : null;
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveTokenDetails(GetTokenModel getTokenModel) async {
    try {
      await _localStorageService.clearContainer(HiveModelConstants.getTokenBoxName);
      await _localStorageService.addItem<GetTokenModel>(HiveModelConstants.getTokenBoxName, getTokenModel);
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
    }
  }
}
