// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_settings_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CallSettingsModelAdapter extends TypeAdapter<CallSettingsModel> {
  @override
  final int typeId = 8;

  @override
  CallSettingsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CallSettingsModel(
      callType: fields[0] as CallType?,
    );
  }

  @override
  void write(BinaryWriter writer, CallSettingsModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.callType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallSettingsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CallSettingsModel _$CallSettingsModelFromJson(Map<String, dynamic> json) =>
    CallSettingsModel(
      callType: $enumDecodeNullable(_$CallTypeEnumMap, json['callType']),
    );

Map<String, dynamic> _$CallSettingsModelToJson(CallSettingsModel instance) =>
    <String, dynamic>{
      'callType': _$CallTypeEnumMap[instance.callType],
    };

const _$CallTypeEnumMap = {
  CallType.notSet: 0,
  CallType.direct: 1,
  CallType.ivr: 2,
};
