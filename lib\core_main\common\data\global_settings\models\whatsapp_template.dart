import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/template_model.dart';

part 'whatsapp_template.g.dart';

@HiveType(typeId: HiveModelConstants.whatsappTemplateModelTypeId)
@JsonSerializable()
class WhatsAppTemplates {
  @HiveField(0)
  final List<Template>? templates;

  WhatsAppTemplates({this.templates});

  factory WhatsAppTemplates.fromJson(Map<String, dynamic> json) => _$WhatsAppTemplatesFromJson(json);

  Map<String, dynamic> toJson() => _$WhatsAppTemplatesToJson(this);
}
