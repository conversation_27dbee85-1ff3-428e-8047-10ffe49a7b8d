import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';

class ChipItemSelectorCubit<T> extends Cubit<ChipItemSelectorState<T>> {
  ChipItemSelectorCubit({
    required bool isMultipleSelectionEnabled,
    List<ItemSimpleModel<T>>? initialSelectedItems,
  }) : super(ChipItemSelectorState<T>(
          selectedItems: initialSelectedItems ?? [],
          isMultipleSelectionEnabled: isMultipleSelectionEnabled,
        ));

  void selectItem(ItemSimpleModel<T> item) {
    final currentSelectedItems = List<ItemSimpleModel<T>>.from(state.selectedItems);

    if (state.isMultipleSelectionEnabled) {
      if (state.isSelected(item)) {
        currentSelectedItems.removeWhere((selectedItem) => selectedItem.title == item.title);
      } else {
        currentSelectedItems.add(item);
      }
    } else {
      if (state.isSelected(item)) {
        currentSelectedItems.clear();
      } else {
        currentSelectedItems.clear();
        currentSelectedItems.add(item);
      }
    }

    emit(state.copyWith(selectedItems: currentSelectedItems));
  }

  void updateSelection(List<ItemSimpleModel<T>>? newSelectedItems) {
    if (newSelectedItems == null) {
      emit(state.copyWith(selectedItems: []));
    } else {
      emit(state.copyWith(selectedItems: newSelectedItems));
    }
  }

  void clearSelection() {
    emit(state.copyWith(selectedItems: []));
  }

  void setSelectedItems(List<ItemSimpleModel<T>> items) {
    if (state.isMultipleSelectionEnabled) {
      emit(state.copyWith(selectedItems: items));
    } else if (items.isNotEmpty) {
      emit(state.copyWith(selectedItems: [items.first]));
    }
  }

  List<ItemSimpleModel<T>> getSelectedItems() {
    return state.selectedItems;
  }

  ItemSimpleModel<T>? getSelectedItem() {
    return state.selectedItems.isNotEmpty ? state.selectedItems.first : null;
  }
}

class ChipItemSelectorState<T> {
  final List<ItemSimpleModel<T>> selectedItems;
  final bool isMultipleSelectionEnabled;

  const ChipItemSelectorState({
    required this.selectedItems,
    required this.isMultipleSelectionEnabled,
  });

  ChipItemSelectorState<T> copyWith({
    List<ItemSimpleModel<T>>? selectedItems,
    bool? isMultipleSelectionEnabled,
  }) {
    return ChipItemSelectorState<T>(
      selectedItems: selectedItems ?? this.selectedItems,
      isMultipleSelectionEnabled: isMultipleSelectionEnabled ?? this.isMultipleSelectionEnabled,
    );
  }

  bool isSelected(ItemSimpleModel<T> item) {
    return selectedItems.any((selectedItem) => selectedItem.title == item.title);
  }
}
