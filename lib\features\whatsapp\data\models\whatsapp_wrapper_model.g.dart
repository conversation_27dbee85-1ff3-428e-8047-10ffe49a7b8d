// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whatsapp_wrapper_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WhatsappWrapperModel _$WhatsappWrapperModelFromJson(
        Map<String, dynamic> json) =>
    WhatsappWrapperModel(
      waTemplates: (json['waTemplates'] as List<dynamic>?)
          ?.map(
              (e) => WhatsappTemplateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      message: json['message'] as String?,
      customerId: json['customerId'] as String?,
      customerNo: json['customerNo'] as String?,
      userId: json['userId'] as String?,
      waApiInfoForTextMessaging: json['waApiInfoForTextMessaging'] == null
          ? null
          : WhatsappApiInfoModel.fromJson(
              json['waApiInfoForTextMessaging'] as Map<String, dynamic>),
      waPayloadMapping: json['waPayloadMapping'] == null
          ? null
          : WhatsappPayLoadMappingModel.fromJson(
              json['waPayloadMapping'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WhatsappWrapperModelToJson(
        WhatsappWrapperModel instance) =>
    <String, dynamic>{
      if (instance.waTemplates case final value?) 'waTemplates': value,
      if (instance.message case final value?) 'message': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.customerNo case final value?) 'customerNo': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.waApiInfoForTextMessaging case final value?)
        'waApiInfoForTextMessaging': value,
      if (instance.waPayloadMapping case final value?)
        'waPayloadMapping': value,
    };

WhatsappTemplateModel _$WhatsappTemplateModelFromJson(
        Map<String, dynamic> json) =>
    WhatsappTemplateModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      jsonBody: json['jsonBody'] as String?,
      message: json['message'] as String?,
      header: json['header'] as String?,
      footer: json['footer'] as String?,
      mediaUrl: json['mediaURL'] as String?,
      mediaType: json['mediaType'] as String?,
      waApiInfo: json['waApiInfo'] == null
          ? null
          : WhatsappApiInfoModel.fromJson(
              json['waApiInfo'] as Map<String, dynamic>),
      headerValues: (json['headerValues'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(int.parse(k), e as String),
      ),
      bodyValues: (json['bodyValues'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(int.parse(k), e as String),
      ),
    );

Map<String, dynamic> _$WhatsappTemplateModelToJson(
        WhatsappTemplateModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.jsonBody case final value?) 'jsonBody': value,
      if (instance.message case final value?) 'message': value,
      if (instance.header case final value?) 'header': value,
      if (instance.footer case final value?) 'footer': value,
      if (instance.mediaUrl case final value?) 'mediaURL': value,
      if (instance.mediaType case final value?) 'mediaType': value,
      if (instance.waApiInfo case final value?) 'waApiInfo': value,
      if (instance.headerValues?.map((k, e) => MapEntry(k.toString(), e))
          case final value?)
        'headerValues': value,
      if (instance.bodyValues?.map((k, e) => MapEntry(k.toString(), e))
          case final value?)
        'bodyValues': value,
    };

WhatsappApiInfoModel _$WhatsappApiInfoModelFromJson(
        Map<String, dynamic> json) =>
    WhatsappApiInfoModel(
      id: json['id'] as String?,
      url: json['url'] as String?,
      methodType: json['methodType'] as String?,
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      shouldUseForTextMessaging: json['shouldUseForTextMessaging'] as bool?,
      jsonPayload: json['jsonPayload'] as String?,
      headerTypes: (json['headerTypes'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
    );

Map<String, dynamic> _$WhatsappApiInfoModelToJson(
        WhatsappApiInfoModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.url case final value?) 'url': value,
      if (instance.methodType case final value?) 'methodType': value,
      if (instance.headers case final value?) 'headers': value,
      if (instance.shouldUseForTextMessaging case final value?)
        'shouldUseForTextMessaging': value,
      if (instance.jsonPayload case final value?) 'jsonPayload': value,
      if (instance.headerTypes case final value?) 'headerTypes': value,
    };

WhatsappPayLoadMappingModel _$WhatsappPayLoadMappingModelFromJson(
        Map<String, dynamic> json) =>
    WhatsappPayLoadMappingModel(
      serviceProviderName: json['serviceProviderName'] as String?,
      statusMapping: (json['statusMapping'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      webhookMapping: (json['webhookMapping'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      responseMapping: (json['responseMapping'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      outboundMapping: (json['outboundMapping'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
    );

Map<String, dynamic> _$WhatsappPayLoadMappingModelToJson(
        WhatsappPayLoadMappingModel instance) =>
    <String, dynamic>{
      if (instance.serviceProviderName case final value?)
        'serviceProviderName': value,
      if (instance.statusMapping case final value?) 'statusMapping': value,
      if (instance.webhookMapping case final value?) 'webhookMapping': value,
      if (instance.responseMapping case final value?) 'responseMapping': value,
      if (instance.outboundMapping case final value?) 'outboundMapping': value,
    };
