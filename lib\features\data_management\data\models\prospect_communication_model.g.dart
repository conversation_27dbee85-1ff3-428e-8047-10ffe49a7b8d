// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prospect_communication_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProspectCommunicationModel _$ProspectCommunicationModelFromJson(
        Map<String, dynamic> json) =>
    ProspectCommunicationModel(
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
      message: json['message'] as String?,
      prospectId: json['prospectId'] as String?,
    );

Map<String, dynamic> _$ProspectCommunicationModelToJson(
        ProspectCommunicationModel instance) =>
    <String, dynamic>{
      'contactType': _$ContactTypeEnumMap[instance.contactType],
      'message': instance.message,
      'prospectId': instance.prospectId,
    };

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};
