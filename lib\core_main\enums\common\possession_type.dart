import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'index')
enum PossessionType {
  none(0, 'None', 0),
  underConstruction(1, 'Under Construction', 0),
  sixMonths(2, '6 Months', 6),
  oneYear(3, 'One Year', 12),
  twoYear(4, 'Two Year', 24),
  customDate(5, 'Custom', 0);

  final int value;
  final String description;
  final int month;

  const PossessionType(this.value, this.description, this.month);
}

@JsonEnum(valueField: 'value')
enum PropertyListingPossessionType {
  none(0, 'None'),
  underConstruction(1, 'Under Construction'),
  sixMonths(2, '6 Months'),
  oneYear(3, 'One Year'),
  twoYear(4, 'Two Year'),
  customDate(5, 'Select Date',),
  immediate(6, "Immediate");

  final int value;
  final String description;

  const PropertyListingPossessionType(this.value, this.description);
}


PossessionType? mapPossessionType(PropertyListingPossessionType? propertyListingType) {
  if (propertyListingType == null) return null;

  switch (propertyListingType) {
    case PropertyListingPossessionType.none:
      return PossessionType.none;
    case PropertyListingPossessionType.underConstruction:
      return PossessionType.underConstruction;
    case PropertyListingPossessionType.sixMonths:
      return PossessionType.sixMonths;
    case PropertyListingPossessionType.oneYear:
      return PossessionType.oneYear;
    case PropertyListingPossessionType.twoYear:
      return PossessionType.twoYear;
    case PropertyListingPossessionType.customDate:
      return PossessionType.customDate;
    case PropertyListingPossessionType.immediate:
      return PossessionType.none;
  }
}