import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/bloc/message_templates_bloc/message_template_bloc.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/pages/share_skeleton_view.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/common/widgets/selectable_template_show_model_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/app_feature.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/module_name.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/widget_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';

class MessageTemplatePage extends LeadratStatefulWidget {
  final ContactType? contactType;
  final AppFeature? appFeature;

  final String? phoneNumber;
  final String? email;
  final List<GetLeadEntity?> getLeadEntities;
  final List<ProspectEntity?> getProspectEntities;

  const MessageTemplatePage({super.key, this.appFeature = AppFeature.leadInfo, this.contactType, this.phoneNumber, this.email, this.getLeadEntities = const [], this.getProspectEntities = const []});

  @override
  State<MessageTemplatePage> createState() => _MessageTemplatePageState();
}

class _MessageTemplatePageState extends LeadratState<MessageTemplatePage> {
  final leadMessageTemplateBloc = getIt<MessageTemplateBloc>();

  @override
  void onInit() {
    super.onInit();
    leadMessageTemplateBloc.add(MessageTemplateInitialEvent(leadEntities: widget.getLeadEntities, getProspectEntities: widget.getProspectEntities));
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplatePageView);
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<MessageTemplateBloc, MessageTemplateState>(
      listener: (context, state) {
        if (state.pageState == PageState.loading && state.errorMessage != null) {
          DialogManager().showTransparentProgressDialog(context, message: state.errorMessage ?? 'fetching ${leadMessageTemplateBloc.category.description} template messages');
        } else if (state.pageState == PageState.success && state.errorMessage != null) {
          DialogManager().hideTransparentProgressDialog();
        } else if (state.pageState == PageState.failure && state.errorMessage != null) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: state.errorMessage ?? "unable to get ${leadMessageTemplateBloc.category.description} template messages");
        }
      },
      builder: (context, state) {
        if (state.pageState == PageState.loading && state.errorMessage == null) {
          return ShareSkeletonView();
        } else if (state.pageState == PageState.success || (state.pageState == PageState.loading && state.errorMessage != null)) {
          return safeAreaWidget(
              state: state,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(left: 24, right: 14),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 15),
                      Text('${widget.appFeature == AppFeature.leadInfo ? 'lead' : 'data'} info', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                      Container(
                        width: context.width(100),
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.only(top: 10, bottom: 20),
                        decoration: BoxDecoration(color: ColorPalette.darkToneInk, border: Border.all(width: 1, color: ColorPalette.primaryLightColor), borderRadius: BorderRadius.circular(5)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            commonTextWidget('Name: ', state.leadNames),
                            const SizedBox(height: 5),
                            if (widget.phoneNumber != null) commonTextWidget('Mobile: ', widget.phoneNumber),
                            if (widget.email != null) commonTextWidget('Email: ', widget.email),
                          ],
                        ),
                      ),
                      getCategoriesWidget(state),
                      Padding(
                        padding: const EdgeInsets.only(top: 15),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (leadMessageTemplateBloc.category != AppModuleName.lead) selectPropertyOrProjectWidget(state),
                            Text('message template', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                            GestureDetector(
                              onTap: () {
                                if (leadMessageTemplateBloc.category != AppModuleName.lead && state.selectedPropertyOrProjects.isEmpty) {
                                  LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "kindly select ${leadMessageTemplateBloc.category.name}");
                                } else {
                                  selectableTemplateShowModelBottomSheet(context: context, getProspectEntities: widget.getProspectEntities, getLeadEntities: widget.getLeadEntities);
                                  if (leadMessageTemplateBloc.category == AppModuleName.lead) {
                                    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonLeadSelectTemplateClick);
                                  } else if (leadMessageTemplateBloc.category == AppModuleName.property) {
                                    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonPropertySelectTemplateClick);
                                  } else if (leadMessageTemplateBloc.category == AppModuleName.project) {
                                    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonProjectSelectTemplateClick);
                                  }
                                }
                              },
                              child: Container(
                                width: context.width(100),
                                padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                                margin: const EdgeInsets.only(top: 10, bottom: 20),
                                decoration: BoxDecoration(
                                  color: ColorPalette.primaryColor,
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(width: 1, color: ColorPalette.lightBackground),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(state.selectedTemplate?.title ?? 'Select Template', style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primary)),
                                    const Icon(Icons.keyboard_arrow_down, size: 25, color: ColorPalette.primary),
                                  ],
                                ),
                              ),
                            ),
                            Text('message', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary)),
                            Container(
                              margin: const EdgeInsets.only(top: 10),
                              width: context.width(100),
                              decoration: BoxDecoration(
                                color: ColorPalette.primaryColor,
                                borderRadius: BorderRadius.circular(5.0),
                                border: Border.all(width: 1, color: ColorPalette.lightBackground),
                              ),
                              child: TextField(
                                cursorColor: ColorPalette.primaryGreen,
                                controller: leadMessageTemplateBloc.messageTextEditingController,
                                decoration: InputDecoration(enabledBorder: InputBorder.none, focusedBorder: InputBorder.none, hintText: 'Please Select Template', hintStyle: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.lightBackground)),
                                maxLines: 10,
                                style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.white),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ));
        } else {
          return safeAreaWidget(
              child: const Center(
                child: Text('No Template Found', style: TextStyle(color: ColorPalette.red, fontSize: 15)),
              ),
              state: state);
        }
      },
    );
  }

  Widget getCategoriesWidget(MessageTemplateState state) {
    return Container(
      height: context.height(5.3),
      width: context.width(64),
      decoration: BoxDecoration(color: ColorPalette.primaryLightColor, borderRadius: BorderRadius.circular(15)),
      child: ListView.builder(
        itemCount: state.templatesCategories.length,
        padding: const EdgeInsets.all(5),
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) => GestureDetector(
          onTap: () {
            leadMessageTemplateBloc.add(SelectCategoryEvent(selectedCategory: state.templatesCategories[index].value ?? AppModuleName.lead));
            if (state.templatesCategories[index].value == AppModuleName.lead) getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonLeadClick);
            if (state.templatesCategories[index].value == AppModuleName.property) getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonPropertyClick);
            if (state.templatesCategories[index].value == AppModuleName.project) getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonProjectClick);
          },
          child: Container(
            height: context.height(2.5),
            width: context.width(18),
            margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(color: state.templatesCategories[index].isSelected ? ColorPalette.primaryGreen : ColorPalette.transparent, borderRadius: BorderRadius.circular(10)),
            child: Center(
                child: Text(
              state.templatesCategories[index].title,
              style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primary),
            )),
          ),
        ),
      ),
    );
  }

  Widget selectPropertyOrProjectWidget(MessageTemplateState state) {
    String projectsOrPropertiesTitles = state.selectedPropertyOrProjects.map((e) => e.title).join(', ');
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      RichText(
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        text: TextSpan(
          children: [
            TextSpan(text: "select ${leadMessageTemplateBloc.category.name}", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary)),
            TextSpan(text: ' *', style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.white.withOpacity(0.8))),
          ],
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(top: 10),
        child: SelectableItemBottomSheet(
          title: "select ${leadMessageTemplateBloc.category.name}",
          selectableItems: leadMessageTemplateBloc.category == AppModuleName.property ? state.propertiesList : state.projectsList,
          onItemsSelected: (selectedItems) {
            leadMessageTemplateBloc.add(SelectPropertyOrProjectEvent(selectedProjectOrProperties: selectedItems));
            if (leadMessageTemplateBloc.category == AppModuleName.property) {
              getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonSelectPropertyClick);
            } else if (leadMessageTemplateBloc.category == AppModuleName.project) {
              getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonSelectProjectClick);
            }
          },
          isMultipleSelection: true,
          canSearchItems: true,
          child: Container(
            width: context.width(100),
            padding: const EdgeInsets.only(left: 10, right: 18, top: 10, bottom: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(width: 1, color: ColorPalette.lightBackground),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  state.selectedPropertyOrProjects.isEmpty ? 'select' : projectsOrPropertiesTitles,
                  style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ).withFlexible(),
                const SizedBox(width: 10),
                const Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: ColorPalette.primary,
                ),
              ],
            ),
          ),
        ),
      ),
      const SizedBox(height: 15),
    ]);
  }

  Widget safeAreaWidget({required Widget child, required MessageTemplateState state}) {
    return SafeArea(
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(60.0),
            child: Stack(
              children: [
                AppBar(
                  centerTitle: false,
                  titleSpacing: 0.0,
                  leadingWidth: 0,
                  backgroundColor: ColorPalette.darkToneInk,
                  toolbarHeight: 60.0,
                  automaticallyImplyLeading: false,
                  title: Padding(
                    padding: const EdgeInsets.fromLTRB(24, 30, 14, 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'send ${widget.contactType?.description ?? ''}',
                          overflow: TextOverflow.visible,
                          style: LexendTextStyles.lexend16Bold.copyWith(color: ColorPalette.tertiaryTextColor),
                        ),
                        const SizedBox(width: 25, child: Divider(height: 1.5, color: ColorPalette.primary)),
                      ],
                    ),
                  ),
                ),
                Positioned(bottom: -24.0, right: 0.0, child: SizedBox(height: 87.09, width: 83.39, child: Image.asset("assets/images/image_share_pattern.png"))),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: const EdgeInsets.symmetric(vertical: 15),
            decoration: const BoxDecoration(border: Border(top: BorderSide(width: 0.4, color: ColorPalette.lightBackground))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LeadratFormButton(
                    onPressed: () {
                      Navigator.pop(context);
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonBackClick);
                    },
                    buttonText: 'back',
                    isShadowVisible: false),
                const SizedBox(width: 20),
                LeadratFormButton(
                    onPressed: () {
                      leadMessageTemplateBloc.add(SendTemplateEvent(
                        contactType: widget.contactType,
                        id: widget.appFeature == AppFeature.leadInfo ? widget.getLeadEntities.firstOrNull?.id ?? '' : widget.getProspectEntities.firstOrNull?.id ?? '',
                        phoneNumber: widget.phoneNumber,
                        email: widget.email,
                        templateTitle: state.selectedTemplate?.title,
                      ));
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileShareTemplateButtonSendMessageClick);
                      Navigator.pop(context);
                    },
                    buttonText: 'send message',
                    isTrailingVisible: true,
                    isShadowVisible: false),
              ],
            ),
          ),
          body: child),
    );
  }

  Widget commonTextWidget(String text1, String? text2) {
    return RichText(
      text: TextSpan(
        text: text1,
        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor),
        children: [
          TextSpan(
            text: text2 ?? '--',
            style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.tertiaryTextColor),
          )
        ],
      ),
    );
  }
}
