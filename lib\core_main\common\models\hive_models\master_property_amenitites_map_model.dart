import 'package:hive_flutter/hive_flutter.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';

part 'master_property_amenitites_map_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterPropertyAmenititesMapModelTypeId)
class MasterPropertyAmenititesMapModel {
  @HiveField(0)
  final String key;

  @HiveField(1)
  final List<MasterPropertyAmenitiesModel> values;

  MasterPropertyAmenititesMapModel({required this.key, required this.values});
}
