import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/leading_icon_with_text.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_date_picker.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/lr_radio_option.dart';
import 'package:leadrat/core_main/common/widgets/lrb_phone_field/lrb_phone_field.dart';
import 'package:leadrat/core_main/common/widgets/preview_dialogue_widget.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/taxation_mode.dart';
import 'package:leadrat/core_main/enums/property_enums/basic_attributes_value_type_enum.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/validator_utils.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/basic_info_bloc/basic_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/property_info_tab_bloc/property_info_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_info_tab.dart';

import '../../../../core_main/common/base/presentation/leadrat_stateless_widget.dart';
import '../../../../core_main/common/widgets/leadrat_form_button.dart';
import '../../../../core_main/common/widgets/leadrat_text_form_field.dart';
import '../../../../core_main/common/widgets/selectable_item_bottom_sheet.dart';
import '../../../../core_main/di/injection_container.dart';
import '../../../../core_main/resources/theme/color_palette.dart';
import '../../../../core_main/resources/theme/text_styles.dart';
import '../../../search_location/presentation/pages/search_location_page.dart';

class PropertyInfoTab extends LeadratStatelessWidget {
  final GlobalKey<FormState> propertyInfoTabFormKey;
  final propertyInfoTabBloc = getIt<PropertyInfoTabBloc>();
  final BuildContext context;
  final PropertyInfoTabState state;

  PropertyInfoTab(this.context, this.state, {super.key, required this.propertyInfoTabFormKey});

  @override
  Widget buildContent(BuildContext context) {
    return LeadratForm(
        formKey: propertyInfoTabFormKey,
        wrapWithScrollView: false,
        trailingButton: LeadratFormButton(
            onPressed: () {
              if (propertyInfoTabFormKey.currentState?.validate() ?? false) {
                propertyInfoTabBloc.add(NavigateToAttributeTabEvent());
              }
            },
            buttonText: "save & continue",
            isTrailingVisible: true),
        leadingButton: LeadratFormButton(onPressed: () => propertyInfoTabBloc.add(NavigateBackToBasicInfoTabEvent()), buttonText: "back"),
        child: SingleChildScrollView(
            child: Padding(
          padding: const EdgeInsets.fromLTRB(0, 20, 0, 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...[
                Text(
                  propertyInfoTabBloc.isRentOrLeaseSelected ? "possession needed By" : "possession type",
                  style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                ),
                const SizedBox(height: 10),
                if (propertyInfoTabBloc.isRentOrLeaseSelected)
                  LeadratDatePicker(
                    showIconAtPrefix: true,
                    selectedDate: state.possessionAvailablity,
                    onDateSelected: (value) {
                      propertyInfoTabBloc.add(SelectPossessionType(state.possessionTypeSelectableItems?.firstWhereOrNull((element) => element.value == PossessionType.customDate)));
                      propertyInfoTabBloc.add(SelectPossessionDateEvent(value));
                    },
                  )
                else ...[
                  SelectableItemBottomSheet<PossessionType?>(
                    title: "select possession type ",
                    selectableItems: state.possessionTypeSelectableItems ?? [],
                    selectedItem: state.possessionTypeSelectedItem,
                    onItemSelected: (selectedValue) {
                      propertyInfoTabBloc.add(SelectPossessionType(selectedValue));
                    },
                    child: Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(
                          color: const Color(0xFFE0E0E0),
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: MediaQuery.of(context).size.width * 0.15,
                            margin: const EdgeInsets.all(2),
                            height: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: ColorPalette.primaryTextColor,
                            ),
                            child: const Icon(Icons.calendar_today),
                          ),
                          const SizedBox(width: 7),
                          Text(
                            state.possessionTypeSelectedItem?.title ?? 'select possession type',
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.primaryDarkColor,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  if (state.isPossessionDateCustomSelected) ...[
                    const SizedBox(height: 20),
                    Text(
                      "possession date",
                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                    ),
                    const SizedBox(height: 10),
                    LeadratMonthPicker(
                      selectedDate: state.possessionAvailablity,
                      onMonthSelected: (value) {
                        propertyInfoTabBloc.add(SelectPossessionDateEvent(value));
                      },
                    ),
                  ],
                ]
              ],

              const SizedBox(height: 25),

              // Maintenance cost
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LeadratTextFormField(
                    keyboardType: TextInputType.number,
                    labelText: "maintenance cost",
                    prefixIcon: SelectableItemBottomSheet<String?>(
                      title: 'select currency',
                      onItemSelected: (value) {
                        getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyMaintenanceCost);
                        propertyInfoTabBloc.add(SelectCurrencyEvent(value));
                      },
                      selectableItems: state.currencies ?? [],
                      canSearchItems: true,
                      child: Container(
                        width: context.width(11),
                        decoration: const BoxDecoration(
                          color: ColorPalette.superSilver,
                          borderRadius: BorderRadius.all(Radius.circular(4)),
                        ),
                        child: Center(
                            child: Text(
                          state.currency ?? (propertyInfoTabBloc.isPropertyListingEnabled ? state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED" : (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR')),
                          style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                        )),
                      ),
                    ),
                    controller: propertyInfoTabBloc.maintennaceCostController,
                    showBudgetInWords: true,
                    hintText: "ex. 24000",
                  ),
                ],
              ),
              const SizedBox(height: 15),

              if (propertyInfoTabBloc.isPropertyListingEnabled) ...[
                // Service Changes
                LeadratTextFormField(
                  keyboardType: TextInputType.number,
                  controller: propertyInfoTabBloc.serviceChargesController,
                  labelText: "Service charges",
                  hintText: "ex. 10000",
                  prefixIcon: SelectableItemBottomSheet<String?>(
                    title: 'select currency',
                    onItemSelected: (value) {
                      propertyInfoTabBloc.add(SelectCurrencyEvent(value));
                    },
                    selectableItems: state.currencies ?? [],
                    canSearchItems: true,
                    child: Container(
                      width: context.width(11),
                      decoration: const BoxDecoration(
                        color: ColorPalette.superSilver,
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                      child: Center(
                          child: Text(
                        state.currency ?? (propertyInfoTabBloc.isPropertyListingEnabled ? state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED" : (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR')),
                        style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                      )),
                    ),
                  ),
                ),
                const SizedBox(height: 15),
              ] else if ((getIt<BasicInfoBloc>().isRentOrLeaseSelected ?? false) && (!(state.isCoWorkingOfficeSpaceSelected ?? false) && !(state.isOfficeSpaceSelected ?? false))) ...[
                // Deposit/Security Amount
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("deposit/security amount", style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                  ],
                ),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LeadratTextFormField(
                      keyboardType: TextInputType.number,
                      prefixIcon: SelectableItemBottomSheet<String?>(
                        title: 'select currency',
                        onItemSelected: (value) {
                          propertyInfoTabBloc.add(SelectDepositCurrencyEvent(value));
                        },
                        selectableItems: state.currencies ?? [],
                        canSearchItems: true,
                        child: Container(
                          width: context.width(11),
                          decoration: const BoxDecoration(
                            color: ColorPalette.superSilver,
                            borderRadius: BorderRadius.all(Radius.circular(4)),
                          ),
                          child: Center(
                              child: Text(
                            state.depositCurrency ?? (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'),
                            style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                          )),
                        ),
                      ),
                      controller: propertyInfoTabBloc.depositAndSecurityController,
                      showBudgetInWords: true,
                      hintText: "ex. 30000",
                    ),
                  ],
                ),
                const SizedBox(height: 15),
              ],
              // Total Price

              Row(
                mainAxisSize: MainAxisSize.max,
                // crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: RichText(
                                text: TextSpan(
                                  style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                  children: [
                                    TextSpan(text: propertyInfoTabBloc.isRentOrLeaseSelected ? "Rent Amount" : "total price "),
                                    const TextSpan(text: "* ", style: TextStyle(color: ColorPalette.red)),
                                    if (state.globalSettings?.shouldEnablePropertyListing ?? false) const TextSpan(text: "per month (incl. of other charges)", style: TextStyle(color: ColorPalette.primaryWhite300TextColor)),
                                  ],
                                ),
                              ),
                            ),
                            if (!propertyInfoTabBloc.isRentOrLeaseSelected) ...[
                              const SizedBox(width: 10),
                              Row(
                                children: [
                                  Checkbox(
                                    visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                                    value: state.isNegotiable ?? false,
                                    onChanged: (value) {
                                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyNegotiableClick);
                                      propertyInfoTabBloc.add(ToggleIsNegotiableEvent(value));
                                    },
                                    activeColor: ColorPalette.primaryGreen,
                                  ),
                                  Text(
                                    'Negotiable',
                                    style: LexendTextStyles.lexend13Medium.copyWith(color: ColorPalette.primaryWhite300TextColor),
                                  ),
                                ],
                              ),
                            ]
                          ],
                        ),
                        const SizedBox(height: 4),
                        LeadratTextFormField(
                          keyboardType: TextInputType.number,
                          prefixIcon: SelectableItemBottomSheet<String?>(
                            title: 'select currency',
                            onItemSelected: (value) {
                              propertyInfoTabBloc.add(SelectCurrencyEvent(value));
                            },
                            selectableItems: state.currencies ?? [],
                            canSearchItems: true,
                            child: Container(
                              width: context.width(11),
                              decoration: const BoxDecoration(
                                color: ColorPalette.superSilver,
                                borderRadius: BorderRadius.all(Radius.circular(4)),
                              ),
                              child: Center(
                                  child: Text(
                                state.currency ?? (propertyInfoTabBloc.isPropertyListingEnabled ? state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED" : (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR')),
                                style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                              )),
                            ),
                          ),
                          controller: propertyInfoTabBloc.totalPriceController,
                          showBudgetInWords: true,
                          hintText: "ex. 24000",
                        ),
                      ],
                    ),
                  ),
                  if (propertyInfoTabBloc.isRentOrLeaseSelected) ...[
                    const SizedBox(width: 10),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Checkbox(
                              visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                              value: state.isNegotiable ?? false,
                              onChanged: (value) {
                                propertyInfoTabBloc.add(ToggleIsNegotiableEvent(value));
                              },
                              activeColor: ColorPalette.primaryGreen,
                            ),
                            Text(
                              'Negotiable',
                              style: LexendTextStyles.lexend13Medium.copyWith(color: ColorPalette.primaryWhite300TextColor),
                            ),
                          ],
                        ),
                        Transform.translate(
                          offset: const Offset(0, -10),
                          child: SelectableItemBottomSheet(
                            title: "select payment frequency",
                            childPadding: const EdgeInsets.only(top: 20),
                            selectableItems: state.selectablePaymentFrequency,
                            selectedItem: state.selectedPaymentFrequency,
                            onItemSelected: (selectedValue) => propertyInfoTabBloc.add((SelectPaymentFrequency(selectedValue))),
                          ),
                        ),
                        const SizedBox(height: 25),
                      ],
                    ),
                  ],
                ],
              ),
              //GST

              if (!propertyInfoTabBloc.isPropertyListingEnabled)
                Transform.translate(
                  offset: const Offset(0, -10),
                  child: LrRadioOptions<TaxationMode>(
                    items: [
                      RadioOptionItem(
                        value: TaxationMode.gstInclusive,
                        label: TaxationMode.gstInclusive.description,
                      ),
                      RadioOptionItem(
                        value: TaxationMode.gstExclusive,
                        label: TaxationMode.gstExclusive.description,
                      ),
                    ],
                    initialValue: state.taxationMode,
                    layout: RadioLayout.horizontal,
                    onChanged: (value) {
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyTaxationMode);
                      propertyInfoTabBloc.add(ChangeTaxationModeEvent(value));
                    },
                  ),
                ),

              const SizedBox(height: 10),
              //No of cheques
              if (propertyInfoTabBloc.isPropertyListingEnabled) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'number of cheques',
                      style: LexendTextStyles.lexend12SemiBold.copyWith(
                        color: ColorPalette.primaryDarkColor,
                      ),
                    ),
                    Row(
                      children: [
                        const SizedBox(
                          width: 10,
                        ),
                        SelectableItemBottomSheet<int>(
                          key: const ValueKey('cheques'),
                          title: "select number of cheques",
                          onItemSelected: (value) => propertyInfoTabBloc.add(SelectNoOfChequeEvent(value)),
                          selectableItems: state.numberOfCheques ?? [],
                          selectedItem: state.selectedNumberOfCheque,
                          canSearchItems: true,
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 15),
              ],
              if ((state.isOfficeSpaceSelected ?? false) || (state.isCoWorkingOfficeSpaceSelected ?? false))
                Column(children: [
                  const SizedBox(height: 15),
                  if (propertyInfoTabBloc.isPropertyListingEnabled)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Security Deposit", style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                        Row(
                          children: [
                            Checkbox(
                                value: state.isSecurityAmountPercentageSelected,
                                activeColor: const Color(0xff439B89),
                                onChanged: (bool? newValue) {
                                  propertyInfoTabBloc.add(ToggleSecurityAmount(newValue));
                                }),
                            Text(
                              '%',
                              style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                            )
                          ],
                        ),
                      ],
                    ),
                  if (propertyInfoTabBloc.isPropertyListingEnabled)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LeadratTextFormField(
                          keyboardType: TextInputType.number,
                          prefixIcon: state.isSecurityAmountPercentageSelected
                              ? Container(
                                  width: context.width(11),
                                  decoration: const BoxDecoration(
                                    color: ColorPalette.superSilver,
                                    borderRadius: BorderRadius.all(Radius.circular(4)),
                                  ),
                                  child: Center(
                                    child: Text(
                                      "%",
                                      style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                    ),
                                  ),
                                )
                              : SelectableItemBottomSheet<String?>(
                                  title: 'select currency',
                                  onItemSelected: (value) {
                                    propertyInfoTabBloc.add(SelectDepositCurrencyEvent(value));
                                  },
                                  selectableItems: state.currencies ?? [],
                                  canSearchItems: true,
                                  child: Container(
                                    width: context.width(11),
                                    decoration: const BoxDecoration(
                                      color: ColorPalette.superSilver,
                                      borderRadius: BorderRadius.all(Radius.circular(4)),
                                    ),
                                    child: Center(
                                        child: Text(
                                      state.depositCurrency ?? (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'),
                                      style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                    )),
                                  ),
                                ),
                          controller: propertyInfoTabBloc.depositAndSecurityController,
                          showBudgetInWords: true,
                          hintText: "ex. 30000",
                        ),
                      ],
                    ),
                  const SizedBox(height: 15),
                  //Lock in period//
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Lock In Period',
                              style: LexendTextStyles.lexend12SemiBold.copyWith(
                                color: ColorPalette.primaryDarkColor,
                              )),
                          SelectableItemBottomSheet<LockInPeriodType>(
                            key: const ValueKey('projects'),
                            title: "select lock in period",
                            onItemSelected: (value) => propertyInfoTabBloc.add(SelectLockInPeriodEvent(value)),
                            selectableItems: state.selectableLockInPeriodType ?? [],
                            selectedItem: state.selectedLockInPeriodType,
                            // canSearchItems: true,
                          ),
                        ],
                      ),
                      if (state.selectedLockInPeriodType != null && state.selectedLockInPeriodType?.title != 'Select')
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 10, right: 2),
                              child: GestureDetector(
                                onTap: () {
                                  propertyInfoTabBloc.add(ClearProjectEvent(clearOption: ClearOption.lockInPeriod));
                                },
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.remove_circle_outline_rounded,
                                      color: ColorPalette.red,
                                      size: 15,
                                    ),
                                    Text(
                                      'clear',
                                      style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.red),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  // Notice period
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Notice Period',
                            style: LexendTextStyles.lexend12SemiBold.copyWith(
                              color: ColorPalette.primaryDarkColor,
                            ),
                          ),
                          SelectableItemBottomSheet<NoticePeriodType>(
                            key: const ValueKey('projects'),
                            title: "select notice period",
                            onItemSelected: (value) => propertyInfoTabBloc.add(SelectNoticePeriodEvent(value)),
                            selectableItems: state.selectableNoticePeriodType ?? [],
                            selectedItem: state.selectedNoticePeriodType,
                            // canSearchItems: true,
                          ),
                        ],
                      ),
                      if (state.selectedNoticePeriodType != null && state.selectedNoticePeriodType?.title != 'Select')
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 10, right: 2),
                              child: GestureDetector(
                                onTap: () {
                                  propertyInfoTabBloc.add(ClearProjectEvent(clearOption: ClearOption.noticePeriod));
                                },
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.remove_circle_outline_rounded,
                                      color: ColorPalette.red,
                                      size: 15,
                                    ),
                                    Text(
                                      'clear',
                                      style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.red),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  // Escalation
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LeadratTextFormField(
                        prefixIcon: const Icon(Icons.percent),
                        hintText: '  ex. 10%',
                        labelText: "Escalation",
                        keyboardType: TextInputType.number,
                        maxLength: 2,
                        controller: propertyInfoTabBloc.escalationController,
                      )
                    ],
                  ),
                  // Tenanat POC Name
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LeadratTextFormField(
                        hintText: 'ex. sufiyan',
                        labelText: "Tenant POC Name",
                        controller: propertyInfoTabBloc.pocNameController,
                      )
                    ],
                  ),
                  if (state.isOfficeSpaceSelected ?? false)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LeadratTextFormField(
                          hintText: 'ex. engineer',
                          labelText: "Tenant POC Designation",
                          controller: propertyInfoTabBloc.pocDesignationController,
                        )
                      ],
                    ),
                  // Tenanat POC Phone Number
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LrbPhoneField(
                        hintText: 'ex. 999XXXXX',
                        controller: propertyInfoTabBloc.pocNumberController,
                        labelText: 'Tenant POC Phone Number',
                        selectedDialCode: state.defaultCountryCode,
                      )
                    ],
                  ),
                  // Common Area Size
                ]),
              // Co-working operator POC Name
              if (state.isCoWorkingOfficeSpaceSelected ?? false)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LeadratTextFormField(
                      hintText: 'ex. sufiyan',
                      labelText: "Co-working operator POC Name",
                      controller: propertyInfoTabBloc.coWorkingOperatorName,
                    ),
                    LrbPhoneField(
                      hintText: 'ex. 999XXXXX',
                      controller: propertyInfoTabBloc.coWorkingOperatorNumber,
                      labelText: 'Co-working operator POC Phone Number',
                      selectedDialCode: state.defaultCountryCode,
                    )
                  ],
                ),
              // Tenanat POC Phone Number
              if ((!propertyInfoTabBloc.isPropertyListingEnabled) && ((state.isCoWorkingOfficeSpaceSelected ?? false) || (state.isOfficeSpaceSelected ?? false)) && (propertyInfoTabBloc.isRentOrLeaseSelected))
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                        children: const [
                          TextSpan(text: "Common Area Charges "),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: context.width(60),
                          child: LeadratTextFormField(
                            keyboardType: TextInputType.number,
                            prefixIcon: SelectableItemBottomSheet<String?>(
                              title: 'select currency',
                              onItemSelected: (value) {
                                propertyInfoTabBloc.add(SelectCurrencyEvent(value));
                              },
                              selectableItems: state.currencies ?? [],
                              canSearchItems: true,
                              child: Container(
                                width: context.width(11),
                                decoration: const BoxDecoration(
                                  color: ColorPalette.superSilver,
                                  borderRadius: BorderRadius.all(Radius.circular(4)),
                                ),
                                child: Center(
                                    child: Text(
                                  state.currency ?? (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'),
                                  style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                )),
                              ),
                            ),
                            controller: propertyInfoTabBloc.commonAreaSizeController,
                            showBudgetInWords: true,
                            hintText: "ex. 24000",
                          ),
                        ),
                        SizedBox(
                            width: context.width(30),
                            child: SelectableItemBottomSheet<MasterAreaUnitsModel>(
                                title: "select common Area Size unit",
                                childPadding: EdgeInsets.only(top: context.width(1.5), left: 2),
                                selectableItems: state.selectableCommonAreaUnits ?? [],
                                selectedItem: state.selectedCommonAreaUnits,
                                onItemSelected: (selectedItem) {
                                  propertyInfoTabBloc.add(SelectCommonAreaUnitsEvent(selectableItem: selectedItem));
                                })),
                      ],
                    ),
                  ],
                ),

              const SizedBox(height: 10),
              if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewOwnerInfo)) getOwnerDetailsWidget(),
              const SizedBox(height: 15),
              // Select projects
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'select project',
                        style: LexendTextStyles.lexend12SemiBold.copyWith(
                          color: ColorPalette.primaryDarkColor,
                        ),
                      ),
                      Row(
                        children: [
                          const SizedBox(
                            width: 10,
                          ),
                          SelectableItemBottomSheet<String>(
                            key: const ValueKey('projects'),
                            title: "select a project",
                            onItemSelected: (value) {
                              getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyProjectClick);
                              propertyInfoTabBloc.add(SelectProjectEvent(value));
                            },
                            selectableItems: state.projects ?? [],
                            selectedItem: state.selectedProject,
                            canSearchItems: true,
                          ),
                        ],
                      ),
                    ],
                  ),
                  if (state.selectedProject != null && state.selectedProject?.title != 'Select')
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 10, right: 2),
                          child: GestureDetector(
                            onTap: () {
                              propertyInfoTabBloc.add(ClearProjectEvent(clearOption: ClearOption.project));
                            },
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.remove_circle_outline_rounded,
                                  color: ColorPalette.red,
                                  size: 15,
                                ),
                                Text(
                                  'clear',
                                  style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.red),
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(
                height: 22,
              ),
              // brokerage
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                          child: LeadratTextFormField(
                        hintText: "ex.10",
                        labelText: "brokerage amount",
                        controller: propertyInfoTabBloc.brokerageController,
                        showBudgetInWords: true,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) return null;
                          var carpet = double.tryParse(value);
                          if (carpet == null) return 'Invalid data';
                          return null;
                        },
                      )),
                      const SizedBox(width: 10),
                      SelectableItemBottomSheet<String>(
                          title: "select brokerage unit",
                          selectableItems: state.brokerageUnits ?? [],
                          selectedItem: state.brokerageUnit,
                          childPadding: const EdgeInsets.only(bottom: 4),
                          onItemSelected: (selectedItem) {
                            getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyBrokerageAmountUnitClick);
                            propertyInfoTabBloc.add(SelectBrokerageUnit(selectedItem));
                          }),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 15),

              //Add listing

              if (propertyInfoTabBloc.isPropertyListingEnabled) _buildListingSourceAndLocationWidgets(context, state),
              if (propertyInfoTabBloc.isPropertyListingEnabled) ...[
                const SizedBox(height: 15),
                //Permit number
                LeadratTextFormField(
                  hintText: 'ex. 76543546',
                  labelText: "permit number",
                  controller: propertyInfoTabBloc.permitNumberController,
                  isEnabled: state.isPermitNumberEditable,
                ),
                Transform.translate(
                  offset: const Offset(0, -14),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      SizedBox(
                          width: 100,
                          height: 20,
                          child: RadioListTile<String>(
                            activeColor: ColorPalette.primaryGreen,
                            visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                            value: "DLD",
                            groupValue: state.selectedPermitValue,
                            title: Text(
                              "DLD",
                              style: LexendTextStyles.lexend12SemiBold.copyWith(
                                color: ColorPalette.primaryDarkColor,
                              ),
                            ),
                            contentPadding: EdgeInsets.zero,
                            onChanged: state.isPermitNumberEditable
                                ? (value) {
                                    if (value != null) {
                                      propertyInfoTabBloc.add(ChangePermitValueEvent(value));
                                    }
                                  }
                                : null, // This disables interaction when condition is false
                          )),
                      SizedBox(
                        width: 100,
                        height: 20,
                        child: RadioListTile<String>(
                          activeColor: ColorPalette.primaryGreen,
                          visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                          value: "DTCM",
                          groupValue: state.selectedPermitValue,
                          title: Text("DTCM", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                          contentPadding: EdgeInsets.zero,
                          onChanged: state.isPermitNumberEditable
                              ? (value) {
                                  if (value != null) propertyInfoTabBloc.add(ChangePermitValueEvent(value));
                                }
                              : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              // Notes
              LeadratTextFormField(
                labelText: "notes",
                hintText: "ex. you can additional information about your lead and their requirements...",
                maxLines: 4,
                controller: propertyInfoTabBloc.notesController,
                previewFunction: () => previewDialogueWidget(context, controller: propertyInfoTabBloc.notesController, previewText: "Notes"),
              ),
              // Location
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  RichText(
                    text: TextSpan(
                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                      children: [
                        const TextSpan(text: "location"),
                        if (!propertyInfoTabBloc.isPropertyListingEnabled) const TextSpan(text: "*", style: TextStyle(color: ColorPalette.red)),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => {
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddPropertyLocationClick),
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => SearchLocationPage(
                                    onLocationSelected: (value) => {
                                      propertyInfoTabBloc.add(SelectLocationEvent(value)),
                                    },
                                  ))),
                    },
                    child: Row(
                      children: [
                        const Icon(
                          Icons.search,
                          color: ColorPalette.primaryGreen,
                        ),
                        Text(
                          'search location',
                          style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryGreen),
                        )
                      ],
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 8,
              ),
              if (state.location != null)
                Container(
                  width: context.width(100),
                  color: ColorPalette.superSilver,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                    child: Center(
                      child: Text(
                        "${state.location?.subLocality ?? state.location?.locality ?? ''}, ${state.location?.city ?? ''}, ${state.location?.state ?? ''}, ${state.location?.country ?? ''}",
                        style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              const SizedBox(height: 25),

              buildCustomBasicAttributesList(
                  state.customBasicAttributes
                      .where(
                        (element) => element.value?.attributeName != 'floorNumber' && element.value?.attributeName != 'numberOfFloors',
                      )
                      .toList(),
                  ''),
              buildNumberOfFloors(state.customBasicAttributes.where((element) => element.value?.attributeName == 'numberOfFloors').toList()),
              const SizedBox(
                height: 15,
              ),
              buildFloorNumber(state.customBasicAttributes.where((element) => element.value?.attributeName == 'floorNumber').toList()),

              if ((state.isOfficeSpaceSelected ?? false) || (state.isCoWorkingOfficeSpaceSelected ?? false))
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Number Of Floors Occupied',
                      style: LexendTextStyles.lexend12SemiBold.copyWith(
                        color: ColorPalette.primaryDarkColor,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.3,
                      child: SelectableItemBottomSheet<int>(
                        key: const ValueKey('projects'),
                        title: "select no.of floors occupied",
                        selectableItems: state.selectableNoOfFloorsOccupied ?? [],
                        onItemsSelected: (value) => propertyInfoTabBloc.add(SelectNumberOfFloorsOccupiedEvent(item: value)),
                        initialSelectedItems: state.selectedNoOfFloorsOccupied,
                        isMultipleSelection: true,
                      ),
                    ),
                  ],
                ),
              if (state.furnishStatuses != null && (state.furnishStatuses?.isNotEmpty ?? false)) ...[
                const SizedBox(height: 10),
                Text("furnish status", style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                Wrap(
                  spacing: 8,
                  children: state.furnishStatuses!
                      .map((furnishStatus) => RawChip(
                            onPressed: () => propertyInfoTabBloc.add(ToggleFurnishStatusEvent(furnishStatus)),
                            label: Text(furnishStatus.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: furnishStatus.isSelected ?? false ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                            backgroundColor: furnishStatus.isSelected ?? false ? ColorPalette.primaryGreen : ColorPalette.white,
                            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                            shape: StadiumBorder(side: BorderSide(color: furnishStatus.isSelected ?? false ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
                          ))
                      .toList(),
                ),
              ],
              // Facing
              if (state.facings != null && (state.facings?.isNotEmpty ?? false)) ...[
                const SizedBox(height: 10),
                Text('facing', style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                Wrap(
                    spacing: 8,
                    children: state.facings!
                        .map((facing) => RawChip(
                              onPressed: () => propertyInfoTabBloc.add(ToggleFacingEvent(facing)),
                              label: Text(facing.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: facing.isSelected ?? false ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                              backgroundColor: facing.isSelected ?? false ? ColorPalette.primaryGreen : ColorPalette.white,
                              visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                              shape: StadiumBorder(side: BorderSide(color: facing.isSelected ?? false ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
                            ))
                        .toList()),
              ],
              // Ratings
              const SizedBox(height: 10),
              Row(
                children: [
                  Text("rating", style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                  const SizedBox(width: 10), // Spacing between label and stars
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List.generate(
                        5,
                        (index) => GestureDetector(
                              onTap: () => propertyInfoTabBloc.add(ToggleRatingsEvent(index)),
                              child: Icon(index < (state.ratings ?? 0) ? Icons.star : Icons.star_border, color: Colors.amber, size: 30),
                            )),
                  ),
                ],
              )
            ],
          ),
        )));
  }

  Widget buildNumberOfFloors(List<SelectableItem<CustomAttributesModel>> floors) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          floors.firstOrNull?.value?.attributeDisplayName ?? '',
          style: LexendTextStyles.lexend12SemiBold.copyWith(
            color: ColorPalette.primaryDarkColor,
          ),
        ),
        Row(
          children: [
            const SizedBox(
              width: 10,
            ),
            SelectableItemBottomSheet<String>(
              title: "select number of floors",
              onItemSelected: (value) => propertyInfoTabBloc.add(SelectNoOfTotalFloorEvent(selectedNoOfTotalFloor: value, customAttributesModel: floors.firstOrNull?.value)),
              selectableItems: state.selectableTotalFloor ?? [],
              selectedItem: state.selectedTotalFloor,
              canSearchItems: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget buildFloorNumber(List<SelectableItem<CustomAttributesModel>> floors) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          floors.firstOrNull?.value?.attributeDisplayName ?? '',
          style: LexendTextStyles.lexend12SemiBold.copyWith(
            color: ColorPalette.primaryDarkColor,
          ),
        ),
        Row(
          children: [
            const SizedBox(
              width: 10,
            ),
            SelectableItemBottomSheet<String>(
              title: "select floor number",
              onItemSelected: (value) => propertyInfoTabBloc.add(SelectFloorNumberEvent(selectedFloorNumber: value, customAttributesModel: floors.firstOrNull?.value)),
              selectableItems: state.selectableFloorNumber ?? [],
              selectedItem: state.selectedFloorNumber,
              canSearchItems: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget buildCustomBasicAttributesList(List<SelectableItem<CustomAttributesModel>> features, String label) {
    return Column(
      children: List.generate(features.length, (index) {
        final feature = features[index];
        final attributeName = feature.value?.attributeDisplayName ?? '';
        final parsedValue = int.tryParse(feature.title ?? '');

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              attributeName,
              style: LexendTextStyles.lexend12SemiBold.copyWith(
                color: ColorPalette.primaryDarkColor,
              ),
            ),
            const SizedBox(height: 7),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: BasicAttributesValueTypeEnum.values.map((type) {
                  final isSelected = isAttributeValueSelected(type, parsedValue);

                  return GestureDetector(
                    onTap: () {
                      propertyInfoTabBloc.add(
                        AttachOrDeAttachCustomAttributeEvent(
                          attribute: feature.value,
                          isAttach: true,
                          value: type.value.toString(),
                        ),
                      );
                    },
                    child: Container(
                      height: 40,
                      margin: const EdgeInsets.only(right: 6),
                      padding: const EdgeInsets.symmetric(horizontal: 23),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: isSelected ? ColorPalette.primaryGreen : ColorPalette.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? ColorPalette.transparent : const Color(0XFFDDDDDD),
                        ),
                      ),
                      child: Text(
                        type.description,
                        style: LexendTextStyles.lexend12SemiBold.copyWith(
                          color: isSelected ? ColorPalette.white : const Color(0XFF8A8A8A),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 15),
          ],
        );
      }),
    );
  }

  bool isAttributeValueSelected(BasicAttributesValueTypeEnum attributeValue, int? value) {
    return value != null && attributeValue == BasicAttributesValueTypeEnum.fromValue(value);
  }

  Widget _getSymbolWidget({required bool isAttach, CustomAttributesModel? value}) {
    return GestureDetector(
      onTap: () => propertyInfoTabBloc.add(AttachOrDeAttachCustomAttributeEvent(attribute: value, isAttach: isAttach)),
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(color: ColorPalette.white, borderRadius: BorderRadius.circular(6)),
        child: Center(child: Text(isAttach ? "+" : "-", style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor))),
      ),
    );
  }

  Widget _buildListingSourceAndLocationWidgets(BuildContext context, PropertyInfoTabState state) {
    return Column(
      children: [
        ...List.generate(
          state.listingSiteAndLocations.length,
          (index) {
            return Column(
              children: [
                if (index > 0) ...[
                  const SizedBox(height: 5),
                  Align(
                    alignment: Alignment.topRight,
                    child: LeadingIconWithText(
                      iconWidget: const Icon(Icons.remove_circle_outline_outlined, color: ColorPalette.fadedRed, size: 16),
                      space: 7,
                      text: 'Remove Location',
                      textStyle: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.fadedRed),
                      onTap: () => propertyInfoTabBloc.add(RemoveNewListingSourceAndAddressEvent(index)),
                    ),
                  ),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // RichText(
                          //   text: TextSpan(
                          //     style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                          //     children: const [
                          //       TextSpan(text: "Listing"),
                          //     ],
                          //   ),
                          // ),
                          // SelectableItemBottomSheet<ViewAddressesModel>(
                          //   title: "select a listing source",
                          //   onItemSelected: (value) {
                          //     propertyInfoTabBloc.add(SelectListingSourceEvent(value, index: index));
                          //   },
                          //   selectableItems: state.listingSiteAndLocations[index].listingSource,
                          //   selectedItem: state.listingSiteAndLocations[index].selectedListingSource,
                          //   child: Container(
                          //     height: 40,
                          //     padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                          //     margin: const EdgeInsets.only(top: 10),
                          //     decoration: BoxDecoration(
                          //       color: ColorPalette.transparent,
                          //       border: Border.all(width: 1, color: ColorPalette.veryLightGray),
                          //       borderRadius: BorderRadius.circular(5),
                          //     ),
                          //     alignment: Alignment.topCenter,
                          //     child: Row(
                          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //       crossAxisAlignment: CrossAxisAlignment.center,
                          //       children: [
                          //         Expanded(
                          //           child: Text(
                          //             state.listingSiteAndLocations[index].selectedListingSource?.title ?? 'select listing',
                          //             style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.corbeau),
                          //             overflow: TextOverflow.ellipsis,
                          //           ),
                          //         ),
                          //         const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.corbeau),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    // const SizedBox(width: 10),
                    // Expanded(
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       RichText(
                    //         text: TextSpan(
                    //           style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                    //           children: const [
                    //             TextSpan(text: "Location"),
                    //           ],
                    //         ),
                    //       ),
                    //       SelectableItemBottomSheet<ViewListingSourceAddressModel>(
                    //         title: "select a listing address",
                    //         onItemSelected: (value) {
                    //           propertyInfoTabBloc.add(SelectListingSiteAddress(value, index: index));
                    //         },
                    //         selectableItems: state.listingSiteAndLocations[index].listingSiteAddress,
                    //         selectedItem: state.listingSiteAndLocations[index].selectedListingSiteAddress,
                    //         child: Container(
                    //           height: 40,
                    //           padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                    //           margin: const EdgeInsets.only(top: 10),
                    //           decoration: BoxDecoration(
                    //             color: ColorPalette.transparent,
                    //             border: Border.all(width: 1, color: ColorPalette.veryLightGray),
                    //             borderRadius: BorderRadius.circular(5),
                    //           ),
                    //           alignment: Alignment.topCenter,
                    //           child: Row(
                    //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //             crossAxisAlignment: CrossAxisAlignment.center,
                    //             children: [
                    //               Expanded(
                    //                 child: Text(
                    //                   state.listingSiteAndLocations[index].selectedListingSiteAddress?.title ?? 'select location',
                    //                   style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.corbeau),
                    //                   overflow: TextOverflow.ellipsis,
                    //                 ),
                    //               ),
                    //               const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.corbeau),
                    //             ],
                    //           ),
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
                const SizedBox(height: 5),
              ],
            );
          },
        ),
        // if (state.listingSiteAndLocations.length < ((state.totalListingSiteAndAddress ?? 0) - 1)) ...[
        //   Align(
        //     alignment: Alignment.topRight,
        //     child: LeadingIconWithText(
        //       iconWidget: const Icon(Icons.add_circle_outline, color: ColorPalette.primaryGreen, size: 16),
        //       space: 7,
        //       text: 'Add location',
        //       textStyle: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primaryGreen),
        //       onTap: () => propertyInfoTabBloc.add(AddNewListingSourceAndAddressEvent()),
        //     ),
        //   ),
        //   const SizedBox(height: 10),
        // ]
      ],
    );
  }

  Widget getOwnerDetailsWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Contact Information',
                style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
              ),
              GestureDetector(
                onTap: () => propertyInfoTabBloc.add(AddContactEvent()),
                child: Row(
                  children: [
                    const Icon(
                      Icons.add_circle_outline,
                      color: ColorPalette.primaryGreen,
                    ),
                    Text(
                      '  Add Contact',
                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryGreen),
                    )
                  ],
                ),
              )
            ],
          ),
          ...state.ownerDetailsStorages.map((e) => getContactForm(
                ownerDetailsStorage: e,
                index: state.ownerDetailsStorages.indexOf(e),
                state: state,
              ))
        ],
      ),
    );
  }

  Widget getContactForm({required OwnerDetailsStorage ownerDetailsStorage, required PropertyInfoTabState state, required index}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(color: ColorPalette.placeHolderTextColor),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                ownerDetailsStorage.isCollapse ? " Owner/Builder${index == 0 ? '' : ' ${index + 1}'} details" : " Owner/Builder Name ${index == 0 ? '' : '${index + 1}'}",
                style: LexendTextStyles.lexend12SemiBold.copyWith(
                  color: ColorPalette.primaryDarkColor,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (!ownerDetailsStorage.isCollapse && state.ownerDetailsStorages.length > 1)
                    GestureDetector(
                      onTap: () => propertyInfoTabBloc.add(RemoveContactEvent(uniqueId: ownerDetailsStorage.uniqueId ?? '')),
                      child: Text(
                        '  Remove ',
                        style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.red),
                      ),
                    ),
                  GestureDetector(
                    onTap: () => propertyInfoTabBloc.add(CollapseOwnerContactEvent(uniqueId: ownerDetailsStorage.uniqueId ?? '')),
                    child: Icon(
                      ownerDetailsStorage.isCollapse ? Icons.arrow_drop_down_outlined : Icons.arrow_drop_up_outlined,
                      color: ColorPalette.black,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (!ownerDetailsStorage.isCollapse)
            Column(
              children: [
                LeadratTextFormField(
                  hintText: 'ex. sachin chavan',
                  controller: ownerDetailsStorage.ownerNameController,
                ),
                LrbPhoneField(
                  hintText: 'ex. 999XXXXX',
                  controller: ownerDetailsStorage.phoneNumberController,
                  labelText: 'Phone number',
                  selectedDialCode: ownerDetailsStorage.primaryCountryCode,
                  onValidationChanged: (isValid, countryCode, phoneNumber) {
                    if (isValid && countryCode != null && phoneNumber != null) {
                      propertyInfoTabBloc.add(OwnerContactChangedEvent(countryCode: countryCode, uniqueId: ownerDetailsStorage.uniqueId ?? "", isAlternateContactNumberChanged: false));
                    }
                  },
                ),
                LrbPhoneField(
                  hintText: 'ex. 999XXXXX',
                  controller: ownerDetailsStorage.alternateNumberController,
                  labelText: 'Alternate number',
                  selectedDialCode: ownerDetailsStorage.altCountryCode,
                  onValidationChanged: (isValid, countryCode, phoneNumber) {
                    if (isValid && countryCode != null && phoneNumber != null) {
                      propertyInfoTabBloc.add(OwnerContactChangedEvent(countryCode: countryCode, uniqueId: ownerDetailsStorage.uniqueId ?? "", isAlternateContactNumberChanged: true));
                    }
                  },
                ),
                LeadratTextFormField(
                  hintText: 'ex. sachin chavan',
                  labelText: "Email",
                  controller: ownerDetailsStorage.emailController,
                  validator: (value) {
                    if (value == null || ValidationUtils.isValidEmail(value)) {
                      return null;
                    } else {
                      return "Enter valid Email";
                    }
                  },
                ),
              ],
            ),
        ],
      ),
    );
  }
}
