import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/payment_frequency.dart';
import 'package:leadrat/core_main/enums/property_listing/compliance_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/data/models/add_property_listing_model.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_listing_contact_information.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_property_basic_attributes.dart';
import 'package:leadrat/main.dart';

part 'property_listing_property_info_event.dart';
part 'property_listing_property_info_state.dart';

class PropertyListingPropertyInfoBloc extends Bloc<PropertyListingPropertyInfoEvent, PropertyListingPropertyInfoState> {
  final _basicAttributesValues = List.generate(20, (index) => ItemSimpleModel<int>(title: "${index + 1}", value: index + 1));
  final _noOfCheques = List.generate(12, (index) => SelectableItem<int>(title: "${index + 1}", value: index + 1));
  final _furnishingStatuses = FurnishStatus.values.where((element) => element != FurnishStatus.none).map((furnishStatus) => ItemSimpleModel<FurnishStatus>(title: furnishStatus.description, value: furnishStatus)).toList();
  final _facings = Facing.values.where((element) => element != Facing.unknown).map((facing) => ItemSimpleModel<Facing>(title: facing.description, value: facing)).toList();
  final _paymentFrequency = PaymentFrequency.values.where((element) => element != PaymentFrequency.none).map((paymentFrequency) => SelectableItem<PaymentFrequency>(title: paymentFrequency.description, value: paymentFrequency)).toList();

  final TextEditingController downPaymentController = TextEditingController();
  final TextEditingController totalPriceController = TextEditingController();
  final TextEditingController projectController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController permitNumberController = TextEditingController();
  final TextEditingController licenceNumberController = TextEditingController();

  AddPropertyListingModel addPropertyListingModel = AddPropertyListingModel(monetaryInfo: PropertyListingMonetaryInfoModel());

  PropertyListingBasicInfoBloc get propertyListingBasicInfoBloc => getIt<PropertyListingBasicInfoBloc>();

  GlobalKey<FormState> get propertyListingPropertyInfoFormKey => GlobalKey<FormState>();

  PropertyListingPropertyInfoBloc() : super(const PropertyListingPropertyInfoState()) {
    on<PropertyListingPropertyInfoInitialEvent>(_onPropertyListingPropertyInfoInitial);
    on<AddContactInformationEvent>(_onAddContactInformation);
    on<RemoveContactInformationEvent>(_onRemoveContactInformation);
    on<ToggleFurnishStatusEvent>(_onToggleNoOfFurnishStatus);
    on<ToggleFacingEvent>(_onToggleNoOfFacing);
    on<ToggleBasicAttributesEvent>(_onToggleBasicAttributes);
    on<SelectPaymentFrequencyEvent>(_onSelectPaymentFrequency);
    on<SelectNoOfChequeEvent>(_onSelectNoOfCheque);
    on<ToggleAvailableFormEvent>(_onToggleAvailableForm);
    on<SelectLocationEvent>(_onLocationSelected);
    on<ChangePermitValueEvent>(_onChangePermitValue);
    on<SelectCurrencyEvent>(_onSelectCurrency);
    on<ResetPropertyListingPropertyInfoEvent>((event, emit) => emit(const PropertyListingPropertyInfoState()));

    // Add listeners to text controllers to trigger quality score calculation
    _setupTextControllerListeners();
  }

  void _setupTextControllerListeners() {
    // Add listeners to text controllers to trigger quality score calculation when fields change
    totalPriceController.addListener(_onFieldChanged);
    downPaymentController.addListener(_onFieldChanged);
    permitNumberController.addListener(_onFieldChanged);
    licenceNumberController.addListener(_onFieldChanged);
    notesController.addListener(_onFieldChanged);
    projectController.addListener(_onFieldChanged);
  }

  void _onFieldChanged() {
    // Trigger quality score calculation when any field changes
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onPropertyListingPropertyInfoInitial(PropertyListingPropertyInfoInitialEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    List<SelectableItem<String>>? allCurrencies = [];
    propertyListingBasicInfoBloc.globalSettingsModel?.countries?.firstOrNull?.currencies?.forEach((currency) {
      final selectedCurrency = addPropertyListingModel.monetaryInfo?.currency ?? (propertyListingBasicInfoBloc.globalSettingsModel?.countries?.firstOrNull?.defaultCurrency ?? 'AED');
      allCurrencies.add(SelectableItem(title: currency.currency ?? '', value: currency.currency ?? '', isSelected: selectedCurrency == currency.currency));
    });
    addPropertyListingModel = propertyListingBasicInfoBloc.addPropertyListingModel;

    final selectedPossessionType = (addPropertyListingModel.possesionType != PropertyListingPossessionType.none || addPropertyListingModel.possessionDate != null) ? addPropertyListingModel.possesionType : PropertyListingPossessionType.immediate;
    final customAttributes = getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAttributes() ?? [];
    final attributes = [
      ...propertyListingBasicInfoBloc.isResidentialSelected ? ["numberOfBedrooms", "numberOfBathrooms"] : [],
      "numberOfParking"
    ];
    final basicAttributes = customAttributes.where((element) => element?.attributeType == "Basic").where((element) => attributes.contains(element?.attributeName)).nonNulls.toList();

    final defaultDialCode = propertyListingBasicInfoBloc.globalSettingsModel?.countries?.firstOrNull?.defaultCallingCode;

    emit(state.copyWith(
      numberOfCheques: _noOfCheques,
      furnishingStatuses: _furnishingStatuses,
      facings: _facings,
      ownerContactInformation: [
        ItemListingContactInformation(
          index: 0,
          alternateNumberController: TextEditingController(),
          emailController: TextEditingController(),
          ownerNameController: TextEditingController(),
          phoneNumberController: TextEditingController(),
        )
      ],
      selectablePaymentFrequencies: _paymentFrequency,
      currencies: allCurrencies,
      selectedPossessionType: selectedPossessionType,
      selectedAvailableDate: addPropertyListingModel.possessionDate ?? DateTime.now(),
      selectedDownPaymentCurrency: '',
      selectedRentAmountCurrency: '',
      customBasicAttributes: basicAttributes.map((e) => ItemPropertyBasicAttributes(id: e.attributeName ?? "", customAttributesModel: e, values: _basicAttributesValues)).toList(),
      defaultCountryCode: defaultDialCode,
    ));
    await _initInitialData(emit);
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onSelectCurrency(SelectCurrencyEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    if (event.selectedItem == null) return;
    final updatedCurrencies = state.currencies.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value)).toList();
    emit(state.copyWith(currencies: updatedCurrencies));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onToggleNoOfFurnishStatus(ToggleFurnishStatusEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    final updatedItems = state.furnishingStatuses.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(furnishingStatuses: updatedItems));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onToggleNoOfFacing(ToggleFacingEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    final updatedItems = state.facings.map((e) => e.copyWith(isSelected: e.value == event.selectedItem?.value ? !(event.selectedItem?.isSelected ?? false) : false)).toList();
    emit(state.copyWith(facings: updatedItems));
  }

  FutureOr<void> _onToggleBasicAttributes(ToggleBasicAttributesEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    final selectedBasicAttribute = state.customBasicAttributes.firstWhereOrNull((element) => element.id == event.selectedItem?.id);
    final updatedItems = selectedBasicAttribute?.values.map((e) => e.copyWith(isSelected: e.value == event.selectedAttributeValue?.value ? !(event.selectedAttributeValue?.isSelected ?? false) : false)).toList();
    final updatedBasicAttributes = state.customBasicAttributes.map((e) => e.id == event.selectedItem?.id ? e.copyWith(values: updatedItems) : e).toList();
    emit(state.copyWith(customBasicAttributes: updatedBasicAttributes));
  }

  FutureOr<void> _onAddContactInformation(AddContactInformationEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    emit(state.copyWith(ownerContactInformation: [
      ...state.ownerContactInformation,
      ItemListingContactInformation(
        index: state.ownerContactInformation.length + 1,
        alternateNumberController: TextEditingController(),
        emailController: TextEditingController(),
        ownerNameController: TextEditingController(),
        phoneNumberController: TextEditingController(),
      ),
    ]));
  }

  FutureOr<void> _onRemoveContactInformation(RemoveContactInformationEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    var updatedContactInformation = List.of(state.ownerContactInformation);
    updatedContactInformation.removeAt(event.index);
    emit(state.copyWith(ownerContactInformation: updatedContactInformation));
  }

  FutureOr<void> _onSelectPaymentFrequency(SelectPaymentFrequencyEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    if (event.selectedPaymentFrequency == null) return;
    final updatedPaymentFrequency = state.selectablePaymentFrequencies.map((e) => e.copyWith(isSelected: e.value == event.selectedPaymentFrequency?.value)).toList();
    emit(state.copyWith(selectablePaymentFrequencies: updatedPaymentFrequency));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  FutureOr<void> _onSelectNoOfCheque(SelectNoOfChequeEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    if (event.selectedNoOfCheque == null) return;
    final updatedNoOfCheques = state.numberOfCheques.map((e) => e.copyWith(isSelected: e.value == event.selectedNoOfCheque?.value)).toList();
    emit(state.copyWith(numberOfCheques: updatedNoOfCheques));
  }

  FutureOr<void> _onToggleAvailableForm(ToggleAvailableFormEvent event, Emitter<PropertyListingPropertyInfoState> emit) async {
    emit(state.copyWith(selectedPossessionType: event.selectedPossessionType, selectedAvailableDate: event.selectedDate));
  }

  FutureOr<void> _onChangePermitValue(ChangePermitValueEvent event, Emitter<PropertyListingPropertyInfoState> emit) {
    emit(state.copyWith(selectedPermitValue: event.selectedPermitValue));
  }

  FutureOr<void> _onLocationSelected(SelectLocationEvent event, Emitter<PropertyListingPropertyInfoState> emit) {
    emit(state.copyWith(location: event.location));

    // Trigger quality score calculation
    getIt<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
  }

  _initInitialData(Emitter<PropertyListingPropertyInfoState> emit) async {
    if (addPropertyListingModel.facing != null) {
      add(ToggleFacingEvent(_facings.firstWhereOrNull((element) => element.value == addPropertyListingModel.facing)));
    }
    if (addPropertyListingModel.furnishStatus != null) {
      add(ToggleFurnishStatusEvent(_furnishingStatuses.firstWhereOrNull((element) => element.value == addPropertyListingModel.furnishStatus)));
    }
    if (addPropertyListingModel.address != null) {
      add(SelectLocationEvent(addPropertyListingModel.address));
    }
    if (addPropertyListingModel.possesionType == PropertyListingPossessionType.customDate) {
      emit(state.copyWith(selectedAvailableDate: addPropertyListingModel.possessionDate, selectedPossessionType: addPropertyListingModel.possesionType));
    }
    if (addPropertyListingModel.monetaryInfo?.noOfChequesAllowed != null && (_noOfCheques.firstWhereOrNull((element) => element.value == addPropertyListingModel.monetaryInfo?.noOfChequesAllowed) != null)) {
      add(SelectNoOfChequeEvent(_noOfCheques.firstWhereOrNull((element) => element.value == addPropertyListingModel.monetaryInfo?.noOfChequesAllowed)));
    }
    if (addPropertyListingModel.monetaryInfo?.paymentFrequency != null && addPropertyListingModel.monetaryInfo?.paymentFrequency != PaymentFrequency.none) {
      add(SelectPaymentFrequencyEvent(_paymentFrequency.firstWhereOrNull((element) => element.value == addPropertyListingModel.monetaryInfo?.paymentFrequency)));
    }
    if (addPropertyListingModel.compliance?.type != null && addPropertyListingModel.compliance?.type != ComplianceType.none) {
      add(ChangePermitValueEvent(addPropertyListingModel.compliance?.type == ComplianceType.rera ? "RERA" : "DTCM"));
    }
    if (addPropertyListingModel.monetaryInfo?.currency != null) {
      add(SelectCurrencyEvent(state.currencies.firstWhereOrNull((element) => element.value == addPropertyListingModel.monetaryInfo?.currency)));
    }
    if (addPropertyListingModel.propertyOwnerDetails?.isNotEmpty ?? false) {
      final updatedPropertyOwnerDetails = List.generate(
        addPropertyListingModel.propertyOwnerDetails?.length ?? 0,
        (index) {
          return ItemListingContactInformation(
            index: index,
            id: addPropertyListingModel.propertyOwnerDetails?[index].id,
            alternateNumberController: TextEditingController(text: addPropertyListingModel.propertyOwnerDetails![index].alternateContactNo),
            emailController: TextEditingController(text: addPropertyListingModel.propertyOwnerDetails![index].email),
            ownerNameController: TextEditingController(text: addPropertyListingModel.propertyOwnerDetails![index].name),
            phoneNumberController: TextEditingController(text: addPropertyListingModel.propertyOwnerDetails![index].phone),
          );
        },
      );
      emit(state.copyWith(ownerContactInformation: updatedPropertyOwnerDetails));
    }
    if (addPropertyListingModel.attributes?.isNotEmpty ?? false) {
      for (var element in state.customBasicAttributes) {
        if (addPropertyListingModel.attributes?.any((attr) => attr.attributeName == element.id) ?? false) {
          final selectedAttribute = addPropertyListingModel.attributes?.firstWhereOrNull((attr) => attr.attributeName == element.id);
          final selectedAttributeValue = _basicAttributesValues.firstWhereOrNull((item) => item.value.toString() == selectedAttribute?.value);
          add(ToggleBasicAttributesEvent(selectedItem: element, selectedAttributeValue: selectedAttributeValue));
        }
      }
    }

    final licenceNumber = propertyListingBasicInfoBloc.state.selectedListingByUsers?.firstOrNull?.value?.licenseNo ?? '';
    permitNumberController.text = addPropertyListingModel.compliance?.listingAdvertisementNumber ?? '';
    licenceNumberController.text = licenceNumber;
    notesController.text = addPropertyListingModel.notes ?? '';
    downPaymentController.text = addPropertyListingModel.monetaryInfo?.downpayment?.toString() ?? '';
    totalPriceController.text = addPropertyListingModel.monetaryInfo?.expectedPrice?.toString() ?? '';
    projectController.text = addPropertyListingModel.links?.firstOrNull ?? '';
  }

  bool canNavigateToNextTab() {
    final isAvailableFromSelected = state.selectedPossessionType == PropertyListingPossessionType.customDate ? state.selectedAvailableDate != null : true;
    final isExceptedPriceNotEmpty = totalPriceController.text.isNotNullOrEmpty();
    final isPaymentFrequencySelected = (state.selectablePaymentFrequencies.firstWhereOrNull((element) => element.isSelected)?.isSelected ?? false);
    final isTotalOrRentAmountAdded = propertyListingBasicInfoBloc.isSaleSelected ? isExceptedPriceNotEmpty : isExceptedPriceNotEmpty && isPaymentFrequencySelected;
    if (!isAvailableFromSelected) {
      LeadratCustomSnackbar.show(message: "kindly select available date", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    } else if (!isTotalOrRentAmountAdded) {
      LeadratCustomSnackbar.show(message: propertyListingBasicInfoBloc.isSaleSelected ? "kindly enter total amount" : "kindly enter rent amount and select payment frequency", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }
}
