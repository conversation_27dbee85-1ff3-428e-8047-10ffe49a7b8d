// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseNotificationModel _$BaseNotificationModelFromJson(
        Map<String, dynamic> json) =>
    BaseNotificationModel(
      notOpenedCount: (json['notOpenedCount'] as num?)?.toInt(),
      totalCount: (json['totalCount'] as num?)?.toInt(),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => GetNotificationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BaseNotificationModelToJson(
        BaseNotificationModel instance) =>
    <String, dynamic>{
      'notOpenedCount': instance.notOpenedCount,
      'totalCount': instance.totalCount,
      'items': instance.items,
    };
