// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PropertyStatusAdapter extends TypeAdapter<PropertyStatus> {
  @override
  final int typeId = 62;

  @override
  PropertyStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyStatus.active;
      case 1:
        return PropertyStatus.sold;
      default:
        return PropertyStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyStatus obj) {
    switch (obj) {
      case PropertyStatus.active:
        writer.writeByte(0);
        break;
      case PropertyStatus.sold:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
