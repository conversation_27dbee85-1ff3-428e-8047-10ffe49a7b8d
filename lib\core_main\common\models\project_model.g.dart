// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectMonetaryInfoModel _$ProjectMonetaryInfoModelFromJson(
        Map<String, dynamic> json) =>
    ProjectMonetaryInfoModel(
      id: json['id'] as String?,
      brokerage: (json['brokerage'] as num?)?.toDouble(),
      brokerageUnit:
          $enumDecodeNullable(_$BrokerageUnitEnumMap, json['brokerageUnit']),
      brokerageCurrency: json['brokerageCurrency'] as String?,
    );

Map<String, dynamic> _$ProjectMonetaryInfoModelToJson(
        ProjectMonetaryInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'brokerage': instance.brokerage,
      'brokerageUnit': _$BrokerageUnitEnumMap[instance.brokerageUnit],
      'brokerageCurrency': instance.brokerageCurrency,
    };

const _$BrokerageUnitEnumMap = {
  BrokerageUnit.none: 0,
  BrokerageUnit.percentage: 1,
  BrokerageUnit.iNR: 2,
};

ProjectBuilderDetailsModel _$ProjectBuilderDetailsModelFromJson(
        Map<String, dynamic> json) =>
    ProjectBuilderDetailsModel(
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      pointOfContact: json['pointOfContact'] as String?,
    );

Map<String, dynamic> _$ProjectBuilderDetailsModelToJson(
        ProjectBuilderDetailsModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'contactNo': instance.contactNo,
      'pointOfContact': instance.pointOfContact,
    };

ProjectBuilderInfoModel _$ProjectBuilderInfoModelFromJson(
        Map<String, dynamic> json) =>
    ProjectBuilderInfoModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$ProjectBuilderInfoModelToJson(
        ProjectBuilderInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };
