import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';

part 'master_property_amenitie_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterPropertyAmenitiesModelTypeId)
@JsonSerializable()
class MasterPropertyAmenitiesModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final String? amenityName;
  @HiveField(9)
  final String? amenityDisplayName;
  @HiveField(10)
  final String? imageURL;
  @HiveField(11)
  final String? amenityType;
  @HiveField(12)
  final String? category;
  @HiveField(13)
  final List<PropertyType>? propertyType;
  @HiveField(14)
  final String? mobileIcon;
  @HiveField(15)
  final int? orderRank;
  @HiveField(16)
  final String? fullImageURL;

  MasterPropertyAmenitiesModel({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.amenityName,
    this.amenityDisplayName,
    this.imageURL,
    this.amenityType,
    this.category,
    this.propertyType,
    this.mobileIcon,
    this.orderRank,
    this.fullImageURL,
  });

  factory MasterPropertyAmenitiesModel.fromJson(Map<String, dynamic> json) => _$MasterPropertyAmenitiesModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterPropertyAmenitiesModelToJson(this);
}
