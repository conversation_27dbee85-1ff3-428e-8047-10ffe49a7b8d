<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.leadrat.black.mobile.droid.leadrat">
    <!-- Required for all network operations, API calls, and Firebase services -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <!-- Allows the app to initiate phone calls directly from the app interface -->
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-feature android:name="android.hardware.telephony" android:required="false"/>
    <!-- Allows the app to read files (images, documents) from the device’s storage -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <!-- Allows the app to save files to the device’s storage (deprecated on Android 11+) -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!-- Allows the app to access the device camera for capturing photos or videos -->
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-feature android:name="android.hardware.camera" android:required="false"/>
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false"/>
    <!-- Allows the app to access precise location information (using GPS and network location sources) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <!-- Allows the app to access approximate location information (using Wi-Fi and cell towers) -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.READ_CONTACTS" />


    <supports-screens
        android:smallScreens="true"
        android:normalScreens="true"
        android:largeScreens="true"
        android:xlargeScreens="true"
        android:anyDensity="true"/>

    <queries>
        <!--  If your app opens https URLs  -->
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="https"/>
        </intent>
        <!--  If your app makes calls  -->
        <intent>
            <action android:name="android.intent.action.DIAL"/>
            <data android:scheme="tel"/>
        </intent>
        <!--  If your sends SMS messages  -->
        <intent>
            <action android:name="android.intent.action.SENDTO"/>
            <data android:scheme="smsto"/>
        </intent>
        <!--  If your app sends emails  -->
        <intent>
            <action android:name="android.intent.action.SEND"/>
            <data android:mimeType="*/*"/>
        </intent>
    </queries>
    <application
        android:label="leadrat"
        android:name="${applicationName}"
        android:icon="@mipmap/leadratapplogo"
        android:roundIcon="@mipmap/round_icon"
        android:enableOnBackInvokedCallback="true"
        android:requestLegacyExternalStorage="true"
        android:allowBackup="false"
        android:usesCleartextTraffic="false"
        android:networkSecurityConfig="@xml/network_security_config">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!--  Specifies an Android theme to apply to this Activity as soon as
                             the Android process has started. This theme is visible to the user
                             while the Flutter UI initializes. After that, this theme continues
                             to determine the Window background behind the Flutter UI.  -->
            <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="app"
                    android:host="com.leadrat.black.mobile.droid"
                    android:pathPrefix="/main" />
            </intent-filter>

        </activity>

        <activity-alias
            android:name=".DefaultIconAlias"
            android:exported="true"
            android:enabled="false"
            android:hardwareAccelerated="true"
            android:icon="@mipmap/leadratapplogo"
            android:label="leadrat"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:name=".HJPropertiesAlias"
            android:exported="true"
            android:enabled="false"
            android:hardwareAccelerated="true"
            android:icon="@mipmap/ic_launcher_hj"
            android:label="hj"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".ProwinnAlias"
            android:exported="true"
            android:enabled="false"
            android:hardwareAccelerated="true"
            android:icon="@mipmap/ic_launcher_prowinn"
            android:label="prowin"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".RealtorsHubAlias"
            android:exported="true"
            android:enabled="false"
            android:hardwareAccelerated="true"
            android:icon="@mipmap/ic_launcher_realtorshub"
            android:label="realtorsHub"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".Kunj4uAlias"
            android:exported="true"
            android:enabled="false"
            android:hardwareAccelerated="true"
            android:icon="@mipmap/ic_launcher_kunj4u"
            android:label="kunj4u"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <!--  Don't delete the meta-data below.
                     This is used by the Flutter tool to generate GeneratedPluginRegistrant.java  -->
        <meta-data android:name="flutterEmbedding" android:value="2"/>
        <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@drawable/ic_launcher"/>
        <meta-data android:name="com.google.firebase.messaging.default_notification_color" android:resource="@color/green"/>
        <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="high_importance_channel"/>
        <meta-data android:name="io.flutter.embedding.android.EnableImpeller" android:value="false" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Firebase Messaging Service -->
        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

    </application>
    <!--  Provide required visibility configuration for API level 30 and above  -->
    <queries>
        <!--  If your app checks for call support  -->
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="tel"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>