import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/local/flag_local_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/models/flag_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/local_storage_service/local_storage_service.dart';

class FlagLocalDataSourceImpl implements FlagLocalDataSource{
  final LocalStorageService _localStorageService;

  FlagLocalDataSourceImpl(this._localStorageService);

  @override
  List<ViewFlagModel?>? getFlags() {
    try {
      return _localStorageService.getAllItems<ViewFlagModel?>(HiveModelConstants.viewFlagsBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveFlags(List<ViewFlagModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.viewFlagsBoxName);
      final validItems = items.whereType<ViewFlagModel>().toList();
      await _localStorageService.addItems<ViewFlagModel>(HiveModelConstants.viewFlagsBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

}