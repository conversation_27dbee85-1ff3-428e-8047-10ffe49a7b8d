// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_area_unit_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterAreaUnitsModelAdapter extends TypeAdapter<MasterAreaUnitsModel> {
  @override
  final int typeId = 0;

  @override
  MasterAreaUnitsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterAreaUnitsModel(
      id: fields[0] as String?,
      isDeleted: fields[1] as bool?,
      createdOn: fields[2] as DateTime?,
      createdBy: fields[3] as String?,
      lastModifiedOn: fields[4] as DateTime?,
      lastModifiedBy: fields[5] as String?,
      unit: fields[6] as String?,
      conversionFactor: fields[7] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterAreaUnitsModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.isDeleted)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdBy)
      ..writeByte(4)
      ..write(obj.lastModifiedOn)
      ..writeByte(5)
      ..write(obj.lastModifiedBy)
      ..writeByte(6)
      ..write(obj.unit)
      ..writeByte(7)
      ..write(obj.conversionFactor);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterAreaUnitsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterAreaUnitsModel _$MasterAreaUnitsModelFromJson(
        Map<String, dynamic> json) =>
    MasterAreaUnitsModel(
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      unit: json['unit'] as String?,
      conversionFactor: (json['conversionFactor'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$MasterAreaUnitsModelToJson(
        MasterAreaUnitsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'unit': instance.unit,
      'conversionFactor': instance.conversionFactor,
    };
