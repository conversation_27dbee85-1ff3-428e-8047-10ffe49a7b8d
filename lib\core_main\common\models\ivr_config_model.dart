import 'package:json_annotation/json_annotation.dart';

part 'ivr_config_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class IvrConfigModel {
  final String? destinationNumber;
  final String? agentNumber;
  final String? callerIdOrVirtualNumber;
  final String? userEmail;
  final String? callerId;
  final String? leadId;
  final String? prospectId;
  final String? userId;

  IvrConfigModel({
    this.destinationNumber,
    this.agentNumber,
    this.callerIdOrVirtualNumber,
    this.userEmail,
    this.callerId,
    this.leadId,
    this.prospectId,
    this.userId,
  });

  factory IvrConfigModel.fromJson(Map<String, dynamic> json) => _$IvrConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$IvrConfigModelToJson(this);

  IvrConfigModel copyWith({
    String? destinationNumber,
    String? agentNumber,
    String? callerIdOrVirtualNumber,
    String? userEmail,
    String? callerId,
    String? leadId,
    String? prospectId,
    String? userId,
  }) {
    return IvrConfigModel(
      destinationNumber: destinationNumber ?? this.destinationNumber,
      agentNumber: agentNumber ?? this.agentNumber,
      callerIdOrVirtualNumber: callerIdOrVirtualNumber ?? this.callerIdOrVirtualNumber,
      userEmail: userEmail ?? this.userEmail,
      callerId: callerId ?? this.callerId,
      leadId: leadId ?? this.leadId,
      prospectId: prospectId ?? this.prospectId,
      userId: userId ?? this.userId,
    );
  }
}

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class VirtualNumberModel {
  final bool? isVirtualNumberAssigned;
  final bool? shouldFetchVirtualNumbers;
  final String? virtualNumber;

  VirtualNumberModel({
    this.isVirtualNumberAssigned,
    this.shouldFetchVirtualNumbers,
    this.virtualNumber,
  });

  factory VirtualNumberModel.fromJson(Map<String, dynamic> json) => _$VirtualNumberModelFromJson(json);

  Map<String, dynamic> toJson() => _$VirtualNumberModelToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class IvrResponseModel {
  final bool? success;
  final String? message;
  final IvrConfigModel? clickToCallCommonDto;
  final String? callId;

  IvrResponseModel({
    this.success,
    this.message,
    this.clickToCallCommonDto,
    this.callId,
  });

  factory IvrResponseModel.fromJson(Map<String, dynamic> json) => _$IvrResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$IvrResponseModelToJson(this);
}
