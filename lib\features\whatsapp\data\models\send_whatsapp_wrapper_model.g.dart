// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_whatsapp_wrapper_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendWhatsappWrapperModel _$SendWhatsappWrapperModelFromJson(
        Map<String, dynamic> json) =>
    SendWhatsappWrapperModel(
      message: json['message'] as String?,
      templateName: json['templateName'] as String?,
      customerId: json['customerId'] as String?,
      customerNo: json['customerNo'] as String?,
      userId: json['userId'] as String?,
      tenantId: json['tenantId'] as String?,
      mediaUrl: json['mediaUrl'] as String?,
      mediaType: json['mediaType'] as String?,
      waApiInfo: json['waApiInfo'] == null
          ? null
          : WhatsappApiInfoModel.fromJson(
              json['waApiInfo'] as Map<String, dynamic>),
      waPayloadMapping: json['waPayloadMapping'] == null
          ? null
          : WhatsappPayLoadMappingModel.fromJson(
              json['waPayloadMapping'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SendWhatsappWrapperModelToJson(
        SendWhatsappWrapperModel instance) =>
    <String, dynamic>{
      if (instance.message case final value?) 'message': value,
      if (instance.templateName case final value?) 'templateName': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.customerNo case final value?) 'customerNo': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.tenantId case final value?) 'tenantId': value,
      if (instance.mediaUrl case final value?) 'mediaUrl': value,
      if (instance.mediaType case final value?) 'mediaType': value,
      if (instance.waApiInfo?.toJson() case final value?) 'waApiInfo': value,
      if (instance.waPayloadMapping?.toJson() case final value?)
        'waPayloadMapping': value,
    };

WhatsappWebhookDto _$WhatsappWebhookDtoFromJson(Map<String, dynamic> json) =>
    WhatsappWebhookDto(
      customerNo: json['customerNo'] as String?,
      message: json['message'] as String?,
      mediaUrl: json['mediaUrl'] as String?,
      deliveryStatus: json['deliveryStatus'] as String?,
      messageId: json['messageId'] as String?,
      waEvent: $enumDecodeNullable(_$WhatsAppEventsEnumMap, json['waEvent'],
          unknownValue: WhatsAppEvents.none),
    );

Map<String, dynamic> _$WhatsappWebhookDtoToJson(WhatsappWebhookDto instance) =>
    <String, dynamic>{
      if (instance.customerNo case final value?) 'customerNo': value,
      if (instance.message case final value?) 'message': value,
      if (instance.mediaUrl case final value?) 'mediaUrl': value,
      if (instance.deliveryStatus case final value?) 'deliveryStatus': value,
      if (instance.messageId case final value?) 'messageId': value,
      if (_$WhatsAppEventsEnumMap[instance.waEvent] case final value?)
        'waEvent': value,
    };

const _$WhatsAppEventsEnumMap = {
  WhatsAppEvents.none: 0,
  WhatsAppEvents.sent: 1,
  WhatsAppEvents.delivered: 2,
  WhatsAppEvents.read: 3,
  WhatsAppEvents.failed: 4,
  WhatsAppEvents.receive: 5,
  WhatsAppEvents.receivedRead: 6,
};
