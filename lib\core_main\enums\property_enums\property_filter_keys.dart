enum PropertyFilterKey {
  lookingFor("Looking For"),
  saleType("Sale Type"),
  propertyType("Property Type"),
  propertySubType("Property Sub Type"),
  listingStatus("Listing Status"),
  offeringType("Offering Type"),
  completionStatus("Completion Status"),
  community("Community"),
  subCommunity("Sub Community"),
  assignTo("Assigned To"),
  facing("Facing"),
  furnishingStatus("Furnishing Status"),
  bHKType("BHK Type"),
  bHK("BHK"),
  floors("Floors"),
  ownerNames("Owner Names"),
  projects("Projects"),
  propertySize("Property Size"),
  dateRange("Date Range"),
  saleableArea("Saleable Area"),
  builtUpArea("Built-Up Area"),
  carpetArea("Carpet Area"),
  locations("Locations"),
  noOfBathrooms("No Of Bathrooms"),
  prospectCount("Prospect Count"),
  leadCount("Lead Count"),
  noOfLivingRooms("No Of LivingRooms"),
  noOfBalconies("No Of Balconies"),
  noOfBedrooms("No Of Bedrooms"),
  noOfKitchens("No Of Kitchens"),
  noOfUtilities("No Of Utilities"),
  minBudget("Price"),
  amenities("Amenities");

  final String description;

  const PropertyFilterKey(this.description);
}
