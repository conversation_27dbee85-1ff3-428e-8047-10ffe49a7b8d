
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';

abstract class CustomAmenitiesAndAttributesLocalDataSource {
  Future<void> saveCustomAmenities(List<CustomAmenitiesModel?>? customAmenitiesModels);

  List<CustomAmenitiesModel?>? getCustomAmenities();

  Future<void> saveCustomAttributes(List<CustomAttributesModel?>? customAttributesModels);

  List<CustomAttributesModel?>? getCustomAttributes();

  Future<void> saveCustomAmenityCategories(List<String?>? customAttributesModels);

  List<String?>? getCustomAmenityCategories();
}
