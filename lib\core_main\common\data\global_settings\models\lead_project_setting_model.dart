import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'lead_project_setting_model.g.dart';

@HiveType(typeId: HiveModelConstants.leadProjectSettingModelTypeId)
@JsonSerializable()
class LeadProjectSettingModel {
  @HiveField(0)
  final bool? isProjectMandatoryEnabled;
  @HiveField(1)
  final bool? isProjectMandatoryOnSiteVisitDone;
  @HiveField(2)
  final bool? isProjectMandatoryOnMeetingDone;
  @HiveField(3)
  final bool? isProjectMandatoryOnBooking;

  LeadProjectSettingModel({
    this.isProjectMandatoryEnabled,
    this.isProjectMandatoryOnSiteVisitDone,
    this.isProjectMandatoryOnMeetingDone,
    this.isProjectMandatoryOnBooking,
  });

  factory LeadProjectSettingModel.fromJson(Map<String, dynamic> json) =>
      _$LeadProjectSettingModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadProjectSettingModelToJson(this);
}
