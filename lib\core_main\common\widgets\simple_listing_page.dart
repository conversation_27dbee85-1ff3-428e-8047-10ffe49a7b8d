import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class SimpleListingPage<T> extends LeadratStatefulWidget {
  final String title;
  final List<ItemSimpleModel<T>> items;
  final void Function(List<ItemSimpleModel<T>> selectedItems) onSave;

  const SimpleListingPage({
    super.key,
    required this.title,
    required this.items,
    required this.onSave,
  });

  @override
  State<SimpleListingPage<T>> createState() => _SimpleListingPageState<T>();
}

class _SimpleListingPageState<T> extends LeadratState<SimpleListingPage<T>> {
  late List<ItemSimpleModel<T>> items;
  late List<ItemSimpleModel<T>> filteredItems;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    items = widget.items.map((item) => item.copyWith(isSelected: false)).toList();
    filteredItems = List.from(items);
  }

  void onItemSelect(ItemSimpleModel<T> item) {
    setState(() {
      final index = items.indexOf(item);
      items[index] = item.copyWith(isSelected: !item.isSelected);

      final filteredIndex = filteredItems.indexOf(item);
      if (filteredIndex != -1) {
        filteredItems[filteredIndex] = item.copyWith(isSelected: !item.isSelected);
      }
    });
  }

  void _searchItems(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredItems = List.from(items);
      } else {
        filteredItems = items.where((item) {
          return item.title.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _updateFilteredItems() {
    setState(() {
      filteredItems = List.from(items);
    });
  }

  @override
  Widget buildContent(BuildContext context) {
    return LeadratForm(
      leadingButton: LeadratFormButton(
        onPressed: () => Navigator.pop(context),
        buttonText: "back",
      ),
      trailingButton: LeadratFormButton(
        onPressed: () {
          final selectedItems = items.where((element) => element.isSelected).toList();
          widget.onSave(selectedItems);
        },
        buttonText: "save & finish",
        isTrailingVisible: true,
      ),
      wrapWithScrollView: false,
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: SvgPicture.asset(
                  ImageResources.iconBack,
                  width: 48,
                  height: 48,
                  colorFilter: const ColorFilter.mode(ColorPalette.primaryDarkColor, BlendMode.srcIn),
                ),
              ),
              Text(widget.title, style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor))
            ],
          ),
          TextField(
            controller: _searchController,
            style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryColor),
            onChanged: (value) {
              _searchItems(value);
            },
            decoration: InputDecoration(
              hintText: "Search item here",
              hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray500),
              prefixIcon: const Icon(Icons.search, color: ColorPalette.gray500, size: 20),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: ColorPalette.gray500,
                        size: 20,
                      ),
                      onPressed: () {
                        _searchController.clear();
                        _updateFilteredItems();
                      },
                    )
                  : null,
            ),
          ),
          Expanded(
            child: ListView.builder(
                itemCount: filteredItems.length,
                itemBuilder: (context, index) {
                  final item = filteredItems[index];
                  return Column(
                    children: [
                      ListTile(
                        onTap: () => onItemSelect(item),
                        contentPadding: const EdgeInsets.all(0),
                        title: Text(
                          item.title,
                          style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                        ),
                        trailing: item.isSelected ? const Icon(Icons.check_box, color: ColorPalette.primaryGreen) : const Icon(Icons.check_box_outline_blank, color: ColorPalette.gray500),
                        visualDensity: const VisualDensity(vertical: -4),
                        style: ListTileStyle.drawer,
                      ),
                      if (index < filteredItems.length - 1) const Divider(thickness: .5),
                    ],
                  );
                }),
          ),
        ],
      ),
    );
  }
}
