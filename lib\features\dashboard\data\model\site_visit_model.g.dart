// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'site_visit_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SiteVisitModel _$SiteVisitModelFromJson(Map<String, dynamic> json) =>
    SiteVisitModel(
      siteVisitDoneUniqueCount:
          (json['siteVisitDoneUniqueCount'] as num?)?.toInt(),
      siteVisitDoneEventCount:
          (json['siteVisitDoneEventCount'] as num?)?.toInt(),
      siteVisitNotDoneUniqueCount:
          (json['siteVisitNotDoneUniqueCount'] as num?)?.toInt(),
      siteVisitNotDoneEventCount:
          (json['siteVisitNotDoneEventCount'] as num?)?.toInt(),
      siteVisitOverdue: (json['siteVisitOverdue'] as num?)?.toInt(),
      upcomingSiteVisits: (json['upcomingSiteVisits'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SiteVisitModelToJson(SiteVisitModel instance) =>
    <String, dynamic>{
      if (instance.siteVisitDoneUniqueCount case final value?)
        'siteVisitDoneUniqueCount': value,
      if (instance.siteVisitDoneEventCount case final value?)
        'siteVisitDoneEventCount': value,
      if (instance.siteVisitNotDoneUniqueCount case final value?)
        'siteVisitNotDoneUniqueCount': value,
      if (instance.siteVisitNotDoneEventCount case final value?)
        'siteVisitNotDoneEventCount': value,
      if (instance.siteVisitOverdue case final value?)
        'siteVisitOverdue': value,
      if (instance.upcomingSiteVisits case final value?)
        'upcomingSiteVisits': value,
    };
