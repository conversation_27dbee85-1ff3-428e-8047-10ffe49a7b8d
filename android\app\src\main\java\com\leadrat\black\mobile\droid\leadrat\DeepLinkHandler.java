package com.leadrat.black.mobile.droid.leadrat;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import java.util.Map;
import java.util.HashMap;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class DeepLinkHandler {
    private static final String DEEP_LINK_CHANNEL = "deepLink";
    private final Activity activity;
    private final FlutterEngine flutterEngine;

    public DeepLinkHandler(Activity activity, FlutterEngine flutterEngine) {
        this.activity = activity;
        this.flutterEngine = flutterEngine;
    }

    public void handleDeepLinkIntent(Intent intent) {
        if (intent == null) return;

        Uri data = intent.getData();
        if (data != null) {
            String screen = data.getQueryParameter("screen");
            String entityId = data.getQueryParameter("id");

            if ("21".equals(screen) || "22".equals(screen)) {
                if (flutterEngine != null) {
                    Map<String, String> deepLinkData = new HashMap<>();
                    deepLinkData.put("screen", screen);
                    deepLinkData.put("id", entityId);

                    new MethodChannel(flutterEngine
                            .getDartExecutor().getBinaryMessenger(), DEEP_LINK_CHANNEL)
                            .invokeMethod("onDeepLinkReceived", deepLinkData);
                }
            }
        }
    }
}