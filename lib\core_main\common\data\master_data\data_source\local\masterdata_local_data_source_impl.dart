import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_associated_bank_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_amenities_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_attribute_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_project_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/common/models/hive_models/master_project_amenitites_map_model.dart';
import 'package:leadrat/core_main/common/models/hive_models/master_property_amenitites_map_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/local_storage_service/local_storage_service.dart';

import '../../models/master_user_services_type_model.dart';

class MasterDataLocalDataSourceImpl implements MasterDataLocalDataSource {
  final LocalStorageService _localStorageService;

  MasterDataLocalDataSourceImpl(this._localStorageService);

  @override
  List<MasterAreaUnitsModel>? getAreaUnits() {
    try {
      return _localStorageService.getAllItems<MasterAreaUnitsModel>(HiveModelConstants.masterAreaUnitBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveAreaUnits(List<MasterAreaUnitsModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterAreaUnitBoxName);
      final validItems = items.whereType<MasterAreaUnitsModel>().toList();
      await _localStorageService.addItems<MasterAreaUnitsModel>(HiveModelConstants.masterAreaUnitBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterProjectTypeModel> getProjectTypes() {
    try {
      return _localStorageService.getAllItems<MasterProjectTypeModel>(HiveModelConstants.masterProjectTypeBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return [];
    }
  }

  @override
  Future<void> saveProjectTypes(List<MasterProjectTypeModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterProjectTypeBoxName);
      final validItems = items.whereType<MasterProjectTypeModel>().toList();
      await _localStorageService.addItems<MasterProjectTypeModel>(HiveModelConstants.masterProjectTypeBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Future<void> saveModifiedDateModels(List<ModifiedDateModel> models) async {
    try {
      await _localStorageService.clearContainer(HiveModelConstants.modifiedDatesBoxName);
      await _localStorageService.addItems<ModifiedDateModel>(HiveModelConstants.modifiedDatesBoxName, models);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<ModifiedDateModel> getModifiedDateModels() {
    try {
      return _localStorageService.getAllItems<ModifiedDateModel>(HiveModelConstants.modifiedDatesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return [];
    }
  }

  @override
  Future<void> updateModifiedDateModel(ModifiedDateModel model) async {
    try {
      final existingModels = getModifiedDateModels();
      final updatedModifiedDates = existingModels.map((item) => item.entityType == model.entityType ? model : item).toList();
      await _localStorageService.clearContainer(HiveModelConstants.modifiedDatesBoxName);
      await _localStorageService.addItems<ModifiedDateModel>(HiveModelConstants.modifiedDatesBoxName, updatedModifiedDates);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterLeadSourceModel>? getLeadSource() {
    try {
      return _localStorageService.getAllItems<MasterLeadSourceModel>(HiveModelConstants.masterLeadSourceBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveLeadSource(List<MasterLeadSourceModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterLeadSourceBoxName);
      final validItems = items.whereType<MasterLeadSourceModel>().toList();
      await _localStorageService.addItems<MasterLeadSourceModel>(HiveModelConstants.masterLeadSourceBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterLeadStatusModel>? getLeadStatuses() {
    try {
      return _localStorageService.getAllItems<MasterLeadStatusModel>(HiveModelConstants.masterLeadStatusBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveLeadStatuses(List<MasterLeadStatusModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterLeadStatusBoxName);
      final validItems = items.whereType<MasterLeadStatusModel>().toList();
      await _localStorageService.addItems<MasterLeadStatusModel>(HiveModelConstants.masterLeadStatusBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Map<String, List<MasterPropertyAmenitiesModel>>? getPropertyAmenitites() {
    try {
      final mapItems = _localStorageService.getAllItems<MasterPropertyAmenititesMapModel>(HiveModelConstants.mapMasterPropertyAmenititesBoxName);
      return Map.fromEntries(mapItems.map((item) => MapEntry(item.key, item.values)));
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> savePropertyAmenitites(Map<String, List<MasterPropertyAmenitiesModel>>? items) async {
    if (items == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.mapMasterPropertyAmenititesBoxName);
      final mapItems = items.entries
          .map(
            (entry) => MasterPropertyAmenititesMapModel(key: entry.key, values: entry.value),
          )
          .toList();
      await _localStorageService.addItems<MasterPropertyAmenititesMapModel>(HiveModelConstants.mapMasterPropertyAmenititesBoxName, mapItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterPropertyAttributesModel>? getPropertyAttributes() {
    try {
      return _localStorageService.getAllItems<MasterPropertyAttributesModel>(HiveModelConstants.masterPropertyAttributesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> savePropertyAttributes(List<MasterPropertyAttributesModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterPropertyAttributesBoxName);
      final validItems = items.whereType<MasterPropertyAttributesModel>().toList();
      await _localStorageService.addItems<MasterPropertyAttributesModel>(HiveModelConstants.masterPropertyAttributesBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterAssociatedBankModel>? getAssociatedBank() {
    try {
      return _localStorageService.getAllItems<MasterAssociatedBankModel>(HiveModelConstants.masterAssociatedBankBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveAssociatedBank(List<MasterAssociatedBankModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterAssociatedBankBoxName);
      final validItems = items.whereType<MasterAssociatedBankModel>().toList();
      await _localStorageService.addItems<MasterAssociatedBankModel>(HiveModelConstants.masterAssociatedBankBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterProjectAttributeModel>? getProjectAttributes() {
    try {
      return _localStorageService.getAllItems<MasterProjectAttributeModel>(HiveModelConstants.masterProjectAttributeBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveProjectAttributes(List<MasterProjectAttributeModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterProjectAttributeBoxName);
      final validItems = items.whereType<MasterProjectAttributeModel>().toList();
      await _localStorageService.addItems<MasterProjectAttributeModel>(HiveModelConstants.masterProjectAttributeBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Future<void> saveProjectAmenitites(Map<String, List<MasterProjectAmenititesModel>>? items) async {
    if (items == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.mapMasterProjectAmenititesBoxName);
      final mapItems = items.entries
          .map(
            (entry) => MasterProjectAmenititesMapModel(key: entry.key, values: entry.value),
          )
          .toList();
      await _localStorageService.addItems<MasterProjectAmenititesMapModel>(HiveModelConstants.mapMasterProjectAmenititesBoxName, mapItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Map<String, List<MasterProjectAmenititesModel>>? getProjectAmenitites() {
    try {
      final mapItems = _localStorageService.getAllItems<MasterProjectAmenititesMapModel>(HiveModelConstants.mapMasterProjectAmenititesBoxName);
      return Map.fromEntries(mapItems.map((item) => MapEntry(item.key, item.values)));
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  List<MasterPropertyTypeModel>? getPropertyTypes() {
    try {
      return _localStorageService.getAllItems<MasterPropertyTypeModel>(HiveModelConstants.masterPropertyTypeBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> savePropertyTypes(List<MasterPropertyTypeModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterPropertyTypeBoxName);
      final validItems = items.whereType<MasterPropertyTypeModel>().toList();
      await _localStorageService.addItems<MasterPropertyTypeModel>(HiveModelConstants.masterPropertyTypeBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Future<void> saveUserServices(List<MasterUserServicesModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterUSerServicesBoxName);
      final validItems = items.whereType<MasterUserServicesModel>().toList();
      await _localStorageService.addItems<MasterUserServicesModel>(HiveModelConstants.masterUSerServicesBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterUserServicesModel>? getUserServices() {
    try {
      return _localStorageService.getAllItems<MasterUserServicesModel>(HiveModelConstants.masterUSerServicesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  List<MasterCustomStatusModel>? getLeadCustomStatus() {
    try {
      return _localStorageService.getAllItems<MasterCustomStatusModel>(HiveModelConstants.masterCustomStatusBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveCustomStatus(List<MasterCustomStatusModel?>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterCustomStatusBoxName);
      final validItems = items.whereType<MasterCustomStatusModel>().toList();
      await _localStorageService.addItems<MasterCustomStatusModel>(HiveModelConstants.masterCustomStatusBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<String>? getAgencyNames() {
    try {
      return _localStorageService.getAllItems<String>(HiveModelConstants.getAgencyBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveAgencyNames(List<String>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.getAgencyBoxName);
      final validItems = items.whereType<String>().toList();
      await _localStorageService.addItems<String>(HiveModelConstants.getAgencyBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<String>? getLeadAddresses() {
    try {
      return _localStorageService.getAllItems<String>(HiveModelConstants.leadAddressesBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveLeadAddresses(List<String>? items) async {
    if (items == null) return;

    try {
      await _localStorageService.clearContainer(HiveModelConstants.leadAddressesBoxName);
      final validItems = items.whereType<String>().toList();
      await _localStorageService.addItems<String>(HiveModelConstants.leadAddressesBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  List<MasterPropertyTypeModel>? getListingPropertyTypes() {
    try {
      return _localStorageService.getAllItems<MasterPropertyTypeModel>(HiveModelConstants.masterPropertyListingTypeBoxName);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> saveListingPropertyTypes(List<MasterPropertyTypeModel?>? items) async {
    if (items == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.masterPropertyListingTypeBoxName);
      final validItems = items.whereType<MasterPropertyTypeModel>().toList();
      await _localStorageService.addItems<MasterPropertyTypeModel>(HiveModelConstants.masterPropertyListingTypeBoxName, validItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }

  @override
  Map<String, List<MasterPropertyAmenitiesModel>>? getPropertyListingAmenitites() {
    try {
      final mapItems = _localStorageService.getAllItems<MasterPropertyAmenititesMapModel>(HiveModelConstants.mapMasterPropertyListingAmenititesBoxName);
      return Map.fromEntries(mapItems.map((item) => MapEntry(item.key, item.values)));
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<void> savePropertyListingAmenitites(Map<String, List<MasterPropertyAmenitiesModel>>? items) async {
    if (items == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.mapMasterPropertyListingAmenititesBoxName);
      final mapItems = items.entries
          .map(
            (entry) => MasterPropertyAmenititesMapModel(key: entry.key, values: entry.value),
      )
          .toList();
      await _localStorageService.addItems<MasterPropertyAmenititesMapModel>(HiveModelConstants.mapMasterPropertyListingAmenititesBoxName, mapItems);
    } catch (ex,stackTrace) {
      ex.logException(stackTrace);
    }
  }
}
