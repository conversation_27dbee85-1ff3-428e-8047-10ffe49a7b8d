// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'convert_to_lead_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConvertToLeadModel _$ConvertToLeadModelFromJson(Map<String, dynamic> json) =>
    ConvertToLeadModel(
      id: json['id'] as String?,
      assignTo: json['assignTo'] as String?,
      leadStatusId: json['leadStatusId'] as String?,
      notes: json['notes'] as String?,
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      convertedDateTime: json['convertedDateTime'] == null
          ? null
          : DateTime.parse(json['convertedDateTime'] as String),
    );

Map<String, dynamic> _$ConvertToLeadModelToJson(ConvertToLeadModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.leadStatusId case final value?) 'leadStatusId': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.scheduledDate?.toIso8601String() case final value?)
        'scheduledDate': value,
      if (instance.projects case final value?) 'projects': value,
      if (instance.convertedDateTime?.toIso8601String() case final value?)
        'convertedDateTime': value,
    };
