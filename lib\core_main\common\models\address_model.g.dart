// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddressModel _$AddressModelFromJson(Map<String, dynamic> json) => AddressModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      placeId: json['placeId'] as String?,
      subLocality: json['subLocality'] as String?,
      locality: json['locality'] as String?,
      district: json['district'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      postalCode: json['postalCode'] as String?,
      longitude: json['longitude'] as String?,
      latitude: json['latitude'] as String?,
      isGoogleMapLocation: json['isGoogleMapLocation'] as bool? ?? false,
      locationId: json['locationId'] as String?,
      isManual: json['isManual'] as bool? ?? false,
      community: json['community'] as String?,
      subCommunity: json['subCommunity'] as String?,
      towerName: json['towerName'] as String?,
    );

Map<String, dynamic> _$AddressModelToJson(AddressModel instance) =>
    <String, dynamic>{
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.id case final value?) 'id': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
      if (instance.placeId case final value?) 'placeId': value,
      if (instance.subLocality case final value?) 'subLocality': value,
      if (instance.locality case final value?) 'locality': value,
      if (instance.district case final value?) 'district': value,
      if (instance.city case final value?) 'city': value,
      if (instance.state case final value?) 'state': value,
      if (instance.country case final value?) 'country': value,
      if (instance.postalCode case final value?) 'postalCode': value,
      if (instance.longitude case final value?) 'longitude': value,
      if (instance.latitude case final value?) 'latitude': value,
      'isGoogleMapLocation': instance.isGoogleMapLocation,
      if (instance.locationId case final value?) 'locationId': value,
      'isManual': instance.isManual,
      if (instance.community case final value?) 'community': value,
      if (instance.subCommunity case final value?) 'subCommunity': value,
      if (instance.towerName case final value?) 'towerName': value,
    };
