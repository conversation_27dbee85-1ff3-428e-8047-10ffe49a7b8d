import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/local_storage_service/local_storage_service.dart';

class CustomAmenitiesAndAttributesLocalDataSourceImpl implements CustomAmenitiesAndAttributesLocalDataSource {
  final LocalStorageService _localStorageService;

  CustomAmenitiesAndAttributesLocalDataSourceImpl(this._localStorageService);

  @override
  Future<void> saveCustomAmenities(List<CustomAmenitiesModel?>? customAmenitiesModels) async {
    if (customAmenitiesModels == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.customAmenitiesBoxName);
      final validItems = customAmenitiesModels.whereType<CustomAmenitiesModel>().toList();
      await _localStorageService.addItems<CustomAmenitiesModel>(HiveModelConstants.customAmenitiesBoxName, validItems);
    } catch (exception) {
      exception.logException();
    }
  }

  @override
  List<CustomAmenitiesModel?>? getCustomAmenities() {
    try {
      final items = _localStorageService.getAllItems<CustomAmenitiesModel>(HiveModelConstants.customAmenitiesBoxName);
      return items.isNotEmpty ? items : null;
    } catch (exception) {
      exception.logException();
      return null;
    }
  }

  @override
  Future<void> saveCustomAttributes(List<CustomAttributesModel?>? customAttributesModels) async {
    if (customAttributesModels == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.customAttributesBoxName);
      final validItems = customAttributesModels.whereType<CustomAttributesModel>().toList();
      await _localStorageService.addItems<CustomAttributesModel>(HiveModelConstants.customAttributesBoxName, validItems);
    } catch (exception) {
      exception.logException();
    }
  }

  @override
  List<CustomAttributesModel?>? getCustomAttributes() {
    try {
      final items = _localStorageService.getAllItems<CustomAttributesModel>(HiveModelConstants.customAttributesBoxName);
      return items.isNotEmpty ? items : null;
    } catch (exception) {
      exception.logException();
      return null;
    }
  }

  @override
  List<String?>? getCustomAmenityCategories() {
    try {
      final items = _localStorageService.getAllItems<String>(HiveModelConstants.customAmenityCategoriesBoxName);
      return items.isNotEmpty ? items : null;
    } catch (exception) {
      exception.logException();
      return null;
    }
  }

  @override
  Future<void> saveCustomAmenityCategories(List<String?>? customAttributesModels) async {
    if (customAttributesModels == null) return;
    try {
      await _localStorageService.clearContainer(HiveModelConstants.customAmenityCategoriesBoxName);
      final validItems = customAttributesModels.whereType<String>().toList();
      await _localStorageService.addItems<String>(HiveModelConstants.customAmenityCategoriesBoxName, validItems);
    } catch (exception) {
      exception.logException();
    }
  }
}
