import 'package:flutter/material.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

import 'link_text_span.dart';

previewDialogueWidget(BuildContext context, {String previewText = "Notes", required TextEditingController? controller}) {
  showDialog(
    context: context,
    builder: (context) => Material(
      color: ColorPalette.transparent,
      child: GestureDetector(
        onTap: () => Navigator.pop(context), // Close when tapping outside
        behavior: HitTestBehavior.opaque,
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevents closing when tapping inside
            behavior: HitTestBehavior.translucent,
            child: Container(
                height: context.width(60),
                width: context.width(100),
                padding: const EdgeInsets.all(7),
                margin: const EdgeInsets.only(right: 16, left: 24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: ColorPalette.white,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(' $previewText (Eng)', style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.darkToneInk)),
                          GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                const Icon(
                                  Icons.highlight_remove_rounded,
                                  color: ColorPalette.red,
                                  size: 15,
                                ),
                                Text(' close ', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.red)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: context.width(45),
                      width: context.width(100),
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: ColorPalette.white, border: Border.all(color: ColorPalette.primary)),
                      child: SingleChildScrollView(
                        child: Text.rich(
                          buildLinkTextSpan(text: controller?.text ?? '', style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.darkToneInk)),
                        ),
                      ),
                    ),
                  ],
                )),
          ),
        ),
      ),
    ),
  );
}
