import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/remote/custom_amenities_and_attributes_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/repository/custom_amenities_and_attributes_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

class CustomAmenitiesAndAttributesRepositoryImpl implements CustomAmenitiesAndAttributesRepository {
  final CustomAmenitiesAndAttributesRemoteDataSource _customAmenitiesAndAttributesRemoteDataSource;
  final CustomAmenitiesAndAttributesLocalDataSource _customAmenitiesAndAttributesLocalDataSource;
  final MasterDataLocalDataSource _localDataSource;

  CustomAmenitiesAndAttributesRepositoryImpl(
    this._customAmenitiesAndAttributesRemoteDataSource,
    this._customAmenitiesAndAttributesLocalDataSource,
    this._localDataSource,
  );

  @override
  Future<List<CustomAmenitiesModel?>?> getCustomAmenities({bool restore = false, Duration timeout = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.customMasterAmenity,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.customMasterAmenity,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      List<CustomAmenitiesModel?>? localData = _customAmenitiesAndAttributesLocalDataSource.getCustomAmenities();

      if (restore || localData == null || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _customAmenitiesAndAttributesRemoteDataSource.getCustomAmenities();
        if (response != null) {
          await _customAmenitiesAndAttributesLocalDataSource.saveCustomAmenities(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);

          return response;
        }
      }

      return localData;
    } catch (e) {
      e.logException();
      return null;
    }
  }

  @override
  Future<List<CustomAttributesModel?>?> getCustomAttributes({bool restore = false, Duration timeout = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.customMasterAttribute,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.customMasterAttribute,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      List<CustomAttributesModel?>? localData = _customAmenitiesAndAttributesLocalDataSource.getCustomAttributes();

      if (restore || localData == null || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _customAmenitiesAndAttributesRemoteDataSource.getCustomAttributes();
        if (response != null) {
          await _customAmenitiesAndAttributesLocalDataSource.saveCustomAttributes(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e) {
      e.logException();
      return null;
    }
  }

  @override
  Future<List<String?>?> getCustomAmenityCategories({bool restore = false, Duration duration = const Duration(seconds: 60), Duration timeout = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.customMasterAmenity,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.customMasterAmenity,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      List<String?>? localData = _customAmenitiesAndAttributesLocalDataSource.getCustomAmenityCategories();

      if (restore || localData == null || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _customAmenitiesAndAttributesRemoteDataSource.getCustomAmenityCategories(timeout: timeout);
        if (response != null) {
          await _customAmenitiesAndAttributesLocalDataSource.saveCustomAmenityCategories(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e) {
      e.logException();
      return null;
    }
  }
}
