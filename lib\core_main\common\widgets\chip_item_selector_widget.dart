import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/cubit/chip_item_selector_cubit.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

enum ChipLayoutType { wrap, scroll, seeMore }

class ChipItemSelectorWidget<T> extends LeadratStatelessWidget {
  final List<ItemSimpleModel<T>> items;
  final String title;
  final bool isRequired;
  final bool isMultipleSelectionEnabled;
  final List<ItemSimpleModel<T>>? initialSelectedItems;
  final ItemSimpleModel<T>? initialSelectedItem;
  final Function(List<ItemSimpleModel<T>>)? onSelectionChanged;
  final Function(ItemSimpleModel<T>)? onItemSelected;
  final ChipLayoutType layoutType;
  final int initialVisibleCount;
  final String seeMoreText;
  final String seeLessText;
  final EdgeInsetsGeometry? chipPadding;

  const ChipItemSelectorWidget({
    super.key,
    required this.items,
    required this.title,
    this.isRequired = false,
    this.isMultipleSelectionEnabled = false,
    this.initialSelectedItems,
    this.initialSelectedItem, // New parameter
    this.onSelectionChanged,
    this.onItemSelected,
    this.layoutType = ChipLayoutType.scroll,
    this.initialVisibleCount = 5,
    this.seeMoreText = 'See More',
    this.seeLessText = 'See Less',
    this.chipPadding,
  }) : assert(
          !(initialSelectedItems != null && initialSelectedItem != null),
          'Cannot provide both initialSelectedItems and initialSelectedItem. Use only one based on isMultipleSelectionEnabled.',
        );

  @override
  Widget buildContent(BuildContext context) {
    List<ItemSimpleModel<T>>? resolvedInitialSelectedItems;

    if (initialSelectedItem != null) {
      resolvedInitialSelectedItems = [initialSelectedItem!];
    } else if (initialSelectedItems != null) {
      resolvedInitialSelectedItems = initialSelectedItems;
    }

    return BlocProvider(
      create: (context) => ChipItemSelectorCubit<T>(
        isMultipleSelectionEnabled: isMultipleSelectionEnabled,
        initialSelectedItems: resolvedInitialSelectedItems,
      ),
      child: _ChipSelectorContent<T>(
        items: items,
        title: title,
        isRequired: isRequired,
        isMultipleSelectionEnabled: isMultipleSelectionEnabled,
        initialSelectedItems: initialSelectedItems,
        initialSelectedItem: initialSelectedItem,
        onSelectionChanged: onSelectionChanged,
        onItemSelected: onItemSelected,
        layoutType: layoutType,
        initialVisibleCount: initialVisibleCount,
        seeMoreText: seeMoreText,
        seeLessText: seeLessText,
        chipPadding: chipPadding,
      ),
    );
  }
}

class _ChipSelectorContent<T> extends StatefulWidget {
  final List<ItemSimpleModel<T>> items;
  final String title;
  final bool isRequired;
  final bool isMultipleSelectionEnabled;
  final List<ItemSimpleModel<T>>? initialSelectedItems;
  final ItemSimpleModel<T>? initialSelectedItem;
  final Function(List<ItemSimpleModel<T>>)? onSelectionChanged;
  final Function(ItemSimpleModel<T>)? onItemSelected;
  final ChipLayoutType layoutType;
  final int initialVisibleCount;
  final String seeMoreText;
  final String seeLessText;
  final EdgeInsetsGeometry? chipPadding;

  const _ChipSelectorContent({
    required this.items,
    required this.title,
    required this.isRequired,
    required this.isMultipleSelectionEnabled,
    this.initialSelectedItems,
    this.initialSelectedItem,
    this.onSelectionChanged,
    this.onItemSelected,
    required this.layoutType,
    required this.initialVisibleCount,
    required this.seeMoreText,
    required this.seeLessText,
    this.chipPadding,
  });

  @override
  State<_ChipSelectorContent<T>> createState() => _ChipSelectorContentState<T>();
}

class _ChipSelectorContentState<T> extends State<_ChipSelectorContent<T>> {
  bool _showAll = false;

  @override
  void didUpdateWidget(_ChipSelectorContent<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    final oldInitialSelectedItems = _resolveInitialSelectedItems(
      oldWidget.initialSelectedItem,
      oldWidget.initialSelectedItems,
    );
    final newInitialSelectedItems = _resolveInitialSelectedItems(
      widget.initialSelectedItem,
      widget.initialSelectedItems,
    );

    if (!_areSelectionListsEqual(oldInitialSelectedItems, newInitialSelectedItems)) {
      context.read<ChipItemSelectorCubit<T>>().updateSelection(newInitialSelectedItems);
    }
  }

  List<ItemSimpleModel<T>>? _resolveInitialSelectedItems(
    ItemSimpleModel<T>? initialSelectedItem,
    List<ItemSimpleModel<T>>? initialSelectedItems,
  ) {
    if (initialSelectedItem != null) {
      return [initialSelectedItem];
    } else if (initialSelectedItems != null) {
      return initialSelectedItems;
    }
    return null;
  }

  bool _areSelectionListsEqual(
    List<ItemSimpleModel<T>>? list1,
    List<ItemSimpleModel<T>>? list2,
  ) {
    if (list1 == null && list2 == null) return true;
    if (list1 == null || list2 == null) return false;
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (list1[i].value != list2[i].value) {
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: widget.title,
                style: LexendTextStyles.lexend12Medium.copyWith(
                  color: ColorPalette.darkToneInk,
                ),
              ),
              if (widget.isRequired)
                TextSpan(
                  text: ' *',
                  style: LexendTextStyles.lexend12Medium.copyWith(
                    color: Colors.red,
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        BlocBuilder<ChipItemSelectorCubit<T>, ChipItemSelectorState<T>>(
          builder: (context, state) {
            return _buildChipLayout(context, state);
          },
        ),
      ],
    );
  }

  Widget _buildChipLayout(BuildContext context, ChipItemSelectorState<T> state) {
    switch (widget.layoutType) {
      case ChipLayoutType.wrap:
        return _buildWrapLayout(context, state);
      case ChipLayoutType.scroll:
        return _buildScrollLayout(context, state);
      case ChipLayoutType.seeMore:
        return _buildSeeMoreLayout(context, state);
    }
  }

  Widget _buildWrapLayout(BuildContext context, ChipItemSelectorState<T> state) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.items.map((item) => _buildChipItem(context, state, item)).toList(),
    );
  }

  Widget _buildScrollLayout(BuildContext context, ChipItemSelectorState<T> state) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: widget.items
            .map((item) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _buildChipItem(context, state, item),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildSeeMoreLayout(BuildContext context, ChipItemSelectorState<T> state) {
    final itemsToShow = _showAll ? widget.items : widget.items.take(widget.initialVisibleCount).toList();

    final hasMoreItems = widget.items.length > widget.initialVisibleCount;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...itemsToShow.map((item) => _buildChipItem(context, state, item)),
            if (hasMoreItems) _buildSeeMoreChip(),
          ],
        ),
      ],
    );
  }

  Widget _buildSeeMoreChip() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showAll = !_showAll;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: Border.all(color: ColorPalette.primaryColor),
          color: Colors.transparent,
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _showAll ? widget.seeLessText : '+${widget.items.length - widget.initialVisibleCount}',
              style: LexendTextStyles.lexend9SemiBold.copyWith(
                color: ColorPalette.primaryColor,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              _showAll ? Icons.expand_less : Icons.expand_more,
              size: 16,
              color: ColorPalette.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChipItem(BuildContext context, ChipItemSelectorState<T> state, ItemSimpleModel<T> item) {
    return GestureDetector(
      onTap: () {
        context.read<ChipItemSelectorCubit<T>>().selectItem(item);

        final cubit = context.read<ChipItemSelectorCubit<T>>();
        if (widget.isMultipleSelectionEnabled) {
          widget.onSelectionChanged?.call(cubit.getSelectedItems());
        } else {
          final selectedItem = cubit.getSelectedItem();
          if (selectedItem != null) {
            widget.onItemSelected?.call(selectedItem);
          }
          widget.onSelectionChanged?.call(cubit.getSelectedItems());
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: Border.all(color: state.isSelected(item) ? ColorPalette.primaryGreen : ColorPalette.veryLightGray),
          color: state.isSelected(item) ? ColorPalette.primaryGreen : Colors.transparent,
        ),
        padding: widget.chipPadding ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
        child: Text(
          item.title,
          style: LexendTextStyles.lexend9SemiBold.copyWith(
            color: state.isSelected(item) ? ColorPalette.white : ColorPalette.tertiaryTextColor,
          ),
        ),
      ),
    );
  }
}
