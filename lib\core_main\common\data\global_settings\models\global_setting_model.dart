import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/call_settings_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/country_info_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/lead_notes_settings.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/lead_project_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/otp_settings_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/whatsapp_template.dart';
import 'package:leadrat/core_main/common/models/notification/notification_settings_model.dart';

part 'global_setting_model.g.dart';

@HiveType(typeId: HiveModelConstants.globalSettingsModelTypeId)
@JsonSerializable()
class GlobalSettingModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final DateTime? lastModifiedOn;
  @HiveField(2)
  final String? lastModifiedBy;
  @HiveField(3)
  final NotificationSettingsModel? notificationSettings;
  @HiveField(4)
  final CallSettingsModel? callSettings;
  @HiveField(5)
  final bool? hasInternationalSupport;
  @HiveField(6)
  final bool? isLeadsExportEnabled;
  @HiveField(7)
  final bool? isLeadSourceEditable;
  @HiveField(8)
  final DateTime? dayStartTime;
  @HiveField(9)
  final DateTime? dayEndTime;
  @HiveField(10)
  final WhatsAppTemplates? whatsAppMessageTemplates;
  @HiveField(11)
  final bool? isCallDetectionActivated;
  @HiveField(12)
  final LeadNotesSettings? leadNotesSetting;
  @HiveField(13)
  final bool? isMaskedLeadContactNo;
  @HiveField(14)
  final bool? isDualOwnershipEnabled;
  @HiveField(15)
  final bool? isIVROutboundEnabled;
  @HiveField(16)
  final bool? isVirtualNumberRequiredForOutbound;
  @HiveField(17)
  final LeadProjectSettingModel? leadProjectSetting;
  @HiveField(18)
  final bool? isTimeZoneEnabled;
  @HiveField(19)
  final List<CountryInfoModel>? countries;
  @HiveField(20)
  final OtpSettingsModel? otpSettings;
  @HiveField(21)
  final bool? isCopyPasteEnabled;
  @HiveField(22)
  final bool? isScreenshotEnabled;
  @HiveField(23)
  final bool? isCustomStatusEnabled;
  @HiveField(24)
  final bool? shouldHideDashBoard;
  @HiveField(25)
  final bool? isWhatsAppDeepIntegration;
  @HiveField(26)
  final bool? isCustomLeadFormEnabled;
  @HiveField(27)
  final bool? isStickyAgentEnabled;
  @HiveField(28)
  final bool? shouldEnablePropertyListing;
  @HiveField(29)
  final DuplicateLeadFeatureInfo? duplicateLeadFeatureInfo;
  @HiveField(30)
  final Map<String, String?>? defaultValues;
  @HiveField(31)
  final bool? isAssignedCallLogsEnabled;
  @HiveField(32)
  final bool? isPastDateSelectionEnabled;
  @HiveField(33)
  final bool? shouldRenameSiteVisitColumn;
  @HiveField(34)
  final GeneralSettings? generalSettings;

  GlobalSettingModel({
    this.id,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.hasInternationalSupport,
    this.isLeadsExportEnabled,
    this.isLeadSourceEditable,
    this.dayStartTime,
    this.dayEndTime,
    this.whatsAppMessageTemplates,
    this.isCallDetectionActivated,
    this.leadNotesSetting,
    this.isMaskedLeadContactNo,
    this.isDualOwnershipEnabled,
    this.isIVROutboundEnabled,
    this.isVirtualNumberRequiredForOutbound,
    this.leadProjectSetting,
    this.isTimeZoneEnabled,
    this.countries,
    this.otpSettings,
    this.isCopyPasteEnabled,
    this.isScreenshotEnabled,
    this.isCustomStatusEnabled,
    this.shouldHideDashBoard,
    this.isWhatsAppDeepIntegration,
    this.notificationSettings,
    this.callSettings,
    this.isCustomLeadFormEnabled,
    this.isStickyAgentEnabled,
    this.shouldEnablePropertyListing,
    this.duplicateLeadFeatureInfo,
    this.defaultValues,
    this.isAssignedCallLogsEnabled,
    this.shouldRenameSiteVisitColumn,
    this.isPastDateSelectionEnabled,
    this.generalSettings,
  });

  factory GlobalSettingModel.fromJson(Map<String, dynamic> json) => _$GlobalSettingModelFromJson(json);

  Map<String, dynamic> toJson() => _$GlobalSettingModelToJson(this);
}

@HiveType(typeId: HiveModelConstants.duplicateLeadFeatureInfoId)
@JsonSerializable()
class DuplicateLeadFeatureInfo {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final String? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final String? lastModifiedOn;
  @HiveField(6)
  final String? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final bool? allowAllDuplicates;
  @HiveField(9)
  final bool? isFeatureAdded;
  @HiveField(10)
  final bool? isSourceBased;
  @HiveField(11)
  final bool? isSubSourceBased;
  @HiveField(12)
  final bool? isProjectBased;
  @HiveField(33)
  final bool? isLocationBased;
  @HiveField(14)
  final List<String?>? statusIds;

  DuplicateLeadFeatureInfo({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.allowAllDuplicates,
    this.isFeatureAdded,
    this.isSourceBased,
    this.isSubSourceBased,
    this.isProjectBased,
    this.isLocationBased,
    this.statusIds,
  });

  factory DuplicateLeadFeatureInfo.fromJson(Map<String, dynamic> json) => _$DuplicateLeadFeatureInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DuplicateLeadFeatureInfoToJson(this);
}

@HiveType(typeId: HiveModelConstants.globalSettingGeneralSettingInfoId)
@JsonSerializable()
class GeneralSettings {
  @HiveField(0)
  final bool? isLocationMandatory;

  const GeneralSettings({
    this.isLocationMandatory,
  });

  factory GeneralSettings.fromJson(Map<String, dynamic> json) => _$GeneralSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$GeneralSettingsToJson(this);
}
