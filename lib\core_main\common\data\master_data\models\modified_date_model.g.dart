// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'modified_date_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ModifiedDateModelAdapter extends TypeAdapter<ModifiedDateModel> {
  @override
  final int typeId = 4;

  @override
  ModifiedDateModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ModifiedDateModel(
      entityType: fields[0] as EntityTypeEnum?,
      lastModifiedDate: fields[1] as DateTime?,
      lastUpdatedLocallyDate: fields[2] as DateTime?,
      userModifiedDates:
          (fields[3] as Map?)?.cast<UserModifiedDateEnum, ModifiedDateModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, ModifiedDateModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.entityType)
      ..writeByte(1)
      ..write(obj.lastModifiedDate)
      ..writeByte(2)
      ..write(obj.lastUpdatedLocallyDate)
      ..writeByte(3)
      ..write(obj.userModifiedDates);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModifiedDateModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
