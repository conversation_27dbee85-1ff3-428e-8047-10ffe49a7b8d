import 'package:hive_flutter/adapters.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'module_name.g.dart';

@HiveType(typeId: HiveModelConstants.appModuleNameTypeId)
@JsonEnum(valueField: 'index')
enum AppModuleName {
  @HiveField(0)
  lead('Lead'),
  @HiveField(1)
  todo('Todo'),
  @HiveField(2)
  integration('Integration'),
  @HiveField(3)
  user('User'),
  @HiveField(4)
  profile('Profile'),
  @HiveField(5)
  project('Project'),
  @HiveField(6)
  property('Property'),
  @HiveField(7)
  team('Team'),
  @HiveField(8)
  email('Email');

  final String description;

  const AppModuleName(this.description);
}
