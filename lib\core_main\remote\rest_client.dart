import 'package:dio/dio.dart';
import 'package:leadrat/core_main/enums/remote_enum/request_methods.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class RestClient {
  Dio dio = Dio();

  RestClient({required String restClientCode}) {
    if (dio.options.baseUrl.isEmpty) {
      final baseUrl = _getRestClientBaseUrl(restClientCode);
      if (baseUrl == null) throw Exception("Base url not found!");
      dio.options.baseUrl = baseUrl;
    }
  }

  String? _getRestClientBaseUrl(String restClientCode) {
    if (restClientCode == "core") {
      return RestResources.leadratBaseUrl;
    } else if (restClientCode == "identity") {
      return RestResources.leadratIdentityBaseUrl;
    }
    return null;
  }
}

class RestRequest extends Options {
  final String path;
  final dynamic body;
  RequestMethod? requestMethod = RequestMethod.get;
  final Duration? timeout;

  RestRequest(this.path, this.body, {RequestMethod? method, dynamic, this.timeout}) {
    requestMethod = method;
  }

  setRequestHeaders(Map<String, dynamic>? headers) {
    super.headers = headers;
  }
}
