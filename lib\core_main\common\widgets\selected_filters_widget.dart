import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_cubit.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/common/widgets/saved_filter/saved_filter_input_dialog_widget.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class SelectedFiltersWidget<T> extends LeadratStatelessWidget {
  final List<ItemSimpleModel<T>> selectedFilters;
  final SavedFilterModule module;
  final Map<String, dynamic>? filterCriteria;
  final void Function(ItemSimpleModel<T> selectedFilter)? removeFilter;
  final void Function()? clearFilter;
  final void Function(ItemSimpleModel<GetSavedFilterModel>? filter)? onUpdateSavedFilters;
  final bool canSaveFilter;

  const SelectedFiltersWidget({
    super.key,
    required this.module,
    this.selectedFilters = const [],
    this.removeFilter,
    this.clearFilter,
    this.onUpdateSavedFilters,
    this.canSaveFilter = false,
    this.filterCriteria,
  });

  @override
  Widget buildContent(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 14),
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(right: 14),
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: EdgeInsets.fromLTRB(24, 10, canSaveFilter ? 84 : 14, 0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: selectedFilters.map((selectedFilter) {
                  return Padding(
                      padding: const EdgeInsets.only(right: 5),
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(12, 8, 0, 8),
                        decoration: BoxDecoration(
                          color: ColorPalette.lightBackground.withOpacity(.4),
                          borderRadius: BorderRadius.circular(100),
                          border: Border.all(color: ColorPalette.primaryLightColor, width: 1.5),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "${selectedFilter.title} ${selectedFilter.description ?? ''}",
                              style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.gray600),
                            ),
                            GestureDetector(
                              onTap: () {
                                if (removeFilter != null) {
                                  removeFilter!(selectedFilter);
                                }
                              },
                              child: const Padding(
                                padding: EdgeInsets.only(right: 8, left: 8),
                                child: Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.tertiaryTextColor, size: 16),
                              ),
                            ),
                          ],
                        ),
                      ));
                }).toList(),
              ),
            ),
          ),
        ),
        if (canSaveFilter)
          BlocBuilder<SaveFilterCubit, SaveFilterState>(
            buildWhen: (previous, current) => previous.isUpdate != current.isUpdate,
            builder: (context, state) {
              return Align(
                alignment: Alignment.centerRight,
                child: Container(
                  margin: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                  padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF232527),
                    borderRadius: const BorderRadius.only(topLeft: Radius.circular(100), bottomLeft: Radius.circular(100)),
                    border: Border.all(color: ColorPalette.primaryLightColor, width: 1.5),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          if (state.isUpdate && onUpdateSavedFilters != null) {
                            onUpdateSavedFilters!(state.filter);
                          } else {
                            showDialog(context: context, barrierDismissible: true, builder: (context) => SavedFilterInputDialogWidget(filterCriteria: filterCriteria, module: module));
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: ColorPalette.white),
                            child: Icon(
                              state.isUpdate ? Icons.edit_outlined : Icons.check,
                              color: const Color(0xFF232527),
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          if (clearFilter != null) clearFilter!();
                          getIt<SavedFilterBloc>().add(ToggleAppliedFilterEvent());
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: ColorPalette.gray400),
                            child: const Icon(
                              Icons.close,
                              color: Color(0xFF232527),
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}
