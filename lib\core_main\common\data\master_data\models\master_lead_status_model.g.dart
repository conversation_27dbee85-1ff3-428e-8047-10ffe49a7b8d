// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_lead_status_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterLeadStatusModelAdapter extends TypeAdapter<MasterLeadStatusModel> {
  @override
  final int typeId = 25;

  @override
  MasterLeadStatusModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterLeadStatusModel(
      id: fields[0] as String?,
      baseId: fields[1] as String?,
      level: fields[2] as int?,
      status: fields[3] as String?,
      displayName: fields[4] as String?,
      actionName: fields[5] as String?,
      masterLeadStatusId: fields[6] as String?,
      childTypes: (fields[7] as List?)?.cast<MasterLeadStatusModel>(),
      lastModifiedOn: fields[8] as DateTime?,
      isDefault: fields[9] as bool?,
      isDefaultChild: fields[10] as bool?,
      isLrbStatus: fields[11] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterLeadStatusModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.baseId)
      ..writeByte(2)
      ..write(obj.level)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.displayName)
      ..writeByte(5)
      ..write(obj.actionName)
      ..writeByte(6)
      ..write(obj.masterLeadStatusId)
      ..writeByte(7)
      ..write(obj.childTypes)
      ..writeByte(8)
      ..write(obj.lastModifiedOn)
      ..writeByte(9)
      ..write(obj.isDefault)
      ..writeByte(10)
      ..write(obj.isDefaultChild)
      ..writeByte(11)
      ..write(obj.isLrbStatus);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterLeadStatusModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterLeadStatusModel _$MasterLeadStatusModelFromJson(
        Map<String, dynamic> json) =>
    MasterLeadStatusModel(
      id: json['id'] as String?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      status: json['status'] as String?,
      displayName: json['displayName'] as String?,
      actionName: json['actionName'] as String?,
      masterLeadStatusId: json['masterLeadStatusId'] as String?,
      childTypes: (json['childTypes'] as List<dynamic>?)
          ?.map(
              (e) => MasterLeadStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      isDefault: json['isDefault'] as bool?,
      isDefaultChild: json['isDefaultChild'] as bool?,
      isLrbStatus: json['isLrbStatus'] as bool?,
    );

Map<String, dynamic> _$MasterLeadStatusModelToJson(
        MasterLeadStatusModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'baseId': instance.baseId,
      'level': instance.level,
      'status': instance.status,
      'displayName': instance.displayName,
      'actionName': instance.actionName,
      'masterLeadStatusId': instance.masterLeadStatusId,
      'childTypes': instance.childTypes,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'isDefault': instance.isDefault,
      'isDefaultChild': instance.isDefaultChild,
      'isLrbStatus': instance.isLrbStatus,
    };
