// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadStatusModel _$LeadStatusModelFromJson(Map<String, dynamic> json) =>
    LeadStatusModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      status: json['status'] as String?,
      displayName: json['displayName'] as String?,
      actionName: json['actionName'] as String?,
      childType: json['childType'] == null
          ? null
          : LeadStatusModel.fromJson(json['childType'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LeadStatusModelToJson(LeadStatusModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'baseId': instance.baseId,
      'level': instance.level,
      'status': instance.status,
      'displayName': instance.displayName,
      'actionName': instance.actionName,
      'childType': instance.childType,
    };

LeadStatusCustomModel _$LeadStatusCustomModelFromJson(
        Map<String, dynamic> json) =>
    LeadStatusCustomModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      baseId: json['baseId'] as String?,
      level: (json['level'] as num?)?.toInt(),
      status: json['status'] as String?,
      displayName: json['displayName'] as String?,
      actionName: json['actionName'] as String?,
      shouldUseForBooking: json['shouldUseForBooking'] as bool?,
      shouldUseForBookingCancel: json['shouldUseForBookingCancel'] as bool?,
      shouldOpenAppointmentPage: json['shouldOpenAppointmentPage'] as bool?,
      shouldUseForMeeting: json['shouldUseForMeeting'] as bool?,
      childType: json['childType'] == null
          ? null
          : LeadStatusCustomModel.fromJson(
              json['childType'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LeadStatusCustomModelToJson(
        LeadStatusCustomModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'baseId': instance.baseId,
      'level': instance.level,
      'status': instance.status,
      'displayName': instance.displayName,
      'actionName': instance.actionName,
      'shouldUseForBooking': instance.shouldUseForBooking,
      'shouldUseForBookingCancel': instance.shouldUseForBookingCancel,
      'shouldOpenAppointmentPage': instance.shouldOpenAppointmentPage,
      'shouldUseForMeeting': instance.shouldUseForMeeting,
      'childType': instance.childType,
    };
