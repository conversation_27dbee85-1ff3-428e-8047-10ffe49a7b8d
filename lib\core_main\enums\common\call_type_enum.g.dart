// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CallTypeAdapter extends TypeAdapter<CallType> {
  @override
  final int typeId = 15;

  @override
  CallType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 3:
        return CallType.notSet;
      case 0:
        return CallType.direct;
      case 1:
        return CallType.ivr;
      default:
        return CallType.notSet;
    }
  }

  @override
  void write(BinaryWriter writer, CallType obj) {
    switch (obj) {
      case CallType.notSet:
        writer.writeByte(3);
        break;
      case CallType.direct:
        writer.writeByte(0);
        break;
      case CallType.ivr:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
