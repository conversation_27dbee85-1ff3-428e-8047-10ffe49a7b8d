// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDocumentModel _$UserDocumentModelFromJson(Map<String, dynamic> json) =>
    UserDocumentModel(
      id: json['id'] as String?,
      docName: json['docName'] as String?,
      filePath: json['filePath'] as String?,
      documentType:
          $enumDecodeNullable(_$DocumentTypeEnumMap, json['documentType']),
      userId: json['userId'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$UserDocumentModelToJson(UserDocumentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'docName': instance.docName,
      'filePath': instance.filePath,
      'documentType': _$DocumentTypeEnumMap[instance.documentType],
      'userId': instance.userId,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.defaultType: 0,
  DocumentType.identity: 1,
  DocumentType.experience: 2,
  DocumentType.signature: 3,
  DocumentType.jpg: 4,
  DocumentType.png: 5,
  DocumentType.pdf: 6,
  DocumentType.docx: 7,
  DocumentType.txt: 8,
  DocumentType.csv: 9,
};
