import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../main.dart';
import '../../resources/theme/text_styles.dart';
import '../data/user/models/leadrat_subscription_details_model.dart';

class SubscriptionExpiryPage extends StatelessWidget {
  final VoidCallback? onClose;
  final VoidCallback? onContactNow;
  final String? userLicenseCount;
  final String? expiryDate;
  final String? daysRemaining;
  final String? contactNumber1;
  final String? contactNumber2;
  final int? suspensionDays;

  const SubscriptionExpiryPage({
    Key? key,
    this.onClose,
    this.onContactNow,
    this.userLicenseCount,
    this.expiryDate,
    this.daysRemaining,
    this.contactNumber1 = '+91 **********',
    this.contactNumber2 = '+91 **********',
    this.suspensionDays,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String suspensionDateStr = '-';
    String suspendedInStr = '';
    int? daysUntilSuspension;
    if (expiryDate != null && expiryDate != '-') {
      try {
        final DateFormat formatter = DateFormat('dd/MM/yyyy');
        final expiry = formatter.parse(expiryDate!);
        final now = DateTime.now();
        daysUntilSuspension = expiry.difference(DateTime(now.year, now.month, now.day)).inDays;
        suspensionDateStr = DateFormat('d MMMM yyyy').format(expiry);
        if (daysUntilSuspension < 0) {
          suspendedInStr = 'Suspended';
        } else if (daysUntilSuspension == 0) {
          suspendedInStr = 'Suspended in today';
        } else {
          suspendedInStr = 'Suspended in $daysUntilSuspension days';
        }
      } catch (ex, stackTracks) {
        ex.logException(stackTracks);
        suspensionDateStr = '-';
        suspendedInStr = '';
      }
    }
    return Material(
      color: ColorPalette.transparent,
      child: Stack(
        fit: StackFit.expand,
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              color: Colors.black.withOpacity(0),
            ),
          ),
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: ColorPalette.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: ColorPalette.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: ColorPalette.primaryLightColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            'Your Leadrat CRM subscription is about to expire',
                            style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.superSilver),
                          ),
                        ),
                        GestureDetector(
                          onTap: onClose ?? () => Navigator.of(context).pop(),
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: ColorPalette.white,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 14,
                              color: ColorPalette.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Center(
                          child: SvgPicture.asset(
                            width: 140,
                            height: 141,
                            ImageResources.imageComputerVector,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Text('We noticed that your subscription is nearing its expiry.\nPlease ensure timely renewal to avoid any\ndisruption in your services.', textAlign: TextAlign.center, style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray800)),
                        const SizedBox(height: 20),

                        // Table UI update
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Table(
                            border: TableBorder.all(color: ColorPalette.gray300, width: 1),
                            columnWidths: const {
                              0: FlexColumnWidth(1.2),
                              1: FlexColumnWidth(1.1),
                              2: FlexColumnWidth(1.1),
                            },
                            children: [
                              TableRow(
                                decoration: const BoxDecoration(
                                  color: ColorPalette.gray100,
                                ),
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                                    child: Center(
                                      child: Text(
                                        'No. Of License',
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray800),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                                    child: Center(
                                      child: Text(
                                        'Expiry Date',
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray800),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                                    child: Center(
                                      child: Text(
                                        'Days Remaining',
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray800),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              TableRow(
                                decoration: const BoxDecoration(
                                  color: ColorPalette.white,
                                ),
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                                    child: Center(
                                      child: Text(
                                        userLicenseCount!,
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.primaryLightColor),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                                    child: Center(
                                      child: Text(
                                        expiryDate!,
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.primaryLightColor),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                                    child: Center(
                                      child: Text(
                                        daysRemaining!,
                                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.primaryLightColor),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          'Current Plan',
                          style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.gray800, decoration: TextDecoration.underline),
                        ),

                        const SizedBox(height: 8),
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray800),
                            children: [
                              const TextSpan(
                                text: 'In case of non-renewal, your account will be ',
                              ),
                              TextSpan(
                                text: daysUntilSuspension == null || suspendedInStr.isEmpty
                                    ? ''
                                    : daysUntilSuspension < 0
                                        ? 'Suspended ($suspensionDateStr)'
                                        : '$suspendedInStr ($suspensionDateStr)',
                                style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.red600),
                              ),
                              const TextSpan(
                                text: '.\nPlease contact your Leadrat Representative for\nrenewal at ',
                              ),
                              TextSpan(
                                text: contactNumber1!,
                                style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryColor),
                              ),
                              const TextSpan(
                                text: ' or ',
                              ),
                              TextSpan(
                                text: contactNumber2!,
                                style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryColor),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),
                        Center(
                          child: SizedBox(
                            width: 124,
                            height: 34,
                            child: ElevatedButton(
                              onPressed: () async {
                                if (onContactNow != null) {
                                  onContactNow!();
                                } else {
                                  Navigator.of(context).pop();
                                  await SubscriptionExpiryHelper._makePhoneCall(contactNumber2!);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorPalette.primaryColor,
                                foregroundColor: ColorPalette.white,
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    ImageResources.iconPhoneInTalk,
                                    fit: BoxFit.cover,
                                    width: 12,
                                    height: 12,
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    'Contact Now',
                                    style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.white),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SubscriptionExpiryHelper {
  static Future<void> showSubscriptionExpiryOverlay({
    LeadratSubscriptionDetailsModel? subscriptionDetails,
    VoidCallback? onContactNow,
    int suspensionDays = 5,
  }) async {
    final licenseCount = '${subscriptionDetails?.totalSoldLicenses ?? 0} Users';
    final expiryDate = subscriptionDetails?.licenseValidity != null ? DateFormat('dd/MM/yyyy').format(subscriptionDetails!.licenseValidity!) : '-';
    final daysRemaining = '${subscriptionDetails?.validDays ?? 0} Days';
    final context = MyApp.navigatorKey.currentState?.context;
    await showDialog(
      context: context!,
      barrierDismissible: false,
      builder: (context) {
        return SubscriptionExpiryPage(
          userLicenseCount: licenseCount,
          expiryDate: expiryDate,
          daysRemaining: daysRemaining,
          contactNumber1: '+91 **********',
          contactNumber2: '+91 **********',
          suspensionDays: suspensionDays,
          onContactNow: onContactNow ??
              () async {
                Navigator.of(context).pop();
                await _makePhoneCall('+91 **********'); // Second number
              },
          onClose: () => Navigator.of(context).pop(),
        );
      },
    );
  }

  static Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      }
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
    }
  }
}
