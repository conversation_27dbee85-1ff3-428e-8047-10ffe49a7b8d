import 'package:leadrat/core_main/common/data/saved_filter/data_source/remote/saved_filter_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/create_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';

class SavedFilterRemoteDataSourceImpl extends LeadratRestService implements SavedFilterRemoteDataSource {
  @override
  Future<String> createSavedFilter(CreateSavedFilterModel model) async {
    final restRequest = createPostRequest(SavedFilterRestResources.savedFilter, body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String>>(restRequest, (json) => ResponseWrapper<String>.fromJson(json, (data) => data as String));
    return response.data ?? '';
  }

  @override
  Future<bool> deleteSavedFilter(String id) async {
    final restRequest = createDeleteRequest(SavedFilterRestResources.savedFilterById(id));
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data ?? false;
  }

  @override
  Future<GetSavedFilterModel?> getSavedFilterById(String id) async {
    final restRequest = createGetRequest(SavedFilterRestResources.savedFilterById(id));
    final response = await executeRequestAsync<ResponseWrapper<GetSavedFilterModel>>(restRequest, (json) => ResponseWrapper<GetSavedFilterModel>.fromJson(json, (data) => fromJsonObject(json, GetSavedFilterModel.fromJson)));
    return response.data;
  }

  @override
  Future<PagedResponse<GetSavedFilterModel, String>> getSavedFilters({required String module, String? searchText}) async {
    final restRequest = createGetRequest("${SavedFilterRestResources.savedFilter}?Module=$module${searchText != null ? 'Search=$searchText' : ''}");
    final response = await executeRequestAsync<PagedResponse<GetSavedFilterModel, String>>(
      restRequest,
      (json) => PagedResponse<GetSavedFilterModel, String>.fromJson(json, (item) => fromJsonObject(item, GetSavedFilterModel.fromJson), (data) => data as String),
    );
    return response;
  }

  @override
  Future<String> updateSavedFilter(CreateSavedFilterModel model) async {
    final restRequest = createPutRequest(SavedFilterRestResources.savedFilter, body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String>>(restRequest, (json) => ResponseWrapper<String>.fromJson(json, (data) => data as String));
    return response.data ?? '';
  }

  @override
  Future<bool> checkIfFilterNameExits({required String filterName, required String module}) async {
    final restRequest = createGetRequest(SavedFilterRestResources.filterExists(filterName: filterName, module: module));
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data ?? false;
  }
}
