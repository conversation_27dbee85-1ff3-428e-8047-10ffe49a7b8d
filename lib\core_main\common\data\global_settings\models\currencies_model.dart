import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'currencies_model.g.dart';

@HiveType(typeId: HiveModelConstants.currenciesTypeId)
@JsonSerializable()
class CurrenciesModel {
  @HiveField(0)
  final String? currency;
  @HiveField(1)
  final String? symbol;

  CurrenciesModel({
    this.currency,
    this.symbol,
  });

  factory CurrenciesModel.fromJson(Map<String, dynamic> json) =>
      _$CurrenciesModelFromJson(json);

  Map<String, dynamic> toJson() => _$CurrenciesModelToJson(this);
}
