// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_receiver.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OTPReceiverAdapter extends TypeAdapter<OTPReceiver> {
  @override
  final int typeId = 18;

  @override
  OTPReceiver read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OTPReceiver.self;
      case 1:
        return OTPReceiver.admin;
      case 2:
        return OTPReceiver.manager;
      case 3:
        return OTPReceiver.specific;
      default:
        return OTPReceiver.self;
    }
  }

  @override
  void write(BinaryWriter writer, OTPReceiver obj) {
    switch (obj) {
      case OTPReceiver.self:
        writer.writeByte(0);
        break;
      case OTPReceiver.admin:
        writer.writeByte(1);
        break;
      case OTPReceiver.manager:
        writer.writeByte(2);
        break;
      case OTPReceiver.specific:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OTPReceiverAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
