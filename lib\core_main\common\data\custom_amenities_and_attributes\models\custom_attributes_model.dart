import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/base_property_type_enum.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';

part 'custom_attributes_model.g.dart';

@HiveType(typeId: HiveModelConstants.customAttributesTypeId)
@JsonSerializable()
class CustomAttributesModel {
  @HiveField(0)
  final String? attributeName;
  @HiveField(1)
  final String? attributeDisplayName;
  @HiveField(2)
  final String? attributeType;
  @HiveField(3)
  final int? fieldType;
  @HiveField(4)
  final String? defaultValue;
  @HiveField(5)
  final List<BasePropertyType>? basePropertyType;
  @HiveField(6)
  final int? orderRank;
  @HiveField(7)
  final String? mobileIcon;
  @HiveField(8)
  final String? activeImageURL;
  @HiveField(9)
  final String? inActiveImageURL;
  @HiveField(10)
  final bool? isActive;
  @HiveField(11)
  final String? masterAttributeId;
  @HiveField(12)
  final String? createdBy;
  @HiveField(13)
  final DateTime? createdOn;
  @HiveField(14)
  final String? lastModifiedBy;
  @HiveField(15)
  final DateTime? lastModifiedOn;
  @HiveField(16)
  final DateTime? deletedOn;
  @HiveField(17)
  final String? deletedBy;
  @HiveField(18)
  final String? id;
  @HiveField(19)
  final bool? isDeleted;

  CustomAttributesModel({
    this.attributeName,
    this.attributeDisplayName,
    this.attributeType,
    this.fieldType,
    this.defaultValue,
    this.basePropertyType,
    this.orderRank,
    this.mobileIcon,
    this.activeImageURL,
    this.inActiveImageURL,
    this.isActive,
    this.masterAttributeId,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.id,
    this.isDeleted,
  });

  factory CustomAttributesModel.fromJson(Map<String, dynamic> json) => _$CustomAttributesModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomAttributesModelToJson(this);

  CustomAttributesModel copyWith(String? defaultValue) {
    return CustomAttributesModel(
      attributeName: attributeName,
      attributeDisplayName: attributeDisplayName,
      attributeType: attributeType,
      fieldType: fieldType,
      defaultValue: defaultValue ?? this.defaultValue,
      basePropertyType: basePropertyType,
      orderRank: orderRank,
      mobileIcon: mobileIcon,
      activeImageURL: activeImageURL,
      inActiveImageURL: inActiveImageURL,
      isActive: isActive,
      masterAttributeId: masterAttributeId,
      createdBy: createdBy,
      createdOn: createdOn,
      lastModifiedBy: lastModifiedBy,
      lastModifiedOn: lastModifiedOn,
      deletedOn: deletedOn,
      deletedBy: deletedBy,
      id: id,
      isDeleted: isDeleted,
    );
  }

  PropertyAttributeModel toPropertyAttributeModel(String value) {
    return PropertyAttributeModel(
      value: value,
      attributeDisplayName: attributeDisplayName,
      attributeName: attributeName,
      masterPropertyAttributeId: masterAttributeId,
    );
  }
}
