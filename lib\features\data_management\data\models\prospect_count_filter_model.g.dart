// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prospect_count_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProspectCountFilterModel _$ProspectCountFilterModelFromJson(
        Map<String, dynamic> json) =>
    ProspectCountFilterModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      baseId: json['baseId'] as String?,
      count: (json['count'] as num?)?.toInt(),
      level: (json['level'] as num?)?.toInt(),
      orderRank: (json['orderRank'] as num?)?.toInt(),
      isForward: json['isForward'] as bool?,
      fromNoOfDays: (json['fromNoOfDays'] as num?)?.toInt(),
      toNoOfDays: (json['toNoOfDays'] as num?)?.toInt(),
      isDefault: json['isDefault'] as bool?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      childType: (json['childType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$ProspectCountFilterModelToJson(
        ProspectCountFilterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'baseId': instance.baseId,
      'count': instance.count,
      'level': instance.level,
      'orderRank': instance.orderRank,
      'isForward': instance.isForward,
      'fromNoOfDays': instance.fromNoOfDays,
      'toNoOfDays': instance.toNoOfDays,
      'isDefault': instance.isDefault,
      'createdOn': instance.createdOn?.toIso8601String(),
      'childType': instance.childType,
    };
