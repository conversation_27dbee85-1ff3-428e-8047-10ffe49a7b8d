// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enquiry_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnquiryModel _$EnquiryModelFromJson(Map<String, dynamic> json) => EnquiryModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      enquiredFor:
          $enumDecodeNullable(_$EnquiryTypeEnumMap, json['enquiredFor']),
      saleType: $enumDecodeNullable(_$SaleTypeEnumMap, json['saleType']),
      leadSource: $enumDecodeNullable(_$LeadSourceEnumMap, json['leadSource']),
      subSource: json['subSource'] as String?,
      lowerBudget: (json['lowerBudget'] as num?)?.toInt(),
      upperBudget: (json['upperBudget'] as num?)?.toInt(),
      noOfBHK: (json['noOfBHK'] as num?)?.toDouble(),
      bHKType: (json['bHKType'] as num?)?.toInt(),
      area: (json['area'] as num?)?.toDouble(),
      areaUnitId: json['areaUnitId'] as String?,
      areaUnit: json['areaUnit'] as String?,
      isPrimary: json['isPrimary'] as bool?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      propertyTypeId: json['propertyTypeId'] as String?,
      carpetArea: (json['carpetArea'] as num?)?.toDouble(),
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      conversionFactor: (json['conversionFactor'] as num?)?.toDouble(),
      builtUpArea: (json['builtUpArea'] as num?)?.toDouble(),
      builtUpAreaUnitId: json['builtUpAreaUnitId'] as String?,
      builtUpAreaConversionFactor:
          (json['builtUpAreaConversionFactor'] as num?)?.toDouble(),
      saleableArea: (json['saleableArea'] as num?)?.toDouble(),
      saleableAreaUnitId: json['saleableAreaUnitId'] as String?,
      saleableAreaConversionFactor:
          (json['saleableAreaConversionFactor'] as num?)?.toDouble(),
      possessionDate: json['possessionDate'] == null
          ? null
          : DateTime.parse(json['possessionDate'] as String),
      currency: json['currency'] as String?,
      enquiryTypes: (json['enquiryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumMap, e))
          .toList(),
      bHKTypes: (json['bhkTypes'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      bHKs: (json['bhKs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) => AddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      propertyType: json['propertyType'] == null
          ? null
          : PropertyTypeModel.fromJson(
              json['propertyType'] as Map<String, dynamic>),
      propertyTypes: (json['propertyTypes'] as List<dynamic>?)
          ?.map((e) => PropertyTypeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      beds: (json['beds'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BedsEnumMap, e))
          .toList(),
      furnished: (json['furnished'] as num?)?.toInt(),
      baths: (json['baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      offerType: (json['offerType'] as num?)?.toInt(),
      floors:
          (json['floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      propertyAreaUnitId: json['propertyAreaUnitId'] as String?,
      propertyArea: (json['propertyArea'] as num?)?.toDouble(),
      netAreaUnitId: json['netAreaUnitId'] as String?,
      netArea: (json['netArea'] as num?)?.toDouble(),
      propertyAreaConversionFactor:
          (json['propertyAreaConversionFactor'] as num?)?.toDouble(),
      netAreaConversionFactor:
          (json['netAreaConversionFactor'] as num?)?.toDouble(),
      unitName: json['unitName'] as String?,
      clusterName: json['clusterName'] as String?,
      purpose: $enumDecodeNullable(_$PurposeEnumEnumMap, json['purpose'],
          unknownValue: PurposeEnum.none),
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$EnquiryModelToJson(EnquiryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'enquiredFor': _$EnquiryTypeEnumMap[instance.enquiredFor],
      'saleType': _$SaleTypeEnumMap[instance.saleType],
      'leadSource': _$LeadSourceEnumMap[instance.leadSource],
      'subSource': instance.subSource,
      'lowerBudget': instance.lowerBudget,
      'upperBudget': instance.upperBudget,
      'noOfBHK': instance.noOfBHK,
      'bHKType': instance.bHKType,
      'area': instance.area,
      'areaUnitId': instance.areaUnitId,
      'areaUnit': instance.areaUnit,
      'isPrimary': instance.isPrimary,
      'address': instance.address,
      'propertyTypeId': instance.propertyTypeId,
      'carpetArea': instance.carpetArea,
      'carpetAreaUnitId': instance.carpetAreaUnitId,
      'conversionFactor': instance.conversionFactor,
      'builtUpArea': instance.builtUpArea,
      'builtUpAreaUnitId': instance.builtUpAreaUnitId,
      'builtUpAreaConversionFactor': instance.builtUpAreaConversionFactor,
      'saleableArea': instance.saleableArea,
      'saleableAreaUnitId': instance.saleableAreaUnitId,
      'saleableAreaConversionFactor': instance.saleableAreaConversionFactor,
      'possessionDate': instance.possessionDate?.toIso8601String(),
      'currency': instance.currency,
      'enquiryTypes':
          instance.enquiryTypes?.map((e) => _$EnquiryTypeEnumMap[e]!).toList(),
      'bhkTypes': instance.bHKTypes,
      'bhKs': instance.bHKs,
      'addresses': instance.addresses,
      'propertyType': instance.propertyType,
      'propertyTypes': instance.propertyTypes,
      'beds': instance.beds?.map((e) => _$BedsEnumMap[e]!).toList(),
      'furnished': instance.furnished,
      'baths': instance.baths,
      'offerType': instance.offerType,
      'floors': instance.floors,
      'propertyAreaUnitId': instance.propertyAreaUnitId,
      'propertyArea': instance.propertyArea,
      'netAreaUnitId': instance.netAreaUnitId,
      'netArea': instance.netArea,
      'propertyAreaConversionFactor': instance.propertyAreaConversionFactor,
      'netAreaConversionFactor': instance.netAreaConversionFactor,
      'unitName': instance.unitName,
      'clusterName': instance.clusterName,
      'purpose': _$PurposeEnumEnumMap[instance.purpose],
      'possesionType': _$PossessionTypeEnumMap[instance.possesionType],
    };

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$SaleTypeEnumMap = {
  SaleType.none: 0,
  SaleType.neu: 1,
  SaleType.resale: 2,
};

const _$LeadSourceEnumMap = {
  LeadSource.any: -1,
  LeadSource.direct: 0,
  LeadSource.iVR: 1,
  LeadSource.facebook: 2,
  LeadSource.linkedIn: 3,
  LeadSource.googleAds: 4,
  LeadSource.magicBricks: 5,
  LeadSource.ninetyNineAcres: 6,
  LeadSource.housing: 7,
  LeadSource.gharOffice: 8,
  LeadSource.referral: 9,
  LeadSource.walkIn: 10,
  LeadSource.website: 11,
  LeadSource.gmail: 12,
  LeadSource.propertyMicrosite: 13,
  LeadSource.portfolioMicrosite: 14,
  LeadSource.phonebook: 15,
  LeadSource.callLogs: 16,
  LeadSource.leadPool: 17,
  LeadSource.squareYards: 18,
  LeadSource.quikrHomes: 19,
  LeadSource.justLead: 20,
  LeadSource.whatsApp: 21,
  LeadSource.youTube: 22,
  LeadSource.qRCode: 23,
  LeadSource.instagram: 24,
  LeadSource.oLX: 25,
  LeadSource.estateDekho: 26,
  LeadSource.googleSheet: 27,
  LeadSource.channelPartner: 28,
  LeadSource.realEstateIndia: 29,
  LeadSource.commonFloor: 30,
  LeadSource.data: 31,
  LeadSource.roofAndFloor: 32,
  LeadSource.microsoftAds: 33,
  LeadSource.propertyWala: 34,
  LeadSource.projectMicrosite: 35,
  LeadSource.myGate: 36,
  LeadSource.flipkart: 37,
  LeadSource.propertyFinder: 38,
  LeadSource.bayut: 39,
  LeadSource.dubizzle: 40,
  LeadSource.webhook: 41,
  LeadSource.tikTok: 42,
  LeadSource.snapchat: 43,
  LeadSource.googleAdsCampaign: 44,
};

const _$BedsEnumMap = {
  Beds.studio: 0,
  Beds.oneBed: 1,
  Beds.twoBed: 2,
  Beds.threeBed: 3,
  Beds.fourBed: 4,
  Beds.fiveBed: 5,
  Beds.sixBed: 6,
  Beds.sevenBed: 7,
  Beds.eightBed: 8,
  Beds.nineBed: 9,
  Beds.tenBed: 10,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
