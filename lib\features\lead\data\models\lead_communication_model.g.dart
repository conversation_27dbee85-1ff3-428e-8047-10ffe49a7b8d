// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_communication_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadCommunicationModel _$LeadCommunicationModelFromJson(
        Map<String, dynamic> json) =>
    LeadCommunicationModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
      message: json['message'] as String?,
      leadId: json['leadId'] as String?,
    );

Map<String, dynamic> _$LeadCommunicationModelToJson(
        LeadCommunicationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'contactType': _$ContactTypeEnumMap[instance.contactType],
      'message': instance.message,
      'leadId': instance.leadId,
    };

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};
