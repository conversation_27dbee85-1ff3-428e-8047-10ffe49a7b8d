// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_notification_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BaseNotificationSettingsAdapter
    extends TypeAdapter<BaseNotificationSettings> {
  @override
  final int typeId = 7;

  @override
  BaseNotificationSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BaseNotificationSettings(
      moduleName: fields[0] as ModuleName?,
      event: fields[1] as Event?,
      minutesBefore: (fields[2] as List?)?.cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, BaseNotificationSettings obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.moduleName)
      ..writeByte(1)
      ..write(obj.event)
      ..writeByte(2)
      ..write(obj.minutesBefore);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BaseNotificationSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseNotificationSettings _$BaseNotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    BaseNotificationSettings(
      moduleName: $enumDecodeNullable(_$ModuleNameEnumMap, json['moduleName']),
      event: $enumDecodeNullable(_$EventEnumMap, json['event']),
      minutesBefore: (json['minutesBefore'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$BaseNotificationSettingsToJson(
        BaseNotificationSettings instance) =>
    <String, dynamic>{
      'moduleName': _$ModuleNameEnumMap[instance.moduleName],
      'event': _$EventEnumMap[instance.event],
      'minutesBefore': instance.minutesBefore,
    };

const _$ModuleNameEnumMap = {
  ModuleName.lead: 0,
  ModuleName.todo: 1,
  ModuleName.integration: 2,
  ModuleName.user: 3,
  ModuleName.profile: 4,
  ModuleName.project: 5,
  ModuleName.property: 6,
  ModuleName.team: 7,
  ModuleName.email: 8,
  ModuleName.invoice: 9,
  ModuleName.unit: 10,
};

const _$EventEnumMap = {
  Event.signUp: 0,
  Event.login: 1,
  Event.logout: 2,
  Event.leadStatusToCallback: 3,
  Event.callbackReminder: 4,
  Event.leadStatusToScheduleMeeting: 5,
  Event.scheduleMeetingReminder: 6,
  Event.leadStatusToScheduleSiteVisit: 7,
  Event.scheduleSiteVisitReminder: 8,
  Event.leadStatusToConvert: 9,
  Event.leadStatusToNotInterest: 10,
  Event.leadSatusToDropped: 11,
  Event.leadStatusToPending: 12,
  Event.singleTaskComplete: 13,
  Event.multipleTaskComplete: 14,
  Event.taskCreation: 15,
  Event.taskUpdation: 16,
  Event.scheduledTaskReminder: 17,
  Event.taskOverdue: 18,
  Event.taskPending: 19,
  Event.leadFromPropertyMicrositeToBuy: 20,
  Event.leadFromPropertyMicrositeForRent: 21,
  Event.leadFromPortfolioMicrosite: 22,
  Event.integrationAccCreation: 23,
  Event.leadFromIntegration: 24,
  Event.propertyCreation: 25,
  Event.propertyDeletion: 26,
  Event.propertySoftDeletion: 27,
  Event.dusKaDumChallengeStarts: 28,
  Event.supportQuery: 29,
  Event.leadAssignment: 30,
  Event.taskAssignment: 31,
  Event.unAssignedLeadUpdate: 32,
};
