import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/local/app_analysis_local_data_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/remote/app_analysis_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/models/app_anaylsis_model.dart';
import 'package:leadrat/core_main/common/data/app_analysis/models/app_feature_model.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/device/repository/device_repository.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/features/auth/domain/repository/auth_repository.dart';

class AppAnalysisRepositoryImpl implements AppAnalysisRepository {
  final AppAnalysisDataSource _appAnalysisDataSource;
  final AppAnalysisLocalDataSource _appAnalysisLocalDataSource;
  final DeviceRepository _deviceRepository;
  final AuthRepository _authRepository;
  final UsersDataRepository _usersDataRepository;

  AppAnalysisRepositoryImpl(
    this._appAnalysisDataSource,
    this._deviceRepository,
    this._authRepository,
    this._usersDataRepository,
    this._appAnalysisLocalDataSource,
  );

  @override
  Future<bool?> sendAppAnalysis({AppAnalysisModel? appAnalysisModel, String? name, String? refId}) async {
    try {
      if(!kReleaseMode) return null;
      assert(appAnalysisModel != null || name != null, 'Either appAnalysisModel or name must be provided.');
      final deviceInfo = await _deviceRepository.getDeviceInfo();
      final tenantDetails = _authRepository.getLocalTenantDetails();
      final userDetails = _usersDataRepository.getLoggedInUser();
      var analysisModel = AppAnalysisModel(featureId: appAnalysisModel?.featureId, actionId: appAnalysisModel?.actionId, refId: appAnalysisModel?.refId ?? refId);
      if (name != null) {
        final appFeatures = _appAnalysisLocalDataSource.getAppFeatures();
        final featureModel = appFeatures?.firstWhereOrNull((element) => element.name?.toLowerCase() == name.toLowerCase());
        analysisModel = analysisModel.copyWith(featureId: featureModel?.id, actionId: featureModel?.actions?.firstOrNull?.id, refId: refId);
      }
      if (analysisModel.featureId == null) return false;
      analysisModel = analysisModel.copyWith(
        appVer: await _deviceRepository.getAppVersion(),
        tenantId: tenantDetails?.id,
        userId: userDetails?.userId,
        deviceType: deviceInfo?.deviceName,
        iP: deviceInfo?.ipAddress,
        osVer: deviceInfo?.osVersion,
        deviceId: deviceInfo?.deviceUDID,
        source: 'Mobile',
      );
      final response = await _appAnalysisDataSource.sendAppAnalysis(analysisModel);
      return response;
    } catch (ex) {
      throw Exception("Error in pushing data to app analysis");
    }
  }

  @override
  Future<List<AppFeatureModel>> getAppFeatures() async {
    try {
      if(!kReleaseMode) return [];
      final response = await _appAnalysisDataSource.getAppFeatures();
      await _appAnalysisLocalDataSource.saveAppFeatures(response);
      return response;
    } catch (ex) {
      throw Exception("Error to get app features");
    }
  }
}
