// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PropertyTypeAdapter extends TypeAdapter<PropertyType> {
  @override
  final int typeId = 27;

  @override
  PropertyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyType.residential;
      case 1:
        return PropertyType.commercial;
      case 2:
        return PropertyType.agricultural;
      default:
        return PropertyType.residential;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyType obj) {
    switch (obj) {
      case PropertyType.residential:
        writer.writeByte(0);
        break;
      case PropertyType.commercial:
        writer.writeByte(1);
        break;
      case PropertyType.agricultural:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
