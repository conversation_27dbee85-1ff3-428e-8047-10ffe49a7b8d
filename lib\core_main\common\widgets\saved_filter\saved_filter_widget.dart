import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_cubit.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';

class SavedFilterWidget extends StatelessWidget {
  final void Function(ItemSimpleModel<GetSavedFilterModel> filter)? onEditSavedFilter;
  final SavedFilterModule savedFilterModule;

  const SavedFilterWidget({
    required this.savedFilterModule,
    this.onEditSavedFilter,
    super.key,
  });

  void showFilter(BuildContext context) {
    showModalBottomSheet(
      context: context,
      enableDrag: true,
      useSafeArea: true,
      isScrollControlled: true,
      backgroundColor: ColorPalette.transparent,
      constraints: BoxConstraints(maxHeight: context.height(70)),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: context.height(70)),
            child: SavedFilterBottomModalWidget(onEditSavedFilter: onEditSavedFilter),
          ),
        );
      },
    );
    context.read<SavedFilterBloc>().add(InitSavedFiltersEvent(savedFilterModule));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => showFilter(context),
      child: Container(
        height: 40,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: const Color.fromRGBO(73, 79, 86, 0.4),
            border: Border.all(
              color: ColorPalette.primaryLightColor,
              width: 1,
            )),
        child: SvgPicture.asset(ImageResources.iconSavedFilter),
      ),
    );
  }
}

class SavedFilterBottomModalWidget extends LeadratStatelessWidget {
  final void Function(ItemSimpleModel<GetSavedFilterModel> filter)? onEditSavedFilter;

  const SavedFilterBottomModalWidget({
    super.key,
    this.onEditSavedFilter,
  });

  @override
  Widget buildContent(BuildContext context) {
    return BlocBuilder<SavedFilterBloc, SavedFilterState>(
      builder: (context, state) {
        return Container(
          decoration: const BoxDecoration(
            color: ColorPalette.darkToneInk,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(40), topRight: Radius.circular(40)),
            border: Border(
              top: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
              left: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
              right: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: <Widget>[
              Center(
                  child: Container(
                margin: const EdgeInsets.only(top: 16),
                decoration: BoxDecoration(color: ColorPalette.white, borderRadius: BorderRadius.circular(20)),
                height: 3,
                width: context.width(24),
              )),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Saved Filter",
                    style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primaryTextColor),
                  ),
                  IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Container(
                        decoration: BoxDecoration(color: ColorPalette.white, borderRadius: BorderRadius.circular(100)),
                        padding: const EdgeInsets.all(2),
                        child: const Icon(Icons.close_rounded, size: 14),
                      )),
                ],
              ),
              const SizedBox(height: 20),
              TextField(
                onChanged: (value) => context.read<SavedFilterBloc>().add(SearchSavedFilterEvent(value)),
                decoration: InputDecoration(
                  fillColor: const Color(0xFF232527),
                  filled: true,
                  border: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(40)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  enabledBorder: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(40)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  focusedBorder: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(40)), borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                  hintText: "type to search",
                  hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray600),
                  suffixIcon: const Icon(CupertinoIcons.search, color: ColorPalette.gray500, size: 18),
                ),
                cursorColor: ColorPalette.primaryGreen,
                style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.whiteSolid),
              ),
              const SizedBox(height: 12),
              if (state.pageState == PageState.error)
                Expanded(child: Text(state.errorMessage ?? "Saved filters not found", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primary)))
              else if (state.pageState == PageState.loading)
                const Expanded(child: Center(child: CircularProgressIndicator(color: ColorPalette.primaryGreen)))
              else if (state.pageState == PageState.success)
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Color(0xFF232527),
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                      border: Border(
                        top: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
                        left: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
                        right: BorderSide(color: ColorPalette.primaryLightColor, width: 2),
                      ),
                    ),
                    child: state.savedFilters.isEmpty
                        ? Center(
                            child: Text(
                              "Saved filters not found",
                              style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primary),
                            ),
                          )
                        : ListView.builder(
                            itemCount: state.savedFilters.length,
                            itemBuilder: (context, index) {
                              final filter = state.savedFilters[index];
                              return ListTile(
                                contentPadding: const EdgeInsets.only(left: 10),
                                minVerticalPadding: 0,
                                onTap: () {
                                  context.read<SavedFilterBloc>().add(ApplySavedFilterEvent(filter));
                                  context.read<SaveFilterCubit>().toggleSaveFilter(isUpdate: true, filter: filter);
                                  Navigator.pop(context);
                                },
                                visualDensity: const VisualDensity(vertical: -4),
                                title: Text(filter.title, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primaryTextColor)),
                                trailing: PopupMenuButton(
                                  offset: const Offset(-35, 10),
                                  menuPadding: const EdgeInsets.all(0),
                                  style: ElevatedButton.styleFrom(foregroundColor: ColorPalette.white, iconSize: 18),
                                  itemBuilder: (context) => [
                                    PopupMenuItem(
                                      onTap: () {
                                        if (onEditSavedFilter != null) {
                                          onEditSavedFilter!(filter);
                                        }
                                      },
                                      height: 35,
                                      child: Text('Edit', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primaryLightColor)),
                                    ),
                                    if (!filter.isSelected)
                                      PopupMenuItem(
                                        onTap: () {
                                          DialogManager().showTransparentProgressDialog(context, message: "Deleting");
                                          context.read<SavedFilterBloc>().add(DeleteSavedFilterEvent(filter));
                                        },
                                        height: 35,
                                        child: Text('Delete', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primaryLightColor)),
                                      ),
                                  ],
                                ),
                              );
                            },
                          ),
                  ),
                )
            ],
          ),
        );
      },
    );
  }
}
