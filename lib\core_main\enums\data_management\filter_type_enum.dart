import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';

@JsonEnum(valueField: 'value')
enum FilterTypeEnum {
  all("All", 0, ImageResources.iconNew),
  new_("New", 3, ImageResources.iconNew),
  qualified("Qualified", 4, ImageResources.iconQualified),
  backlog("Backlog", 2, ""),
  followUps("Follow ups", 1, ImageResources.iconNew),
  notAnswered("Not Answered", 7, ImageResources.iconNotAnswered),
  notInterested("Not Interested", 6, ImageResources.iconNotInterested),
  notReachable("Not Reachable", 5, ImageResources.iconNotReachable),
  inValid("Invalid", 8, ImageResources.iconInvalid),
  converted("Converted", 9, "");

  final String description;
  final int value;
  final String imageResource;
  const FilterTypeEnum(this.description, this.value, this.imageResource);
}

FilterTypeEnum getFilterTypeDescriptionByEnumName(String name) {
  switch (name) {
    case 'all':
      return FilterTypeEnum.all;
    case 'new_':
      return FilterTypeEnum.new_;
    case 'qualified':
      return FilterTypeEnum.qualified;
    case 'backlog':
      return FilterTypeEnum.backlog;
    case 'followUps':
      return FilterTypeEnum.followUps;
    case 'notAnswered':
      return FilterTypeEnum.notAnswered;
    case 'notInterested':
      return FilterTypeEnum.notInterested;
    case 'notReachable':
      return FilterTypeEnum.notReachable;
    case 'inValid':
      return FilterTypeEnum.inValid;
    case 'converted':
      return FilterTypeEnum.converted;
    default:
      return FilterTypeEnum.all;
  }
}

String getFilterTypeImageResource(String name) {
  switch (name) {
    case 'all':
      return FilterTypeEnum.all.imageResource;
    case 'new':
      return FilterTypeEnum.new_.imageResource;
    case 'qualified':
      return FilterTypeEnum.qualified.imageResource;
    case 'backlog':
      return FilterTypeEnum.backlog.imageResource;
    case 'follow_ups':
      return FilterTypeEnum.followUps.imageResource;
    case 'not_answered':
      return FilterTypeEnum.notAnswered.imageResource;
    case 'not_interested':
      return FilterTypeEnum.notInterested.imageResource;
    case 'not_reachable':
      return FilterTypeEnum.notReachable.imageResource;
    case 'invalid/wrong_number':
      return FilterTypeEnum.inValid.imageResource;
    case 'converted':
      return FilterTypeEnum.converted.imageResource;
    default:
      return FilterTypeEnum.all.imageResource;
  }
}
