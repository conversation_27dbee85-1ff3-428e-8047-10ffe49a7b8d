// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_property_type_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BasePropertyTypeAdapter extends TypeAdapter<BasePropertyType> {
  @override
  final int typeId = 30;

  @override
  BasePropertyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BasePropertyType.residential;
      case 1:
        return BasePropertyType.commercial;
      case 2:
        return BasePropertyType.agricultural;
      default:
        return BasePropertyType.residential;
    }
  }

  @override
  void write(BinaryWriter writer, BasePropertyType obj) {
    switch (obj) {
      case BasePropertyType.residential:
        writer.writeByte(0);
        break;
      case BasePropertyType.commercial:
        writer.writeByte(1);
        break;
      case BasePropertyType.agricultural:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BasePropertyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
