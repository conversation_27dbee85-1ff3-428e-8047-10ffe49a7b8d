import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'blood_group_type_enum.g.dart';

@HiveType(typeId: HiveModelConstants.bloodGroupEnumTypeId)
@JsonEnum(valueField: 'value')
enum BloodGroupType {
  @HiveField(0)
  none(0,"None"),
  @HiveField(1)
  aPositive(1,"A+"),
  @HiveField(2)
  aNegative(2,"A-"),
  @HiveField(3)
  bPositive(3,"B+"),
  @HiveField(4)
  bNegative(4,"B-"),
  @HiveField(5)
  oPositive(5,"O+"),
  @HiveField(6)
  oNegative(6,"O-",),
  @HiveField(7)
  abPositive(7,"AB+"),
  @HiveField(8)
  abNegative(8,"AB-");

  final int value;
  final String description;
  const BloodGroupType( this.value,this.description,);

}
