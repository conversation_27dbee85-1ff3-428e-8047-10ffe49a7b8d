import Foundation
import CallKit
import Flutter

@available(iOS 10.0, *)
class CallTrackingManager: NSObject {
    static let shared = CallTrackingManager()
    
    private let callObserver = CXCallObserver()
    private var methodChannel: FlutterMethodChannel?
    private var activeCallStartTime: Date?
    private var activeCallConnectedTime: Date?
    private var activeCallUUID: UUID?
    private var isTrackingCall = false
    private var wasCallConnected = false
    
    // UserDefaults keys for persistence
    private let callStartTimeKey = "CallTrackingStartTime"
    private let callUUIDKey = "CallTrackingUUID"
    private let isTrackingKey = "CallTrackingActive"
    
    override init() {
        super.init()
        setupCallObserver()
        restoreCallStateFromPersistence()
    }
    
    func setupMethodChannel(_ channel: FlutterMethodChannel) {
        self.methodChannel = channel
    }
    
    private func setupCallObserver() {
        callObserver.setDelegate(self, queue: nil)
        print("CallTrackingManager: CXCallObserver delegate set")
    }
    
    // MARK: - Public Methods
    
    func startCallTracking() {
        print("CallTrackingManager: Starting call tracking")
        
        // Clean up any previous tracking
        if isTrackingCall {
            print("CallTrackingManager: Warning - Already tracking a call, cleaning up")
            endCallTracking(reason: "new_call_started")
        }
        
        let startTime = Date()
        let callUUID = UUID()
        
        activeCallStartTime = startTime
        activeCallUUID = callUUID
        isTrackingCall = true
        
        // Persist to UserDefaults for crash recovery
        saveCallStateToPersistence()
        
        print("CallTrackingManager: Call tracking started at \(startTime)")
        print("CallTrackingManager: Call UUID: \(callUUID)")
        
        // Start monitoring for existing calls
        checkForActiveCalls()
    }
    
    func endCallTracking(reason: String = "manual") {
        guard isTrackingCall, let startTime = activeCallStartTime else {
            print("CallTrackingManager: No active call to end")
            return
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        print("CallTrackingManager: Ending call tracking")
        print("CallTrackingManager: Reason: \(reason)")
        print("CallTrackingManager: Duration: \(duration) seconds")
        
        // Send duration to Flutter
        sendCallDurationToFlutter(duration: duration, startTime: startTime, endTime: endTime)
        
        // Clean up
        cleanup()
    }
    
    func isCurrentlyTracking() -> Bool {
        return isTrackingCall && activeCallStartTime != nil
    }
    
    func getCurrentCallDuration() -> TimeInterval? {
        guard let startTime = activeCallStartTime, isTrackingCall else {
            return nil
        }
        return Date().timeIntervalSince(startTime)
    }
    
    // MARK: - Private Methods
    
    private func checkForActiveCalls() {
        let activeCalls = callObserver.calls.filter { !$0.hasEnded }
        print("CallTrackingManager: Found \(activeCalls.count) active calls")
        
        for call in activeCalls {
            print("CallTrackingManager: Active call - UUID: \(call.uuid), Outgoing: \(call.isOutgoing), Connected: \(call.hasConnected)")
        }
    }
    
    private func sendCallDurationToFlutter(duration: TimeInterval, startTime: Date, endTime: Date) {
        guard let channel = methodChannel else {
            print("CallTrackingManager: No method channel available")
            return
        }

        // Calculate total call duration for business tracking purposes
        let actualDuration: TimeInterval
        let actualStartTime: Date

        if wasCallConnected, let connectedTime = activeCallConnectedTime {
            // ✅ FIXED: For connected calls, use TOTAL duration from call start to end time
            // This includes ring time + talk time for complete business tracking
            actualDuration = duration // Use the total duration (start to end)
            actualStartTime = startTime // Use the original start time
            let talkDuration = endTime.timeIntervalSince(connectedTime)
            print("CallTrackingManager: Connected call - Total duration: \(actualDuration)s (Talk: \(talkDuration)s)")
            NSLog("RELEASE_LOG: CallTrackingManager: Connected call - Total duration: \(actualDuration)s (Talk: \(talkDuration)s)")
        } else {
            // For disconnected calls, use original duration (will be set to 0 in Flutter)
            actualDuration = duration
            actualStartTime = startTime
            print("CallTrackingManager: Disconnected call - Ring duration: \(actualDuration)s")
            NSLog("RELEASE_LOG: CallTrackingManager: Disconnected call - Ring duration: \(actualDuration)s")
        }

        let callData: [String: Any] = [
            "duration": actualDuration,
            "startTime": actualStartTime.timeIntervalSince1970 * 1000, // Convert to milliseconds
            "endTime": endTime.timeIntervalSince1970 * 1000,
            "isConnected": wasCallConnected,
            "source": "callkit_observer"
        ]

        DispatchQueue.main.async {
            channel.invokeMethod("onCallEnded", arguments: callData)
            print("CallTrackingManager: Call data sent to Flutter - Duration: \(actualDuration)s, Connected: \(self.wasCallConnected)")
        }
    }
    
    private func cleanup() {
        activeCallStartTime = nil
        activeCallConnectedTime = nil
        activeCallUUID = nil
        isTrackingCall = false
        wasCallConnected = false
        clearCallStateFromPersistence()
        print("CallTrackingManager: Cleanup completed")
    }
    
    // MARK: - Persistence Methods
    
    private func saveCallStateToPersistence() {
        let defaults = UserDefaults.standard
        
        if let startTime = activeCallStartTime {
            defaults.set(startTime.timeIntervalSince1970, forKey: callStartTimeKey)
        }
        
        if let uuid = activeCallUUID {
            defaults.set(uuid.uuidString, forKey: callUUIDKey)
        }
        
        defaults.set(isTrackingCall, forKey: isTrackingKey)
        defaults.synchronize()
        
        print("CallTrackingManager: Call state saved to persistence")
    }
    
    private func restoreCallStateFromPersistence() {
        let defaults = UserDefaults.standard
        
        guard defaults.bool(forKey: isTrackingKey) else {
            print("CallTrackingManager: No persisted call state found")
            return
        }
        
        let startTimeInterval = defaults.double(forKey: callStartTimeKey)
        let uuidString = defaults.string(forKey: callUUIDKey)
        
        guard startTimeInterval > 0, let uuidString = uuidString, let uuid = UUID(uuidString: uuidString) else {
            print("CallTrackingManager: Invalid persisted call state, clearing")
            clearCallStateFromPersistence()
            return
        }
        
        let startTime = Date(timeIntervalSince1970: startTimeInterval)
        let timeSinceStart = Date().timeIntervalSince(startTime)
        
        // If more than 10 minutes have passed, assume the call ended
        if timeSinceStart > 600 {
            print("CallTrackingManager: Persisted call is too old (\(timeSinceStart)s), ending it")
            activeCallStartTime = startTime
            activeCallUUID = uuid
            isTrackingCall = true
            endCallTracking(reason: "restored_and_expired")
            return
        }
        
        // Restore the call state
        activeCallStartTime = startTime
        activeCallUUID = uuid
        isTrackingCall = true
        
        print("CallTrackingManager: Restored call state from persistence")
        print("CallTrackingManager: Call duration so far: \(timeSinceStart)s")
        
        // Check if there are still active calls
        checkForActiveCalls()
        
        // If no active calls found, end the tracking
        let activeCalls = callObserver.calls.filter { !$0.hasEnded }
        if activeCalls.isEmpty {
            print("CallTrackingManager: No active calls found after restoration, ending tracking")
            endCallTracking(reason: "restored_no_active_calls")
        }
    }
    
    private func clearCallStateFromPersistence() {
        let defaults = UserDefaults.standard
        defaults.removeObject(forKey: callStartTimeKey)
        defaults.removeObject(forKey: callUUIDKey)
        defaults.removeObject(forKey: isTrackingKey)
        defaults.synchronize()
        print("CallTrackingManager: Persisted call state cleared")
    }

    func getPendingCallDataFromUserDefaults() -> [[String: Any]] {
        let defaults = UserDefaults.standard
        if let pendingCallsData = defaults.array(forKey: "pendingCallData") as? [[String: Any]] {
            print("CallTrackingManager: Retrieved \(pendingCallsData.count) pending calls from UserDefaults")
            NSLog("RELEASE_LOG: CallTrackingManager: Retrieved \(pendingCallsData.count) pending calls from UserDefaults")
            return pendingCallsData
        } else {
            print("CallTrackingManager: No pending calls found in UserDefaults")
            NSLog("RELEASE_LOG: CallTrackingManager: No pending calls found in UserDefaults")
            return []
        }
    }

    func clearPendingCallDataFromUserDefaults() {
        let defaults = UserDefaults.standard
        defaults.removeObject(forKey: "pendingCallData")
        defaults.synchronize()
        print("CallTrackingManager: Cleared pending call data from UserDefaults")
        NSLog("RELEASE_LOG: CallTrackingManager: Cleared pending call data from UserDefaults")
    }
}

// MARK: - CXCallObserverDelegate

@available(iOS 10.0, *)
extension CallTrackingManager: CXCallObserverDelegate {
    func callObserver(_ callObserver: CXCallObserver, callChanged call: CXCall) {
        print("CallTrackingManager: Call state changed")
        print("CallTrackingManager: UUID: \(call.uuid)")
        print("CallTrackingManager: Outgoing: \(call.isOutgoing)")
        print("CallTrackingManager: Connected: \(call.hasConnected)")
        print("CallTrackingManager: Ended: \(call.hasEnded)")
        print("CallTrackingManager: On Hold: \(call.isOnHold)")
        
        // Only track outgoing calls
        guard call.isOutgoing else {
            print("CallTrackingManager: Ignoring incoming call")
            return
        }
        
        if call.hasConnected && !call.hasEnded && isTrackingCall && !wasCallConnected {
            print("CallTrackingManager: Outgoing call connected")
            wasCallConnected = true
            activeCallConnectedTime = Date()
            print("CallTrackingManager: Connection time recorded: \(activeCallConnectedTime!)")
        }
        
        if call.hasEnded && isTrackingCall {
            print("CallTrackingManager: Outgoing call ended by system")
            endCallTracking(reason: "call_ended_by_system")
        }
    }
}
