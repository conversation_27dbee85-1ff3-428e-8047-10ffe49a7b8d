// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_unit_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectUnitInfoModel _$ProjectUnitInfoModelFromJson(
        Map<String, dynamic> json) =>
    ProjectUnitInfoModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      furnishingStatus: (json['furnishingStatus'] as num?)?.toInt(),
      noOfBHK: (json['noOfBHK'] as num?)?.toInt(),
      facing: (json['facing'] as num?)?.toInt(),
      area: (json['area'] as num?)?.toInt(),
      areaUnitId: json['areaUnitId'] as String?,
      carpetArea: (json['carpetArea'] as num?)?.toInt(),
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      buildUpArea: (json['buildUpArea'] as num?)?.toInt(),
      buildUpAreaId: json['buildUpAreaId'] as String?,
      superBuildUpArea: (json['superBuildUpArea'] as num?)?.toInt(),
      superBuildUpAreaUnit: json['superBuildUpAreaUnit'] as String?,
      pricePerUnit: (json['pricePerUnit'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      unitFilePath: json['unitFilePath'] as String?,
      currency: json['currency'] as String?,
      maintenanceCost: (json['maintenanceCost'] as num?)?.toInt(),
      facings: (json['facings'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      attributes: (json['attributes'] as List<dynamic>?)
          ?.map(
              (e) => ProjectAttributeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      unitType: json['unitType'] == null
          ? null
          : ProjectTypeModel.fromJson(json['unitType'] as Map<String, dynamic>),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => ProjectGalleryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      videos: (json['videos'] as List<dynamic>?)
          ?.map((e) => ProjectGalleryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      unitInfoGalleries: (json['unitInfoGalleries'] as List<dynamic>?)
          ?.map((e) => ProjectGalleryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactRecords: (json['contactRecords'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
    );

Map<String, dynamic> _$ProjectUnitInfoModelToJson(
        ProjectUnitInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'furnishingStatus': instance.furnishingStatus,
      'noOfBHK': instance.noOfBHK,
      'facing': instance.facing,
      'area': instance.area,
      'areaUnitId': instance.areaUnitId,
      'carpetArea': instance.carpetArea,
      'carpetAreaUnitId': instance.carpetAreaUnitId,
      'buildUpArea': instance.buildUpArea,
      'buildUpAreaId': instance.buildUpAreaId,
      'superBuildUpArea': instance.superBuildUpArea,
      'superBuildUpAreaUnit': instance.superBuildUpAreaUnit,
      'pricePerUnit': instance.pricePerUnit,
      'price': instance.price,
      'unitFilePath': instance.unitFilePath,
      'currency': instance.currency,
      'maintenanceCost': instance.maintenanceCost,
      'facings': instance.facings,
      'attributes': instance.attributes,
      'unitType': instance.unitType,
      'images': instance.images,
      'videos': instance.videos,
      'unitInfoGalleries': instance.unitInfoGalleries,
      'contactRecords': instance.contactRecords,
    };

ProjectWithDegreeMatchedModel _$ProjectWithDegreeMatchedModelFromJson(
        Map<String, dynamic> json) =>
    ProjectWithDegreeMatchedModel(
      totalNoOfFields: (json['totalNoOfFields'] as num?)?.toInt(),
      noOfFieldsMatched: (json['noOfFieldsMatched'] as num?)?.toInt(),
      percentageOfFieldsMatched: json['percentageOfFieldsMatched'] as String?,
      id: json['id'] as String?,
      projectName: json['projectName'] as String?,
      location: json['location'] == null
          ? null
          : AddressModel.fromJson(json['location'] as Map<String, dynamic>),
      unitInfo: json['unitInfo'] == null
          ? null
          : ProjectUnitInfoModel.fromJson(
              json['unitInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProjectWithDegreeMatchedModelToJson(
        ProjectWithDegreeMatchedModel instance) =>
    <String, dynamic>{
      'totalNoOfFields': instance.totalNoOfFields,
      'noOfFieldsMatched': instance.noOfFieldsMatched,
      'percentageOfFieldsMatched': instance.percentageOfFieldsMatched,
      'id': instance.id,
      'projectName': instance.projectName,
      'location': instance.location,
      'unitInfo': instance.unitInfo,
    };
