// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_saved_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateSavedFilterModel _$CreateSavedFilterModelFromJson(
        Map<String, dynamic> json) =>
    CreateSavedFilterModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      module: json['module'] as String?,
      filterCriteria: json['filterCriteria'] as String?,
    );

Map<String, dynamic> _$CreateSavedFilterModelToJson(
        CreateSavedFilterModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.module case final value?) 'module': value,
      if (instance.filterCriteria case final value?) 'filterCriteria': value,
    };
