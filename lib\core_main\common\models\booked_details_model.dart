import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/project_model.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/booked_document_type.dart';
import 'package:leadrat/core_main/enums/common/brokerage_type.dart';
import 'package:leadrat/core_main/enums/common/brokerage_unit.dart';
import 'package:leadrat/core_main/enums/common/discount_type.dart';
import 'package:leadrat/core_main/enums/common/payment_type.dart';
import 'package:leadrat/core_main/enums/common/token_type.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/payment_frequency.dart';
import 'package:leadrat/core_main/enums/user_profile/document_type_enum.dart';

import '../../../features/properties/data/models/property_dimension_model.dart';

part 'booked_details_model.g.dart';

@JsonSerializable()
class BookedDetailsModel {
  final DateTime? bookedDate;
  final String? bookedBy;
  final String? bookedByName;
  final String? secondaryOwner;
  final String? secondaryOwnerName;
  final String? bookedByUser;
  final String? bookedUnderName;
  final String? userId;
  final String? userName;
  final String? soldPrice;
  final String? notes;
  final List<String>? projectsList;
  final List<String>? propertiesList;
  final String? teamHead;
  final String? teamHeadName;
  final double? agreementValue;
  final Iterable<DocumentsModel>? documents;
  final double? carParkingCharges;
  final double? additionalCharges;
  final double? tokenAmount;
  @JsonKey(unknownEnumValue: TokenType.none)
  final TokenType? paymentMode;
  final double? discount;
  final String? discountUnit;
  @JsonKey(unknownEnumValue: DiscountType.none)
  final DiscountType? discountMode;
  final double? remainingAmount;
  final PaymentType? paymentType;
  final String? leadBrokerageInfoId;
  final LeadBrokerageInfoModel? brokerageInfo;
  final BasicPropertyInfoModel? property;
  final BasicProjectModel? projects;
  final bool? isBookingCompleted;
  final DateTime? lastModifiedOn;
  final String? currency;
  final UnitTypeModel? unitType;

  BookedDetailsModel({
    this.bookedDate,
    this.bookedBy,
    this.bookedByName,
    this.secondaryOwner,
    this.secondaryOwnerName,
    this.bookedByUser,
    this.bookedUnderName,
    this.userId,
    this.userName,
    this.soldPrice,
    this.notes,
    this.projectsList,
    this.propertiesList,
    this.teamHead,
    this.teamHeadName,
    this.agreementValue,
    this.documents,
    this.carParkingCharges,
    this.additionalCharges,
    this.tokenAmount,
    this.paymentMode,
    this.discount,
    this.discountUnit,
    this.discountMode,
    this.remainingAmount,
    this.paymentType,
    this.leadBrokerageInfoId,
    this.brokerageInfo,
    this.property,
    this.projects,
    this.isBookingCompleted,
    this.lastModifiedOn,
    this.currency,
    this.unitType,
  });

  factory BookedDetailsModel.fromJson(Map<String, dynamic> json) => _$BookedDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookedDetailsModelToJson(this);
}

@JsonSerializable()
class DocumentsModel {
  final String? documentName;
  final String? filePath;
  final DocumentType? type;
  @JsonKey(unknownEnumValue: BookedDocumentType.none)
  final BookedDocumentType? bookedDocumentType;
  final DateTime? uploadedOn;

  DocumentsModel({
    this.documentName,
    this.filePath,
    this.type,
    this.bookedDocumentType,
    this.uploadedOn,
  });

  factory DocumentsModel.fromJson(Map<String, dynamic> json) => _$DocumentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentsModelToJson(this);
}

@JsonSerializable()
class LeadBrokerageInfoModel {
  final double? soldPrice;
  final double? agreementValue;
  final double? brokerageCharges;
  final double? netBrokerageAmount;
  final double? gst;
  final double? totalBrokerage;
  final String? referralNumber;
  final String? referralName;
  final String? referredBy;
  final double? commission;
  final String? commissionUnit;
  final BrokerageType? brokerageType;
  final double? earnedBrokerage;
  final String? gSTUnit;
  final String? brokerageUnit;

  LeadBrokerageInfoModel({
    this.soldPrice,
    this.agreementValue,
    this.brokerageCharges,
    this.netBrokerageAmount,
    this.gst,
    this.totalBrokerage,
    this.referralNumber,
    this.referralName,
    this.referredBy,
    this.commission,
    this.commissionUnit,
    this.brokerageType,
    this.earnedBrokerage,
    this.gSTUnit,
    this.brokerageUnit,
  });

  factory LeadBrokerageInfoModel.fromJson(Map<String, dynamic> json) => _$LeadBrokerageInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadBrokerageInfoModelToJson(this);
}

@JsonSerializable()
class UnitTypeModel {
  final String? id;
  final String? name;
  final double? carpetArea;
  final String? carpetAreaUnitId;
  final double? superBuildUpArea;
  final String? superBuildUpAreaUnit;

  UnitTypeModel({
    this.id,
    this.name,
    this.carpetArea,
    this.carpetAreaUnitId,
    this.superBuildUpArea,
    this.superBuildUpAreaUnit,
  });

  factory UnitTypeModel.fromJson(Map<String, dynamic> json) => _$UnitTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$UnitTypeModelToJson(this);
}

@JsonSerializable()
class BasicPropertyInfoModel {
  final String? title;
  final EnquiryType? enquiredFor;
  final String? propertyTypeId;
  final double? noOfBHK;
  final double? noOfBHKs;
  final BHKType? bHKType;
  final PropertyDimensionModel? dimension;
  final String? aboutProperty;
  final String? project;
  final PropertyOwnerDetailsModel? ownerDetails;
  final PropertyMonetaryInfoModel? monetaryInfo;

  BasicPropertyInfoModel({
    this.title,
    this.enquiredFor,
    this.propertyTypeId,
    this.noOfBHK,
    this.noOfBHKs,
    this.bHKType,
    this.dimension,
    this.aboutProperty,
    this.project,
    this.ownerDetails,
    this.monetaryInfo,
  });

  factory BasicPropertyInfoModel.fromJson(Map<String, dynamic> json) => _$BasicPropertyInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$BasicPropertyInfoModelToJson(this);
}

@JsonSerializable()
class PropertyOwnerDetailsModel {
  final String? id;
  final String? name;
  final String? phone;
  final String? alternateContactNo;
  final String? email;

  PropertyOwnerDetailsModel({
    this.id,
    this.name,
    this.phone,
    this.alternateContactNo,
    this.email,
  });

  factory PropertyOwnerDetailsModel.fromJson(Map<String, dynamic> json) => _$PropertyOwnerDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyOwnerDetailsModelToJson(this);

  PropertyOwnerDetailsModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? alternateContactNo,
    String? email,
  }) {
    return PropertyOwnerDetailsModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      alternateContactNo: alternateContactNo ?? this.alternateContactNo,
      email: email ?? this.email,
    );
  }
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class PropertyMonetaryInfoModel {
  final String? id;
  final int? expectedPrice;
  final bool? isNegotiable;
  final double? brokerage;
  @JsonKey(unknownEnumValue: BrokerageUnit.none)
  final BrokerageUnit? brokerageUnit;
  final String? currency;
  final String? brokerageCurrency;
  final int? depositAmount;
  final int? maintenanceCost;
  final int? monthlyRentAmount;
  final int? escalationPercentage;
  final bool? isPriceVissible;
  final PaymentFrequency? paymentFrequency;
  final double? serviceChange;
  final int? noOfChequesAllowed;

  PropertyMonetaryInfoModel({
    this.id,
    this.expectedPrice,
    this.isNegotiable,
    this.brokerage,
    this.brokerageUnit,
    this.currency,
    this.brokerageCurrency,
    this.depositAmount,
    this.maintenanceCost,
    this.monthlyRentAmount,
    this.escalationPercentage,
    this.isPriceVissible,
    this.paymentFrequency,
    this.serviceChange,
    this.noOfChequesAllowed,
  });

  factory PropertyMonetaryInfoModel.fromJson(Map<String, dynamic> json) => _$PropertyMonetaryInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyMonetaryInfoModelToJson(this);

  PropertyMonetaryInfoModel copyWith({
    String? id,
    int? expectedPrice,
    bool? isNegotiable,
    double? brokerage,
    BrokerageUnit? brokerageUnit,
    String? currency,
    String? brokerageCurrency,
    int? depositAmount,
    int? maintenanceCost,
    int? monthlyRentAmount,
    int? escalationPercentage,
    bool? isPriceVissible,
    PaymentFrequency? paymentFrequency,
    double? serviceChange,
    int? noOfChequesAllowed,
  }) {
    return PropertyMonetaryInfoModel(
      id: id ?? this.id,
      monthlyRentAmount: monthlyRentAmount ?? this.monthlyRentAmount,
      expectedPrice: expectedPrice ?? this.expectedPrice,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      brokerage: brokerage ?? this.brokerage,
      brokerageUnit: brokerageUnit ?? this.brokerageUnit,
      currency: currency ?? this.currency,
      brokerageCurrency: brokerageCurrency ?? this.brokerageCurrency,
      depositAmount: depositAmount ?? this.depositAmount,
      maintenanceCost: maintenanceCost ?? this.maintenanceCost,
      escalationPercentage: escalationPercentage ?? this.escalationPercentage,
      isPriceVissible: isPriceVissible ?? this.isPriceVissible,
      paymentFrequency: paymentFrequency ?? this.paymentFrequency,
      serviceChange: serviceChange ?? this.serviceChange,
      noOfChequesAllowed: noOfChequesAllowed ?? this.noOfChequesAllowed,
    );
  }

  PropertyMonetaryInfoModel reset({
    String? id,
    bool isOfficeOrCoWorkingSpaceSelected = false,
    int? expectedPrice,
    bool? isNegotiable,
    double? brokerage,
    BrokerageUnit? brokerageUnit,
    String? currency,
    String? brokerageCurrency,
    int? depositAmount,
    int? maintenanceCost,
    int? monthlyRentAmount,
    int? escalationPercentage,
    bool? isPriceVissible,
    PaymentFrequency? paymentFrequency,
    double? serviceChange,
    int? noOfChequesAllowed,
  }) {
    return PropertyMonetaryInfoModel(
      id: id ?? this.id,
      expectedPrice: expectedPrice ?? this.expectedPrice,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      brokerage: brokerage ?? this.brokerage,
      brokerageUnit: brokerageUnit ?? this.brokerageUnit,
      currency: currency ?? this.currency,
      brokerageCurrency: brokerageCurrency ?? this.brokerageCurrency,
      depositAmount: depositAmount ?? this.depositAmount,
      maintenanceCost: maintenanceCost ?? this.maintenanceCost,
      escalationPercentage: isOfficeOrCoWorkingSpaceSelected ? escalationPercentage ?? this.escalationPercentage : null,
      isPriceVissible: isPriceVissible ?? this.isPriceVissible,
      paymentFrequency: paymentFrequency ?? this.paymentFrequency,
      serviceChange: serviceChange ?? this.serviceChange,
      noOfChequesAllowed: noOfChequesAllowed ?? this.noOfChequesAllowed,
    );
  }
}

@JsonSerializable()
class BasicProjectModel {
  final String? id;
  final String? name;
  final ProjectBuilderInfoModel? builderDetail;
  final ProjectMonetaryInfoModel? monetaryInfo;

  BasicProjectModel({
    this.id,
    this.name,
    this.builderDetail,
    this.monetaryInfo,
  });

  factory BasicProjectModel.fromJson(Map<String, dynamic> json) => _$BasicProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$BasicProjectModelToJson(this);
}
