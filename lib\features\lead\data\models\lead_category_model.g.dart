// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadCategoryModel _$LeadCategoryModelFromJson(Map<String, dynamic> json) =>
    LeadCategoryModel(
      leadFilter:
          $enumDecodeNullable(_$LeadCategoryTypeEnumMap, json['leadFilter']),
      totalCount: (json['totalCount'] as num?)?.toInt(),
      itemsCount: (json['itemsCount'] as num?)?.toInt(),
      leads: (json['leads'] as List<dynamic>?)
          ?.map((e) => GetAllLeadModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LeadCategoryModelToJson(LeadCategoryModel instance) =>
    <String, dynamic>{
      'leadFilter': _$LeadCategoryTypeEnumMap[instance.leadFilter],
      'totalCount': instance.totalCount,
      'itemsCount': instance.itemsCount,
      'leads': instance.leads,
    };

const _$LeadCategoryTypeEnumMap = {
  LeadCategoryType.newLead: 0,
  LeadCategoryType.pending: 1,
  LeadCategoryType.scheduledMeeting: 2,
  LeadCategoryType.siteVisitScheduled: 3,
  LeadCategoryType.notInterested: 4,
  LeadCategoryType.callBack: 5,
  LeadCategoryType.unassignedLeads: 6,
  LeadCategoryType.booked: 7,
  LeadCategoryType.dropped: 8,
  LeadCategoryType.hotLeads: 9,
  LeadCategoryType.escalated: 10,
  LeadCategoryType.aboutToConvert: 11,
  LeadCategoryType.scheduleToday: 12,
  LeadCategoryType.overdue: 13,
  LeadCategoryType.warmLeads: 15,
  LeadCategoryType.coldLeads: 16,
  LeadCategoryType.highlightedLeads: 17,
  LeadCategoryType.allLeads: 14,
  LeadCategoryType.scheduledTomorrow: 18,
  LeadCategoryType.upcomingSchedules: 19,
  LeadCategoryType.active: 20,
  LeadCategoryType.allWithNID: 21,
  LeadCategoryType.bookingCancel: 22,
  LeadCategoryType.untouched: 23,
  LeadCategoryType.expressionOfInterest: 24,
  LeadCategoryType.siteVisitDone: 25,
  LeadCategoryType.meetingDone: 26,
  LeadCategoryType.invoiced: 27,
};

LeadCategoryCustomModel _$LeadCategoryCustomModelFromJson(
        Map<String, dynamic> json) =>
    LeadCategoryCustomModel(
      leadFilter: json['leadFilter'] as String?,
      totalCount: (json['totalCount'] as num?)?.toInt(),
      itemsCount: (json['itemsCount'] as num?)?.toInt(),
      leads: (json['leads'] as List<dynamic>?)
          ?.map((e) => GetAllLeadModel.fromJson(e as Map<String, dynamic>)),
      filterId: json['filterId'] as String?,
    );

Map<String, dynamic> _$LeadCategoryCustomModelToJson(
        LeadCategoryCustomModel instance) =>
    <String, dynamic>{
      'leadFilter': instance.leadFilter,
      'filterId': instance.filterId,
      'totalCount': instance.totalCount,
      'itemsCount': instance.itemsCount,
      'leads': instance.leads?.toList(),
    };
