import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/common/entites/area_unit_entity.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';

class ProspectEntityMapper {
  final MasterDataLocalDataSource _masterDataRepository;
  final UsersLocalDataSource _usersLocalDataSource;

  ProspectEntityMapper(this._masterDataRepository, this._usersLocalDataSource);

  BaseUserModel? getUser(String? userId) {
    if (userId.isNullOrEmpty()) return null;

    final localUsersDetails = _usersLocalDataSource.getAllUsers();
    final matchingUser = localUsersDetails?.firstWhereOrNull((getAllUserModel) => getAllUserModel?.id == userId);
    return matchingUser?.toBaseUserModel();
  }

  AreaUnitEntity? getArea(String? areaUnit) {
    if (areaUnit.isNullOrEmpty()) return null;

    final localAreaUnits = _masterDataRepository.getAreaUnits();
    final areaUnitModel = localAreaUnits?.firstWhereOrNull((getAreaUnit) => getAreaUnit.id == areaUnit);
    return areaUnitModel?.toEntity();
  }
}
