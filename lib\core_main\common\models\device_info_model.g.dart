// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostDeviceInfoModel _$PostDeviceInfoModelFromJson(Map<String, dynamic> json) =>
    PostDeviceInfoModel(
      userId: json['userId'] as String,
      operatingSystem: (json['operatingSystem'] as num).toInt(),
      osVersion: json['osVersion'] as String,
      countryCode: json['countryCode'] as String,
      languageCode: json['languageCode'] as String,
      currencyCode: json['currencyCode'] as String,
      currencySymbol: json['currencySymbol'] as String,
      deviceTimeZone: json['deviceTimeZone'] as String,
      timeFormat: (json['timeFormat'] as num).toInt(),
      deviceModel: json['deviceModel'] as String,
      deviceUDID: json['deviceUDID'] as String,
      deviceName: json['deviceName'] as String,
      isDebug: json['isDebug'] as bool,
      isDeveloperMode: json['isDeveloperMode'] as bool,
      model: json['model'] as String,
      manufacturer: json['manufacturer'] as String,
      environment: json['environment'] as String,
      ipAddress: json['ipAddress'] as String,
      latitude: json['latitude'] as String,
      longitude: json['longitude'] as String,
    );

Map<String, dynamic> _$PostDeviceInfoModelToJson(
        PostDeviceInfoModel instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'operatingSystem': instance.operatingSystem,
      'osVersion': instance.osVersion,
      'countryCode': instance.countryCode,
      'languageCode': instance.languageCode,
      'currencyCode': instance.currencyCode,
      'currencySymbol': instance.currencySymbol,
      'deviceTimeZone': instance.deviceTimeZone,
      'timeFormat': instance.timeFormat,
      'deviceModel': instance.deviceModel,
      'deviceUDID': instance.deviceUDID,
      'deviceName': instance.deviceName,
      'isDebug': instance.isDebug,
      'isDeveloperMode': instance.isDeveloperMode,
      'model': instance.model,
      'manufacturer': instance.manufacturer,
      'environment': instance.environment,
      'ipAddress': instance.ipAddress,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
