// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationModel _$LocationModelFromJson(Map<String, dynamic> json) =>
    LocationModel(
      id: json['id'] as String?,
      location: json['location'] as String?,
      placeId: json['placeId'] as String?,
      isManual: json['isManual'] as bool? ?? false,
      community: json['community'] as String?,
      subCommunity: json['subCommunity'] as String?,
    );

Map<String, dynamic> _$LocationModelToJson(LocationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'location': instance.location,
      'placeId': instance.placeId,
      'isManual': instance.isManual,
      'community': instance.community,
      'subCommunity': instance.subCommunity,
    };
