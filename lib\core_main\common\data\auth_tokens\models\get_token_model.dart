import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'get_token_model.g.dart';

@HiveType(typeId: HiveModelConstants.getTokenModelTypeId)
@JsonSerializable(explicitToJson: true, includeIfNull: true)
class GetTokenModel {
  @HiveField(0)
  final String? idToken;
  @HiveField(1)
  final String? accessToken;
  @HiveField(2)
  final String? refreshToken;
  @HiveField(3)
  final String? sessionId;

  GetTokenModel({this.idToken, this.accessToken, this.refreshToken, this.sessionId});

  factory GetTokenModel.fromJson(Map<String, dynamic> json) => _$GetTokenModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetTokenModelToJson(this);

  GetTokenModel copyWith({
    String? idToken,
    String? accessToken,
    String? refreshToken,
    String? sessionId,
  }) {
    return GetTokenModel(
      idToken: idToken ?? this.idToken,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      sessionId: sessionId ?? this.sessionId,
    );
  }
}
