// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_feature_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppFeatureModelAdapter extends TypeAdapter<AppFeatureModel> {
  @override
  final int typeId = 190;

  @override
  AppFeatureModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppFeatureModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      createdAt: fields[2] as DateTime?,
      updatedAt: fields[3] as DateTime?,
      actions: (fields[4] as List?)?.cast<FeatureActionModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, AppFeatureModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.createdAt)
      ..writeByte(3)
      ..write(obj.updatedAt)
      ..writeByte(4)
      ..write(obj.actions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppFeatureModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FeatureActionModelAdapter extends TypeAdapter<FeatureActionModel> {
  @override
  final int typeId = 191;

  @override
  FeatureActionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FeatureActionModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, FeatureActionModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeatureActionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppFeatureModel _$AppFeatureModelFromJson(Map<String, dynamic> json) =>
    AppFeatureModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      actions: (json['actions'] as List<dynamic>?)
          ?.map((e) => FeatureActionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AppFeatureModelToJson(AppFeatureModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'actions': instance.actions,
    };

FeatureActionModel _$FeatureActionModelFromJson(Map<String, dynamic> json) =>
    FeatureActionModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$FeatureActionModelToJson(FeatureActionModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
    };
