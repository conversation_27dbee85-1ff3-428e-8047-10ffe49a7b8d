import 'package:json_annotation/json_annotation.dart';

part 'registration_device_response_model.g.dart';

@JsonSerializable()
class RegistrationDeviceResponseModel {
  final int? statusCode;
  final String? error;
  final bool? isSuccess;

  RegistrationDeviceResponseModel({this.statusCode, this.error, this.isSuccess});

  factory RegistrationDeviceResponseModel.fromJson(Map<String, dynamic> json) => _$RegistrationDeviceResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegistrationDeviceResponseModelToJson(this);
}