// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_record_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CallRecordsDataModel _$CallRecordsDataModelFromJson(
        Map<String, dynamic> json) =>
    CallRecordsDataModel(
      userId: json['userId'] as String?,
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      leadName: json['leadName'] as String?,
      contactNo: json['contactNo'] as String?,
      modifiedOn: json['modifiedOn'] == null
          ? null
          : DateTime.parse(json['modifiedOn'] as String),
      modifiedBy: json['modifiedBy'] as String?,
      callRecordingURL: json['callRecordingURL'] as String?,
      callStatus: json['callStatus'] as String?,
      callDoneBy: json['callDoneBy'] as String?,
      callDoneOn: json['callDoneOn'] == null
          ? null
          : DateTime.parse(json['callDoneOn'] as String),
    );

Map<String, dynamic> _$CallRecordsDataModelToJson(
        CallRecordsDataModel instance) =>
    <String, dynamic>{
      if (instance.userId case final value?) 'userId': value,
      if (instance.id case final value?) 'id': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.leadName case final value?) 'leadName': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.modifiedOn?.toIso8601String() case final value?)
        'modifiedOn': value,
      if (instance.modifiedBy case final value?) 'modifiedBy': value,
      if (instance.callRecordingURL case final value?)
        'callRecordingURL': value,
      if (instance.callStatus case final value?) 'callStatus': value,
      if (instance.callDoneBy case final value?) 'callDoneBy': value,
      if (instance.callDoneOn?.toIso8601String() case final value?)
        'callDoneOn': value,
    };
