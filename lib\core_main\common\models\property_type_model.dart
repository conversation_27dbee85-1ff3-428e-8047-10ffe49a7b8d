import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';

part 'property_type_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class PropertyTypeModel {
  final String? id;
  final String? baseId;
  final int? level;
  final String? type;
  final String? displayName;
  final PropertyTypeModel? childType;

  PropertyTypeModel({
    this.id,
    this.baseId,
    this.level,
    this.type,
    this.displayName,
    this.childType,
  });

  factory PropertyTypeModel.fromJson(Map<String, dynamic> json) => _$PropertyTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyTypeModelToJson(this);

  PropertyTypeEntity toEntity() {
    return PropertyTypeEntity(
      id: id,
      baseId: baseId,
      level: level,
      type: type,
      displayName: displayName,
      childType: childType?.toEntity(),
    );
  }
}
