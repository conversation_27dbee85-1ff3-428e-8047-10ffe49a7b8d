{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ed63360b0415701b7f5f88672a135b3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9851589a8a86169a7d4e43f7e721b0f20b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858d6d8898bfaa0d4518f6e677f7e16ea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d0c1514c57985a5e53aadebe2ae287f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858d6d8898bfaa0d4518f6e677f7e16ea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982709444fe987cc09a97b32f646e8ed34", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6dff1ce26036cf0322333dfd7e1a552", "guid": "bfdfe7dc352907fc980b868725387e98259913acc1a8a89c38a87ddbacf032fe", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d155a9b779503316725e67af3f19b6af", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851a76464274ba447610dc5e650c65023", "guid": "bfdfe7dc352907fc980b868725387e989058b7a80530af9b6bb027ec852e0522"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3b97fd408be7aeb1d255a9c1652eaf", "guid": "bfdfe7dc352907fc980b868725387e983160182cfc1b67c50e3e5c54d7482957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987826d55d34eb7e9c0d098eebff9c5598", "guid": "bfdfe7dc352907fc980b868725387e98a09d4acdc36b478e6e32f7de4cbc9b53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05422d85933b3212cde13b52c4394a0", "guid": "bfdfe7dc352907fc980b868725387e9826eb0293330ac852507d7f53d62dcb23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91f3b124e4e4b2a1ff94496d055fd70", "guid": "bfdfe7dc352907fc980b868725387e98387e400016e09faec0bc78f15d689dd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dccd970e91fb716c5ffe814e00cb1765", "guid": "bfdfe7dc352907fc980b868725387e988e13ae8a64f80a95d6d1ddb7a500429a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d0b1b28b121ef9bd5332b7b00f86c6", "guid": "bfdfe7dc352907fc980b868725387e98e0c2aab0035dc140117d0c024e99980e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d17b244b949c4c966d52acc9f03f19c", "guid": "bfdfe7dc352907fc980b868725387e987d41d0e445f838b54c8a7aa81c1023d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986723efedf2acaca411fe52593bbc09c4", "guid": "bfdfe7dc352907fc980b868725387e98061fc859972fb6eb6eebb6aed919497c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98822ea313fd5db89b5141707bc2d87267", "guid": "bfdfe7dc352907fc980b868725387e98e7cf385c79703868e443172593ec97a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98861ec9cc9c1c819167fdef9ba59f9720", "guid": "bfdfe7dc352907fc980b868725387e98ba6a14f2566ea31ad200a57d9b3c3bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f69e7c80dcbf57f4b519fb1ba3bb471e", "guid": "bfdfe7dc352907fc980b868725387e986b68d994bf28842ae8ab90fc7135bcdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4381f07d7ce921b827e61fea81bfeef", "guid": "bfdfe7dc352907fc980b868725387e98674cdc1851e0cc939b18d3e19317baad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f42d3c39156f12d712e46e9fed1683b", "guid": "bfdfe7dc352907fc980b868725387e9890ca0a224cb5e6b7fe7c0155dd3a1166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4036778b5f592e0d0ff8d1dfbc70cfb", "guid": "bfdfe7dc352907fc980b868725387e98ef9b15885e8458ce6ad5ecd7ac803b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981005d72c0f499fdfae5d689778582813", "guid": "bfdfe7dc352907fc980b868725387e984fd814099d9df1b3f44f03212df9e778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf259242a482e3fa791dd047b135477", "guid": "bfdfe7dc352907fc980b868725387e98a3f616279d29d2c5e80ad9d10a8ffc05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbffe2a547bbcdd87892b541e77dde0f", "guid": "bfdfe7dc352907fc980b868725387e98dadc6a16398978481aa6f407d7cd0b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c0d6c215c9fe880b8847572dc9ef83", "guid": "bfdfe7dc352907fc980b868725387e984a3777692b931da79c868d901d08b2c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee4f2987dd1619b981d213d48c469cb", "guid": "bfdfe7dc352907fc980b868725387e98b7fbce2514b34261d48b419285adfb5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7861046cb987f2c443cc2702095c199", "guid": "bfdfe7dc352907fc980b868725387e9892c1d86532455d3398ab090d2453b0fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881fa076a11a115b4f91fadc399016f4f", "guid": "bfdfe7dc352907fc980b868725387e98a36fdd39645c4a1a107cec08cd8ab642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe910acbd3a9b6152fcadf4f3c20f3c3", "guid": "bfdfe7dc352907fc980b868725387e98d9cf4dc982a4ff59ebdec6ab3ab4da06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb09ad260d5f5b02bb0acc15732c0336", "guid": "bfdfe7dc352907fc980b868725387e98e46d289f0043719e8861db81a73f45aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd4ff7016d971b1e80dfff182402c5f", "guid": "bfdfe7dc352907fc980b868725387e982be18b887a5340a31e487596636aa236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d99bff74610c7ae60b0799be4e4c18", "guid": "bfdfe7dc352907fc980b868725387e986b1463bfa16122b7cccb97451ec81b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1318df0a0eeee7962e3ba8c298e5ba8", "guid": "bfdfe7dc352907fc980b868725387e98a8f751852b8a5f84d0efb189c1bcc52a"}], "guid": "bfdfe7dc352907fc980b868725387e98d11fadd5c2fbb7a5fe22f78f7dd2db2b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8f76d148b86e45f5a4fde2844b1b676", "guid": "bfdfe7dc352907fc980b868725387e98dadf38829be95b35cbc2366ea6f380c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ca0483875414283457834de9e17fdc", "guid": "bfdfe7dc352907fc980b868725387e9866517975b32fd83471d00a512dab3a0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98319dd1f13acf22235c0ed07e76444d06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989febcf50f0594135daa125ad9e14dbc4", "guid": "bfdfe7dc352907fc980b868725387e98a23acc4639a09574d32f9cfdf658211a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98466b309f315d5fd61120912851c47deb"}], "guid": "bfdfe7dc352907fc980b868725387e983ab139472a6297358dd42503556e6023", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e14fc405dcd0afb977396de55940e19d", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98642a35287e366540f77224c72a5545c1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}