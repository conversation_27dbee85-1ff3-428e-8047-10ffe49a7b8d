import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_cubit.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/create_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/repository/saved_filter_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/data_management/data/models/data_filter_model.dart';
import 'package:leadrat/features/data_management/presentation/bloc/manage_data_bloc/manage_data_bloc.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/main.dart';

part 'saved_filter_event.dart';
part 'saved_filter_state.dart';

class SavedFilterBloc extends Bloc<SavedFilterEvent, SavedFilterState> {
  final SavedFilterRepository _savedFilterRepository;
  List<ItemSimpleModel<GetSavedFilterModel>> tempSavedFilters = [];

  SavedFilterBloc(this._savedFilterRepository) : super(const SavedFilterState()) {
    on<InitSavedFiltersEvent>(_onInitSavedFilters);
    on<CreateSavedFilterEvent>(_onCreateSavedFilter);
    on<DeleteSavedFilterEvent>(_onDeleteSavedFilter);
    on<UpdateSavedFilterEvent>(_onUpdateSavedFilter);
    on<ApplySavedFilterEvent>(_onApplySavedFilter);
    on<SearchSavedFilterEvent>(_onSearchSavedFilter);
    on<ToggleAppliedFilterEvent>(_onToggleAppliedFilter);
  }

  FutureOr<void> _onInitSavedFilters(InitSavedFiltersEvent event, Emitter<SavedFilterState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, savedFilters: [], errorMessage: '', savedFilterModule: event.savedFilterModule, dialogState: PageState.initial));
    tempSavedFilters = [];
    final savedFilters = await _savedFilterRepository.getSavedFilters(module: event.savedFilterModule.description);
    if (savedFilters?.items.isNotEmpty ?? false) {
      final filters = savedFilters?.items.map((savedFilter) => ItemSimpleModel<GetSavedFilterModel>(title: savedFilter.name ?? '', value: savedFilter, description: savedFilter.id, isSelected: state.appliedFilter?.trim() == savedFilter.name?.trim())).toList();
      emit(state.copyWith(pageState: PageState.success, savedFilters: filters));
      tempSavedFilters = state.savedFilters;
    } else {
      emit(state.copyWith(pageState: PageState.error, errorMessage: "No saved filters found"));
    }
  }

  FutureOr<void> _onSearchSavedFilter(SearchSavedFilterEvent event, Emitter<SavedFilterState> emit) async {
    if (event.searchText.trim().isEmpty) {
      emit(state.copyWith(savedFilters: tempSavedFilters));
      return;
    }
    final searchedItems = tempSavedFilters.where((element) => element.title.toLowerCase().contains(event.searchText.toLowerCase().trim())).toList();
    emit(state.copyWith(savedFilters: searchedItems));
  }

  FutureOr<void> _onCreateSavedFilter(CreateSavedFilterEvent event, Emitter<SavedFilterState> emit) async {
    if (await _savedFilterRepository.checkIfFilterNameExits(filterName: event.filterName, module: event.module.description)) {
      LeadratCustomSnackbar.show(message: "Filter name already exists", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
      return;
    }
    final escapedJsonString = jsonEncode(event.filterCriteria);
    final createSavedFilter = CreateSavedFilterModel(filterCriteria: escapedJsonString, name: event.filterName.trim(), module: event.module.description);
    final response = await _savedFilterRepository.createSavedFilter(createSavedFilter);
    if (response.isNotNullOrEmpty()) {
      LeadratCustomSnackbar.show(message: "Filter saved successfully", type: SnackbarType.success, navigatorKey: MyApp.navigatorKey);
      emit(state.copyWith(dialogState: PageState.success));
      final filter = ItemSimpleModel<GetSavedFilterModel>(
          title: createSavedFilter.name ?? '',
          value: GetSavedFilterModel(
            module: state.savedFilterModule.description,
            name: event.filterName,
            filterCriteria: escapedJsonString,
            id: response,
          ),
          description: response);
      getIt<SaveFilterCubit>().toggleSaveFilter(isUpdate: true, filter: filter);
      add(ToggleAppliedFilterEvent(filterName: event.filterName.trim()));
    } else {
      LeadratCustomSnackbar.show(message: "Failed to save the filter", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
    }
    DialogManager().hideTransparentProgressDialog();
  }

  FutureOr<void> _onDeleteSavedFilter(DeleteSavedFilterEvent event, Emitter<SavedFilterState> emit) async {
    final response = await _savedFilterRepository.deleteSavedFilter(event.item.description ?? "");
    if (response) {
      LeadratCustomSnackbar.show(message: "Filter deleted successfully", type: SnackbarType.success, navigatorKey: MyApp.navigatorKey);
      final updatedSavedFilters = state.savedFilters.map((e) => e.description != event.item.description ? e : null).whereNotNull().toList();
      emit(state.copyWith(savedFilters: updatedSavedFilters, dialogState: PageState.success));
      tempSavedFilters = updatedSavedFilters;
      DialogManager().hideTransparentProgressDialog();
    } else {
      LeadratCustomSnackbar.show(message: "Failed to delete the filter", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
    }
  }

  FutureOr<void> _onUpdateSavedFilter(UpdateSavedFilterEvent event, Emitter<SavedFilterState> emit) async {
    if (event.checkFilterExits && (await _savedFilterRepository.checkIfFilterNameExits(module: event.module.description, filterName: event.filterName))) {
      LeadratCustomSnackbar.show(message: "Filter name already exists", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
      return;
    }
    final escapedJsonString = jsonEncode(event.filterCriteria);
    final createSavedFilter = CreateSavedFilterModel(id: event.filterId, filterCriteria: escapedJsonString, name: event.filterName.trim(), module: event.module.description);
    final response = await _savedFilterRepository.updateSavedFilter(createSavedFilter);
    if (response.isNotNullOrEmpty()) {
      LeadratCustomSnackbar.show(message: "Filter updated successfully", type: SnackbarType.success, navigatorKey: MyApp.navigatorKey);
      emit(state.copyWith(dialogState: PageState.success));
      final updatedFilter = state.savedFilters.firstWhereOrNull((element) => element.description == event.filterId);
      getIt<SaveFilterCubit>().toggleSaveFilter(isUpdate: true, filter: updatedFilter);
    } else {
      LeadratCustomSnackbar.show(message: "Failed to update the filter", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
    }
    DialogManager().hideTransparentProgressDialog();
  }

  FutureOr<void> _onApplySavedFilter(ApplySavedFilterEvent event, Emitter<SavedFilterState> emit) async {
    try {
      if (state.savedFilterModule == SavedFilterModule.prospects) {
        final savedFilterModel = DataFilterModel.fromJson(jsonDecode(event.filter.value?.filterCriteria ?? ''));
        getIt<ManageDataBloc>().add(ManageDataInitialEvent(filterType: null, dataFilter: savedFilterModel));
      } else if (state.savedFilterModule == SavedFilterModule.leads) {
        final savedFilterModel = LeadFilterModel.fromJson(jsonDecode(event.filter.value?.filterCriteria ?? ''));
        getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: savedFilterModel));
      }
      emit(state.copyWith(pageState: PageState.success, appliedFilter: event.filter.title));
    } catch (ex) {
      ex.logException();
    }
  }

  FutureOr<void> _onToggleAppliedFilter(ToggleAppliedFilterEvent event, Emitter<SavedFilterState> emit) async {
    emit(state.copyWith(appliedFilter: event.filterName, resetSelectedAppliedFilter: event.filterName == null, pageState: PageState.initial, dialogState: PageState.initial));
  }
}
