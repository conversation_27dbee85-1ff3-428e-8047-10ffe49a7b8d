import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/bloc/message_templates_bloc/message_template_bloc.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';

void selectableTemplateShowModelBottomSheet({required BuildContext context, required List<ProspectEntity?> getProspectEntities, required List<GetLeadEntity?> getLeadEntities}) {
  final leadMessageTemplateBloc = getIt<MessageTemplateBloc>();
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    isDismissible: true,
    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(20))),
    builder: (context) {
      return BlocBuilder<MessageTemplateBloc, MessageTemplateState>(
        builder: (context, state) {
          final templates = state.templates?.where((element) => element.isEnabled).map((e) => e).toList();
          return Container(
            height: context.height(70),
            color: ColorPalette.white,
            child: Column(
              children: [
                Container(
                  decoration: const BoxDecoration(color: ColorPalette.primaryTextColor, borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
                  height: context.height(6),
                  padding: const EdgeInsets.only(left: 20, right: 10),
                  child: Row(
                    children: [
                      Text("select a template", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryLightColor)),
                      const SizedBox(width: 10),
                      Container(height: 10, width: 1, color: ColorPalette.veryLightGray),
                      const Spacer(),
                      IconButton(onPressed: () => Navigator.pop(context), icon: SvgPicture.asset(ImageResources.iconRoundedClose)),
                    ],
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
                    child: TextField(
                      controller: leadMessageTemplateBloc.searchTemplateTextEditingController,
                      onChanged: (value) => leadMessageTemplateBloc.add(SearchTemplateEvent(searchText: value)),
                      style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryColor),
                      cursorColor: ColorPalette.primaryGreen,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.search, color: ColorPalette.gravelFint),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                        hintText: "type to search",
                        hintStyle: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.placeHolderTextColor),
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(50.0), borderSide: const BorderSide(color: ColorPalette.veryLightGray, width: 1)),
                        enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50.0), borderSide: const BorderSide(color: ColorPalette.veryLightGray, width: 1)),
                        focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50.0), borderSide: const BorderSide(color: ColorPalette.veryLightGray, width: 1)),
                      ),
                    )),
                if (templates?.isEmpty ?? true)
                  Expanded(child: Center(child: Text("No items found!", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.lightDarkBackground))))
                else
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      height: context.height(50),
                      margin: const EdgeInsets.only(left: 20, top: 10, right: 10),
                      child: ListView.builder(
                        itemCount: templates?.length ?? 0,
                        itemBuilder: (context, index) => IntrinsicWidth(
                          child: GestureDetector(
                            onTap: () {
                              leadMessageTemplateBloc.add(SelectTemplateEvent(
                                selectedTemplate: templates?[index],
                                leadEntity: getLeadEntities,
                                prospectEntities: getProspectEntities,
                              ));
                              Navigator.pop(context);
                            },
                            child: Container(
                              padding: const EdgeInsets.only(top: 13, bottom: 3),
                              decoration: const BoxDecoration(border: Border(bottom: BorderSide(color: ColorPalette.veryLightGray, width: 1))),
                              alignment: Alignment.bottomLeft,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                    width: context.width(70),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(templates?[index].value?.title ?? '', style: LexendTextStyles.lexend10SemiBold.copyWith(color: templates?[index].value == state.selectedTemplate?.value ? ColorPalette.primaryGreen : ColorPalette.black)),
                                        Text(
                                          templates?[index].value?.message ?? '',
                                          style: LexendTextStyles.lexend10SemiBold.copyWith(color: templates?[index].value == state.selectedTemplate?.value ? ColorPalette.primaryGreen : ColorPalette.primary),
                                          maxLines: 1,
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (templates?[index].value == state.selectedTemplate?.value)
                                    Container(
                                      alignment: Alignment.center,
                                      width: 20,
                                      height: 20,
                                      margin: const EdgeInsets.only(right: 8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(100),
                                        border: Border.all(color: ColorPalette.primaryGreen, width: 1.5),
                                      ),
                                      child: const Icon(
                                        Icons.check_rounded,
                                        color: ColorPalette.primaryGreen,
                                        size: 16,
                                      ),
                                    )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      );
    },
  );
}
