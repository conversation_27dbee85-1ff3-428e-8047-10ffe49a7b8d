// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_token_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GetTokenModelAdapter extends TypeAdapter<GetTokenModel> {
  @override
  final int typeId = 36;

  @override
  GetTokenModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GetTokenModel(
      idToken: fields[0] as String?,
      accessToken: fields[1] as String?,
      refreshToken: fields[2] as String?,
      sessionId: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, GetTokenModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.idToken)
      ..writeByte(1)
      ..write(obj.accessToken)
      ..writeByte(2)
      ..write(obj.refreshToken)
      ..writeByte(3)
      ..write(obj.sessionId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetTokenModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetTokenModel _$GetTokenModelFromJson(Map<String, dynamic> json) =>
    GetTokenModel(
      idToken: json['idToken'] as String?,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      sessionId: json['sessionId'] as String?,
    );

Map<String, dynamic> _$GetTokenModelToJson(GetTokenModel instance) =>
    <String, dynamic>{
      'idToken': instance.idToken,
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'sessionId': instance.sessionId,
    };
