// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_status_count.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserStatusCountModel _$UserStatusCountModelFromJson(
        Map<String, dynamic> json) =>
    UserStatusCountModel(
      totalLeads: (json['totalLeads'] as num?)?.toInt(),
      activeLeads: (json['activeLeads'] as num?)?.toInt(),
      unassignedLeads: (json['unassignedLeads'] as num?)?.toInt(),
      deletedLeads: (json['deletedLeads'] as num?)?.toInt(),
      dropped: (json['dropped'] as num?)?.toInt(),
      pending: (json['pending'] as num?)?.toInt(),
      callback: (json['callback'] as num?)?.toInt(),
      booked: (json['booked'] as num?)?.toInt(),
      bookingCancel: (json['bookingCancel'] as num?)?.toInt(),
      meetingScheduled: (json['meetingScheduled'] as num?)?.toInt(),
      siteVisitScheduled: (json['siteVisitScheduled'] as num?)?.toInt(),
      overdue: (json['overdue'] as num?)?.toInt(),
      notInterested: (json['notInterested'] as num?)?.toInt(),
      expressionOfInterest: (json['expressionOfInterest'] as num?)?.toInt(),
      siteVisitDoneUniqueCount:
          (json['siteVisitDoneUniqueCount'] as num?)?.toInt(),
      siteVisitDone: (json['siteVisitDone'] as num?)?.toInt(),
      meetingDoneUniqueCount: (json['meetingDoneUniqueCount'] as num?)?.toInt(),
      meetingDone: (json['meetingDone'] as num?)?.toInt(),
      newLeads: (json['new'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UserStatusCountModelToJson(
        UserStatusCountModel instance) =>
    <String, dynamic>{
      if (instance.totalLeads case final value?) 'totalLeads': value,
      if (instance.activeLeads case final value?) 'activeLeads': value,
      if (instance.unassignedLeads case final value?) 'unassignedLeads': value,
      if (instance.deletedLeads case final value?) 'deletedLeads': value,
      if (instance.dropped case final value?) 'dropped': value,
      if (instance.pending case final value?) 'pending': value,
      if (instance.callback case final value?) 'callback': value,
      if (instance.booked case final value?) 'booked': value,
      if (instance.bookingCancel case final value?) 'bookingCancel': value,
      if (instance.meetingScheduled case final value?)
        'meetingScheduled': value,
      if (instance.siteVisitScheduled case final value?)
        'siteVisitScheduled': value,
      if (instance.overdue case final value?) 'overdue': value,
      if (instance.notInterested case final value?) 'notInterested': value,
      if (instance.expressionOfInterest case final value?)
        'expressionOfInterest': value,
      if (instance.siteVisitDoneUniqueCount case final value?)
        'siteVisitDoneUniqueCount': value,
      if (instance.siteVisitDone case final value?) 'siteVisitDone': value,
      if (instance.meetingDoneUniqueCount case final value?)
        'meetingDoneUniqueCount': value,
      if (instance.meetingDone case final value?) 'meetingDone': value,
      if (instance.newLeads case final value?) 'new': value,
    };
