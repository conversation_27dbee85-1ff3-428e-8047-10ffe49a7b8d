import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/local/auth_token_local_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/remote/auth_token_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/models/get_token_model.dart';
import 'package:leadrat/core_main/common/models/base_url_model.dart';
import 'package:leadrat/core_main/common/models/force_update_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/errors/business_exception/rest_token_invalid_exception.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/token_manager.dart';
import 'package:leadrat/features/auth/domain/repository/auth_repository.dart';
import 'package:leadrat/features/auth/presentation/pages/auth_domain_page.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/main.dart';

import 'auth_token_repository.dart';

class AuthTokenRepositoryImpl implements AuthTokenRepository {
  final AuthTokenDataSource _authTokenDataSource;
  final AuthTokenLocalDataSource _authTokenLocalDataSource;
  final AuthRepository _authRepository;
  final TokenManager _tokenManager;

  AuthTokenRepositoryImpl(this._authTokenDataSource, this._tokenManager, this._authTokenLocalDataSource, this._authRepository);

  @override
  Future<GetTokenModel?> getRefreshToken() async {
    try {
      final localTokenData = _authTokenLocalDataSource.getTokenDetails();
      if (localTokenData == null && localTokenData?.idToken == null || localTokenData?.refreshToken == null) throw "Token details not found";

      final localTenantDetails = _authRepository.getLocalTenantDetails();
      if (localTenantDetails == null && localTenantDetails?.id == null) throw "Tenant details not found";

      final refreshTokenResponse = await _authTokenDataSource.getRefreshToken(
        idToken: localTokenData!.idToken!,
        refreshToken: localTokenData.refreshToken!,
        tenant: localTenantDetails!.id!,
      );
      if (refreshTokenResponse != null) {
        _tokenManager.setIdToken(refreshTokenResponse.idToken ?? "");
        _tokenManager.setRefreshToken(refreshTokenResponse.refreshToken ?? "");
        final updatedTokenModel = localTokenData.copyWith(refreshToken: refreshTokenResponse.refreshToken, idToken: refreshTokenResponse.idToken);
        await _authTokenLocalDataSource.saveTokenDetails(updatedTokenModel);
      }
      return refreshTokenResponse;
    } on RestTokenInvalidException catch (_) {
      getIt<LeadratHomeBloc>().add(LogoutEvent());
      MyApp.navigatorKey.currentState?.pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const AuthDomainPage()),
            (route) => false,
      );
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
      rethrow;
    }
  }

  @override
  Future<GetTokenModel?> getToken({required String tenant, required String userName, required String password}) async {
    try {
      final tokenResponse = await _authTokenDataSource.getToken(tenant: tenant, userName: userName, password: password);
      if (tokenResponse != null) {
        await _tokenManager.setIdToken(tokenResponse.idToken ?? "");
        await _tokenManager.setRefreshToken(tokenResponse.refreshToken ?? "");
        await _tokenManager.setTenant(tenant);
        await _tokenManager.setPass(password);
        await _tokenManager.setUsername(userName);
        await _authTokenLocalDataSource.saveTokenDetails(tokenResponse);

        // To get the UserId of the logged in user
        // We decode the JWT token to get the userid
        Map<String, dynamic> jsonObject = JWT.decode(tokenResponse.idToken ?? '').payload;
        if (jsonObject.containsKey('sub')) {
          String userId = jsonObject['sub'];
          await _tokenManager.setUserId(userId);
        }
      }
      return tokenResponse;
    } catch (exception,stackTrace) {
      exception.logException(stackTrace);
      rethrow;
    }
  }

  @override
  Future<String?> generateMultiFactorAuthOtp({required String userName}) async {
    try {
      final localTenantDetails = _authRepository.getLocalTenantDetails();
      if (localTenantDetails == null) return null;
      return await _authTokenDataSource.generateMultiFactorAuthOtp(userName: userName, tenant: localTenantDetails.id!);
    } catch (ex) {
      throw Exception("unable to generate otp");
    }
  }

  @override
  Future<GetTokenModel?> verifyAuthOtp({required String userName, required String sessionId, required String otp}) async {
    try {
      final localTenantDetails = _authRepository.getLocalTenantDetails();
      if (localTenantDetails == null) return null;
      final tokenResponse = await _authTokenDataSource.verifyAuthOtp(userName: userName, sessionId: sessionId, otp: otp, tenant: localTenantDetails.id!);
      if (tokenResponse != null) {
        await _tokenManager.setIdToken(tokenResponse.idToken ?? "");
        await _tokenManager.setRefreshToken(tokenResponse.refreshToken ?? "");
        await _tokenManager.setUsername(userName);
        await _authTokenLocalDataSource.saveTokenDetails(tokenResponse);

        Map<String, dynamic> jsonObject = JWT.decode(tokenResponse.idToken ?? '').payload;
        if (jsonObject.containsKey('sub')) {
          String userId = jsonObject['sub'];
          await _tokenManager.setUserId(userId);
        }
      }
      return tokenResponse;
    } catch (ex) {
      rethrow;
    }
  }

  @override
  Future<BaseUrlModel?> getBaseUrl() async {
    try {
      final getBaseUrl = await _authTokenDataSource.getBaseUrl();
      if (getBaseUrl == null) return null;
      return getBaseUrl;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<ForceUpdateModel?> getUpdateAppVersion() async{
    try {
      final getUpdateAppVersion = await _authTokenDataSource.getUpdateAppVersion();
      if (getUpdateAppVersion == null) return null;
      return getUpdateAppVersion;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }
}
