import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/event_enum.dart';
import 'package:leadrat/core_main/enums/common/module_name_enum.dart';

part 'base_notification_settings.g.dart';

@HiveType(typeId: HiveModelConstants.baseNotificationSettingsModelTypeId)
@JsonSerializable()
class BaseNotificationSettings {
  @HiveField(0)
  final ModuleName? moduleName;
  @HiveField(1)
  final Event? event;
  @HiveField(2)
  final List<int>? minutesBefore;

  BaseNotificationSettings({this.moduleName, this.event, this.minutesBefore});

  factory BaseNotificationSettings.fromJson(Map<String, dynamic> json) => _$BaseNotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$BaseNotificationSettingsToJson(this);
}
