// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_saved_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetSavedFilterModel _$GetSavedFilterModelFromJson(Map<String, dynamic> json) =>
    GetSavedFilterModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      module: json['module'] as String?,
      filterCriteria: json['filterCriteria'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      userId: json['userId'] as String?,
    );

Map<String, dynamic> _$GetSavedFilterModelToJson(
        GetSavedFilterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'module': instance.module,
      'filterCriteria': instance.filterCriteria,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedBy': instance.lastModifiedBy,
      'userId': instance.userId,
    };
