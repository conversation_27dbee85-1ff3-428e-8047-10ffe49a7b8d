// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_settings_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OtpSettingsModelAdapter extends TypeAdapter<OtpSettingsModel> {
  @override
  final int typeId = 12;

  @override
  OtpSettingsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OtpSettingsModel(
      isEnabled: fields[0] as bool?,
      receiver: fields[1] as OTPReceiver?,
      receiverUserIds: (fields[2] as List?)?.cast<String>(),
      retryInSecs: fields[4] as int?,
      channels: (fields[3] as List?)?.cast<ContactType>(),
    );
  }

  @override
  void write(BinaryWriter writer, OtpSettingsModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.isEnabled)
      ..writeByte(1)
      ..write(obj.receiver)
      ..writeByte(2)
      ..write(obj.receiverUserIds)
      ..writeByte(3)
      ..write(obj.channels)
      ..writeByte(4)
      ..write(obj.retryInSecs);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OtpSettingsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OtpSettingsModel _$OtpSettingsModelFromJson(Map<String, dynamic> json) =>
    OtpSettingsModel(
      isEnabled: json['isEnabled'] as bool?,
      receiver: $enumDecodeNullable(_$OTPReceiverEnumMap, json['receiver'],
          unknownValue: OTPReceiver.self),
      receiverUserIds: (json['receiverUserIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      retryInSecs: (json['retryInSecs'] as num?)?.toInt(),
      channels: (json['channels'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ContactTypeEnumMap, e))
          .toList(),
    );

Map<String, dynamic> _$OtpSettingsModelToJson(OtpSettingsModel instance) =>
    <String, dynamic>{
      'isEnabled': instance.isEnabled,
      'receiver': _$OTPReceiverEnumMap[instance.receiver],
      'receiverUserIds': instance.receiverUserIds,
      'channels':
          instance.channels?.map((e) => _$ContactTypeEnumMap[e]!).toList(),
      'retryInSecs': instance.retryInSecs,
    };

const _$OTPReceiverEnumMap = {
  OTPReceiver.self: 0,
  OTPReceiver.admin: 1,
  OTPReceiver.manager: 2,
  OTPReceiver.specific: 3,
};

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};
