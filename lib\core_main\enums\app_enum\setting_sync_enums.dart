
enum SyncDataEnums {
  users(0, "Users"),
  status(1, "Statuses"),
  projects(2, "Projects"),
  properties(3, "Properties"),
  source(4, "Sources"),
  subSource(5, "Sub Sources"),
  agencyNames(6, "Agency Names"),
  addresses(7, "Addresses"),
  propertyType(8, "Property Types"),
  areaUnits(9, "Area Units"),
  globalSettings(10, "Global Settings"),
  all(11, "All"),
  none(12, "None");

  final int value;
  final String key;

  const SyncDataEnums(this.value, this.key);

  factory SyncDataEnums.fromString(int value) {
    return SyncDataEnums.values.firstWhere(
          (e) => e.value == value,
      orElse: () => SyncDataEnums.none,
    );
  }
}