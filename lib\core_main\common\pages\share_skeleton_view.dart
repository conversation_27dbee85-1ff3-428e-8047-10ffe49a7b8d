import 'package:flutter/material.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:shimmer/shimmer.dart';

class ShareSkeletonView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          centerTitle: false,
          titleSpacing: 0.0,
          leadingWidth: 0,
          backgroundColor: ColorPalette.darkToneInk,
          toolbarHeight: 60.0,
          title: Padding(
            padding: const EdgeInsets.fromLTRB(24, 30, 14, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 5),
                _buildShimmerContainer(height: 2, width: 25,color: ColorPalette.darkToneInk),
              ],
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24, 20, 14, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildShimmerBox(height: 40, width: double.infinity,color: ColorPalette.darkToneInk),
              const SizedBox(height: 40),
              _buildShimmerBox(height: 60, width: double.infinity,color: ColorPalette.primaryColor),
              const SizedBox(height: 40),
              _buildShimmerBox(height: 120, width: double.infinity,color: ColorPalette.primaryColor),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerContainer({double height = 10, double width = 100,required Color color}) {
    return Shimmer.fromColors(
      baseColor: color,
      highlightColor: color,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );
  }

  Widget _buildShimmerBox({double height = 50, double width = double.infinity,required Color color}) {
    return Shimmer.fromColors(
      baseColor: color,
      highlightColor: color,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
        ),
      ),
    );
  }
}
