// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_notes_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadNotesSettingsAdapter extends TypeAdapter<LeadNotesSettings> {
  @override
  final int typeId = 10;

  @override
  LeadNotesSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LeadNotesSettings(
      isNotesMandatoryEnabled: fields[0] as bool?,
      isNotesMandatoryOnAddLead: fields[1] as bool?,
      isNotesMandatoryOnSiteVisitDone: fields[2] as bool?,
      isNotesMandatoryOnMeetingDone: fields[3] as bool?,
      isNotesMandatoryOnUpdateLead: fields[4] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, LeadNotesSettings obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.isNotesMandatoryEnabled)
      ..writeByte(1)
      ..write(obj.isNotesMandatoryOnAddLead)
      ..writeByte(2)
      ..write(obj.isNotesMandatoryOnSiteVisitDone)
      ..writeByte(3)
      ..write(obj.isNotesMandatoryOnMeetingDone)
      ..writeByte(4)
      ..write(obj.isNotesMandatoryOnUpdateLead);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadNotesSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadNotesSettings _$LeadNotesSettingsFromJson(Map<String, dynamic> json) =>
    LeadNotesSettings(
      isNotesMandatoryEnabled: json['isNotesMandatoryEnabled'] as bool?,
      isNotesMandatoryOnAddLead: json['isNotesMandatoryOnAddLead'] as bool?,
      isNotesMandatoryOnSiteVisitDone:
          json['isNotesMandatoryOnSiteVisitDone'] as bool?,
      isNotesMandatoryOnMeetingDone:
          json['isNotesMandatoryOnMeetingDone'] as bool?,
      isNotesMandatoryOnUpdateLead:
          json['isNotesMandatoryOnUpdateLead'] as bool?,
    );

Map<String, dynamic> _$LeadNotesSettingsToJson(LeadNotesSettings instance) =>
    <String, dynamic>{
      'isNotesMandatoryEnabled': instance.isNotesMandatoryEnabled,
      'isNotesMandatoryOnAddLead': instance.isNotesMandatoryOnAddLead,
      'isNotesMandatoryOnSiteVisitDone':
          instance.isNotesMandatoryOnSiteVisitDone,
      'isNotesMandatoryOnMeetingDone': instance.isNotesMandatoryOnMeetingDone,
      'isNotesMandatoryOnUpdateLead': instance.isNotesMandatoryOnUpdateLead,
    };
