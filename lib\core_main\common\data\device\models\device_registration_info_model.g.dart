// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_registration_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceRegistrationInfoModel _$DeviceRegistrationInfoModelFromJson(
        Map<String, dynamic> json) =>
    DeviceRegistrationInfoModel(
      endpointId: json['endpointId'] as String?,
      platform:
          $enumDecodeNullable(_$OperatingSystemEnumEnumMap, json['platform']),
      deviceModel: json['deviceModel'] as String?,
      deviceUDID: json['deviceUDID'] as String?,
      applicationTokenValue: json['applicationTokenValue'] as String?,
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
      email: json['email'] as String?,
      token: json['token'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      isTablet: json['isTablet'] as bool? ?? false,
      appVersion: json['appVersion'] as String?,
      aDSecurityGroups: (json['aDSecurityGroups'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      topics:
          (json['topics'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$DeviceRegistrationInfoModelToJson(
        DeviceRegistrationInfoModel instance) =>
    <String, dynamic>{
      'endpointId': instance.endpointId,
      'platform': _$OperatingSystemEnumEnumMap[instance.platform],
      'deviceModel': instance.deviceModel,
      'deviceUDID': instance.deviceUDID,
      'applicationTokenValue': instance.applicationTokenValue,
      'userId': instance.userId,
      'userName': instance.userName,
      'email': instance.email,
      'token': instance.token,
      'isActive': instance.isActive,
      'isTablet': instance.isTablet,
      'appVersion': instance.appVersion,
      'aDSecurityGroups': instance.aDSecurityGroups,
      'topics': instance.topics,
    };

const _$OperatingSystemEnumEnumMap = {
  OperatingSystemEnum.android: 0,
  OperatingSystemEnum.ios: 1,
};
