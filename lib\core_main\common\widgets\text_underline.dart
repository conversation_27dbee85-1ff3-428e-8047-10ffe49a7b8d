import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class UnderlineTextWidget extends LeadratStatelessWidget {
  const UnderlineTextWidget({required this.title, super.key});

  final String title;

  @override
  Widget buildContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      height: 20,
      decoration: const BoxDecoration(border: Border(bottom: BorderSide(color: ColorPalette.green, width: 1))),
      child: Text(
        title,
        style: LexendTextStyles.lexend12SemiBold.copyWith(
          color: ColorPalette.green,
        ),
      ),
    );
  }
}
