{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c23296c8d85a26c47bcf242e856b0849", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98833335af3780c163f29c81f8b2305ae8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98833335af3780c163f29c81f8b2305ae8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984976a3973db7a2fcc42054a5a41f1970", "guid": "bfdfe7dc352907fc980b868725387e981a59e4bc63db348c16a53b64887127a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982952b441d5af2d06c34e9c83e30b4b92", "guid": "bfdfe7dc352907fc980b868725387e989d7b068913e0196ca85a0a1f77221060", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6034dd95ca4afbd391729306c4f8bcf", "guid": "bfdfe7dc352907fc980b868725387e98558d5a870052b1d4360140bfe30a78bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984adb412177a99ab13d3fcc3af360c8e2", "guid": "bfdfe7dc352907fc980b868725387e9876917ef1e7eed5c409a4fadc1c7710f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987344571a00bdcdf69f40e64e75316dcc", "guid": "bfdfe7dc352907fc980b868725387e980e49cee18cd5cde337767afabad33dce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f79c518936115c6f115551bbe8e11b1", "guid": "bfdfe7dc352907fc980b868725387e983c654c03a1abdf2aaf934bc515b8ab15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85a5910fa0eb66b17d90662e647b46b", "guid": "bfdfe7dc352907fc980b868725387e9895638bda85be2d6bc53cbd99a77d5ab4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899436d0b596cfc231f27bc0fca38a45a", "guid": "bfdfe7dc352907fc980b868725387e980e872170372e42e1407d376bc3cae064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a683be2b5075cca3f2570a3fa72ecd7", "guid": "bfdfe7dc352907fc980b868725387e98ec195e90211f4d90560d48276df8716e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a8566854031e57f318f577a95c8b74f", "guid": "bfdfe7dc352907fc980b868725387e9850da2d62c732d150dca615c7a8153271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d705093f721aaa85b24ae4da7ae7579a", "guid": "bfdfe7dc352907fc980b868725387e98f576ef79686523f237254e79ed39aac7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf03e0b51f28da59c211421e224ebb8d", "guid": "bfdfe7dc352907fc980b868725387e98ca0a77a7192047da27f2d618056586a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98941042c0047bd3795c81d07355bdca17", "guid": "bfdfe7dc352907fc980b868725387e98d932a181d7555a18b5e0ee1ca49c673e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b60f53e5a7a6dc911c22ef56361a1b", "guid": "bfdfe7dc352907fc980b868725387e986be0dabfcb7f65fe9c00323fc232a8df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de48fd07c6dbc9f87893db79029679b8", "guid": "bfdfe7dc352907fc980b868725387e98ef68d88c039822d97ab243f4678db744", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abedb7c19720a11ce34a8e305bce459e", "guid": "bfdfe7dc352907fc980b868725387e98f48e7a1fbefc308deb13c80e6b16d76b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988527b84c3ea289252d5dea9a6b6d0317", "guid": "bfdfe7dc352907fc980b868725387e984c88ca20f7e9b9f3618c2b345340dc83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a931330748c0a18eadf9bbbb6cc5085", "guid": "bfdfe7dc352907fc980b868725387e9813f6e8a4aa0dc2249c4627d04447739e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d99f4984676f732250649abe534466c", "guid": "bfdfe7dc352907fc980b868725387e986f7cabf29082bf59327d7d4e030a134c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869db6c081d94ab205bc5e82742c050fc", "guid": "bfdfe7dc352907fc980b868725387e98648f86a72f2c6f4ad94defaba7f84d03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f8b041a8bcf78d27b198722e4ab128e", "guid": "bfdfe7dc352907fc980b868725387e98ba91ab43606de3cf2845ec4a3f659154", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ebad90ddb2e07ba75e30d321e8396aa", "guid": "bfdfe7dc352907fc980b868725387e98cc710046cef41a8bbe3ec0d6c32604e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759db82c02c85552db19c1cee9ef60a6", "guid": "bfdfe7dc352907fc980b868725387e9899d8eff216e253b9bbd5fef5c42d7620", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c056ed07ee7008d1cab50da9d1e90647", "guid": "bfdfe7dc352907fc980b868725387e987fb40a8c62e9552d86f0284f53b1266e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98424c82edc583d61394e8c0b84b28bacd", "guid": "bfdfe7dc352907fc980b868725387e986f8c994a8698a5aa113e4681f315284d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6f244f49b6e937872cc0ae7df68275", "guid": "bfdfe7dc352907fc980b868725387e989ffde279377003fdb6eb744c0325a488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984955f1ca301ec171255e17bbe8f04f2f", "guid": "bfdfe7dc352907fc980b868725387e9818a5b2ab49eb6a642bd4e2943c291e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa662df706ae72f45104394ce0012472", "guid": "bfdfe7dc352907fc980b868725387e986aeaadc6f33c03985e80d49b42de12ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdc019afcac1c80c92211be03c654624", "guid": "bfdfe7dc352907fc980b868725387e9890f300a115e6d33f249642c3d9a30ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91b5f1d235f8cdc89c1bcc61fd88fb9", "guid": "bfdfe7dc352907fc980b868725387e98d910ef43ffe664af1939c94d191fe6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2183c13a70625a007880b61f764974", "guid": "bfdfe7dc352907fc980b868725387e9861ed0928b528703719d883983fa4f573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851fcab307b8d05dec13cc63b3969211c", "guid": "bfdfe7dc352907fc980b868725387e98930737d5eb6b20350a460ba7be7b4b6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5fe8fd112ec23a015462e441701f313", "guid": "bfdfe7dc352907fc980b868725387e980664827343d9a8576da517f083011a99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307cae4db099acb20f8a3b97d0bf900b", "guid": "bfdfe7dc352907fc980b868725387e98c31cae6b77f88680c7fd22f6de9c6787"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a576ec6708bbab3f0724bc988254c9f", "guid": "bfdfe7dc352907fc980b868725387e982fbb8879706a4b804f4a62fbd9784184"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca1f4003c857f3a81c9b2b962d5c91b", "guid": "bfdfe7dc352907fc980b868725387e98db26b25d60148bd9dcf403fa7c9d4f99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821cf98d72234c7ba0efa4795e969f4f3", "guid": "bfdfe7dc352907fc980b868725387e98adef10a43deca2ef53ab29ca58ce7254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e3c4d4824b96a3590e9c5cae864ce6", "guid": "bfdfe7dc352907fc980b868725387e98dd91fdab738c707941756cf8f8d78b37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831cdbebf83bc4966dc423eb9508e9d83", "guid": "bfdfe7dc352907fc980b868725387e984b79d0815a31ff4655744954fe99eeea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263d868b778234e25b1547691ed58cff", "guid": "bfdfe7dc352907fc980b868725387e9874ead448222d472b284788c39bfda639"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832122b137abf0f3367fd3a1fabe1e8c1", "guid": "bfdfe7dc352907fc980b868725387e98ce715e0738b6676f7902b13333324359"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87178d90a0fc6555dca39f55e58feea", "guid": "bfdfe7dc352907fc980b868725387e989cde56c9ea0a1684090043e1c90764e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822faeefd69d2a76ed127b38a1f29b63f", "guid": "bfdfe7dc352907fc980b868725387e98c77ee16c95cade6c3125cfa98cb7b898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98441c39fe2a1bdae301db43d848e77b4a", "guid": "bfdfe7dc352907fc980b868725387e9899daeadf175f3c4a797547221ebae54b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982360fe4dbcfc0687287c66de7418567c", "guid": "bfdfe7dc352907fc980b868725387e98f3778c968f5ed0c1cd2b322b7a4a2fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52ae6b6880b708a153c73ee87555e99", "guid": "bfdfe7dc352907fc980b868725387e98879eb0da1344eb19ee53670d39ec6cd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865013f48bf908a2a73ac1aa8fda7dfc9", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}