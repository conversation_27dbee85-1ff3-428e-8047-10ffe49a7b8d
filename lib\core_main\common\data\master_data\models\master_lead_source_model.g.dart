// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_lead_source_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterLeadSourceModelAdapter extends TypeAdapter<MasterLeadSourceModel> {
  @override
  final int typeId = 24;

  @override
  MasterLeadSourceModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterLeadSourceModel(
      id: fields[0] as String?,
      createdOn: fields[1] as DateTime?,
      createdBy: fields[2] as String?,
      lastModifiedOn: fields[3] as DateTime?,
      lastModifiedBy: fields[4] as String?,
      deletedOn: fields[5] as DateTime?,
      deletedBy: fields[6] as String?,
      displayName: fields[7] as String?,
      value: fields[8] as int?,
      orderRank: fields[9] as int?,
      imageURL: fields[10] as String?,
      progressColor: fields[11] as String?,
      backgroundColor: fields[12] as String?,
      leadSourceType: fields[13] as LeadSource?,
      isEnabled: fields[14] as bool?,
      isDefault: fields[15] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterLeadSourceModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdOn)
      ..writeByte(2)
      ..write(obj.createdBy)
      ..writeByte(3)
      ..write(obj.lastModifiedOn)
      ..writeByte(4)
      ..write(obj.lastModifiedBy)
      ..writeByte(5)
      ..write(obj.deletedOn)
      ..writeByte(6)
      ..write(obj.deletedBy)
      ..writeByte(7)
      ..write(obj.displayName)
      ..writeByte(8)
      ..write(obj.value)
      ..writeByte(9)
      ..write(obj.orderRank)
      ..writeByte(10)
      ..write(obj.imageURL)
      ..writeByte(11)
      ..write(obj.progressColor)
      ..writeByte(12)
      ..write(obj.backgroundColor)
      ..writeByte(13)
      ..write(obj.leadSourceType)
      ..writeByte(14)
      ..write(obj.isEnabled)
      ..writeByte(15)
      ..write(obj.isDefault);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterLeadSourceModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterLeadSourceModel _$MasterLeadSourceModelFromJson(
        Map<String, dynamic> json) =>
    MasterLeadSourceModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      displayName: json['displayName'] as String?,
      value: (json['value'] as num?)?.toInt(),
      orderRank: (json['orderRank'] as num?)?.toInt(),
      imageURL: json['imageURL'] as String?,
      progressColor: json['progressColor'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      leadSourceType:
          $enumDecodeNullable(_$LeadSourceEnumMap, json['leadSourceType']),
      isEnabled: json['isEnabled'] as bool?,
      isDefault: json['isDefault'] as bool?,
    );

Map<String, dynamic> _$MasterLeadSourceModelToJson(
        MasterLeadSourceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'displayName': instance.displayName,
      'value': instance.value,
      'orderRank': instance.orderRank,
      'imageURL': instance.imageURL,
      'progressColor': instance.progressColor,
      'backgroundColor': instance.backgroundColor,
      'leadSourceType': _$LeadSourceEnumMap[instance.leadSourceType],
      'isEnabled': instance.isEnabled,
      'isDefault': instance.isDefault,
    };

const _$LeadSourceEnumMap = {
  LeadSource.any: -1,
  LeadSource.direct: 0,
  LeadSource.iVR: 1,
  LeadSource.facebook: 2,
  LeadSource.linkedIn: 3,
  LeadSource.googleAds: 4,
  LeadSource.magicBricks: 5,
  LeadSource.ninetyNineAcres: 6,
  LeadSource.housing: 7,
  LeadSource.gharOffice: 8,
  LeadSource.referral: 9,
  LeadSource.walkIn: 10,
  LeadSource.website: 11,
  LeadSource.gmail: 12,
  LeadSource.propertyMicrosite: 13,
  LeadSource.portfolioMicrosite: 14,
  LeadSource.phonebook: 15,
  LeadSource.callLogs: 16,
  LeadSource.leadPool: 17,
  LeadSource.squareYards: 18,
  LeadSource.quikrHomes: 19,
  LeadSource.justLead: 20,
  LeadSource.whatsApp: 21,
  LeadSource.youTube: 22,
  LeadSource.qRCode: 23,
  LeadSource.instagram: 24,
  LeadSource.oLX: 25,
  LeadSource.estateDekho: 26,
  LeadSource.googleSheet: 27,
  LeadSource.channelPartner: 28,
  LeadSource.realEstateIndia: 29,
  LeadSource.commonFloor: 30,
  LeadSource.data: 31,
  LeadSource.roofAndFloor: 32,
  LeadSource.microsoftAds: 33,
  LeadSource.propertyWala: 34,
  LeadSource.projectMicrosite: 35,
  LeadSource.myGate: 36,
  LeadSource.flipkart: 37,
  LeadSource.propertyFinder: 38,
  LeadSource.bayut: 39,
  LeadSource.dubizzle: 40,
  LeadSource.webhook: 41,
  LeadSource.tikTok: 42,
  LeadSource.snapchat: 43,
  LeadSource.googleAdsCampaign: 44,
};
