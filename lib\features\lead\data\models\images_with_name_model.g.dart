// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'images_with_name_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImagesWithNameModel _$ImagesWithNameModelFromJson(Map<String, dynamic> json) =>
    ImagesWithNameModel(
      documentName: json['documentName'] as String?,
      filePath: json['filePath'] as String?,
      leadDocumentType: $enumDecodeNullable(
          _$LeadDocumentTypeEnumEnumMap, json['leadDocumentType']),
      uploadedOn: json['uploadedOn'] == null
          ? null
          : DateTime.parse(json['uploadedOn'] as String),
    );

Map<String, dynamic> _$ImagesWithNameModelToJson(
        ImagesWithNameModel instance) =>
    <String, dynamic>{
      'documentName': instance.documentName,
      'filePath': instance.filePath,
      'leadDocumentType':
          _$LeadDocumentTypeEnumEnumMap[instance.leadDocumentType],
      'uploadedOn': instance.uploadedOn?.toIso8601String(),
    };

const _$LeadDocumentTypeEnumEnumMap = {
  LeadDocumentTypeEnum.none: 0,
  LeadDocumentTypeEnum.lead: 1,
  LeadDocumentTypeEnum.meeting: 2,
  LeadDocumentTypeEnum.siteVisit: 3,
};
