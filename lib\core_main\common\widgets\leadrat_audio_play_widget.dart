import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:path_provider/path_provider.dart';

class AudioManager {
  static AudioPlayer? _currentPlayer;
  static bool _isAnyPlaying = false;

  static void setCurrentPlayer(AudioPlayer player) {
    if (_currentPlayer != null && _currentPlayer != player) {
      _currentPlayer!.pause();
    }
    _currentPlayer = player;
  }

  static Future<void> setPlayingState(bool isPlaying) async {
    _isAnyPlaying = isPlaying;
    if (_isAnyPlaying && _currentPlayer != null) {
      final playerState = _currentPlayer!.playerState;
      if (playerState.processingState != ProcessingState.completed &&
          !playerState.playing) {
        await _currentPlayer!.play();
      }
    }
  }

  static bool isAnyPlaying() => _isAnyPlaying;
}

class LeadratAudioPlayWidget extends StatefulWidget {
  final String? url;
  final bool showPastDuration;

  const LeadratAudioPlayWidget({
    super.key,
    this.url,
    this.showPastDuration = false,
  });

  @override
  State<LeadratAudioPlayWidget> createState() => _LeadratAudioPlayWidgetState();
}

class _LeadratAudioPlayWidgetState extends State<LeadratAudioPlayWidget>
    with SingleTickerProviderStateMixin {
  bool isPlay = false;
  bool isSpeedMenuOpen = false;
  bool _hasError = false;
  bool _isDownloading = false;
  late AudioPlayer _player;
  late AnimationController _speedMenuController;
  late Animation<double> _speedMenuAnimation;
  Duration position = Duration.zero;
  Duration duration = Duration.zero;
  double _playbackSpeed = 1.0;
  final List<double> _speedOptions = [0.5, 0.75, 1.0, 1.5, 2.0, 3.0];
  int selectedSpeedOptionIndex = 0;
  String? _localFilePath;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _setupSpeedMenuAnimation();
    _initializeAudio();
  }

  void _setupSpeedMenuAnimation() {
    _speedMenuController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _speedMenuAnimation = CurvedAnimation(
      parent: _speedMenuController,
      curve: Curves.easeInOut,
    );
  }

  Future<String?> _downloadAndProcessAudioFile(String url) async {
    try {
      setState(() => _isDownloading = true);

      // Generate a unique filename based on URL hash
      final urlBytes = utf8.encode(url);
      final digest = sha256.convert(urlBytes);
      final fileName = digest.toString();

      // Get the original file extension
      final uri = Uri.parse(url);
      final originalPath = uri.path;
      String extension = originalPath.split('.').last.toLowerCase();

      // Convert .mp3 to .m4a as requested
      if (extension == 'mp3') {
        extension = 'm4a';
      }

      // Get cache directory
      final cacheDir = await getTemporaryDirectory();
      final localFilePath = '${cacheDir.path}/$fileName.$extension';
      final localFile = File(localFilePath);

      // Check if file already exists in cache
      if (await localFile.exists()) {
        setState(() => _isDownloading = false);
        return localFilePath;
      }

      // Download the file
      final dio = Dio();
      await dio.download(url, localFilePath);

      setState(() => _isDownloading = false);
      return localFilePath;
    } catch (e) {
      setState(() {
        _isDownloading = false;
        _hasError = true;
      });
      e.toString().logException(StackTrace.current);
      return null;
    }
  }

  void _initializeAudio() async {
    if (widget.url == null || widget.url!.isEmpty) {
      setState(() => _hasError = true);
      return;
    }

    try {
      // Download and process the audio file first
      _localFilePath = await _downloadAndProcessAudioFile(widget.url!);

      if (_localFilePath == null) {
        setState(() => _hasError = true);
        return;
      }

      // Use the local file path instead of the URL
      await _player.setFilePath(_localFilePath!);

      _player.durationStream.listen((d) {
        setState(() {
          duration = d ?? Duration.zero;
        });
      });

      _player.positionStream.listen((p) {
        setState(() {
          position = p;
        });
      });

      _player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          setState(() {
            isPlay = false;
            _player.seek(Duration.zero);
            _player.pause();
            AudioManager.setPlayingState(false);
          });
        }
      });
    } catch (error) {
      setState(() => _hasError = true);
      error.toString().logException(StackTrace.current);
    }

    // Listen for playback errors
    _player.playbackEventStream.listen(
      (_) {},
      onError: (Object e, StackTrace stackTrace) {
        setState(() => _hasError = true);
      },
    );
  }

  Future<void> togglePlayPause() async {
    if (_hasError || _isDownloading) return;
    try {
      if (_player.playing) {
        await _player.pause();
        await AudioManager.setPlayingState(false);
      } else {
        AudioManager.setCurrentPlayer(_player);
        await _player.play();
        await AudioManager.setPlayingState(true);
      }
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  void toggleSpeedMenu() {
    setState(() {
      isSpeedMenuOpen = !isSpeedMenuOpen;
      if (isSpeedMenuOpen) {
        _speedMenuController.forward();
      } else {
        _speedMenuController.reverse();
      }
    });
  }

  void setPlaybackSpeed() {
    selectedSpeedOptionIndex =
        selectedSpeedOptionIndex == _speedOptions.length - 1
            ? 0
            : selectedSpeedOptionIndex + 1;
    setState(() {
      _playbackSpeed = _speedOptions[selectedSpeedOptionIndex];
      _player.setSpeed(_speedOptions[selectedSpeedOptionIndex]);
      toggleSpeedMenu(); // Close the menu after selection
    });
  }

  void seek(Duration position) {
    if (!_hasError && !_isDownloading) {
      _player.seek(position);
    }
  }

  @override
  void dispose() {
    _player.dispose();
    _speedMenuController.dispose();
    // Note: We don't delete the cached file here to allow reuse
    // The system will clean up the cache directory when needed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6, left: 16, top: 6, right: 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: togglePlayPause,
            child: Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Color(0xFF3A3A3A),
                shape: BoxShape.circle,
              ),
              child: _isDownloading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: ColorPalette.white,
                      ),
                    )
                  : Icon(
                      _hasError
                          ? Icons.error_outline
                          : _player.playerState.playing
                              ? Icons.pause_rounded
                              : Icons.play_arrow_rounded,
                      size: 24,
                      color: _hasError ? ColorPalette.red : ColorPalette.white,
                    ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.showPastDuration)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      _hasError
                          ? 'Error loading audio'
                          : _isDownloading
                              ? 'Downloading audio...'
                              : '${formatDuration(position, duration, widget.showPastDuration)} / ${formatDuration(duration, duration, false)}',
                      style: LexendTextStyles.lexend12Medium.copyWith(
                        color:
                            _hasError ? ColorPalette.red : ColorPalette.white,
                      ),
                    ),
                  ),
                SliderTheme(
                  data: SliderThemeData(
                    thumbColor:
                        _hasError ? ColorPalette.red : ColorPalette.white,
                    activeTrackColor: _hasError
                        ? ColorPalette.red.withValues(alpha: 0.5)
                        : ColorPalette.white,
                    inactiveTrackColor:
                        (_hasError ? ColorPalette.red : ColorPalette.white)
                            .withValues(alpha: 0.2),
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 4,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 12,
                    ),
                  ),
                  child: Slider(
                    value: position.inSeconds.toDouble(),
                    min: 0.0,
                    max: duration.inSeconds.toDouble(),
                    onChanged: (_hasError || _isDownloading)
                        ? null
                        : (value) {
                            seek(Duration(seconds: value.toInt()));
                          },
                  ),
                ),
              ],
            ),
          ),
          if (!_hasError && _player.playerState.playing) ...[
            GestureDetector(
              onTap: () => setPlaybackSpeed(),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF3A3A3A),
                  borderRadius: BorderRadius.circular(13),
                  border: Border.all(
                    color: const Color(0xFF3A3A3A),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${_playbackSpeed.toStringAsFixed(_playbackSpeed.truncateToDouble() == _playbackSpeed ? 0 : 2)}x',
                  style: LexendTextStyles.lexend12Medium.copyWith(
                    color: ColorPalette.white,
                    fontSize: 11,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String formatDuration(
      Duration position, Duration total, bool canShowPastDuration) {
    Duration displayDuration =
        canShowPastDuration ? total - position : position;
    if (canShowPastDuration && displayDuration.isNegative) {
      displayDuration = Duration.zero;
    }
    final minutes = displayDuration.inMinutes;
    final seconds = displayDuration.inSeconds % 60;
    if (canShowPastDuration) {
      return '-${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
