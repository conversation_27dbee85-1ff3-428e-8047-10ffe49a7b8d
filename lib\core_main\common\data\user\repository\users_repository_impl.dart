import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_user_designation_model.dart';
import 'package:leadrat/core_main/common/data/user/models/search_response_model.dart';
import 'package:leadrat/core_main/common/data/user/models/leadrat_subscription_details_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_clockin_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/entites/dto_with_name_entity.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/user_modified_date_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/features/lead/data/models/lead_search_property_model.dart';

import '../../../../enums/app_enum/entity_type_enum.dart';
import '../../master_data/data_source/local/masterdata_local_data_source.dart';
import '../../master_data/models/modified_date_model.dart';
import '../data_source/local/user_local_data_source.dart';
import '../data_source/remote/user_remote_data_source.dart';
import '../models/get_all_users_model.dart';
import '../models/user_details_model.dart';

class UsersDataRepositoryImpl implements UsersDataRepository {
  final MasterDataLocalDataSource _masterDataLocalDataSource;
  final UsersLocalDataSource _localDataSource;
  final UserRemoteDataSource _remoteDataSource;

  UsersDataRepositoryImpl(this._localDataSource, this._remoteDataSource, this._masterDataLocalDataSource);

  @override
  Future<List<GetAllUsersModel?>?> getAllUsers({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.userModifiedDates?[UserModifiedDateEnum.allUsers] ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getAllUsers();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAllUsers(duration: duration);
        if (response != null) {
          await _localDataSource.saveAllUsers(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          final lastModifiedDateModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.updateLastUpdatedLocallyDate(userModifiedDateEnum: UserModifiedDateEnum.allUsers, lastUpdatedLocallyDate: lastModifiedModel.lastUpdatedLocallyDate);

          if (lastModifiedDateModel != null) await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedDateModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<UserDetailsModel?> getUser({bool restore = true, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.userModifiedDates?[UserModifiedDateEnum.user] ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getUser();
      if (restore || localData == null || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getUser();
        if (response != null) {
          await _localDataSource.saveUser(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          final lastModifiedDateModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.updateLastUpdatedLocallyDate(userModifiedDateEnum: UserModifiedDateEnum.user, lastUpdatedLocallyDate: lastModifiedModel.lastUpdatedLocallyDate);
          if (lastModifiedDateModel != null) await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedDateModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  List<String?> getUserPermissions() {
    try {
      final userDetail = _localDataSource.getUser();
      final userPermissions = userDetail?.rolePermission?.whereNot((element) => element?.name?.toLowerCase() == "default").expand((role) => role?.permissions ?? <String>[]).toSet().toList();
      return userPermissions ?? [];
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return [];
    }
  }

  @override
  bool checkHasPermission(AppModule appModule, CommandType commandType) {
    try {
      final userPermission = getUserPermissions();
      final hasPermission = userPermission.any((element) => element == "Permissions.${appModule.description}.${commandType.description}");
      return hasPermission;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return false;
    }
  }

  @override
  UserDetailsModel? getLoggedInUser() {
    return _localDataSource.getUser();
  }

  @override
  Future<List<GetAllUsersModel?>?> getAdminsAndReportee({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.userModifiedDates?[UserModifiedDateEnum.report] ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getAdminsAndReportee();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAdminsAndReportee(duration: duration);
        if (response != null) {
          await _localDataSource.saveAdminsAndReportee(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          final lastModifiedDateModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.updateLastUpdatedLocallyDate(userModifiedDateEnum: UserModifiedDateEnum.report, lastUpdatedLocallyDate: lastModifiedModel.lastUpdatedLocallyDate);

          if (lastModifiedDateModel != null) await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedDateModel);
          return response;
        }
      }

      return localData;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return [];
    }
  }

  @override
  Future<List<GetAllUsersModel?>?> getAllReportees({bool restore = true, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.userModifiedDates?[UserModifiedDateEnum.allReport] ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getAllReportees();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAllReportees(duration: duration);
        if (response != null) {
          await _localDataSource.saveAllReportees(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          final lastModifiedDateModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.updateLastUpdatedLocallyDate(userModifiedDateEnum: UserModifiedDateEnum.allReport, lastUpdatedLocallyDate: lastModifiedModel.lastUpdatedLocallyDate);

          if (lastModifiedDateModel != null) await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedDateModel);
          return response;
        }
      }

      return localData;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return [];
    }
  }

  @override
  Future<List<GetAllUsersModel?>?> getAssignUser() async {
    if (checkHasPermission(AppModule.users, CommandType.assignToAny)) {
      return await getAllUsers();
    } else {
      return await getAdminsAndReportee();
    }
  }

  @override
  Future<Map<String, List<GetUserDesignationModel>>?> getAllUserDesignations({bool restore = false}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.userModifiedDates?[UserModifiedDateEnum.designations] ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.userDetails,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getUserDesignations();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getAllUserWithDesignations();
        if (response != null) {
          await _localDataSource.saveUserDesignations(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          final lastModifiedDateModel = lastModifiedDateList
              .firstWhereOrNull(
                (element) => element.entityType == EntityTypeEnum.userDetails,
              )
              ?.updateLastUpdatedLocallyDate(userModifiedDateEnum: UserModifiedDateEnum.designations, lastUpdatedLocallyDate: lastModifiedModel.lastUpdatedLocallyDate);

          if (lastModifiedDateModel != null) await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedDateModel);
          return response;
        }
      }

      return localData;
    } catch (e) {
      e.logException();
      return null;
    }
  }

  @override
  Future<List<DtoWithNameEntity?>?> getAllDesignations() async {
    try {
      final response = await _remoteDataSource.getAllDesignations();
      return response?.map((e) => e?.toEntity()).toList();
    } catch (exception) {
      exception.logException();
      return [];
    }
  }

  @override
  Future<UserDetailsClockInModel?>? getGeoFenceDetails() async {
    try {
      final response = await _remoteDataSource.getGeoFenceDetails();
      return response;
    } catch (exception) {
      exception.logException();
      return UserDetailsClockInModel();
    }
  }

  @override
  Future<List<SearchFilterModel>?> getAllSearchResult(int moduleType) async {
    return await _remoteDataSource.getAllSearchResult(moduleType);
  }

  @override
  Future<bool?> updateSearch(SearchResponse searchResponseModel) async {
    return await _remoteDataSource.updateSearch(searchResponseModel);
  }

  @override
  Future<List<ItemSimpleModel<GetAllUsersModel>>?> getAllSortedUsers() async {
    List<ItemSimpleModel<GetAllUsersModel>> users = [];
    final allUsers = await getAllUsers();
    if (allUsers?.isNotEmpty ?? false) {
      final currentUser = getLoggedInUser();
      users = allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => ItemSimpleModel<GetAllUsersModel>(title: user?.fullName ?? '', value: user, isEnabled: true)).toList();
      allUsers.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => users.add(ItemSimpleModel<GetAllUsersModel>(title: disabledUsers?.fullName ?? '', value: disabledUsers, isEnabled: false)));
      users.insert(
          0,
          ItemSimpleModel<GetAllUsersModel>(
              title: "You",
              value: GetAllUsersModel(
                id: currentUser?.userId,
                firstName: currentUser?.firstName,
                lastName: currentUser?.lastName,
                userName: currentUser?.userName,
                imageUrl: currentUser?.imageUrl,
                isActive: currentUser?.isActive,
                isMFAEnabled: false,
                licenseNo: currentUser?.licenseNo,
                isGeoFenceActive: currentUser?.isGeoFenceActive,
              ),
              isEnabled: true));
    }
    return users;
  }

  @override
  Future<LeadratSubscriptionDetailsModel?>? getSubscriptionDetails(String? userId) {
    try {
      return _remoteDataSource.getSubscriptionDetails(userId);
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
    }
  }
}
