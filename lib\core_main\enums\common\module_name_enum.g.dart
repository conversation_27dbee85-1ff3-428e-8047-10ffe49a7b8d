// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'module_name_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ModuleNameAdapter extends TypeAdapter<ModuleName> {
  @override
  final int typeId = 17;

  @override
  ModuleName read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ModuleName.lead;
      case 1:
        return ModuleName.todo;
      case 2:
        return ModuleName.integration;
      case 3:
        return ModuleName.user;
      case 4:
        return ModuleName.profile;
      case 5:
        return ModuleName.project;
      case 6:
        return ModuleName.property;
      case 7:
        return ModuleName.team;
      case 8:
        return ModuleName.email;
      case 9:
        return ModuleName.invoice;
      case 10:
        return ModuleName.unit;
      default:
        return ModuleName.lead;
    }
  }

  @override
  void write(BinaryWriter writer, ModuleName obj) {
    switch (obj) {
      case ModuleName.lead:
        writer.writeByte(0);
        break;
      case ModuleName.todo:
        writer.writeByte(1);
        break;
      case ModuleName.integration:
        writer.writeByte(2);
        break;
      case ModuleName.user:
        writer.writeByte(3);
        break;
      case ModuleName.profile:
        writer.writeByte(4);
        break;
      case ModuleName.project:
        writer.writeByte(5);
        break;
      case ModuleName.property:
        writer.writeByte(6);
        break;
      case ModuleName.team:
        writer.writeByte(7);
        break;
      case ModuleName.email:
        writer.writeByte(8);
        break;
      case ModuleName.invoice:
        writer.writeByte(9);
        break;
      case ModuleName.unit:
        writer.writeByte(10);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModuleNameAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
