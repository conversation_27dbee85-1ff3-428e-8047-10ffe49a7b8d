import 'package:hive/hive.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/enums/app_enum/user_modified_date_enum.dart';

part 'modified_date_model.g.dart';

@HiveType(typeId: HiveModelConstants.modifiedDatesModelTypeId)
class ModifiedDateModel {
  @HiveField(0)
  final EntityTypeEnum? entityType;
  @HiveField(1)
  final DateTime? lastModifiedDate;
  @HiveField(2)
  final DateTime? lastUpdatedLocallyDate;
  @HiveField(3)
  final Map<UserModifiedDateEnum, ModifiedDateModel>? userModifiedDates;

  ModifiedDateModel({
    this.entityType,
    this.lastModifiedDate,
    this.lastUpdatedLocallyDate,
    this.userModifiedDates,
  });

  ModifiedDateModel copyWith({
    EntityTypeEnum? entityType,
    DateTime? lastModifiedDate,
    DateTime? lastUpdatedLocallyDate,
    Map<UserModifiedDateEnum, ModifiedDateModel>? userModifiedDates,
  }) {
    return ModifiedDateModel(
      entityType: entityType ?? this.entityType,
      lastModifiedDate: lastModifiedDate ?? this.lastModifiedDate,
      lastUpdatedLocallyDate: lastUpdatedLocallyDate ?? this.lastUpdatedLocallyDate,
      userModifiedDates: userModifiedDates ?? this.userModifiedDates,
    );
  }

  ModifiedDateModel updateLastUpdatedLocallyDate({required UserModifiedDateEnum userModifiedDateEnum, DateTime? lastUpdatedLocallyDate}) {
    Map<UserModifiedDateEnum, ModifiedDateModel>? userModifiedDates = {};
    if (entityType == EntityTypeEnum.userDetails) {
      for (var userModifiedDateEnum in UserModifiedDateEnum.values) {
        userModifiedDates[userModifiedDateEnum] = ModifiedDateModel(
          entityType: entityType,
          lastModifiedDate: lastModifiedDate,
          lastUpdatedLocallyDate: userModifiedDateEnum == userModifiedDateEnum ? lastUpdatedLocallyDate : this.userModifiedDates?[userModifiedDateEnum]?.lastUpdatedLocallyDate,
        );
      }
    }
    return ModifiedDateModel(entityType: entityType, userModifiedDates: userModifiedDates, lastUpdatedLocallyDate: this.lastUpdatedLocallyDate, lastModifiedDate: lastModifiedDate);
  }
}
