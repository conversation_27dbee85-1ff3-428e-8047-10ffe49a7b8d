import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/local/flag_local_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/remote/flag_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/models/flag_model.dart';
import 'package:leadrat/core_main/common/data/flags/repository/flag_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

class FlagRepositoryImpl implements FlagRepository{
  final MasterDataLocalDataSource _masterDataLocalDataSource;
  final FlagLocalDataSource _localDataSource;
  final FlagRemoteDataSource _remoteDataSource;

  FlagRepositoryImpl(this._masterDataLocalDataSource, this._localDataSource, this._remoteDataSource);
  @override
  Future<List<ViewFlagModel?>?> getFlagsByModule(String module,{bool restore = false}) async {
    try {
      final lastModifiedDateList = _masterDataLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.flag,
      ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.flag,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _localDataSource.getFlags();

      if (restore || localData == null || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _remoteDataSource.getFlagsByModule(module);
        if (response != null) {
          await _localDataSource.saveFlags(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _masterDataLocalDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e,stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

}