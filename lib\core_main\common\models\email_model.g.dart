// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'email_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmailModel _$EmailModelFromJson(Map<String, dynamic> json) => EmailModel(
      sender: json['sender'] as String?,
      subject: json['subject'] as String?,
      contentBody: json['contentBody'] as String?,
      toRecipients: json['toRecipients'] as String?,
      ccRecipients: json['ccRecipients'] as String?,
      bccRecipients: json['bccRecipients'] as String?,
    );

Map<String, dynamic> _$EmailModelToJson(EmailModel instance) =>
    <String, dynamic>{
      if (instance.sender case final value?) 'sender': value,
      if (instance.subject case final value?) 'subject': value,
      if (instance.contentBody case final value?) 'contentBody': value,
      if (instance.toRecipients case final value?) 'toRecipients': value,
      if (instance.ccRecipients case final value?) 'ccRecipients': value,
      if (instance.bccRecipients case final value?) 'bccRecipients': value,
    };
