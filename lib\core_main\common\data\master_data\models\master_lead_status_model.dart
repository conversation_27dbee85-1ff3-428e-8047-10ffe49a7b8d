import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/features/lead/domain/entities/lead_status_entity.dart';

part 'master_lead_status_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterLeadStatusModelTypeId)
@JsonSerializable()
class MasterLeadStatusModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? baseId;
  @HiveField(2)
  final int? level;
  @HiveField(3)
  final String? status;
  @HiveField(4)
  final String? displayName;
  @HiveField(5)
  final String? actionName;
  @HiveField(6)
  final String? masterLeadStatusId;
  @HiveField(7)
  final List<MasterLeadStatusModel>? childTypes;
  @HiveField(8)
  final DateTime? lastModifiedOn;
  @HiveField(9)
  final bool? isDefault;
  @HiveField(10)
  final bool? isDefaultChild;
  @HiveField(11)
  final bool? isLrbStatus;

  MasterLeadStatusModel({
    this.id,
    this.baseId,
    this.level,
    this.status,
    this.displayName,
    this.actionName,
    this.masterLeadStatusId,
    this.childTypes,
    this.lastModifiedOn,
    this.isDefault,
    this.isDefaultChild,
    this.isLrbStatus,
  });

  factory MasterLeadStatusModel.fromJson(Map<String, dynamic> json) => _$MasterLeadStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterLeadStatusModelToJson(this);

  LeadStatusEntity toEntity() {
    return LeadStatusEntity(
      id: id,
      level: level,
      status: status,
      displayName: displayName,
      actionName: actionName,
      childType: childTypes?.firstOrNull?.toEntity(),
      childTypes: childTypes?.map((e) => e.toEntity()).toList(),
      baseId: baseId,
    );
  }
  MasterLeadStatusModel copyWith({
    String? id,
    String? baseId,
    int? level,
    String? status,
    String? displayName,
    String? actionName,
    String? masterLeadStatusId,
    List<MasterLeadStatusModel>? childTypes,
    DateTime? lastModifiedOn,
    bool? isDefault,
    bool? isDefaultChild,
    bool? isLrbStatus,
  }) {
    return MasterLeadStatusModel(
      id: id ?? this.id,
      baseId: baseId ?? this.baseId,
      level: level ?? this.level,
      status: status ?? this.status,
      displayName: displayName ?? this.displayName,
      actionName: actionName ?? this.actionName,
      masterLeadStatusId: masterLeadStatusId ?? this.masterLeadStatusId,
      childTypes: childTypes ?? this.childTypes,
      lastModifiedOn: lastModifiedOn ?? this.lastModifiedOn,
      isDefault: isDefault ?? this.isDefault,
      isDefaultChild: isDefaultChild ?? this.isDefaultChild,
      isLrbStatus: isLrbStatus ?? this.isLrbStatus,
    );
  }
}

