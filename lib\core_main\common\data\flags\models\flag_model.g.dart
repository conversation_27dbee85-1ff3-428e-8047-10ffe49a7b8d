// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flag_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ViewFlagModelAdapter extends TypeAdapter<ViewFlagModel> {
  @override
  final int typeId = 42;

  @override
  ViewFlagModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ViewFlagModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      activeImagePath: fields[2] as String?,
      inactiveImagePath: fields[3] as String?,
      activeBackgroundColor: fields[4] as String?,
      inactiveBackgroundColor: fields[5] as String?,
      module: fields[6] as String?,
      notes: fields[7] as String?,
      isActive: fields[8] as bool?,
      lastModifiedOn: fields[9] as DateTime?,
      createdOn: fields[10] as DateTime?,
      createdBy: fields[11] as String?,
      lastModifiedBy: fields[12] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ViewFlagModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.activeImagePath)
      ..writeByte(3)
      ..write(obj.inactiveImagePath)
      ..writeByte(4)
      ..write(obj.activeBackgroundColor)
      ..writeByte(5)
      ..write(obj.inactiveBackgroundColor)
      ..writeByte(6)
      ..write(obj.module)
      ..writeByte(7)
      ..write(obj.notes)
      ..writeByte(8)
      ..write(obj.isActive)
      ..writeByte(9)
      ..write(obj.lastModifiedOn)
      ..writeByte(10)
      ..write(obj.createdOn)
      ..writeByte(11)
      ..write(obj.createdBy)
      ..writeByte(12)
      ..write(obj.lastModifiedBy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ViewFlagModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ViewFlagModel _$ViewFlagModelFromJson(Map<String, dynamic> json) =>
    ViewFlagModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      activeImagePath: json['activeImagePath'] as String?,
      inactiveImagePath: json['inactiveImagePath'] as String?,
      activeBackgroundColor: json['activeBackgroundColor'] as String?,
      inactiveBackgroundColor: json['inactiveBackgroundColor'] as String?,
      module: json['module'] as String?,
      notes: json['notes'] as String?,
      isActive: json['isActive'] as bool?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
    );

Map<String, dynamic> _$ViewFlagModelToJson(ViewFlagModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'activeImagePath': instance.activeImagePath,
      'inactiveImagePath': instance.inactiveImagePath,
      'activeBackgroundColor': instance.activeBackgroundColor,
      'inactiveBackgroundColor': instance.inactiveBackgroundColor,
      'module': instance.module,
      'notes': instance.notes,
      'isActive': instance.isActive,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedBy': instance.lastModifiedBy,
    };
