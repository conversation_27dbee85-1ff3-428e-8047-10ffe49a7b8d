{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edf99548a40be7dc53d6558886a458f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989faba2dd4a70405dc549a3f69166e8fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd8287831a3d8ca30c4493fce5521b7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0c99912ea9dc6b2736cf53f826b9ccf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd8287831a3d8ca30c4493fce5521b7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa920e808268d4339a9b5b89c77713c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986b4cc3812e360aa8b14169ce18fe632d", "guid": "bfdfe7dc352907fc980b868725387e983d72323f69f853e151b918a10774ab77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1049abf8eda4d108bb502332c2bc158", "guid": "bfdfe7dc352907fc980b868725387e98e5921249f2dec19bf8a45a150ccba5d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c450bfb8d3e54ea1cdc430cca528419", "guid": "bfdfe7dc352907fc980b868725387e983e2dca2320477cc9e2f7abb90a7a05d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f7276ffa964b66b0e8a3ce45e6a7b", "guid": "bfdfe7dc352907fc980b868725387e98e4b746352447b63056b6a4cdf4919528", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bf8421f55567fdf34e38a881d36bbc", "guid": "bfdfe7dc352907fc980b868725387e980574b09fa65d4402c2f554dd8b258cc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e12596f4920b7c4b702575bd3723fe", "guid": "bfdfe7dc352907fc980b868725387e981af28cb8c318f0c52f588ba2d674c699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e47cfbed1c5a52b0f831810396d704", "guid": "bfdfe7dc352907fc980b868725387e988b386b30b89622beb27e7606cbdb3e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e6f3bbbc9d6f2c3919f1ea0f16b8f4a", "guid": "bfdfe7dc352907fc980b868725387e98d99c1e7db5ca795ba22f038b9f347e76", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b636020d6f2716519963ab7b3238e603", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981888f56e92ef9e966331d9b2f8a5bab5", "guid": "bfdfe7dc352907fc980b868725387e9805350e5955d785c791f08c267bb3c6f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fdd8f44686eefe399c8806bfce47e54", "guid": "bfdfe7dc352907fc980b868725387e9807b96ecc82f8727304cfbd15a0d9bed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d0230a4f6128b86bac8de69145d330", "guid": "bfdfe7dc352907fc980b868725387e98f176e3d9a0ab3c243e4a6d2ceac1d9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c402a0f26aa213c8b35570283410ac28", "guid": "bfdfe7dc352907fc980b868725387e986db7de6c8f3238411b4baf7d4ccf4aa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c381dc7df1a99b193f6cdfebb6d7c196", "guid": "bfdfe7dc352907fc980b868725387e9896fcd8801a70db95687b4f5a61973f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98991d982c2b923d514ae35636ea114d3c", "guid": "bfdfe7dc352907fc980b868725387e9805f8261c203570ad4e0e84fae6387933"}], "guid": "bfdfe7dc352907fc980b868725387e988693da0e7f3a03ff570f03851f21ae11", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98257e8d35398f9f54fe462a23bdd75ce3"}], "guid": "bfdfe7dc352907fc980b868725387e9835248f20df0a319df78eec18576a59e1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c3bbbfef215aac3290faf180641526de", "targetReference": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b"}], "guid": "bfdfe7dc352907fc980b868725387e98ca26a53555f40ca456d09c50b4114fec", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b", "name": "network_info_plus-network_info_plus_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf57ad863a4d83e4bcb3860e61a5af3", "name": "network_info_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ebbb202873b59db98b5eabd407a20857", "name": "network_info_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}