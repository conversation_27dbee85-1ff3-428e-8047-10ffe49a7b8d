import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_amenitie_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_attributes_model.dart';
import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';

class PropertyEntityMapper {
  final MasterDataLocalDataSource _masterDataRepository;
  final UsersLocalDataSource _usersLocalDataSource;
  final CustomAmenitiesAndAttributesLocalDataSource _customAmenitiesAndAttributesLocalDataSource;

  PropertyEntityMapper(this._masterDataRepository, this._usersLocalDataSource, this._customAmenitiesAndAttributesLocalDataSource);

  CustomAttributesModel? getCustomAttributeIdByName(String? attributeName, String? value) {
    if (attributeName.isNullOrEmpty()) return null;
    final localPropertyCustomAttributes = _customAmenitiesAndAttributesLocalDataSource.getCustomAttributes();
    for (CustomAttributesModel? attribute in localPropertyCustomAttributes ?? []) {
      if (attribute?.attributeName == attributeName) {
        return attribute?.copyWith(value ?? ((attribute.attributeType == "Additional") ? 'true' : null));
      }
    }
    return null;
  }

  CustomAmenityModel? getCustomAmenity(String? amenityId) {
    if (amenityId.isNullOrEmpty()) return null;

    final localCustomAmenities = _customAmenitiesAndAttributesLocalDataSource.getCustomAmenities();
    CustomAmenityModel? customAmenityModel;
    for (CustomAmenitiesModel? value in localCustomAmenities ?? []) {
      for (CustomAmenityModel amenity in value?.amenities ?? []) {
        if (amenity.id == amenityId) customAmenityModel = amenity;
      }
    }
    return customAmenityModel;
  }

  List<CustomAmenityModel>? getAllCustomAmenities() {
    final localCustomAmenities = _customAmenitiesAndAttributesLocalDataSource.getCustomAmenities();
    List<CustomAmenityModel> customAmenitiesModels = [];
    for (CustomAmenitiesModel? value in localCustomAmenities ?? []) {
      for (CustomAmenityModel amenity in value?.amenities ?? []) {
        customAmenitiesModels.add(amenity);
      }
    }
    return customAmenitiesModels;
  }

  List<String?>? getAllCustomAmenitiesWithCategories() {
    final localCustomAmenities = _customAmenitiesAndAttributesLocalDataSource.getCustomAmenityCategories();

    return localCustomAmenities;
  }

  MasterPropertyAttributesModel? getAttributeIdByName(String? attributeName) {
    if (attributeName.isNullOrEmpty()) return null;

    final localPropertyAttributes = _masterDataRepository.getPropertyAttributes();
    for (MasterPropertyAttributesModel attribute in localPropertyAttributes ?? []) {
      if (attribute.attributeName == attributeName) return attribute;
    }
    return null;
  }

  MasterPropertyAmenitiesModel? getAmenity(String? amenityId) {
    if (amenityId.isNullOrEmpty()) return null;
    final isPropertyListingEnabled = getIt<LeadratHomeBloc>().isPropertyListingEnabled;

    final localPropertyAmenity = isPropertyListingEnabled ? _masterDataRepository.getPropertyListingAmenitites() : _masterDataRepository.getPropertyAmenitites();
    MasterPropertyAmenitiesModel? masterPropertyAmenities;
    localPropertyAmenity?.forEach(
      (key, value) {
        for (MasterPropertyAmenitiesModel amenity in value) {
          if (amenity.id == amenityId) masterPropertyAmenities = amenity;
        }
      },
    );
    return masterPropertyAmenities;
  }

  List<MasterPropertyAmenitiesModel>? getAllAmenities() {
    final isPropertyListingEnabled = getIt<LeadratHomeBloc>().isPropertyListingEnabled;

    final localPropertyAmenity = isPropertyListingEnabled ? _masterDataRepository.getPropertyListingAmenitites() : _masterDataRepository.getPropertyAmenitites();
    List<MasterPropertyAmenitiesModel> masterPropertyAmenitiesModels = [];
    localPropertyAmenity?.forEach((key, value) => masterPropertyAmenitiesModels.addAll(value.map(
          (e) => e,
        )));
    return masterPropertyAmenitiesModels;
  }

  List<GetAllUsersModel?>? getAllUsers() {
    return _usersLocalDataSource.getAllUsers();
  }
}
