// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_tenant_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GetTenantModelAdapter extends TypeAdapter<GetTenantModel> {
  @override
  final int typeId = 37;

  @override
  GetTenantModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GetTenantModel(
      id: fields[0] as String?,
      name: fields[1] as String?,
      adminEmail: fields[2] as String?,
      validUpto: fields[3] as DateTime?,
      issuer: fields[4] as String?,
      isWhiteLabeled: fields[5] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, GetTenantModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.adminEmail)
      ..writeByte(3)
      ..write(obj.validUpto)
      ..writeByte(4)
      ..write(obj.issuer)
      ..writeByte(5)
      ..write(obj.isWhiteLabeled);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetTenantModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetTenantModel _$GetTenantModelFromJson(Map<String, dynamic> json) =>
    GetTenantModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      adminEmail: json['adminEmail'] as String?,
      validUpto: json['validUpto'] == null
          ? null
          : DateTime.parse(json['validUpto'] as String),
      issuer: json['issuer'] as String?,
      isWhiteLabeled: json['isWhiteLabeled'] as bool?,
    );

Map<String, dynamic> _$GetTenantModelToJson(GetTenantModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'adminEmail': instance.adminEmail,
      'validUpto': instance.validUpto?.toIso8601String(),
      'issuer': instance.issuer,
      'isWhiteLabeled': instance.isWhiteLabeled,
    };
