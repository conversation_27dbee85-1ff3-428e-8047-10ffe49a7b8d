{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848f272e75d3ed6e968358e34bfeff788", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98811415115d3822f03596a5aaad39f066", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98811415115d3822f03596a5aaad39f066", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a79374d4e7cd39a1458a967e4fe91d3c", "guid": "bfdfe7dc352907fc980b868725387e985a63d93e93828a5ffb1b1c0bfc20e3b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df4318d01a68119fb02345277b59c30c", "guid": "bfdfe7dc352907fc980b868725387e983578620150ec4cfaced272678fe44d7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862eef6e3fc0b535fe4ae78a2579ae6bf", "guid": "bfdfe7dc352907fc980b868725387e98c18de3ab5182d84c9c0175c3aba0fd45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8fb7a90550f5df904a6d96cb5d23bee", "guid": "bfdfe7dc352907fc980b868725387e984a297d3f9b295183e3167ce8f3e637d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988144c0a20ef9c0922396f8c1bb8a4d01", "guid": "bfdfe7dc352907fc980b868725387e9809e32571848693982a1e49d490acd168", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a968c7747bfa37717c4a5366d117c0", "guid": "bfdfe7dc352907fc980b868725387e983177711f1a68140586480fe8ab8e1148", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853712c6219a1817aebf6d7562ef5c103", "guid": "bfdfe7dc352907fc980b868725387e982b94efcba5bcf3d6f079d46b014c2ba9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98218f8da2bffbe5215e97af1e107ad3ad", "guid": "bfdfe7dc352907fc980b868725387e983267a210d5ed3f2377367aac18fb611d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ef45e04bd8662291b2256587bbf6fb", "guid": "bfdfe7dc352907fc980b868725387e985fb5e30d85c549d0f46d6de6bd67c457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985046c33a40084312accc9eee36a3a574", "guid": "bfdfe7dc352907fc980b868725387e986f14500ccefea82da288481b5dddcdb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989937364b6ff37b21281b2f1fbb1bf29e", "guid": "bfdfe7dc352907fc980b868725387e98bf0abbb8ec3561691b61d3b58ceda1cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848df59e6e05fb9178ba9cb9d4ed241f9", "guid": "bfdfe7dc352907fc980b868725387e985b0d38c3b75a6fa0957c0dfece06f84e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449870da352118b44eec5237e8609102", "guid": "bfdfe7dc352907fc980b868725387e98eaad785e8502dba1ac89989df49e5651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986103c49d900a90e0846a33d6dc1d16ad", "guid": "bfdfe7dc352907fc980b868725387e980ae95d60b4f1ad46f709cd42b28b9c54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ed2e171a07ec5c6de18d30e69ed4cf", "guid": "bfdfe7dc352907fc980b868725387e98001edf29f30f3a0d6968f1b9e63b7c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e24ea64953229de00541a0ab8ca6f1", "guid": "bfdfe7dc352907fc980b868725387e98cdb7002555716bf47bd7586b4ac8236d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c67f6783d70207edb4a72796a76de758", "guid": "bfdfe7dc352907fc980b868725387e98afe46c3822400c8579fd6865c4ec06a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98303af597e286aaa138dc38090e84ea1d", "guid": "bfdfe7dc352907fc980b868725387e982e652fc935434bcee769772a3371419c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821170b21a53909820346a7a3cd8f19af", "guid": "bfdfe7dc352907fc980b868725387e988747aaddd72cf806c7f759385ed906c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885330cb5e50f7b91e5f929937420aa17", "guid": "bfdfe7dc352907fc980b868725387e9863e009fb137902e81297ce1c64bc2c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982beea4548229c1abfe3bec8ab913330b", "guid": "bfdfe7dc352907fc980b868725387e981738f2fe66b2f1157c6d5bcd6723dcff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c417a66f9a81d0d538a892ea3fc1c9d7", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b7928c52ef5263912793e744f23fa8", "guid": "bfdfe7dc352907fc980b868725387e982c00cb8e7484561ce8a52e3103d40d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841ae77ed2beff120eac7cb603c9e2081", "guid": "bfdfe7dc352907fc980b868725387e984a6c13a334bae14cd96094284d90a4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995eb899d4897f3961fa32ebde032cb6", "guid": "bfdfe7dc352907fc980b868725387e9892d9842ee650c2ef18ab68a744a8c35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c765cd397a058ef743d74fdd971277c", "guid": "bfdfe7dc352907fc980b868725387e982e1ea7128555c3484de12aa0d21baa58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e48a43693057beb3cf2e580f147201e", "guid": "bfdfe7dc352907fc980b868725387e986c53e4a9cb33b77a82c428c58f00d6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d1260e6291e8cd8746e521b562c3ff", "guid": "bfdfe7dc352907fc980b868725387e98f12b35618e3c290a4a26cbd67c9c0700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866eb8eb4806c5c8aa11a2ddf307af574", "guid": "bfdfe7dc352907fc980b868725387e98939bb9403a0fe28be3dcaf7b38e459ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580116fbe14c02d0e167eddf4504dab8", "guid": "bfdfe7dc352907fc980b868725387e9837a212af2ef8d8299b0af7555e8de3dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf30efe6e24ed9621c9da3689bdfd0b", "guid": "bfdfe7dc352907fc980b868725387e98ba539d3d57137bdb21a3b8d3ebb5b664"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}