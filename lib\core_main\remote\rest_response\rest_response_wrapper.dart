class ResponseWrapper<T> {
  final bool succeeded;
  final String? message;
  final List<String>? errors;
  final T? data;

  ResponseWrapper({
    required this.succeeded,
    this.message,
    this.errors,
    this.data,
  });

  factory ResponseWrapper.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic json) fromJsonT,
  ) {
    final dynamic data = json['data'];
    final T? parsedData;

    if (data is String || data is bool || data is num || data == null) {
      parsedData = data as T?;
    } else {
      parsedData = data != null ? fromJsonT(data) : null;
    }

    return ResponseWrapper(
      succeeded: json['succeeded'] ?? false,
      message: json['message'],
      errors: json['errors'] != null ? List<String>.from(json['errors']) : null,
      data: parsedData,
    );
  }
}

T fromJsonObject<T>(dynamic json, T Function(Map<String, dynamic>) fromJsonT) {
  if (json is Map<String, dynamic>) {
    return fromJsonT(json);
  } else if (json is T) {
    return json;
  } else {
    throw ArgumentError('Expected a Map<String, dynamic> or $T but got ${json.runtimeType}');
  }
}

List<T> fromJsonList<T>(dynamic json, T Function(Map<String, dynamic>) fromJsonT) {
  return (json as List<dynamic>).map((item) => fromJsonObject(item, fromJsonT)).toList();
}

Map<TEnum, T>? mapResponseToEnum<T, TEnum>(
  Map<String, dynamic>? data,
  TEnum Function(String) fromString,
  T Function(dynamic) fromJson,
) {
  if (data == null) return null;

  return data.map<TEnum, T>(
    (key, value) {
      final enumValue = fromString(key);
      final parsedValue = fromJson(value);
      return MapEntry(enumValue, parsedValue);
    },
  );
}

Map<TEnum, T>? mapStringToEnum<T, TEnum>(
  Map<String, dynamic>? data,
  TEnum Function(String) fromString,
  T Function(dynamic) fromJson,
) {
  if (data == null) return null;

  return data.map<TEnum, T>(
    (key, value) {
      final enumValue = fromString(key);
      final parsedValue = fromJson(value);
      return MapEntry(enumValue, parsedValue);
    },
  );
}
