import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/local/global_settings_local_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/remote/global_setting_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/country_info_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

class GlobalSettingRepositoryImpl implements GlobalSettingRepository {
  final GlobalSettingRemoteDataSource _globalSettingRemoteDataSource;
  final GlobalSettingsLocalDataSource _globalSettingsLocalDataSource;
  final MasterDataLocalDataSource _localDataSource;

  GlobalSettingRepositoryImpl(
    this._globalSettingsLocalDataSource,
    this._globalSettingRemoteDataSource,
    this._localDataSource,
  );

  @override
  Future<GlobalSettingModel?> getGlobalSettings({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.globalSettings,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.globalSettings,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      GlobalSettingModel? localData = _globalSettingsLocalDataSource.getGlobalSettings();

      if (restore || localData == null || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _globalSettingRemoteDataSource.getGlobalSettingModel(timeout: duration);
        if (response != null) {
          await _globalSettingsLocalDataSource.saveGlobalSettings(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return response;
        }
      }

      return localData;
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }

  @override
  Future<BaseCountryInfoModel?> getCountriesInfo({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      return await _globalSettingRemoteDataSource.getCountriesInfo(timeout: duration);
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return null;
    }
  }
}
