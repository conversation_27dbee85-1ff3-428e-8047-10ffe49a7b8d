import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/enums/common/brokerage_unit.dart';

part 'project_model.g.dart';

@JsonSerializable()
class ProjectMonetaryInfoModel {
  final String? id;
  final double? brokerage;
  final BrokerageUnit? brokerageUnit;
  final String? brokerageCurrency;

  ProjectMonetaryInfoModel({
    this.id,
    this.brokerage,
    this.brokerageUnit,
    this.brokerageCurrency,
  });

  factory ProjectMonetaryInfoModel.fromJson(Map<String, dynamic> json) => _$ProjectMonetaryInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectMonetaryInfoModelToJson(this);
}

@JsonSerializable()
final class ProjectBuilderDetailsModel {
  final String? name;
  final String? contactNo;
  final String? pointOfContact;

  ProjectBuilderDetailsModel({this.name, this.contactNo, this.pointOfContact});

  factory ProjectBuilderDetailsModel.fromJson(Map<String, dynamic> json) => _$ProjectBuilderDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectBuilderDetailsModelToJson(this);
}

@JsonSerializable()
class ProjectBuilderInfoModel {
  final String? id;
  final String? name;

  ProjectBuilderInfoModel({this.id, this.name});

  factory ProjectBuilderInfoModel.fromJson(Map<String, dynamic> json) => _$ProjectBuilderInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectBuilderInfoModelToJson(this);
}
