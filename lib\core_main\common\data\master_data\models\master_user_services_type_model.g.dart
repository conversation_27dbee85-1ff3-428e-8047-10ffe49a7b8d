// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_user_services_type_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MasterUserServicesModelAdapter
    extends TypeAdapter<MasterUserServicesModel> {
  @override
  final int typeId = 38;

  @override
  MasterUserServicesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MasterUserServicesModel(
      displayName: fields[0] as String?,
      description: fields[1] as String?,
      imageUrl: fields[2] as String?,
      orderRank: fields[3] as int?,
      createdBy: fields[4] as String?,
      createdOn: fields[5] as DateTime?,
      lastModifiedBy: fields[6] as String?,
      lastModifiedOn: fields[7] as DateTime?,
      deletedOn: fields[8] as DateTime?,
      deletedBy: fields[9] as String?,
      id: fields[10] as String?,
      isDeleted: fields[11] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, MasterUserServicesModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.displayName)
      ..writeByte(1)
      ..write(obj.description)
      ..writeByte(2)
      ..write(obj.imageUrl)
      ..writeByte(3)
      ..write(obj.orderRank)
      ..writeByte(4)
      ..write(obj.createdBy)
      ..writeByte(5)
      ..write(obj.createdOn)
      ..writeByte(6)
      ..write(obj.lastModifiedBy)
      ..writeByte(7)
      ..write(obj.lastModifiedOn)
      ..writeByte(8)
      ..write(obj.deletedOn)
      ..writeByte(9)
      ..write(obj.deletedBy)
      ..writeByte(10)
      ..write(obj.id)
      ..writeByte(11)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MasterUserServicesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterUserServicesModel _$MasterUserServicesModelFromJson(
        Map<String, dynamic> json) =>
    MasterUserServicesModel(
      displayName: json['displayName'] as String?,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String?,
      orderRank: (json['orderRank'] as num?)?.toInt(),
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
    );

Map<String, dynamic> _$MasterUserServicesModelToJson(
        MasterUserServicesModel instance) =>
    <String, dynamic>{
      'displayName': instance.displayName,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'orderRank': instance.orderRank,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'id': instance.id,
      'isDeleted': instance.isDeleted,
    };
