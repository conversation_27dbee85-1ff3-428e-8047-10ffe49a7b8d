{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f3f6839bf4de28e1693e1cb1daf7a26", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888873aaa797b40862d9f920ed65567ff", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888873aaa797b40862d9f920ed65567ff", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a672275c1506fcc14ae8dbb6ae55cc8", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982aebcc06f9ae2d137ef71bf7cd0c7111", "guid": "bfdfe7dc352907fc980b868725387e98969408efbabd6c244fb31026a70b0e67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987704f9c32062b333b1566e292c3fa139", "guid": "bfdfe7dc352907fc980b868725387e98ccc662fd71b0284b67383206314978f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985777c73d14ab54c3dc3d989ead69b464", "guid": "bfdfe7dc352907fc980b868725387e98a1d9f2375a69945874d713b2e9d10914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819a1c1bc5a4eb2e6206677f4b1b87de2", "guid": "bfdfe7dc352907fc980b868725387e9831c4597a13c2a83e1eb4b6365e79c8a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec53b35486ab351bbd21407dca0a8311", "guid": "bfdfe7dc352907fc980b868725387e98a38a8909fbb968ee8962d0eb3a64c90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6219de588c97831512ec3c7ad2f4324", "guid": "bfdfe7dc352907fc980b868725387e98c0986a0665428367160e1e55ec465718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987035bf5c5537de0468486d704282425d", "guid": "bfdfe7dc352907fc980b868725387e98897de58191c27aa4fe939008a06c809d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc28ac72d9a8a9d2ee91918c8172b8a", "guid": "bfdfe7dc352907fc980b868725387e9854c5968b776e8718b35f17e0dff8eb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898a9e4fdd48dfb1f8b1fec7d21086f7e", "guid": "bfdfe7dc352907fc980b868725387e98ab96995fc8e4a45a40f3c884fe968273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343e5d214fec63a3a44e22ccf257a7cb", "guid": "bfdfe7dc352907fc980b868725387e9899af2dda6527bf8fb092669ac6408cf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579a760036026f99686e9af7eb89825f", "guid": "bfdfe7dc352907fc980b868725387e9876349763c5b1cf0979ef72fba08af750"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980820312bbc56ae794447b9aafb7b1969", "guid": "bfdfe7dc352907fc980b868725387e98807a98cc2e15ab353cc80cd5a2911dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa6883e166d41b4a0d638de954a0f6cf", "guid": "bfdfe7dc352907fc980b868725387e98f4fd326e0ca504595dc36191fefcf041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc0ece8c4809f33a53ff100241df0eb", "guid": "bfdfe7dc352907fc980b868725387e98614e7b84060dfca1532c9261932bedba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a54dacb82c02a723f02aedffc18e19", "guid": "bfdfe7dc352907fc980b868725387e986f8ab02e63746154e009cf16b40b5ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d548c533ea953d1ead5eace3c191ae", "guid": "bfdfe7dc352907fc980b868725387e984722d323bbb9bbc17b36d513ba5ca692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79d9e1e0aff584f0c05c26d226b754c", "guid": "bfdfe7dc352907fc980b868725387e98023a186b4da6cb3c95409fe9cabdc07f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb12cdb083f3e61e7dffc931bc3d590", "guid": "bfdfe7dc352907fc980b868725387e9829f9bddb91fc2ae033d4ff0390bd4077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd76915b7b27c53bb558e47a2d9b10c0", "guid": "bfdfe7dc352907fc980b868725387e9883231659dc0afd4499e758a9f0ac4bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91bc4c057258105c1277bb3e357e541", "guid": "bfdfe7dc352907fc980b868725387e980578bc68fa4d6001c19f15cea2cf0916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e728684352431f4147b237be911aaa02", "guid": "bfdfe7dc352907fc980b868725387e98127007af8599b62b2d07e49be3a3a435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874bb13c1199810f17e4382874c818f3b", "guid": "bfdfe7dc352907fc980b868725387e984780857419970627e136821200fd6ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d919cdb1d60276bffb61e719f8302ae", "guid": "bfdfe7dc352907fc980b868725387e9801dc05cf4d659d6c5ffddde739f677ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984413c467e50e5f983d772a2c52ae6950", "guid": "bfdfe7dc352907fc980b868725387e98f8e623a8064d5de453d3f5b25a482784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98715a7aedeae4e5920406fee2cb3b713f", "guid": "bfdfe7dc352907fc980b868725387e9896432c7de9753ce65d6c09472c6f77d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebf0c54f924f140b4059d7c46a17c463", "guid": "bfdfe7dc352907fc980b868725387e9860763840ab26c983d8fa8905475b90b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984c7757b9e8894983fdbc0955397905", "guid": "bfdfe7dc352907fc980b868725387e9896909fd38aa76b2683f8efc4d8ccc7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b416e996763fb90e3a54d8e7766f62", "guid": "bfdfe7dc352907fc980b868725387e98062843da7211295493f6e6943e3833cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989002b21cbe0242a37dec196843c32716", "guid": "bfdfe7dc352907fc980b868725387e983424fea5e879691c8f8c58e5ec913d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124ca2bf91370ffa847d43d88f453e35", "guid": "bfdfe7dc352907fc980b868725387e985f2d59c25bbf2bc9057916c51b751229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60e726db2ff83775ff2accd62106602", "guid": "bfdfe7dc352907fc980b868725387e98e039bbe167efa69be6380f8e93017b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846249602159db7b706f2de80372f4b39", "guid": "bfdfe7dc352907fc980b868725387e98ebbe03d7b48c2a6be2ce2affa2b6ae48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984f8b629e4cc77840e9fb4101c01201", "guid": "bfdfe7dc352907fc980b868725387e98810205a4c17760bb9cdceb27780dc2fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98079a5f2224f9cdb905161ba75d617f30", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94454a008349d7b8b8b972947a11f43", "guid": "bfdfe7dc352907fc980b868725387e9848c8613314694b7b2103a9a1feff2255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebebe05873fd1f8ce64a3c0fa9e30105", "guid": "bfdfe7dc352907fc980b868725387e98b081cf1e1109fd8daeefbf5d083c8d20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982436343ce0e4b08f00432d095306fc5e", "guid": "bfdfe7dc352907fc980b868725387e9892f7d93ec0427d7daa11e33b8c3504a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aac0e692644bc055faf0cfe5d17d8e2", "guid": "bfdfe7dc352907fc980b868725387e98ec2088e131689a01ad1b2b5d940a7622"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}