import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum StatusEnum {
  all(0, "All"),
  today(1, "Today"),
  upComing(2, "UpComing"),
  completed(3, "Completed"),
  overdue(4, "Overdue");

  final int value;
  final String description;

  static StatusEnum getEnum(int value) {
    return StatusEnum.values.where((element) => element.value == value).firstOrNull ?? StatusEnum.today;
  }

  const StatusEnum(this.value, this.description);
}
