import 'package:flutter/material.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class QualityScoreGuidelinesWidget extends StatelessWidget {
  const QualityScoreGuidelinesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Listing Quality Score Guidelines',
                style: LexendTextStyles.lexend16SemiBold.copyWith(
                  color: ColorPalette.primaryDarkColor,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, size: 20),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Each listing will be evaluated based on the following criteria. If the condition is met, the corresponding score will be awarded:',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.primaryWhite300TextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildGuidelinesTable(),
        ],
      ),
    );
  }

  Widget _buildGuidelinesTable() {
    final guidelines = [
      _GuidelineItem('Description', 'Character count between 750 and 2000', 10),
      _GuidelineItem('Images', 'At least upload up to 10 minimum photos', 6),
      _GuidelineItem('Image Diversity', 'Images showcase different areas (e.g., bedroom, bathroom, kitchen, etc.)', 5),
      _GuidelineItem('Image Duplication', 'No duplicate images', 18),
      _GuidelineItem('Listing Completion', 'All mandatory listing fields are filled', 2),
      _GuidelineItem('Location', '• Tower name (4)\n• Subcommunity (5)\n• Community (5)\n• City (5)', 19),
      _GuidelineItem('Title Length', 'Title character count between 30 and 50', 10),
      _GuidelineItem('Listing Verification', 'Listing is marked as verified', 20),
      _GuidelineItem('Image Dimensions', 'All images have dimensions of 1920x1080', 10),
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: ColorPalette.superSilver),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: ColorPalette.superSilver.withOpacity(0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Criteria',
                    style: LexendTextStyles.lexend12SemiBold.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Condition',
                    style: LexendTextStyles.lexend12SemiBold.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Score',
                    style: LexendTextStyles.lexend12SemiBold.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Guidelines rows
          ...guidelines.asMap().entries.map((entry) {
            final index = entry.key;
            final guideline = entry.value;
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: index.isEven ? Colors.white : ColorPalette.superSilver.withOpacity(0.2),
                border: Border(
                  bottom: index < guidelines.length - 1
                      ? BorderSide(color: ColorPalette.superSilver.withOpacity(0.5))
                      : BorderSide.none,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${index + 1}. ${guideline.criteria}',
                      style: LexendTextStyles.lexend11Medium.copyWith(
                        color: ColorPalette.primaryDarkColor,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      guideline.condition,
                      style: LexendTextStyles.lexend11Regular.copyWith(
                        color: ColorPalette.primaryWhite300TextColor,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      guideline.score.toString(),
                      style: LexendTextStyles.lexend11SemiBold.copyWith(
                        color: ColorPalette.primaryGreen,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

class _GuidelineItem {
  final String criteria;
  final String condition;
  final int score;

  _GuidelineItem(this.criteria, this.condition, this.score);
}
