<svg width="51" height="21" viewBox="0 0 51 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_613_5436)">
<path d="M0 0H41C46.5228 0 51 4.47715 51 10V21H10C4.47715 21 0 16.5228 0 11V0Z" fill="#50BEA8"/>
<path d="M10.25 14L12.96 7H14.06L16.75 14H15.6L14 9.78C13.98 9.73333 13.94 9.62 13.88 9.44C13.8267 9.26 13.7633 9.06 13.69 8.84C13.6167 8.62 13.55 8.42 13.49 8.24C13.43 8.05333 13.39 7.93333 13.37 7.88L13.6 7.87C13.56 7.98333 13.51 8.12667 13.45 8.3C13.3967 8.47333 13.3367 8.65667 13.27 8.85C13.21 9.04333 13.15 9.22667 13.09 9.4C13.03 9.56667 12.98 9.70667 12.94 9.82L11.35 14H10.25ZM11.45 12.26L11.85 11.22H15.03L15.49 12.26H11.45ZM19.7566 14.1C19.27 14.1 18.8333 13.98 18.4466 13.74C18.0666 13.5 17.7633 13.1733 17.5366 12.76C17.3166 12.3467 17.2066 11.8833 17.2066 11.37C17.2066 10.8567 17.3166 10.3933 17.5366 9.98C17.7633 9.56667 18.0666 9.24 18.4466 9C18.8333 8.76 19.27 8.64 19.7566 8.64C20.2233 8.64 20.6466 8.73667 21.0266 8.93C21.4133 9.11667 21.7066 9.37333 21.9066 9.7L21.3366 10.4C21.23 10.2467 21.0933 10.1067 20.9266 9.98C20.76 9.85333 20.5833 9.75333 20.3966 9.68C20.21 9.60667 20.03 9.57 19.8566 9.57C19.5366 9.57 19.25 9.65 18.9966 9.81C18.75 9.96333 18.5533 10.1767 18.4066 10.45C18.26 10.7233 18.1866 11.03 18.1866 11.37C18.1866 11.71 18.2633 12.0167 18.4166 12.29C18.57 12.5567 18.7733 12.77 19.0266 12.93C19.28 13.09 19.56 13.17 19.8666 13.17C20.0466 13.17 20.22 13.14 20.3866 13.08C20.56 13.02 20.7266 12.93 20.8866 12.81C21.0466 12.69 21.1966 12.5433 21.3366 12.37L21.9066 13.07C21.6933 13.37 21.3866 13.6167 20.9866 13.81C20.5933 14.0033 20.1833 14.1 19.7566 14.1ZM23.5282 14V7.41H24.5582V14H23.5282ZM22.4382 9.75V8.75H25.7782V9.75H22.4382ZM26.8861 14V8.75H27.9161V14H26.8861ZM27.3861 7.59C27.1661 7.59 26.9961 7.53333 26.8761 7.42C26.7561 7.30667 26.6961 7.14667 26.6961 6.94C26.6961 6.74667 26.7561 6.59 26.8761 6.47C27.0028 6.35 27.1728 6.29 27.3861 6.29C27.6061 6.29 27.7761 6.34667 27.8961 6.46C28.0161 6.57333 28.0761 6.73333 28.0761 6.94C28.0761 7.13333 28.0128 7.29 27.8861 7.41C27.7661 7.53 27.5994 7.59 27.3861 7.59ZM31.2193 14L28.9993 8.75H30.1093L31.7093 12.68L31.5193 12.77L33.2293 8.75H34.2893L31.9593 14H31.2193ZM37.5352 14.1C36.9952 14.1 36.5152 13.9867 36.0952 13.76C35.6819 13.5267 35.3552 13.21 35.1152 12.81C34.8819 12.41 34.7652 11.95 34.7652 11.43C34.7652 11.0167 34.8319 10.64 34.9652 10.3C35.0986 9.96 35.2819 9.66667 35.5152 9.42C35.7552 9.16667 36.0386 8.97333 36.3652 8.84C36.6986 8.7 37.0586 8.63 37.4452 8.63C37.7852 8.63 38.1019 8.69667 38.3952 8.83C38.6886 8.95667 38.9419 9.13333 39.1552 9.36C39.3752 9.58667 39.5419 9.85667 39.6552 10.17C39.7752 10.4767 39.8319 10.8133 39.8252 11.18L39.8152 11.62H35.5252L35.2952 10.8H38.9252L38.7752 10.97V10.73C38.7552 10.51 38.6819 10.3133 38.5552 10.14C38.4286 9.96667 38.2686 9.83 38.0752 9.73C37.8819 9.63 37.6719 9.58 37.4452 9.58C37.0852 9.58 36.7819 9.65 36.5352 9.79C36.2886 9.92333 36.1019 10.1233 35.9752 10.39C35.8486 10.65 35.7852 10.9733 35.7852 11.36C35.7852 11.7267 35.8619 12.0467 36.0152 12.32C36.1686 12.5867 36.3852 12.7933 36.6652 12.94C36.9452 13.0867 37.2686 13.16 37.6352 13.16C37.8952 13.16 38.1352 13.1167 38.3552 13.03C38.5819 12.9433 38.8252 12.7867 39.0852 12.56L39.6052 13.29C39.4452 13.45 39.2486 13.59 39.0152 13.71C38.7886 13.83 38.5452 13.9267 38.2852 14C38.0319 14.0667 37.7819 14.1 37.5352 14.1Z" fill="white"/>
<g filter="url(#filter0_f_613_5436)">
<path d="M-19 20L-7.45989 0H-6.06109L-17.2515 20H-19Z" fill="url(#paint0_linear_613_5436)"/>
<path d="M-16.7365 20L-5.5461 0H-1L-11.491 20H-16.7365Z" fill="url(#paint1_linear_613_5436)"/>
</g>
</g>
<defs>
<filter id="filter0_f_613_5436" x="-21" y="-2" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_613_5436"/>
</filter>
<linearGradient id="paint0_linear_613_5436" x1="-13.4054" y1="-0.952381" x2="-13.4054" y2="16.1905" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint1_linear_613_5436" x1="-9.56766" y1="-0.952381" x2="-9.56766" y2="16.1905" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<clipPath id="clip0_613_5436">
<path d="M0 0H41C46.5228 0 51 4.47715 51 10V21H10C4.47715 21 0 16.5228 0 11V0Z" fill="white"/>
</clipPath>
</defs>
</svg>
