import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/entites/dto_with_name_entity.dart';

import '../constants/hive_model_constants.dart';

part 'dto_with_name_model.g.dart';

@HiveType(typeId: HiveModelConstants.dTOWithNameModelTypeId)
@JsonSerializable(explicitToJson: true, includeIfNull: false)
class DTOWithNameModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final DateTime? createdOn;
  @HiveField(3)
  final String? createdBy;
  @HiveField(4)
  final DateTime? lastModifiedOn;
  @HiveField(5)
  final String? lastModifiedBy;

  DTOWithNameModel({
    this.id,
    this.name,
    this.createdBy,
    this.createdOn,
    this.lastModifiedOn,
    this.lastModifiedBy,
  });

  factory DTOWithNameModel.fromJson(Map<String, dynamic> json) =>
      _$DTOWithNameModelFromJson(json);

  Map<String, dynamic> toJson() => _$DTOWithNameModelToJson(this);

  DtoWithNameEntity toEntity() {
    return DtoWithNameEntity(
      id: id,
      name: name,
      createdBy: createdBy,
      createdOn: createdOn,
      lastModifiedOn: lastModifiedOn,
      lastModifiedBy: lastModifiedBy,
    );
  }
}
