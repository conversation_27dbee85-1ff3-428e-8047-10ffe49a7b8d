import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/base_property_type_enum.dart';

part 'master_project_attribute_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterProjectAttributeModelTypeId)
@JsonSerializable()
class MasterProjectAttributeModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final String? attributeName;
  @HiveField(9)
  final String? attributeDisplayName;
  @HiveField(10)
  final String? attributeType;
  @HiveField(11)
  final String? defaultValue;
  @HiveField(12)
  final int? orderRank;
  @HiveField(13)
  final String? mobileIcon;
  @HiveField(14)
  final List<BasePropertyType>? basePropertyType;

  MasterProjectAttributeModel({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.attributeName,
    this.attributeDisplayName,
    this.attributeType,
    this.defaultValue,
    this.orderRank,
    this.mobileIcon,
    this.basePropertyType,
  });

  factory MasterProjectAttributeModel.fromJson(Map<String, dynamic> json) => _$MasterProjectAttributeModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterProjectAttributeModelToJson(this);
}
