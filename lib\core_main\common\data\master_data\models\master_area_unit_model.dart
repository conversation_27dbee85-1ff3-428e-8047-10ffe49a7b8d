import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/common/entites/area_unit_entity.dart';

part 'master_area_unit_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterAreaUnitModelTypeId)
@JsonSerializable()
class MasterAreaUnitsModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final DateTime? createdOn;
  @HiveField(3)
  final String? createdBy;
  @HiveField(4)
  final DateTime? lastModifiedOn;
  @HiveField(5)
  final String? lastModifiedBy;
  @HiveField(6)
  final String? unit;
  @HiveField(7)
  final double? conversionFactor;

  MasterAreaUnitsModel({
    this.id,
    this.isDeleted,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.unit,
    this.conversionFactor,
  });

  factory MasterAreaUnitsModel.fromJson(Map<String, dynamic> json) => _$MasterAreaUnitsModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterAreaUnitsModelToJson(this);

  AreaUnitEntity toEntity() {
    return AreaUnitEntity(
      id: id,
      isDeleted: isDeleted,
      createdOn: createdOn,
      createdBy: createdBy,
      lastModifiedOn: lastModifiedOn,
      lastModifiedBy: lastModifiedBy,
      unit: unit,
      conversionFactor: conversionFactor,
    );
  }
}
