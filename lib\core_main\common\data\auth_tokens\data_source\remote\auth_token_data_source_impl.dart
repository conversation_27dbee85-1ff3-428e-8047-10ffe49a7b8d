import 'package:leadrat/core_main/common/data/auth_tokens/data_source/remote/auth_token_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/models/get_token_model.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/models/otp_session_model.dart';
import 'package:leadrat/core_main/common/models/base_url_model.dart';
import 'package:leadrat/core_main/common/models/force_update_model.dart';
import 'package:leadrat/core_main/remote/identity_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/services/secret_manager_service/secret_manager_local_service.dart';

class AuthTokenDataSourceImpl extends IdentityRestService implements AuthTokenDataSource {
  final SecretManagerLocalService localStorageService;

  AuthTokenDataSourceImpl(this.localStorageService);

  @override
  Future<GetTokenModel?> getRefreshToken({required String tenant, required String idToken, required String refreshToken}) async {
    final restRequest = createPostRequest(TokenServices.refreshToken, body: {
      "token": idToken,
      "refreshToken": refreshToken,
    });
    restRequest.headers = {"tenant": tenant};
    final response = await executeRequestAsync<ResponseWrapper<GetTokenModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetTokenModel?>.fromJson(json, (data) => fromJsonObject(data, GetTokenModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<GetTokenModel?> getToken({required String tenant, required String userName, required String password}) async {
    final restRequest = createPostRequest(TokenServices.getToken, body: {
      "username": userName,
      "password": password,
    });
    restRequest.headers = {"tenant": tenant};
    final response = await executeRequestAsync<ResponseWrapper<GetTokenModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetTokenModel?>.fromJson(json, (data) => fromJsonObject(data, GetTokenModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<String?> generateMultiFactorAuthOtp({required String userName, required String tenant}) async {
    final restRequest = createPostRequest("${TokenServices.postForOtp}?userName=$userName");
    restRequest.headers = {"tenant": tenant};
    final response = await executeRequestAsync<OtpSessionModel>(restRequest, (data) => OtpSessionModel.fromJson(data));
    return response.sessionId;
  }

  @override
  Future<GetTokenModel?> verifyAuthOtp({required String userName, required String sessionId, required String otp, required String tenant}) async {
    final restRequest = createPostRequest("${TokenServices.postOtpForVerify}?userName=$userName&sessionId=$sessionId&otp=$otp");
    restRequest.headers = {"tenant": tenant};
    final response = await executeRequestAsync<ResponseWrapper<GetTokenModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetTokenModel?>.fromJson(json, (data) => fromJsonObject(data, GetTokenModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<BaseUrlModel?> getBaseUrl() async {
    final restRequest = createGetRequest(TokenServices.awsSecretManager("BaseUrls"));
    final response = await executeRequestAsync<ResponseWrapper<BaseUrlModel>>(
      restRequest,
      (json) => ResponseWrapper<BaseUrlModel>.fromJson(json, (data) => BaseUrlModel.fromJson(data is List ? data : [data])),
    );

    await localStorageService.saveBaseUrl(response.data ?? BaseUrlModel());
    return response.data;
  }

  @override
  Future<ForceUpdateModel?> getUpdateAppVersion() async {
    final restRequest = createGetRequest(TokenServices.awsSecretManager("FlutterForceUpdate"));
    final response = await executeRequestAsync<ResponseWrapper<ForceUpdateModel?>>(
      restRequest,
      (json) => ResponseWrapper<ForceUpdateModel?>.fromJson(json, (data) => fromJsonObject(data, ForceUpdateModel.fromJson)),
    );
    return response.data;
  }
}
