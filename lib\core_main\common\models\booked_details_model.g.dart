// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'booked_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BookedDetailsModel _$BookedDetailsModelFromJson(Map<String, dynamic> json) =>
    BookedDetailsModel(
      bookedDate: json['bookedDate'] == null
          ? null
          : DateTime.parse(json['bookedDate'] as String),
      bookedBy: json['bookedBy'] as String?,
      bookedByName: json['bookedByName'] as String?,
      secondaryOwner: json['secondaryOwner'] as String?,
      secondaryOwnerName: json['secondaryOwnerName'] as String?,
      bookedByUser: json['bookedByUser'] as String?,
      bookedUnderName: json['bookedUnderName'] as String?,
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
      soldPrice: json['soldPrice'] as String?,
      notes: json['notes'] as String?,
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      teamHead: json['teamHead'] as String?,
      teamHeadName: json['teamHeadName'] as String?,
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      documents: (json['documents'] as List<dynamic>?)
          ?.map((e) => DocumentsModel.fromJson(e as Map<String, dynamic>)),
      carParkingCharges: (json['carParkingCharges'] as num?)?.toDouble(),
      additionalCharges: (json['additionalCharges'] as num?)?.toDouble(),
      tokenAmount: (json['tokenAmount'] as num?)?.toDouble(),
      paymentMode: $enumDecodeNullable(_$TokenTypeEnumMap, json['paymentMode'],
          unknownValue: TokenType.none),
      discount: (json['discount'] as num?)?.toDouble(),
      discountUnit: json['discountUnit'] as String?,
      discountMode: $enumDecodeNullable(
          _$DiscountTypeEnumMap, json['discountMode'],
          unknownValue: DiscountType.none),
      remainingAmount: (json['remainingAmount'] as num?)?.toDouble(),
      paymentType:
          $enumDecodeNullable(_$PaymentTypeEnumMap, json['paymentType']),
      leadBrokerageInfoId: json['leadBrokerageInfoId'] as String?,
      brokerageInfo: json['brokerageInfo'] == null
          ? null
          : LeadBrokerageInfoModel.fromJson(
              json['brokerageInfo'] as Map<String, dynamic>),
      property: json['property'] == null
          ? null
          : BasicPropertyInfoModel.fromJson(
              json['property'] as Map<String, dynamic>),
      projects: json['projects'] == null
          ? null
          : BasicProjectModel.fromJson(
              json['projects'] as Map<String, dynamic>),
      isBookingCompleted: json['isBookingCompleted'] as bool?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      currency: json['currency'] as String?,
      unitType: json['unitType'] == null
          ? null
          : UnitTypeModel.fromJson(json['unitType'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BookedDetailsModelToJson(BookedDetailsModel instance) =>
    <String, dynamic>{
      'bookedDate': instance.bookedDate?.toIso8601String(),
      'bookedBy': instance.bookedBy,
      'bookedByName': instance.bookedByName,
      'secondaryOwner': instance.secondaryOwner,
      'secondaryOwnerName': instance.secondaryOwnerName,
      'bookedByUser': instance.bookedByUser,
      'bookedUnderName': instance.bookedUnderName,
      'userId': instance.userId,
      'userName': instance.userName,
      'soldPrice': instance.soldPrice,
      'notes': instance.notes,
      'projectsList': instance.projectsList,
      'propertiesList': instance.propertiesList,
      'teamHead': instance.teamHead,
      'teamHeadName': instance.teamHeadName,
      'agreementValue': instance.agreementValue,
      'documents': instance.documents?.toList(),
      'carParkingCharges': instance.carParkingCharges,
      'additionalCharges': instance.additionalCharges,
      'tokenAmount': instance.tokenAmount,
      'paymentMode': _$TokenTypeEnumMap[instance.paymentMode],
      'discount': instance.discount,
      'discountUnit': instance.discountUnit,
      'discountMode': _$DiscountTypeEnumMap[instance.discountMode],
      'remainingAmount': instance.remainingAmount,
      'paymentType': _$PaymentTypeEnumMap[instance.paymentType],
      'leadBrokerageInfoId': instance.leadBrokerageInfoId,
      'brokerageInfo': instance.brokerageInfo,
      'property': instance.property,
      'projects': instance.projects,
      'isBookingCompleted': instance.isBookingCompleted,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'currency': instance.currency,
      'unitType': instance.unitType,
    };

const _$TokenTypeEnumMap = {
  TokenType.none: 0,
  TokenType.cheque: 1,
  TokenType.dD: 2,
  TokenType.iMPS: 3,
  TokenType.nEFT: 4,
  TokenType.uPI: 5,
  TokenType.rTGS: 6,
  TokenType.cash: 7,
};

const _$DiscountTypeEnumMap = {
  DiscountType.none: 0,
  DiscountType.cashback: 1,
  DiscountType.directAdjustment: 1,
};

const _$PaymentTypeEnumMap = {
  PaymentType.none: 0,
  PaymentType.bankLoan: 1,
  PaymentType.pendingLoanApproval: 2,
  PaymentType.partialLoanCash: 3,
  PaymentType.loanApplied: 4,
  PaymentType.onlineTransfer: 5,
  PaymentType.cash: 6,
  PaymentType.cheque: 7,
  PaymentType.dD: 8,
};

DocumentsModel _$DocumentsModelFromJson(Map<String, dynamic> json) =>
    DocumentsModel(
      documentName: json['documentName'] as String?,
      filePath: json['filePath'] as String?,
      type: $enumDecodeNullable(_$DocumentTypeEnumMap, json['type']),
      bookedDocumentType: $enumDecodeNullable(
          _$BookedDocumentTypeEnumMap, json['bookedDocumentType'],
          unknownValue: BookedDocumentType.none),
      uploadedOn: json['uploadedOn'] == null
          ? null
          : DateTime.parse(json['uploadedOn'] as String),
    );

Map<String, dynamic> _$DocumentsModelToJson(DocumentsModel instance) =>
    <String, dynamic>{
      'documentName': instance.documentName,
      'filePath': instance.filePath,
      'type': _$DocumentTypeEnumMap[instance.type],
      'bookedDocumentType':
          _$BookedDocumentTypeEnumMap[instance.bookedDocumentType],
      'uploadedOn': instance.uploadedOn?.toIso8601String(),
    };

const _$DocumentTypeEnumMap = {
  DocumentType.defaultType: 0,
  DocumentType.identity: 1,
  DocumentType.experience: 2,
  DocumentType.signature: 3,
  DocumentType.jpg: 4,
  DocumentType.png: 5,
  DocumentType.pdf: 6,
  DocumentType.docx: 7,
  DocumentType.txt: 8,
  DocumentType.csv: 9,
};

const _$BookedDocumentTypeEnumMap = {
  BookedDocumentType.none: 0,
  BookedDocumentType.leadImage: 1,
  BookedDocumentType.aadhar: 2,
  BookedDocumentType.pancard: 3,
  BookedDocumentType.passport: 4,
};

LeadBrokerageInfoModel _$LeadBrokerageInfoModelFromJson(
        Map<String, dynamic> json) =>
    LeadBrokerageInfoModel(
      soldPrice: (json['soldPrice'] as num?)?.toDouble(),
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      brokerageCharges: (json['brokerageCharges'] as num?)?.toDouble(),
      netBrokerageAmount: (json['netBrokerageAmount'] as num?)?.toDouble(),
      gst: (json['gst'] as num?)?.toDouble(),
      totalBrokerage: (json['totalBrokerage'] as num?)?.toDouble(),
      referralNumber: json['referralNumber'] as String?,
      referralName: json['referralName'] as String?,
      referredBy: json['referredBy'] as String?,
      commission: (json['commission'] as num?)?.toDouble(),
      commissionUnit: json['commissionUnit'] as String?,
      brokerageType:
          $enumDecodeNullable(_$BrokerageTypeEnumMap, json['brokerageType']),
      earnedBrokerage: (json['earnedBrokerage'] as num?)?.toDouble(),
      gSTUnit: json['gSTUnit'] as String?,
      brokerageUnit: json['brokerageUnit'] as String?,
    );

Map<String, dynamic> _$LeadBrokerageInfoModelToJson(
        LeadBrokerageInfoModel instance) =>
    <String, dynamic>{
      'soldPrice': instance.soldPrice,
      'agreementValue': instance.agreementValue,
      'brokerageCharges': instance.brokerageCharges,
      'netBrokerageAmount': instance.netBrokerageAmount,
      'gst': instance.gst,
      'totalBrokerage': instance.totalBrokerage,
      'referralNumber': instance.referralNumber,
      'referralName': instance.referralName,
      'referredBy': instance.referredBy,
      'commission': instance.commission,
      'commissionUnit': instance.commissionUnit,
      'brokerageType': _$BrokerageTypeEnumMap[instance.brokerageType],
      'earnedBrokerage': instance.earnedBrokerage,
      'gSTUnit': instance.gSTUnit,
      'brokerageUnit': instance.brokerageUnit,
    };

const _$BrokerageTypeEnumMap = {
  BrokerageType.none: 0,
  BrokerageType.agreementValue: 1,
  BrokerageType.soldPrice: 2,
};

UnitTypeModel _$UnitTypeModelFromJson(Map<String, dynamic> json) =>
    UnitTypeModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      carpetArea: (json['carpetArea'] as num?)?.toDouble(),
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      superBuildUpArea: (json['superBuildUpArea'] as num?)?.toDouble(),
      superBuildUpAreaUnit: json['superBuildUpAreaUnit'] as String?,
    );

Map<String, dynamic> _$UnitTypeModelToJson(UnitTypeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'carpetArea': instance.carpetArea,
      'carpetAreaUnitId': instance.carpetAreaUnitId,
      'superBuildUpArea': instance.superBuildUpArea,
      'superBuildUpAreaUnit': instance.superBuildUpAreaUnit,
    };

BasicPropertyInfoModel _$BasicPropertyInfoModelFromJson(
        Map<String, dynamic> json) =>
    BasicPropertyInfoModel(
      title: json['title'] as String?,
      enquiredFor:
          $enumDecodeNullable(_$EnquiryTypeEnumMap, json['enquiredFor']),
      propertyTypeId: json['propertyTypeId'] as String?,
      noOfBHK: (json['noOfBHK'] as num?)?.toDouble(),
      noOfBHKs: (json['noOfBHKs'] as num?)?.toDouble(),
      bHKType: $enumDecodeNullable(_$BHKTypeEnumMap, json['bHKType']),
      dimension: json['dimension'] == null
          ? null
          : PropertyDimensionModel.fromJson(
              json['dimension'] as Map<String, dynamic>),
      aboutProperty: json['aboutProperty'] as String?,
      project: json['project'] as String?,
      ownerDetails: json['ownerDetails'] == null
          ? null
          : PropertyOwnerDetailsModel.fromJson(
              json['ownerDetails'] as Map<String, dynamic>),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : PropertyMonetaryInfoModel.fromJson(
              json['monetaryInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BasicPropertyInfoModelToJson(
        BasicPropertyInfoModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'enquiredFor': _$EnquiryTypeEnumMap[instance.enquiredFor],
      'propertyTypeId': instance.propertyTypeId,
      'noOfBHK': instance.noOfBHK,
      'noOfBHKs': instance.noOfBHKs,
      'bHKType': _$BHKTypeEnumMap[instance.bHKType],
      'dimension': instance.dimension,
      'aboutProperty': instance.aboutProperty,
      'project': instance.project,
      'ownerDetails': instance.ownerDetails,
      'monetaryInfo': instance.monetaryInfo,
    };

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

PropertyOwnerDetailsModel _$PropertyOwnerDetailsModelFromJson(
        Map<String, dynamic> json) =>
    PropertyOwnerDetailsModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$PropertyOwnerDetailsModelToJson(
        PropertyOwnerDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'alternateContactNo': instance.alternateContactNo,
      'email': instance.email,
    };

PropertyMonetaryInfoModel _$PropertyMonetaryInfoModelFromJson(
        Map<String, dynamic> json) =>
    PropertyMonetaryInfoModel(
      id: json['id'] as String?,
      expectedPrice: (json['expectedPrice'] as num?)?.toInt(),
      isNegotiable: json['isNegotiable'] as bool?,
      brokerage: (json['brokerage'] as num?)?.toDouble(),
      brokerageUnit: $enumDecodeNullable(
          _$BrokerageUnitEnumMap, json['brokerageUnit'],
          unknownValue: BrokerageUnit.none),
      currency: json['currency'] as String?,
      brokerageCurrency: json['brokerageCurrency'] as String?,
      depositAmount: (json['depositAmount'] as num?)?.toInt(),
      maintenanceCost: (json['maintenanceCost'] as num?)?.toInt(),
      monthlyRentAmount: (json['monthlyRentAmount'] as num?)?.toInt(),
      escalationPercentage: (json['escalationPercentage'] as num?)?.toInt(),
      isPriceVissible: json['isPriceVissible'] as bool?,
      paymentFrequency: $enumDecodeNullable(
          _$PaymentFrequencyEnumMap, json['paymentFrequency']),
      serviceChange: (json['serviceChange'] as num?)?.toDouble(),
      noOfChequesAllowed: (json['noOfChequesAllowed'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PropertyMonetaryInfoModelToJson(
        PropertyMonetaryInfoModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.expectedPrice case final value?) 'expectedPrice': value,
      if (instance.isNegotiable case final value?) 'isNegotiable': value,
      if (instance.brokerage case final value?) 'brokerage': value,
      if (_$BrokerageUnitEnumMap[instance.brokerageUnit] case final value?)
        'brokerageUnit': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.brokerageCurrency case final value?)
        'brokerageCurrency': value,
      if (instance.depositAmount case final value?) 'depositAmount': value,
      if (instance.maintenanceCost case final value?) 'maintenanceCost': value,
      if (instance.monthlyRentAmount case final value?)
        'monthlyRentAmount': value,
      if (instance.escalationPercentage case final value?)
        'escalationPercentage': value,
      if (instance.isPriceVissible case final value?) 'isPriceVissible': value,
      if (_$PaymentFrequencyEnumMap[instance.paymentFrequency]
          case final value?)
        'paymentFrequency': value,
      if (instance.serviceChange case final value?) 'serviceChange': value,
      if (instance.noOfChequesAllowed case final value?)
        'noOfChequesAllowed': value,
    };

const _$BrokerageUnitEnumMap = {
  BrokerageUnit.none: 0,
  BrokerageUnit.percentage: 1,
  BrokerageUnit.iNR: 2,
};

const _$PaymentFrequencyEnumMap = {
  PaymentFrequency.none: 0,
  PaymentFrequency.daily: 1,
  PaymentFrequency.weekly: 2,
  PaymentFrequency.monthly: 3,
  PaymentFrequency.yearly: 4,
};

BasicProjectModel _$BasicProjectModelFromJson(Map<String, dynamic> json) =>
    BasicProjectModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      builderDetail: json['builderDetail'] == null
          ? null
          : ProjectBuilderInfoModel.fromJson(
              json['builderDetail'] as Map<String, dynamic>),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : ProjectMonetaryInfoModel.fromJson(
              json['monetaryInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BasicProjectModelToJson(BasicProjectModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'builderDetail': instance.builderDetail,
      'monetaryInfo': instance.monetaryInfo,
    };
