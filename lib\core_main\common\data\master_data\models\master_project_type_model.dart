import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';

part 'master_project_type_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterProjectTypeId)
@JsonSerializable()
class MasterProjectTypeModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String? baseId;
  @HiveField(2)
  final int? level;
  @HiveField(3)
  final String? type;
  @HiveField(4)
  final String? displayName;
  @HiveField(5)
  final List<MasterProjectTypeModel>? childTypes;

  MasterProjectTypeModel({
    this.id,
    this.baseId,
    this.level,
    this.type,
    this.displayName,
    this.childTypes,
  });

  factory MasterProjectTypeModel.fromJson(Map<String, dynamic> json) => _$MasterProjectTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterProjectTypeModelToJson(this);
}
