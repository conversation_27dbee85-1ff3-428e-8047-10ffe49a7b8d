import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/constants/hive_model_constants.dart';
import 'package:leadrat/core_main/enums/common/base_property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/property_attribute_field_type_enum.dart';

part 'master_property_attributes_model.g.dart';

@HiveType(typeId: HiveModelConstants.masterPropertyAttributesModelTypeId)
@JsonSerializable()
class MasterPropertyAttributesModel {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final bool? isDeleted;
  @HiveField(2)
  final String? createdBy;
  @HiveField(3)
  final DateTime? createdOn;
  @HiveField(4)
  final String? lastModifiedBy;
  @HiveField(5)
  final DateTime? lastModifiedOn;
  @HiveField(6)
  final DateTime? deletedOn;
  @HiveField(7)
  final String? deletedBy;
  @HiveField(8)
  final String? attributeName;
  @HiveField(9)
  final String? attributeDisplayName;
  @HiveField(10)
  final String? attributeType;
  @HiveField(11)
  final String? defaultValue;
  @HiveField(12)
  final PropertyAttributeFieldType? fieldType;
  @HiveField(13)
  final List<BasePropertyType>? basePropertyType;
  @HiveField(14)
  final int? orderRank;
  @HiveField(15)
  final String? mobileIcon;

  MasterPropertyAttributesModel({
    this.id,
    this.isDeleted,
    this.createdBy,
    this.createdOn,
    this.lastModifiedBy,
    this.lastModifiedOn,
    this.deletedOn,
    this.deletedBy,
    this.attributeName,
    this.attributeDisplayName,
    this.attributeType,
    this.defaultValue,
    this.fieldType,
    this.basePropertyType,
    this.orderRank,
    this.mobileIcon,
  });

  factory MasterPropertyAttributesModel.fromJson(Map<String, dynamic> json) => _$MasterPropertyAttributesModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterPropertyAttributesModelToJson(this);
}
