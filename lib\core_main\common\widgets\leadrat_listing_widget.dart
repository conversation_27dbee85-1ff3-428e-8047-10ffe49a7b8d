import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/cubit/basic_user_details_cubit.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/notification/presentation/pages/notification_page.dart';
import 'package:leadrat/features/user_profile/presentation/pages/user_profile_page.dart';

import '../../resources/theme/color_palette.dart';

class LeadratListingWidget extends StatelessWidget {
  final Widget child;
  final Widget trailingImageResource;
  final String? userProfileImage;
  final String? userName;
  final String title;
  final String subTitle;
  final void Function()? onNotificationIconTap;
  final Widget? floatingActionButton;

  const LeadratListingWidget({
    super.key,
    required this.child,
    required this.trailingImageResource,
    required this.title,
    required this.subTitle,
    this.userProfileImage,
    this.onNotificationIconTap,
    this.userName,
    this.floatingActionButton,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: floatingActionButton,
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              ImageResources.imageListingPageBackgroundPattern,
              fit: BoxFit.fill,
              width: context.width(100),
              height: context.height(100),
            ),
            Column(
              children: [
                BlocBuilder<BasicUserDetailsCubit, BasicUserDetailsState>(
                  builder: (context, state) {
                    return Container(
                      decoration: const BoxDecoration(
                        color: ColorPalette.darkToneInk,
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                              left: 0,
                              bottom: 0,
                              child: Image.asset(
                                ImageResources.imageTopBarBottomLeftPattern,
                                width: context.width(30),
                              )),
                          Padding(
                            padding: const EdgeInsets.only(left: 16, right: 14, bottom: 16, top: 18),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SizedBox(
                                  width: context.width(50),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              Navigator.push(context, MaterialPageRoute(builder: (context) => const UserProfilePage()));
                                              getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileManageLeadsButtonUserProfileClick);
                                            },
                                            child: Container(
                                              width: 34,
                                              height: 34,
                                              margin: const EdgeInsets.only(right: 10),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(10),
                                                color: ColorPalette.transparent,
                                                border: Border.all(color: ColorPalette.white),
                                              ),
                                              child: ClipRRect(
                                                borderRadius: BorderRadius.circular(10),
                                                child: state.userDetailsModel != null && state.userDetailsModel!.imageUrl.isNotNullOrEmpty()
                                                    ? CachedNetworkImage(
                                                        imageUrl: state.userDetailsModel!.imageUrl!.appendWithImageBaseUrl(),
                                                        fit: BoxFit.cover,
                                                      )
                                                    : Center(
                                                        child: Text(
                                                          (state.userDetailsModel?.fullName ?? "").nameToInitial().replaceRange(1, 2, ""),
                                                          style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primary),
                                                        ),
                                                      ),
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: RichText(
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                text: TextSpan(children: [
                                                  TextSpan(text: "Hi, ", style: LexendTextStyles.lexend18Regular.copyWith(color: ColorPalette.primary)),
                                                  TextSpan(text: state.userDetailsModel?.firstName ?? '--', style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis)),
                                                ])),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 30),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          Text(
                                            title,
                                            style: LexendTextStyles.lexend18Bold.copyWith(
                                              color: ColorPalette.primary,
                                              fontSize: 20,
                                            ),
                                          ),
                                          const SizedBox(height: 8), // Adjust spacing here
                                          Text(
                                            subTitle,
                                            style: LexendTextStyles.lexend11Regular.copyWith(
                                              color: ColorPalette.gray600,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    IconButton(
                                        onPressed: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => const NotificationPage(), // Replace NewScreen with your target widget
                                            ),
                                          );
                                          getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileManageLeadsButtonNotificationClick);
                                        },
                                        icon: SvgPicture.asset(ImageResources.iconNotificationRounded, height: 20)),
                                    const SizedBox(height: 8),
                                    trailingImageResource
                                  ],
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                Expanded(child: child),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
