{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890638ab2fb9b30c36cab4dbdaeaa2506", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c1a49d2eb4928f38153fbb752b43f0d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818cac62638ad19561e2bbe3915a57c6e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a061109697a6fecb5c4052027830873", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818cac62638ad19561e2bbe3915a57c6e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a2bfc0afebe00a97dd280fbd3b086c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855292cdf77686f862522601b6510ec37", "guid": "bfdfe7dc352907fc980b868725387e98305321bb0f6c61d76ab6f8f834f9e1bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989c7318c71e29c5c2fa728fab937f84e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9841eb6e57d989843ef28bca58d3a569db", "guid": "bfdfe7dc352907fc980b868725387e986b9c07eb90d1c19fb7ac9f978eac2cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36658088a9bc7a48fbc177745018b41", "guid": "bfdfe7dc352907fc980b868725387e9899bf2eef86ec86d3071d0464ba311bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27673f6aae8ad55c8f183fc6dae4e2d", "guid": "bfdfe7dc352907fc980b868725387e989923225760583f9044f3477d68370fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b19689f8173505d357fe07ee3b54185e", "guid": "bfdfe7dc352907fc980b868725387e980b8fe6bd9ce7b7f7f9b672f675ac40ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c3337fcde6332adb1901ea3a90dd50", "guid": "bfdfe7dc352907fc980b868725387e98c7914d6db7a604491a406b7ffd04ffdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb54d2f0c5e976a35e686a33faf0016", "guid": "bfdfe7dc352907fc980b868725387e98eb2913c855fd2bba90610341d1005093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dbc0347a4b39cc12e833c4b0c525cad", "guid": "bfdfe7dc352907fc980b868725387e9818bf3e3f4e2e4ff6409fd3de93a3e67b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bdccaf6a89f9eec8bf914f76c6fa88e", "guid": "bfdfe7dc352907fc980b868725387e9862f3f56aa2a92efeb6d501703a17fb78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c2a9a0321bd316909fbf7411fa8d91", "guid": "bfdfe7dc352907fc980b868725387e986f0a5a4d4d049699b38b834e5127e27c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c197604dda71aa3d33fdaaaed7f51c", "guid": "bfdfe7dc352907fc980b868725387e98b11d52cb23817759d43df3901c9dfdcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b284e508bc51f11c466860a8d887d9ec", "guid": "bfdfe7dc352907fc980b868725387e982e2cfaddbe60c31e36e71f4cf7b78cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986783e99ed2977d11291fa34a58b56328", "guid": "bfdfe7dc352907fc980b868725387e9880b17e68f279e7d7783a51e9bb5287b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1da854af53b38797fcd9d83e19d5714", "guid": "bfdfe7dc352907fc980b868725387e98edb05713e1e170990070d795fbfd9ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da56a05e07c866f6f0948306a9030a86", "guid": "bfdfe7dc352907fc980b868725387e98571373774a6c562d2a57eeef6625aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d132d53f8983d2c5ca8df7d8fb4c6fd1", "guid": "bfdfe7dc352907fc980b868725387e982b8ddf46d054df8e6dd54a4248a7bf7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a14e692c474a5d5a2165a6fd533a6e8", "guid": "bfdfe7dc352907fc980b868725387e98a0be07ca2e426e3bc60d30898d361288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b247599dfdbae5329f0b91b7ce6f948e", "guid": "bfdfe7dc352907fc980b868725387e98b3f4cf0af8ed4ae02ddeeb10e435cd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ea4bcb611442d3f351747d5b4ddf5c", "guid": "bfdfe7dc352907fc980b868725387e9812c712ccbecb48d4ce26f86166ff880e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b283819424989068a6116ebb5916a73", "guid": "bfdfe7dc352907fc980b868725387e982a440638f7cb9965d090ab0c181da156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa95f69ef82ffed6869e0e35099f9d8", "guid": "bfdfe7dc352907fc980b868725387e9803b6b2b356410bc7bcfcc1b875c69472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1741a57534605ee0b47f84a4ac12e3d", "guid": "bfdfe7dc352907fc980b868725387e98ec9c4546923a074af807de40344c27c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f412bafcfdb2e177d816542f8759014", "guid": "bfdfe7dc352907fc980b868725387e985cfeda817f2be498febfcb296540639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f61d5733907674bfd478c78503bc17f", "guid": "bfdfe7dc352907fc980b868725387e982082b1aebfa678d630e6c739c18a5003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ec483f3e002d7252f0bf6948fdb40a", "guid": "bfdfe7dc352907fc980b868725387e987c4d107baf407af054f8312cf0ad4178"}], "guid": "bfdfe7dc352907fc980b868725387e98239213265a3abaa90df058a8c1bb91cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9840e570f7d28f64055b1dab9dc749246d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989febcf50f0594135daa125ad9e14dbc4", "guid": "bfdfe7dc352907fc980b868725387e98192e2db6ac44b70d83d52bc9a34503d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98f0a88717dff9f0a44bc37f82f0d7d1f4"}], "guid": "bfdfe7dc352907fc980b868725387e98819fd04ebca59115ef049e04b7b72408", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c2f740ef6174a43ddde75c8d424f6f2", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e984c4b1b52a05e595bcc1230a93d57ff06", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}