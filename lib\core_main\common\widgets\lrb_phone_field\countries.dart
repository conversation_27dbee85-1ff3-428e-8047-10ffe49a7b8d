// see: https://en.wikipedia.org/wiki/List_of_country_calling_codes
// for list of country/calling codes

const List<Country> countryCodes = [
  Country(
    name: "Afghanistan",
    nameTranslations: {"sk": "Afganistan", "se": "Afghanistan", "pl": "Afganistan", "no": "Afghanistan", "ja": "アフガニスタン", "it": "Afghanistan", "zh": "阿富汗", "nl": "Afghanistan", "de": "Afghanistan", "fr": "Afghanistan", "es": "Afganistán", "en": "Afghanistan", "pt_BR": "Afeganistão", "sr-Cyrl": "Авганистан", "sr-Latn": "Avganistan", "zh_TW": "阿富汗", "tr": "Afganistan", "ro": "Afganistan", "ar": "أفغانستان", "fa": "افغانستان", "yue": "阿富汗"},
    flag: "🇦🇫",
    code: "AF",
    dialCode: "93",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Åland Islands",
    nameTranslations: {"sk": "Alandy", "se": "Ålánda", "pl": "Wyspy Alandzkie", "no": "Åland", "ja": "オーランド諸島", "it": "Isole Åland", "zh": "奥兰群岛", "nl": "Åland", "de": "Ålandinseln", "fr": "Îles Åland", "es": "Islas Åland", "en": "Åland Islands", "pt_BR": "Ilhas Aland", "sr-Cyrl": "Аландска Острва", "sr-Latn": "Alandska Ostrva", "zh_TW": "奧蘭群島", "tr": "Åland", "ro": "Insulele Åland", "ar": "جزر أولاند", "fa": "جزیره اولاند", "yue": "奧蘭群島"},
    flag: "🇦🇽",
    code: "AX",
    dialCode: "358",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Albania",
    nameTranslations: {"sk": "Albánsko", "se": "Albánia", "pl": "Albania", "no": "Albania", "ja": "アルバニア", "it": "Albania", "zh": "阿尔巴尼亚", "nl": "Albanië", "de": "Albanien", "fr": "Albanie", "es": "Albania", "en": "Albania", "pt_BR": "Albânia", "sr-Cyrl": "Албанија", "sr-Latn": "Albanija", "zh_TW": "阿爾巴尼亞", "tr": "Arnavutluk", "ro": "Albania", "ar": "ألبانيا", "fa": "آلبانی", "yue": "阿爾巴尼亞"},
    flag: "🇦🇱",
    code: "AL",
    dialCode: "355",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Algeria",
    nameTranslations: {"sk": "Alžírsko", "se": "Algeria", "pl": "Algieria", "no": "Algerie", "ja": "アルジェリア", "it": "Algeria", "zh": "阿尔及利亚", "nl": "Algerije", "de": "Algerien", "fr": "Algérie", "es": "Argelia", "en": "Algeria", "pt_BR": "Argélia", "sr-Cyrl": "Аргентина", "sr-Latn": "Argentina", "zh_TW": "阿爾及利亞", "tr": "Cezayir", "ro": "Algeria", "ar": "الجزائر", "fa": "الجزیره", "yue": "阿爾及利亞"},
    flag: "🇩🇿",
    code: "DZ",
    dialCode: "213",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "American Samoa",
    nameTranslations: {"sk": "Americká Samoa", "se": "Amerihká Samoa", "pl": "Samoa Amerykańskie", "no": "Amerikansk Samoa", "ja": "米領サモア", "it": "Samoa americane", "zh": "美属萨摩亚", "nl": "Amerikaans-Samoa", "de": "Amerikanisch-Samoa", "fr": "Samoa américaines", "es": "Samoa Americana", "en": "American Samoa", "pt_BR": "Samoa Americana", "sr-Cyrl": "Америчка Самоа", "sr-Latn": "Američka Samoa", "zh_TW": "美屬薩摩亞", "tr": "Amerikan Samoası", "ro": "Samoa Americană", "ar": "ساموا الأمريكية", "fa": "ساموا آمریکا", "yue": "美屬薩摩亞"},
    flag: "🇦🇸",
    code: "AS",
    dialCode: "1684",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Andorra",
    nameTranslations: {"sk": "Andorra", "se": "Andorra", "pl": "Andora", "no": "Andorra", "ja": "アンドラ", "it": "Andorra", "zh": "安道尔", "nl": "Andorra", "de": "Andorra", "fr": "Andorre", "es": "Andorra", "en": "Andorra", "pt_BR": "Andorra", "sr-Cyrl": "Андора", "sr-Latn": "Andora", "zh_TW": "安道爾", "tr": "Andora", "ro": "Andorra", "ar": "أندورا", "fa": "آندورا", "yue": "安道爾"},
    flag: "🇦🇩",
    code: "AD",
    dialCode: "376",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Angola",
    nameTranslations: {"sk": "Angola", "se": "Angola", "pl": "Angola", "no": "Angola", "ja": "アンゴラ", "it": "Angola", "zh": "安哥拉", "nl": "Angola", "de": "Angola", "fr": "Angola", "es": "Angola", "en": "Angola", "pt_BR": "Angola", "sr-Cyrl": "Ангола", "sr-Latn": "Angola", "zh_TW": "安哥拉", "tr": "Angola", "ro": "Angola", "ar": "أنغولا", "fa": "آنگولا", "yue": "安哥拉"},
    flag: "🇦🇴",
    code: "AO",
    dialCode: "244",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Anguilla",
    nameTranslations: {"sk": "Anguilla", "se": "Anguilla", "pl": "Anguilla", "no": "Anguilla", "ja": "アンギラ", "it": "Anguilla", "zh": "安圭拉", "nl": "Anguilla", "de": "Anguilla", "fr": "Anguilla", "es": "Anguila", "en": "Anguilla", "pt_BR": "Anguilla", "sr-Cyrl": "Ангвила", "sr-Latn": "Angvila", "zh_TW": "安圭拉", "tr": "Anguilla", "ro": "Anguilla", "ar": "أنغويلا", "fa": "آنگولیا", "yue": "安圭拉"},
    flag: "🇦🇮",
    code: "AI",
    dialCode: "1264",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Antarctica",
    nameTranslations: {"sk": "Antarktída", "se": "Antárktis", "pl": "Antarktyda", "no": "Antarktis", "ja": "南極", "it": "Antartide", "zh": "南极洲", "nl": "Antarctica", "de": "Antarktis", "fr": "Antarctique", "es": "Antártida", "en": "Antarctica", "pt_BR": "Antártica", "sr-Cyrl": "Антарктик", "sr-Latn": "Antarktik", "zh_TW": "南極", "tr": "Antarktika", "ro": "Antarctica", "ar": "القارة القطبية الجنوبية", "fa": "قطب جنوب", "yue": "南极洲"},
    flag: "🇦🇶",
    code: "AQ",
    dialCode: "672",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Antigua and Barbuda",
    nameTranslations: {"sk": "Antigua a Barbuda", "se": "Antigua ja Barbuda", "pl": "Antigua i Barbuda", "no": "Antigua og Barbuda", "ja": "アンティグア・バーブーダ", "it": "Antigua e Barbuda", "zh": "安提瓜和巴布达", "nl": "Antigua en Barbuda", "de": "Antigua und Barbuda", "fr": "Antigua-et-Barbuda", "es": "Antigua y Barbuda", "en": "Antigua & Barbuda", "pt_BR": "Antigua e Barbuda", "sr-Cyrl": "Антигва и Барбуда", "sr-Latn": "Antigva i Barbuda", "zh_TW": "安提瓜和巴布達", "tr": "Antigua ve Barbuda", "ro": "Antigua şi Barbuda", "ar": "أنتيغوا وباربودا", "fa": "آنتیگوآ و باربودا", "yue": "安提瓜同巴布达"},
    flag: "🇦🇬",
    code: "AG",
    dialCode: "1268",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Argentina",
    nameTranslations: {"sk": "Argentína", "se": "Argentina", "pl": "Argentyna", "no": "Argentina", "ja": "アルゼンチン", "it": "Argentina", "zh": "阿根廷", "nl": "Argentinië", "de": "Argentinien", "fr": "Argentine", "es": "Argentina", "en": "Argentina", "pt_BR": "Argentina", "sr-Cyrl": "Аргентина", "sr-Latn": "Argentina", "zh_TW": "阿根廷", "tr": "Arjantin", "ro": "Argentina", "ar": "الأرجنتين", "fa": "آرژانتین", "yue": "阿根廷"},
    flag: "🇦🇷",
    code: "AR",
    dialCode: "54",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Armenia",
    nameTranslations: {"sk": "Arménsko", "se": "Armenia", "pl": "Armenia", "no": "Armenia", "ja": "アルメニア", "it": "Armenia", "zh": "亚美尼亚", "nl": "Armenië", "de": "Armenien", "fr": "Arménie", "es": "Armenia", "en": "Armenia", "pt_BR": "Armênia", "sr-Cyrl": "Јерменија", "sr-Latn": "Jermenija", "zh_TW": "亞美尼亞", "tr": "Ermenistan", "ro": "Armenia", "ar": "أرمينيا", "fa": "ارمنستان", "yue": "亞美尼亞"},
    flag: "🇦🇲",
    code: "AM",
    dialCode: "374",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Aruba",
    nameTranslations: {"sk": "Aruba", "se": "Aruba", "pl": "Aruba", "no": "Aruba", "ja": "アルバ", "it": "Aruba", "zh": "阿鲁巴", "nl": "Aruba", "de": "Aruba", "fr": "Aruba", "es": "Aruba", "en": "Aruba", "pt_BR": "Aruba", "sr-Cyrl": "Аруба", "sr-Latn": "Aruba", "zh_TW": "阿魯巴", "tr": "Aruba", "ro": "Aruba", "ar": "أروبا", "fa": "آروبا", "yue": "阿魯巴島"},
    flag: "🇦🇼",
    code: "AW",
    dialCode: "297",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Australia",
    nameTranslations: {"sk": "Austrália", "se": "Austrália", "pl": "Australia", "no": "Australia", "ja": "オーストラリア", "it": "Australia", "zh": "澳大利亚", "nl": "Australië", "de": "Australien", "fr": "Australie", "es": "Australia", "en": "Australia", "pt_BR": "Austrália", "sr-Cyrl": "Аустралија", "sr-Latn": "Australija", "zh_TW": "澳州", "tr": "Avustralya", "ro": "Australia", "ar": "أستراليا", "fa": "استرالیا", "yue": "澳洲"},
    flag: "🇦🇺",
    code: "AU",
    dialCode: "61",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Austria",
    nameTranslations: {"sk": "Rakúsko", "se": "Nuortariika", "pl": "Austria", "no": "Østerrike", "ja": "オーストリア", "it": "Austria", "zh": "奥地利", "nl": "Oostenrijk", "de": "Österreich", "fr": "Autriche", "es": "Austria", "en": "Austria", "pt_BR": "Áustria", "sr-Cyrl": "Аустрија", "sr-Latn": "Austrija", "zh_TW": "奥地利", "tr": "Avusturya", "ro": "Austria", "ar": "النمسا", "fa": "اتریش", "yue": "奧地利"},
    flag: "🇦🇹",
    code: "AT",
    dialCode: "43",
    minLength: 13,
    maxLength: 13,
  ),
  Country(
    name: "Azerbaijan",
    nameTranslations: {"sk": "Azerbajdžan", "se": "Aserbaižan", "pl": "Azerbejdżan", "no": "Aserbajdsjan", "ja": "アゼルバイジャン", "it": "Azerbaigian", "zh": "阿塞拜疆", "nl": "Azerbeidzjan", "de": "Aserbaidschan", "fr": "Azerbaïdjan", "es": "Azerbaiyán", "en": "Azerbaijan", "pt_BR": "Azerbaijão", "sr-Cyrl": "Азербејџан", "sr-Latn": "Azerbejdžan", "zh_TW": "亞塞拜然", "tr": "Azerbaycan", "ro": "Azerbaidjan", "ar": "أذربيجان", "fa": "آذربایجان", "yue": "阿塞拜疆"},
    flag: "🇦🇿",
    code: "AZ",
    dialCode: "994",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Bahamas",
    nameTranslations: {"sk": "Bahamy", "se": "Bahamas", "pl": "Bahamy", "no": "Bahamas", "ja": "バハマ", "it": "Bahamas", "zh": "巴哈马", "nl": "Bahama's", "de": "Bahamas", "fr": "Bahamas", "es": "Bahamas", "en": "Bahamas", "pt_BR": "Bahamas", "sr-Cyrl": "Бахаме", "sr-Latn": "Bahame", "zh_TW": "巴哈馬", "tr": "Bahama", "ro": "Bahamas", "ar": "باهاماس", "fa": "باهاماس", "yue": "巴哈馬"},
    flag: "🇧🇸",
    code: "BS",
    dialCode: "1242",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bahrain",
    nameTranslations: {"sk": "Bahrajn", "se": "Bahrain", "pl": "Bahrajn", "no": "Bahrain", "ja": "バーレーン", "it": "Bahrein", "zh": "巴林", "nl": "Bahrein", "de": "Bahrain", "fr": "Bahreïn", "es": "Baréin", "en": "Bahrain", "pt_BR": "Bahrain", "sr-Cyrl": "Бахреин", "sr-Latn": "Bahrein", "zh_TW": "巴林", "tr": "Bahreyn", "ro": "Bahrein", "ar": "البحرين", "fa": "بحرین", "yue": "巴林"},
    flag: "🇧🇭",
    code: "BH",
    dialCode: "973",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bangladesh",
    nameTranslations: {"sk": "Bangladéš", "se": "Bangladesh", "pl": "Bangladesz", "no": "Bangladesh", "ja": "バングラデシュ", "it": "Bangladesh", "zh": "孟加拉国", "nl": "Bangladesh", "de": "Bangladesch", "fr": "Bangladesh", "es": "Bangladés", "en": "Bangladesh", "pt_BR": "Bangladesh", "sr-Cyrl": "Бангладеш", "sr-Latn": "Bangladeš", "zh_TW": "孟加拉", "tr": "Bangladeş", "ro": "Bangladesh", "ar": "بنغلاديش", "fa": "بنگلادش", "yue": "孟加拉囯"},
    flag: "🇧🇩",
    code: "BD",
    dialCode: "880",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Barbados",
    nameTranslations: {"sk": "Barbados", "se": "Barbados", "pl": "Barbados", "no": "Barbados", "ja": "バルバドス", "it": "Barbados", "zh": "巴巴多斯", "nl": "Barbados", "de": "Barbados", "fr": "Barbade", "es": "Barbados", "en": "Barbados", "pt_BR": "Barbados", "sr-Cyrl": "Барбадос", "sr-Latn": "Barbados", "zh_TW": "巴巴多斯", "tr": "Barbados", "ro": "Barbados", "ar": "باربادوس", "fa": "باربادوس", "yue": "巴巴多斯"},
    flag: "🇧🇧",
    code: "BB",
    dialCode: "1246",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Belarus",
    nameTranslations: {"sk": "Bielorusko", "se": "Vilges-Ruošša", "pl": "Białoruś", "no": "Hviterussland", "ja": "ベラルーシ", "it": "Bielorussia", "zh": "白俄罗斯", "nl": "Belarus", "de": "Belarus", "fr": "Biélorussie", "es": "Bielorrusia", "en": "Belarus", "pt_BR": "Bielo-Rússia", "sr-Cyrl": "Белорусија", "sr-Latn": "Belorusija", "zh_TW": "白俄羅斯", "tr": "Belarus", "ro": "Belarus", "ar": "بيلاروس", "fa": "بلاروس", "yue": "白俄羅斯"},
    flag: "🇧🇾",
    code: "BY",
    dialCode: "375",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Belgium",
    nameTranslations: {"sk": "Belgicko", "se": "Belgia", "pl": "Belgia", "no": "Belgia", "ja": "ベルギー", "it": "Belgio", "zh": "比利时", "nl": "België", "de": "Belgien", "fr": "Belgique", "es": "Bélgica", "en": "Belgium", "pt_BR": "Bélgica", "sr-Cyrl": "Белгија", "sr-Latn": "Belgija", "zh_TW": "比利時", "tr": "Belçika", "ro": "Belgia", "ar": "بلجيكا", "fa": "بلژیک", "yue": "比利時"},
    flag: "🇧🇪",
    code: "BE",
    dialCode: "32",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Belize",
    nameTranslations: {"sk": "Belize", "se": "Belize", "pl": "Belize", "no": "Belize", "ja": "ベリーズ", "it": "Belize", "zh": "伯利兹", "nl": "Belize", "de": "Belize", "fr": "Belize", "es": "Belice", "en": "Belize", "pt_BR": "Belize", "sr-Cyrl": "Белизе", "sr-Latn": "Belize", "zh_TW": "伯利茲", "tr": "Belize", "ro": "Belize", "ar": "بليز", "fa": "بليز", "yue": "伯利茲"},
    flag: "🇧🇿",
    code: "BZ",
    dialCode: "501",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Benin",
    nameTranslations: {"sk": "Benin", "se": "Benin", "pl": "Benin", "no": "Benin", "ja": "ベナン", "it": "Benin", "zh": "贝宁", "nl": "Benin", "de": "Benin", "fr": "Bénin", "es": "Benín", "en": "Benin", "pt_BR": "Benin", "sr-Cyrl": "Бенин", "sr-Latn": "Benin", "zh_TW": "貝南", "tr": "Benin", "ro": "Benin", "ar": "بنين", "fa": "بنين", "yue": "貝寧"},
    flag: "🇧🇯",
    code: "BJ",
    dialCode: "229",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bermuda",
    nameTranslations: {"sk": "Bermudy", "se": "Bermuda", "pl": "Bermudy", "no": "Bermuda", "ja": "バミューダ", "it": "Bermuda", "zh": "百慕大", "nl": "Bermuda", "de": "Bermuda", "fr": "Bermudes", "es": "Bermudas", "en": "Bermuda", "pt_BR": "Bermudas", "sr-Cyrl": "Бермуда", "sr-Latn": "Bermuda", "zh_TW": "百慕達", "tr": "Bermuda", "ro": "Insulele Bermude", "ar": "برمودا", "fa": "برمودا", "yue": "百慕大"},
    flag: "🇧🇲",
    code: "BM",
    dialCode: "1441",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bhutan",
    nameTranslations: {"sk": "Bhután", "se": "Bhutan", "pl": "Bhutan", "no": "Bhutan", "ja": "ブータン", "it": "Bhutan", "zh": "不丹", "nl": "Bhutan", "de": "Bhutan", "fr": "Bhoutan", "es": "Bután", "en": "Bhutan", "pt_BR": "Butão", "sr-Cyrl": "Бутан", "sr-Latn": "Butan", "zh_TW": "不丹", "tr": "Bhutan", "ro": "Bhutan", "ar": "بوتان", "fa": "بوتان", "yue": "不丹"},
    flag: "🇧🇹",
    code: "BT",
    dialCode: "975",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bolivia, Plurinational State of bolivia",
    nameTranslations: {"sk": "Bolívia", "se": "Bolivia", "pl": "Boliwia", "no": "Bolivia", "ja": "ボリビア", "it": "Bolivia", "zh": "玻利维亚", "nl": "Bolivia", "de": "Bolivien", "fr": "Bolivie", "es": "Bolivia", "en": "Bolivia", "pt_BR": "Bolívia", "sr-Cyrl": "Боливија", "sr-Latn": "Bolivija", "zh_TW": "玻利維亞", "tr": "Bolivya", "ro": "Bolivia", "ar": "بوليفيا", "fa": "بولیوی", "yue": "玻利維亞（多民族國家）"},
    flag: "🇧🇴",
    code: "BO",
    dialCode: "591",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bosnia and Herzegovina",
    nameTranslations: {"sk": "Bosna a Hercegovina", "se": "Bosnia-Hercegovina", "pl": "Bośnia i Hercegowina", "no": "Bosnia-Hercegovina", "ja": "ボスニア・ヘルツェゴビナ", "it": "Bosnia ed Erzegovina", "zh": "波斯尼亚和黑塞哥维那", "nl": "Bosnië en Herzegovina", "de": "Bosnien und Herzegowina", "fr": "Bosnie-Herzégovine", "es": "Bosnia y Herzegovina", "en": "Bosnia & Herzegovina", "pt_BR": "Bósnia e Herzegovina", "sr-Cyrl": "Босна и Херцеговина", "sr-Latn": "Bosna i Hercegovina", "zh_TW": "波士尼亞和黑塞哥維那", "tr": "Bosna Hersek", "ro": "Bosnia și Herțegovina", "ar": "البوسنة والهرسك", "fa": "بوسنی و هرزگوین", "yue": "波斯尼亞黑塞哥維那"},
    flag: "🇧🇦",
    code: "BA",
    dialCode: "387",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Botswana",
    nameTranslations: {"sk": "Botswana", "se": "Botswana", "pl": "Botswana", "no": "Botswana", "ja": "ボツワナ", "it": "Botswana", "zh": "博茨瓦纳", "nl": "Botswana", "de": "Botsuana", "fr": "Botswana", "es": "Botsuana", "en": "Botswana", "pt_BR": "Botswana", "sr-Cyrl": "Боцвана", "sr-Latn": "Bocvana", "zh_TW": "博茨瓦納", "tr": "Botsvana", "ro": "Botswana", "ar": "بوتسوانا", "fa": "بوتسوانا", "yue": "博茨瓦納"},
    flag: "🇧🇼",
    code: "BW",
    dialCode: "267",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bouvet Island",
    nameTranslations: {"sk": "Bouvetov ostrov", "se": "Bouvet-sullot", "pl": "Wyspa Bouveta", "no": "Bouvetøya", "ja": "ブーベ島", "it": "Isola Bouvet", "zh": "布韦岛", "nl": "Bouveteiland", "de": "Bouvetinsel", "fr": "Île Bouvet", "es": "Isla Bouvet", "en": "Bouvet Island", "pt_BR": "Ilha Bouvet", "sr-Cyrl": "Острво Буве", "sr-Latn": "Ostrvo Buve", "zh_TW": "布維特島", "tr": "Bouvet Adası", "ro": "Insula Bouvet", "ar": "جزيرة بوفيه", "fa": "جزیره بووه", "yue": "布维特岛"},
    flag: "🇧🇻",
    code: "BV",
    dialCode: "47",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Brazil",
    nameTranslations: {"sk": "Brazília", "se": "Brasil", "pl": "Brazylia", "no": "Brasil", "ja": "ブラジル", "it": "Brasile", "zh": "巴西", "nl": "Brazilië", "de": "Brasilien", "fr": "Brésil", "es": "Brasil", "en": "Brazil", "pt_BR": "Brasil", "sr-Cyrl": "Бразил", "sr-Latn": "Brazil", "zh_TW": "巴西", "tr": "Brezilya", "ro": "Brazilia", "ar": "البرازيل", "fa": "برزیل", "yue": "巴西"},
    flag: "🇧🇷",
    code: "BR",
    dialCode: "55",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "British Indian Ocean Territory",
    nameTranslations: {"sk": "Britské indickooceánske územie", "se": "British Indian Ocean Territory", "pl": "Brytyjskie Terytorium Oceanu Indyjskiego", "no": "Det britiske territoriet i Indiahavet", "ja": "英領インド洋地域", "it": "Territorio britannico dell'Oceano Indiano", "zh": "英属印度洋领地", "nl": "Brits Indische Oceaanterritorium", "de": "Britisches Territorium im Indischen Ozean", "fr": "Territoire britannique de l'océan Indien", "es": "Territorio Británico del Océano Índico", "en": "British Indian Ocean Territory", "pt_BR": "Território Britânico do Oceano Índico", "sr-Cyrl": "Британска територија Индијског океана", "sr-Latn": "Britanska teritorija Indijskog okeana", "zh_TW": "英屬印度洋領地", "tr": "Britanya Hint Okyanusu Toprakları", "ro": "Teritoriul Britanic din Oceanul Indian", "ar": "إقليم المحيط الهندي البريطاني", "fa": "سرزمین دریایی هند - بریتانیا", "yue": "英屬印度洋領土"},
    flag: "🇮🇴",
    code: "IO",
    dialCode: "246",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Brunei Darussalam",
    nameTranslations: {"sk": "Brunej", "se": "Brunei", "pl": "Brunei", "no": "Brunei", "ja": "ブルネイ", "it": "Brunei", "zh": "文莱", "nl": "Brunei", "de": "Brunei Darussalam", "fr": "Brunéi Darussalam", "es": "Brunéi", "en": "Brunei", "pt_BR": "Brunei", "sr-Cyrl": "Брунеј", "sr-Latn": "Brunej", "zh_TW": "汶萊", "tr": "Bruney", "ro": "Brunei", "ar": "بروناي", "fa": "برونئی", "yue": "文萊達魯薩蘭國"},
    flag: "🇧🇳",
    code: "BN",
    dialCode: "673",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bulgaria",
    nameTranslations: {"sk": "Bulharsko", "se": "Bulgária", "pl": "Bułgaria", "no": "Bulgaria", "ja": "ブルガリア", "it": "Bulgaria", "zh": "保加利亚", "nl": "Bulgarije", "de": "Bulgarien", "fr": "Bulgarie", "es": "Bulgaria", "en": "Bulgaria", "pt_BR": "Bulgária", "sr-Cyrl": "Бугарска", "sr-Latn": "Bugarska", "zh_TW": "保加利亞", "tr": "Bulgaristan", "ro": "Bulgaria", "ar": "بلغاريا", "fa": "بلغارستان", "yue": "保加利亞"},
    flag: "🇧🇬",
    code: "BG",
    dialCode: "359",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Burkina Faso",
    nameTranslations: {"sk": "Burkina Faso", "se": "Burkina Faso", "pl": "Burkina Faso", "no": "Burkina Faso", "ja": "ブルキナファソ", "it": "Burkina Faso", "zh": "布基纳法索", "nl": "Burkina Faso", "de": "Burkina Faso", "fr": "Burkina Faso", "es": "Burkina Faso", "en": "Burkina Faso", "pt_BR": "Burkina Faso", "sr-Cyrl": "Буркина Фасо", "sr-Latn": "Burkina Faso", "zh_TW": "布吉納法索", "tr": "Burkina Faso", "ro": "Burkina Faso", "ar": "بوركينا فاسو", "fa": "بورکینافاسو", "yue": "布基納法索"},
    flag: "🇧🇫",
    code: "BF",
    dialCode: "226",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Burundi",
    nameTranslations: {"sk": "Burundi", "se": "Burundi", "pl": "Burundi", "no": "Burundi", "ja": "ブルンジ", "it": "Burundi", "zh": "布隆迪", "nl": "Burundi", "de": "Burundi", "fr": "Burundi", "es": "Burundi", "en": "Burundi", "pt_BR": "Burundi", "sr-Cyrl": "Бурунди", "sr-Latn": "Burundi", "zh_TW": "蒲隆地", "tr": "Burundi", "ro": "Burundi", "ar": "بوروندي", "fa": "بوروندی", "yue": "蒲隆地"},
    flag: "🇧🇮",
    code: "BI",
    dialCode: "257",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Cambodia",
    nameTranslations: {"sk": "Kambodža", "se": "Kambodža", "pl": "Kambodża", "no": "Kambodsja", "ja": "カンボジア", "it": "Cambogia", "zh": "柬埔寨", "nl": "Cambodja", "de": "Kambodscha", "fr": "Cambodge", "es": "Camboya", "en": "Cambodia", "pt_BR": "Camboja", "sr-Cyrl": "Камбоџа", "sr-Latn": "Kambodža", "zh_TW": "柬埔寨", "tr": "Kamboçya", "ro": "Cambogia", "ar": "كمبوديا", "fa": "کامبوج", "yue": "柬埔寨"},
    flag: "🇰🇭",
    code: "KH",
    dialCode: "855",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Cameroon",
    nameTranslations: {"sk": "Kamerun", "se": "Kamerun", "pl": "Kamerun", "no": "Kamerun", "ja": "カメルーン", "it": "Camerun", "zh": "喀麦隆", "nl": "Kameroen", "de": "Kamerun", "fr": "Cameroun", "es": "Camerún", "en": "Cameroon", "pt_BR": "Camarões", "sr-Cyrl": "Камерун", "sr-Latn": "Kamerun", "zh_TW": "喀麥隆", "tr": "Kamerun", "ro": "Camerun", "ar": "الكاميرون", "fa": "کامرون", "yue": "喀 麥 隆"},
    flag: "🇨🇲",
    code: "CM",
    dialCode: "237",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Canada",
    nameTranslations: {"sk": "Kanada", "se": "Kanáda", "pl": "Kanada", "no": "Canada", "ja": "カナダ", "it": "Canada", "zh": "加拿大", "nl": "Canada", "de": "Kanada", "fr": "Canada", "es": "Canadá", "en": "Canada", "pt_BR": "Canadá", "sr-Cyrl": "Канада", "sr-Latn": "Kanada", "zh_TW": "加拿大", "tr": "Kanada", "ro": "Canada", "ar": "كندا", "fa": "کانادا", "yue": "加拿大"},
    flag: "🇨🇦",
    code: "CA",
    dialCode: "1",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Cayman Islands",
    nameTranslations: {"sk": "Kajmanie ostrovy", "se": "Cayman-sullot", "pl": "Kajmany", "no": "Caymanøyene", "ja": "ケイマン諸島", "it": "Isole Cayman", "zh": "开曼群岛", "nl": "Kaaimaneilanden", "de": "Kaimaninseln", "fr": "Îles Caïmans", "es": "Islas Caimán", "en": "Cayman Islands", "pt_BR": "Ilhas Cayman", "sr-Cyrl": "Кајманска Острва", "sr-Latn": "Kajmanska Ostrva", "zh_TW": "開曼群島", "tr": "Cayman Adaları", "ro": "Insulele Cayman", "ar": "جزر كايمان", "fa": "جزایر کیمن", "yue": "開曼群島"},
    flag: "🇰🇾",
    code: "KY",
    dialCode: "345",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Central African Republic",
    nameTranslations: {"sk": "Stredoafrická republika", "se": "Gaska-Afrihká dásseváldi", "pl": "Republika Środkowoafrykańska", "no": "Den sentralafrikanske republikk", "ja": "中央アフリカ共和国", "it": "Repubblica Centrafricana", "zh": "中非共和国", "nl": "Centraal-Afrikaanse Republiek", "de": "Zentralafrikanische Republik", "fr": "République centrafricaine", "es": "República Centroafricana", "en": "Central African Republic", "pt_BR": "República Centro-Africana", "sr-Cyrl": "Централноафричка Република", "sr-Latn": "Centralnoafrička Republika", "zh_TW": "中非共和國", "tr": "Orta Afrika Cumhuriyeti", "ro": "Republica Centrafricană", "ar": "جمهورية أفريقيا الوسطى", "fa": "جمهوری افریقای مرکزی", "yue": "中非共和國"},
    flag: "🇨🇫",
    code: "CF",
    dialCode: "236",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Chad",
    nameTranslations: {"sk": "Čad", "se": "Tčad", "pl": "Czad", "no": "Tsjad", "ja": "チャド", "it": "Ciad", "zh": "乍得", "nl": "Tsjaad", "de": "Tschad", "fr": "Tchad", "es": "Chad", "en": "Chad", "pt_BR": "Chade", "sr-Cyrl": "Чад", "sr-Latn": "Čad", "zh_TW": "查德", "tr": "Çad", "ro": "Ciad", "ar": "تشاد", "fa": "چاد", "yue": "乍得"},
    flag: "🇹🇩",
    code: "TD",
    dialCode: "235",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Chile",
    nameTranslations: {"sk": "Čile", "se": "Čiile", "pl": "Chile", "no": "Chile", "ja": "チリ", "it": "Cile", "zh": "智利", "nl": "Chili", "de": "Chile", "fr": "Chili", "es": "Chile", "en": "Chile", "pt_BR": "Chile", "sr-Cyrl": "Чиле", "sr-Latn": "Čile", "zh_TW": "智利", "tr": "Şili", "ro": "Chile", "ar": "تشيلي", "fa": "شیلی", "yue": "智利"},
    flag: "🇨🇱",
    code: "CL",
    dialCode: "56",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "China",
    nameTranslations: {"sk": "Čína", "se": "Kiinná", "pl": "Chiny", "no": "Kina", "ja": "中国", "it": "Cina", "zh": "中国", "nl": "China", "de": "China", "fr": "Chine", "es": "China", "en": "China", "pt_BR": "China", "sr-Cyrl": "Кина", "sr-Latn": "Kina", "zh_TW": "中國", "tr": "Çin", "ro": "China", "ar": "الصين", "fa": "چین", "yue": "中國"},
    flag: "🇨🇳",
    code: "CN",
    dialCode: "86",
    minLength: 11,
    maxLength: 12,
  ),
  Country(
    name: "Christmas Island",
    nameTranslations: {"sk": "Vianočný ostrov", "se": "Juovllat-sullot", "pl": "Wyspa Bożego Narodzenia", "no": "Christmasøya", "ja": "クリスマス島", "it": "Isola Christmas", "zh": "圣诞岛", "nl": "Christmaseiland", "de": "Weihnachtsinsel", "fr": "Île Christmas", "es": "Isla de Navidad", "en": "Christmas Island", "pt_BR": "Ilha do Natal", "sr-Cyrl": "Ускршња Острва", "sr-Latn": "Uskršnja Ostrva", "zh_TW": "聖誕島", "tr": "Christmas Adası", "ro": "Insula Crăciunului", "ar": "جزيرة عيد الميلاد", "fa": "جزیره کریسمس", "yue": "聖誕島"},
    flag: "🇨🇽",
    code: "CX",
    dialCode: "61",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Cocos (Keeling) Islands",
    nameTranslations: {"sk": "Kokosové ostrovy", "se": "Cocos-sullot", "pl": "Wyspy Kokosowe", "no": "Kokosøyene", "ja": "ココス(キーリング)諸島", "it": "Isole Cocos (Keeling)", "zh": "科科斯（基林）群岛", "nl": "Cocoseilanden", "de": "Kokosinseln", "fr": "Îles Cocos", "es": "Islas Cocos", "en": "Cocos (Keeling) Islands", "pt_BR": "Ilhas Cocos (Keeling)", "sr-Cyrl": "Кокосова Острва", "sr-Latn": "Kokosova Ostrva", "zh_TW": "科科斯（基林）群島", "tr": "Cocos (Keyling) Adaları", "ro": "Insulele Cocos", "ar": "جزر كوكوس", "fa": "جزایر کوکوس", "yue": "可可島（基林）群島"},
    flag: "🇨🇨",
    code: "CC",
    dialCode: "61",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Colombia",
    nameTranslations: {"sk": "Kolumbia", "se": "Kolombia", "pl": "Kolumbia", "no": "Colombia", "ja": "コロンビア", "it": "Colombia", "zh": "哥伦比亚", "nl": "Colombia", "de": "Kolumbien", "fr": "Colombie", "es": "Colombia", "en": "Colombia", "pt_BR": "Colômbia", "sr-Cyrl": "Колумбија", "sr-Latn": "Kolumbija", "zh_TW": "哥倫比亞", "tr": "Kolombiya", "ro": "Columbia", "ar": "كولومبيا", "fa": "کلمبیا", "yue": "哥倫比亞"},
    flag: "🇨🇴",
    code: "CO",
    dialCode: "57",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Comoros",
    nameTranslations: {"sk": "Komory", "se": "Komoros", "pl": "Komory", "no": "Komorene", "ja": "コモロ", "it": "Comore", "zh": "科摩罗", "nl": "Comoren", "de": "Komoren", "fr": "Comores", "es": "Comoras", "en": "Comoros", "pt_BR": "Comores", "sr-Cyrl": "Комори", "sr-Latn": "Komori", "zh_TW": "科摩羅", "tr": "Komor Adaları", "ro": "Comore", "ar": "جزر القمر", "fa": "جزیره کومور", "yue": "科摩羅"},
    flag: "🇰🇲",
    code: "KM",
    dialCode: "269",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Congo",
    nameTranslations: {"sk": "Konžská republika", "se": "Kongo-Brazzaville", "pl": "Kongo", "no": "Kongo-Brazzaville", "ja": "コンゴ共和国(ブラザビル)", "it": "Congo-Brazzaville", "zh": "刚果（布）", "nl": "Congo-Brazzaville", "de": "Kongo-Brazzaville", "fr": "Congo-Brazzaville", "es": "Congo", "en": "Congo - Brazzaville", "pt_BR": "República do Congo", "sr-Cyrl": "Република Конго", "sr-Latn": "Republika Kongo", "zh_TW": "剛果共和國（布拉柴維爾）", "tr": "Kongo Cumhuriyeti", "ro": "Republica Congo", "ar": "جمهورية الكونغو", "fa": "جمهوری کنگو", "yue": "剛果（共和國）"},
    flag: "🇨🇬",
    code: "CG",
    dialCode: "242",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Congo, The Democratic Republic of the Congo",
    nameTranslations: {"sk": "Konžská demokratická republika", "se": "Kongo-Kinshasa", "pl": "Demokratyczna Republika Konga", "no": "Kongo-Kinshasa", "ja": "コンゴ民主共和国(キンシャサ)", "it": "Congo - Kinshasa", "zh": "刚果（金）", "nl": "Congo-Kinshasa", "de": "Kongo-Kinshasa", "fr": "Congo-Kinshasa", "es": "República Democrática del Congo", "en": "Congo - Kinshasa", "pt_BR": "República Democrática do Congo", "sr-Cyrl": "Демократска Република Конго", "sr-Latn": "Demokratska Republika Kongo", "zh_TW": "剛果民主共和國（金沙薩）", "tr": "Kongo Demokratik Cumhuriyeti", "ro": "Republica Democrată Congo", "ar": "جمهورية الكونغو الديمقراطية", "fa": "جمهوری دموکراتیک کنگو", "yue": "剛果（金）"},
    flag: "🇨🇩",
    code: "CD",
    dialCode: "243",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Cook Islands",
    nameTranslations: {"sk": "Cookove ostrovy", "se": "Cook-sullot", "pl": "Wyspy Cooka", "no": "Cookøyene", "ja": "クック諸島", "it": "Isole Cook", "zh": "库克群岛", "nl": "Cookeilanden", "de": "Cookinseln", "fr": "Îles Cook", "es": "Islas Cook", "en": "Cook Islands", "pt_BR": "Ilhas Cook", "sr-Cyrl": "Кукова Острва", "sr-Latn": "Kukova Ostrva", "zh_TW": "庫克群島", "tr": "Cook Adaları", "ro": "Insulele Cook", "ar": "جزر كوك", "fa": "جزایر کوک", "yue": "庫克群島"},
    flag: "🇨🇰",
    code: "CK",
    dialCode: "682",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Costa Rica",
    nameTranslations: {"sk": "Kostarika", "se": "Costa Rica", "pl": "Kostaryka", "no": "Costa Rica", "ja": "コスタリカ", "it": "Costa Rica", "zh": "哥斯达黎加", "nl": "Costa Rica", "de": "Costa Rica", "fr": "Costa Rica", "es": "Costa Rica", "en": "Costa Rica", "pt_BR": "Costa Rica", "sr-Cyrl": "Коста Рика", "sr-Latn": "Kosta Rika", "zh_TW": "哥斯大黎加", "tr": "Kosta Rika", "ro": "Costa Rica", "ar": "كوستاريكا", "fa": "کاستاریکا", "yue": "哥斯達黎加"},
    flag: "🇨🇷",
    code: "CR",
    dialCode: "506",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Côte d'Ivoire",
    nameTranslations: {"sk": "Pobrežie Slonoviny", "se": "Elfenbenariddu", "pl": "Côte d'Ivoire", "no": "Elfenbenskysten", "ja": "コートジボワール", "it": "Costa d'Avorio", "zh": "科特迪瓦", "nl": "Ivoorkust", "de": "Côte d'Ivoire", "fr": "Côte d'Ivoire", "es": "Côte d'Ivoire", "en": "Côte d'Ivoire", "pt_BR": "Côte d'Ivoire", "sr-Cyrl": "Обала Слоноваче", "sr-Latn": "Obala Slonovače", "zh_TW": "象牙海岸", "tr": "Fildişi Kıyısı", "ro": "Coasta de fildeș", "ar": "ساحل العاج", "fa": "ساحل عاج", "yue": "科特迪瓦"},
    flag: "🇨🇮",
    code: "CI",
    dialCode: "225",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Croatia",
    nameTranslations: {"sk": "Chorvátsko", "se": "Kroátia", "pl": "Chorwacja", "no": "Kroatia", "ja": "クロアチア", "it": "Croazia", "zh": "克罗地亚", "nl": "Kroatië", "de": "Kroatien", "fr": "Croatie", "es": "Croacia", "en": "Croatia", "pt_BR": "Croácia", "sr-Cyrl": "Хрватска", "sr-Latn": "Hrvatska", "zh_TW": "克羅埃西亞", "tr": "Hırvatistan", "ro": "Croația", "ar": "كرواتيا", "fa": "کرواسی", "yue": "克羅地亞"},
    flag: "🇭🇷",
    code: "HR",
    dialCode: "385",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Cuba",
    nameTranslations: {"sk": "Kuba", "se": "Kuba", "pl": "Kuba", "no": "Cuba", "ja": "キューバ", "it": "Cuba", "zh": "古巴", "nl": "Cuba", "de": "Kuba", "fr": "Cuba", "es": "Cuba", "en": "Cuba", "pt_BR": "Cuba", "sr-Cyrl": "Куба", "sr-Latn": "Kuba", "zh_TW": "古巴", "tr": "Küba", "ro": "Cuba", "ar": "كوبا", "fa": "كوبا", "yue": "古巴"},
    flag: "🇨🇺",
    code: "CU",
    dialCode: "53",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Cyprus",
    nameTranslations: {"sk": "Cyprus", "se": "Kypros", "pl": "Cypr", "no": "Kypros", "ja": "キプロス", "it": "Cipro", "zh": "塞浦路斯", "nl": "Cyprus", "de": "Zypern", "fr": "Chypre", "es": "Chipre", "en": "Cyprus", "pt_BR": "Chipre", "sr-Cyrl": "Кипар", "sr-Latn": "Kipar", "zh_TW": "塞普勒斯", "tr": "Kıbrıs", "ro": "Cipru", "ar": "قبرص", "fa": "قبرس", "yue": "塞浦路斯"},
    flag: "🇨🇾",
    code: "CY",
    dialCode: "357",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Czech Republic",
    nameTranslations: {"sk": "Česko", "se": "Čeahkka", "pl": "Czechy", "no": "Tsjekkia", "ja": "チェコ", "it": "Cechia", "zh": "捷克", "nl": "Tsjechië", "de": "Tschechien", "fr": "Tchéquie", "es": "Chequia", "en": "Czechia", "pt_BR": "Czechia", "sr-Cyrl": "Чешка", "sr-Latn": "Češka", "zh_TW": "捷克", "tr": "Çek Cumhuriyeti", "ro": "Cehia", "ar": "جمهورية التشيك", "fa": "جمهوری چک", "yue": "捷克共和國"},
    flag: "🇨🇿",
    code: "CZ",
    dialCode: "420",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Denmark",
    nameTranslations: {"sk": "Dánsko", "se": "Dánmárku", "pl": "Dania", "no": "Danmark", "ja": "デンマーク", "it": "Danimarca", "zh": "丹麦", "nl": "Denemarken", "de": "Dänemark", "fr": "Danemark", "es": "Dinamarca", "en": "Denmark", "pt_BR": "Dinamarca", "sr-Cyrl": "Данска", "sr-Latn": "Danska", "zh_TW": "丹麥", "tr": "Danimarka", "ro": "Danemarca", "ar": "الدنمارك", "fa": "دانمارک", "yue": "丹麥"},
    flag: "🇩🇰",
    code: "DK",
    dialCode: "45",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Djibouti",
    nameTranslations: {"sk": "Džibutsko", "se": "Djibouti", "pl": "Dżibuti", "no": "Djibouti", "ja": "ジブチ", "it": "Gibuti", "zh": "吉布提", "nl": "Djibouti", "de": "Dschibuti", "fr": "Djibouti", "es": "Yibuti", "en": "Djibouti", "pt_BR": "Djibouti", "sr-Cyrl": "Џибути", "sr-Latn": "Džibuti", "zh_TW": "吉布地", "tr": "Cibuti", "ro": "Djibouti", "ar": "جيبوتي", "fa": "جیبوتی", "yue": "吉布提"},
    flag: "🇩🇯",
    code: "DJ",
    dialCode: "253",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Dominica",
    nameTranslations: {"sk": "Dominika", "se": "Dominica", "pl": "Dominika", "no": "Dominica", "ja": "ドミニカ国", "it": "Dominica", "zh": "多米尼克", "nl": "Dominica", "de": "Dominica", "fr": "Dominique", "es": "Dominica", "en": "Dominica", "pt_BR": "Dominica", "sr-Cyrl": "Доминика", "sr-Latn": "Dominika", "zh_TW": "多明尼加", "tr": "Dominika", "ro": "Dominica", "ar": "دومينيكا", "fa": "دومينيكا", "yue": "多米尼加"},
    flag: "🇩🇲",
    code: "DM",
    dialCode: "1767",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Dominican Republic",
    nameTranslations: {"sk": "Dominikánska republika", "se": "Dominikána dásseváldi", "pl": "Dominikana", "no": "Den dominikanske republikk", "ja": "ドミニカ共和国", "it": "Repubblica Dominicana", "zh": "多米尼加共和国", "nl": "Dominicaanse Republiek", "de": "Dominikanische Republik", "fr": "République dominicaine", "es": "República Dominicana", "en": "Dominican Republic", "pt_BR": "República Dominicana", "sr-Cyrl": "Доминиканска Република", "sr-Latn": "Dominikanska Republika", "zh_TW": "多明尼加共和國", "tr": "Dominik Cumhuriyeti", "ro": "Republica Dominicană", "ar": "جمهورية الدومينيكان", "fa": "جمهوری دومنیکن", "yue": "多明尼加共和國"},
    flag: "🇩🇴",
    code: "DO",
    dialCode: "1",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Ecuador",
    nameTranslations: {"sk": "Ekvádor", "se": "Ecuador", "pl": "Ekwador", "no": "Ecuador", "ja": "エクアドル", "it": "Ecuador", "zh": "厄瓜多尔", "nl": "Ecuador", "de": "Ecuador", "fr": "Équateur", "es": "Ecuador", "en": "Ecuador", "pt_BR": "Equador", "sr-Cyrl": "Еквадор", "sr-Latn": "Ekvador", "zh_TW": "厄瓜多", "tr": "Ekvador", "ro": "Ecuador", "ar": "الإكوادور", "fa": "اكوادور", "yue": "厄瓜多爾"},
    flag: "🇪🇨",
    code: "EC",
    dialCode: "593",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Egypt",
    nameTranslations: {"sk": "Egypt", "se": "Egypt", "pl": "Egipt", "no": "Egypt", "ja": "エジプト", "it": "Egitto", "zh": "埃及", "nl": "Egypt", "de": "Ägypt", "fr": "Égypte", "es": "Egipt", "en": "Egypt", "pt_BR": "Egito", "sr-Cyrl": "Египат", "sr-Latn": "Egipat", "zh_TW": "埃及", "tr": "Mısır", "ro": "Egipt", "ar": "مصر", "fa": "مصر", "yue": "埃及"},
    flag: "🇪🇬",
    code: "EG",
    dialCode: "20",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "El Salvador",
    nameTranslations: {"sk": "Salvádor", "se": "El Salvador", "pl": "Salwador", "no": "El Salvador", "ja": "エルサルバドル", "it": "El Salvador", "zh": "萨尔瓦多", "nl": "El Salvador", "de": "El Salvador", "fr": "Salvador", "es": "El Salvador", "en": "El Salvador", "pt_BR": "El Salvador", "sr-Cyrl": "Салвадор", "sr-Latn": "Salvador", "zh_TW": "薩爾瓦多", "tr": "El Salvador", "ro": "Salvador", "ar": "السلفادور", "fa": "ال سالوادور", "yue": "薩爾瓦多"},
    flag: "🇸🇻",
    code: "SV",
    dialCode: "503",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Equatorial Guinea",
    nameTranslations: {"sk": "Rovníková Guinea", "se": "Ekvatoriála Guinea", "pl": "Gwinea Równikowa", "no": "Ekvatorial-Guinea", "ja": "赤道ギニア", "it": "Guinea Equatoriale", "zh": "赤道几内亚", "nl": "Equatoriaal-Guinea", "de": "Äquatorialguinea", "fr": "Guinée équatoriale", "es": "Guinea Ecuatorial", "en": "Equatorial Guinea", "pt_BR": "Guiné Equatorial", "sr-Cyrl": "Екваторијална Гвинеја", "sr-Latn": "Ekvatorijalna Gvineja", "zh_TW": "赤道幾內亞", "tr": "Ekvator Ginesi", "ro": "Guineea Ecuatorială", "ar": "غينيا الاستوائية", "fa": "گینه استوایی", "yue": "赤道幾內亞"},
    flag: "🇬🇶",
    code: "GQ",
    dialCode: "240",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Eritrea",
    nameTranslations: {"sk": "Eritrea", "se": "Eritrea", "pl": "Erytrea", "no": "Eritrea", "ja": "エリトリア", "it": "Eritrea", "zh": "厄立特里亚", "nl": "Eritrea", "de": "Eritrea", "fr": "Érythrée", "es": "Eritrea", "en": "Eritrea", "pt_BR": "Eritreia", "sr-Cyrl": "Еритреја", "sr-Latn": "Eritreja", "zh_TW": "厄立特裡亞", "tr": "Eritre", "ro": "Eritreea", "ar": "إريتريا", "fa": "اریتره", "yue": "厄立特里亞"},
    flag: "🇪🇷",
    code: "ER",
    dialCode: "291",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Estonia",
    nameTranslations: {"sk": "Estónsko", "se": "Estlánda", "pl": "Estonia", "no": "Estland", "ja": "エストニア", "it": "Estonia", "zh": "爱沙尼亚", "nl": "Estland", "de": "Estland", "fr": "Estonie", "es": "Estonia", "en": "Estonia", "pt_BR": "Estônia", "sr-Cyrl": "Естонија", "sr-Latn": "Estonija", "zh_TW": "愛沙尼亞", "tr": "Estonya", "ro": "Estonia", "ar": "إستونيا", "fa": "استونی", "yue": "愛沙尼亞"},
    flag: "🇪🇪",
    code: "EE",
    dialCode: "372",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Ethiopia",
    nameTranslations: {"sk": "Etiópia", "se": "Etiopia", "pl": "Etiopia", "no": "Etiopia", "ja": "エチオピア", "it": "Etiopia", "zh": "埃塞俄比亚", "nl": "Ethiopië", "de": "Äthiopien", "fr": "Éthiopie", "es": "Etiopía", "en": "Ethiopia", "pt_BR": "Etiópia", "sr-Cyrl": "Етиопија", "sr-Latn": "Etiopija", "zh_TW": "伊索比亞", "tr": "Etiyopya", "ro": "Etiopia", "ar": "إثيوبيا", "fa": "اتیوپی", "yue": "埃塞俄比亞"},
    flag: "🇪🇹",
    code: "ET",
    dialCode: "251",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Falkland Islands (Malvinas)",
    nameTranslations: {"sk": "Falklandy", "se": "Falklandsullot", "pl": "Falklandy", "no": "Falklandsøyene", "ja": "フォークランド諸島", "it": "Isole Falkland", "zh": "福克兰群岛", "nl": "Falklandeilanden", "de": "Falklandinseln", "fr": "Îles Malouines", "es": "Islas Malvinas", "en": "Falkland Islands", "pt_BR": "Ilhas Falkland", "sr-Cyrl": "Фокландска Острва", "sr-Latn": "Foklandska Ostrva", "zh_TW": "福克蘭群島", "tr": "Falkland Adaları", "ro": "Insulele Falklands", "ar": "جزر فوكلاند", "fa": "جزایر فالکلند", "yue": "福克蘭群島（馬爾維納斯群島）"},
    flag: "🇫🇰",
    code: "FK",
    dialCode: "500",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Faroe Islands",
    nameTranslations: {"sk": "Faerské ostrovy", "se": "Fearsullot", "pl": "Wyspy Owcze", "no": "Færøyene", "ja": "フェロー諸島", "it": "Isole Fær Øer", "zh": "法罗群岛", "nl": "Faeröer", "de": "Färöer", "fr": "Îles Féroé", "es": "Islas Feroe", "en": "Faroe Islands", "pt_BR": "ilhas Faroe", "sr-Cyrl": "Фарска Острва", "sr-Latn": "Farska Ostrva", "zh_TW": "法羅群島", "tr": "Faroe Adaları", "ro": "Insulele Feroe", "ar": "جزر فارو", "fa": "جزایر فارو", "yue": "法羅群島"},
    flag: "🇫🇴",
    code: "FO",
    dialCode: "298",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Fiji",
    nameTranslations: {"sk": "Fidži", "se": "Fijisullot", "pl": "Fidżi", "no": "Fiji", "ja": "フィジー", "it": "Figi", "zh": "斐济", "nl": "Fiji", "de": "Fidschi", "fr": "Fidji", "es": "Fiyi", "en": "Fiji", "pt_BR": "Fiji", "sr-Cyrl": "Фиџи", "sr-Latn": "Fidži", "zh_TW": "斐濟", "tr": "Fiji", "ro": "Fiji", "ar": "فيجي", "fa": "فيجي", "yue": "斐濟"},
    flag: "🇫🇯",
    code: "FJ",
    dialCode: "679",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Finland",
    nameTranslations: {"sk": "Fínsko", "se": "Suopma", "pl": "Finlandia", "no": "Finland", "ja": "フィンランド", "it": "Finlandia", "zh": "芬兰", "nl": "Finland", "de": "Finnland", "fr": "Finlande", "es": "Finlandia", "en": "Finland", "pt_BR": "Finlândia", "sr-Cyrl": "Финска", "sr-Latn": "Finska", "zh_TW": "芬蘭", "tr": "Finlandiya", "ro": "Finlanda", "ar": "فنلندا", "fa": "فنلاند", "yue": "芬蘭"},
    flag: "🇫🇮",
    code: "FI",
    dialCode: "358",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "France",
    nameTranslations: {"sk": "Francúzsko", "se": "Frankriika", "pl": "Francja", "no": "Frankrike", "ja": "フランス", "it": "Francia", "zh": "法国", "nl": "Frankrijk", "de": "Frankreich", "fr": "France", "es": "Francia", "en": "France", "pt_BR": "França", "sr-Cyrl": "Француска", "sr-Latn": "Francuska", "zh_TW": "法國", "tr": "Fransa", "ro": "Franța", "ar": "فرنسا", "fa": "فرانسه", "yue": "法國"},
    flag: "🇫🇷",
    code: "FR",
    dialCode: "33",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "French Guiana",
    nameTranslations: {"sk": "Francúzska Guyana", "se": "Frankriikka Guayana", "pl": "Gujana Francuska", "no": "Fransk Guyana", "ja": "仏領ギアナ", "it": "Guyana francese", "zh": "法属圭亚那", "nl": "Frans-Guyana", "de": "Französisch-Guayana", "fr": "Guyane française", "es": "Guayana Francesa", "en": "French Guiana", "pt_BR": "Guiana Francesa", "sr-Cyrl": "Француска Гвајана", "sr-Latn": "Francuska Gvajana", "zh_TW": "法屬蓋亞那", "tr": "Fransız Guyanası", "ro": "Guiana Franceză", "ar": "غويانا الفرنسية", "fa": "گویان فرانسه", "yue": "法屬圭亞那"},
    flag: "🇬🇫",
    code: "GF",
    dialCode: "594",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "French Polynesia",
    nameTranslations: {"sk": "Francúzska Polynézia", "se": "Frankriikka Polynesia", "pl": "Polinezja Francuska", "no": "Fransk Polynesia", "ja": "仏領ポリネシア", "it": "Polinesia francese", "zh": "法属波利尼西亚", "nl": "Frans-Polynesië", "de": "Französisch-Polynesien", "fr": "Polynésie française", "es": "Polinesia Francesa", "en": "French Polynesia", "pt_BR": "Polinésia Francesa", "sr-Cyrl": "Француска Полинезија", "sr-Latn": "Francuska Polinezija", "zh_TW": "法屬玻里尼西亞", "tr": "Fransız Polinezyası", "ro": "Polinezia Franceză", "ar": "بولينزيا الفرنسية", "fa": "پلی‌نزی فرانسه", "yue": "法屬波利尼西亞"},
    flag: "🇵🇫",
    code: "PF",
    dialCode: "689",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "French Southern Territories",
    nameTranslations: {"sk": "Francúzske južné a antarktické územia", "se": "French Southern Territories", "pl": "Francuskie Terytoria Południowe i Antarktyczne", "no": "De franske sørterritorier", "ja": "仏領極南諸島", "it": "Terre australi francesi", "zh": "法属南部领地", "nl": "Franse Gebieden in de zuidelijke Indische Oceaan", "de": "Französische Süd- und Antarktisgebiete", "fr": "Terres australes françaises", "es": "Territorios Australes Franceses", "en": "French Southern Territories", "pt_BR": "Territórios Franceses do Sul", "sr-Cyrl": "Француске јужне и антарктичке земље", "sr-Latn": "Francuske južne i antarktičke zemlje", "zh_TW": "法屬南部屬地", "tr": "Fransız Güney ve Antarktika Toprakları", "ro": "Teritoriile australe și antarctice franceze", "ar": "أراض فرنسية جنوبية وأنتارتيكية", "fa": "سرزمین‌های جنوبی فرانسه", "yue": "法國南部領土"},
    flag: "🇹🇫",
    code: "TF",
    dialCode: "262",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Gabon",
    nameTranslations: {"sk": "Gabon", "se": "Gabon", "pl": "Gabon", "no": "Gabon", "ja": "ガボン", "it": "Gabon", "zh": "加蓬", "nl": "Gabon", "de": "Gabun", "fr": "Gabon", "es": "Gabón", "en": "Gabon", "pt_BR": "Gabão", "sr-Cyrl": "Габон", "sr-Latn": "Gabon", "zh_TW": "加彭", "tr": "Gabon", "ro": "Gabon", "ar": "الغابون", "fa": "گابن", "yue": "加蓬"},
    flag: "🇬🇦",
    code: "GA",
    dialCode: "241",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Gambia",
    nameTranslations: {"sk": "Gambia", "se": "Gámbia", "pl": "Gambia", "no": "Gambia", "ja": "ガンビア", "it": "Gambia", "zh": "冈比亚", "nl": "Gambia", "de": "Gambia", "fr": "Gambie", "es": "Gambia", "en": "Gambia", "pt_BR": "Gâmbia", "sr-Cyrl": "Гамбија", "sr-Latn": "Gambija", "zh_TW": "岡比亞", "tr": "Gambiya", "ro": "Gambia", "ar": "غامبيا", "fa": "گامبیا", "yue": "岡比亞"},
    flag: "🇬🇲",
    code: "GM",
    dialCode: "220",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Georgia",
    nameTranslations: {"sk": "Gruzínsko", "se": "Georgia", "pl": "Gruzja", "no": "Georgia", "ja": "ジョージア", "it": "Georgia", "zh": "格鲁吉亚", "nl": "Georgië", "de": "Georgien", "fr": "Géorgie", "es": "Georgia", "en": "Georgia", "pt_BR": "Georgia", "sr-Cyrl": "Грузија", "sr-Latn": "Gruzija", "zh_TW": "喬治亞", "tr": "Gürcistan", "ro": "Georgia", "ar": "جورجيا", "fa": "گرجستان", "yue": "格魯吉亞"},
    flag: "🇬🇪",
    code: "GE",
    dialCode: "995",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Germany",
    nameTranslations: {"sk": "Nemecko", "se": "Duiska", "pl": "Niemcy", "no": "Tyskland", "ja": "ドイツ", "it": "Germania", "zh": "德国", "nl": "Duitsland", "de": "Deutschland", "fr": "Allemagne", "es": "Alemania", "en": "Germany", "pt_BR": "Alemanha", "sr-Cyrl": "Немачка", "sr-Latn": "Nemačka", "zh_TW": "德國", "tr": "Almanya", "ro": "Germania", "ar": "ألمانيا", "fa": "آلمان", "yue": "德國"},
    flag: "🇩🇪",
    code: "DE",
    dialCode: "49",
    minLength: 9,
    maxLength: 13,
  ),
  Country(
    name: "Ghana (GH)",
    nameTranslations: {"sk": "Ghana", "se": "Ghana", "pl": "Ghana", "no": "Ghana", "ja": "ガーナ", "it": "Ghana", "zh": "加纳", "nl": "Ghana", "de": "Ghana", "fr": "Ghana", "es": "Ghana", "en": "Ghana", "pt_BR": "Gana", "sr-Cyrl": "Гана", "sr-Latn": "Gana", "zh_TW": "迦納", "tr": "Gana", "ro": "Ghana", "ar": "غانا", "fa": "غنا", "yue": "加納"},
    flag: "🇬🇭",
    code: "GH",
    dialCode: "233",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Gibraltar",
    nameTranslations: {"sk": "Gibraltár", "se": "Gibraltar", "pl": "Gibraltar", "no": "Gibraltar", "ja": "ジブラルタル", "it": "Gibilterra", "zh": "直布罗陀", "nl": "Gibraltar", "de": "Gibraltar", "fr": "Gibraltar", "es": "Gibraltar", "en": "Gibraltar", "pt_BR": "Gibraltar", "sr-Cyrl": "Гибралтар", "sr-Latn": "Gibraltar", "zh_TW": "直布羅陀", "tr": "Cebelitarık", "ro": "Gibraltar", "ar": "جبل طارق", "fa": "جبل الطارق", "yue": "直布羅陀"},
    flag: "🇬🇮",
    code: "GI",
    dialCode: "350",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Greece",
    nameTranslations: {"sk": "Grécko", "se": "Greika", "pl": "Grecja", "no": "Hellas", "ja": "ギリシャ", "it": "Grecia", "zh": "希腊", "nl": "Griekenland", "de": "Griechenland", "fr": "Grèce", "es": "Grecia", "en": "Greece", "pt_BR": "Grécia", "sr-Cyrl": "Грчка", "sr-Latn": "Grčka", "zh_TW": "希臘", "tr": "Yunanistan", "ro": "Grecia", "ar": "اليونان", "fa": "یونان", "yue": "希臘"},
    flag: "🇬🇷",
    code: "GR",
    dialCode: "30",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Greenland",
    nameTranslations: {"sk": "Grónsko", "se": "Kalaallit Nunaat", "pl": "Grenlandia", "no": "Grønland", "ja": "グリーンランド", "it": "Groenlandia", "zh": "格陵兰", "nl": "Groenland", "de": "Grönland", "fr": "Groenland", "es": "Groenlandia", "en": "Greenland", "pt_BR": "Groenlândia", "sr-Cyrl": "Гренланд", "sr-Latn": "Grenland", "zh_TW": "格陵蘭", "tr": "Grönland", "ro": "Groenlanda", "ar": "جرينلاند", "fa": "گرینلند", "yue": "格陵蘭"},
    flag: "🇬🇱",
    code: "GL",
    dialCode: "299",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Grenada",
    nameTranslations: {"sk": "Grenada", "se": "Grenada", "pl": "Grenada", "no": "Grenada", "ja": "グレナダ", "it": "Grenada", "zh": "格林纳达", "nl": "Grenada", "de": "Grenada", "fr": "Grenade", "es": "Granada", "en": "Grenada", "pt_BR": "Grenada", "sr-Cyrl": "Гренада", "sr-Latn": "Grenada", "zh_TW": "格林納達", "tr": "Grenada", "ro": "Grenada", "ar": "غرينادا", "fa": "گرنادا", "yue": "格林納達"},
    flag: "🇬🇩",
    code: "GD",
    dialCode: "1473",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guadeloupe",
    nameTranslations: {"sk": "Guadeloupe", "se": "Guadeloupe", "pl": "Gwadelupa", "no": "Guadeloupe", "ja": "グアドループ", "it": "Guadalupa", "zh": "瓜德罗普", "nl": "Guadeloupe", "de": "Guadeloupe", "fr": "Guadeloupe", "es": "Guadalupe", "en": "Guadeloupe", "pt_BR": "Guadalupe", "sr-Cyrl": "Гваделуп", "sr-Latn": "Gvadelup", "zh_TW": "瓜地洛普", "tr": "Guadeloupe", "ro": "Guadelupa", "ar": "غوادلوب", "fa": "گوادلوپ", "yue": "瓜德罗普"},
    flag: "🇬🇵",
    code: "GP",
    dialCode: "590",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Guam",
    nameTranslations: {"sk": "Guam", "se": "Guam", "pl": "Guam", "no": "Guam", "ja": "グアム", "it": "Guam", "zh": "关岛", "nl": "Guam", "de": "Guam", "fr": "Guam", "es": "Guam", "en": "Guam", "pt_BR": "Guam", "sr-Cyrl": "Гвам", "sr-Latn": "Gvam", "zh_TW": "關島", "tr": "Guam", "ro": "Guam", "ar": "غوام", "fa": "گوام", "yue": "關島"},
    flag: "🇬🇺",
    code: "GU",
    dialCode: "1671",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guatemala",
    nameTranslations: {"sk": "Guatemala", "se": "Guatemala", "pl": "Gwatemala", "no": "Guatemala", "ja": "グアテマラ", "it": "Guatemala", "zh": "危地马拉", "nl": "Guatemala", "de": "Guatemala", "fr": "Guatemala", "es": "Guatemala", "en": "Guatemala", "pt_BR": "Guatemala", "sr-Cyrl": "Гватемала", "sr-Latn": "Gvatemala", "zh_TW": "瓜地馬拉", "tr": "Guatemala", "ro": "Guatemala", "ar": "غواتيمالا", "fa": "گواتمالا", "yue": "危地馬拉"},
    flag: "🇬🇹",
    code: "GT",
    dialCode: "502",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Guernsey",
    nameTranslations: {"sk": "Guernsey", "se": "Guernsey", "pl": "Guernsey", "no": "Guernsey", "ja": "ガーンジー", "it": "Guernsey", "zh": "根西岛", "nl": "Guernsey", "de": "Guernsey", "fr": "Guernesey", "es": "Guernsey", "en": "Guernsey", "pt_BR": "Guernsey", "sr-Cyrl": "Гернзи", "sr-Latn": "Gernzi", "zh_TW": "根息島", "tr": "Guernsey", "ro": "Guernsey", "ar": "غيرنزي", "fa": "گرنزی", "yue": "格恩西島"},
    flag: "🇬🇬",
    code: "GG",
    dialCode: "44",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Guinea",
    nameTranslations: {"sk": "Guinea", "se": "Guinea", "pl": "Gwinea", "no": "Guinea", "ja": "ギニア", "it": "Guinea", "zh": "几内亚", "nl": "Guinee", "de": "Guinea", "fr": "Guinée", "es": "Guinea", "en": "Guinea", "pt_BR": "Guiné", "sr-Cyrl": "Гвинеја", "sr-Latn": "Gvineja", "zh_TW": "幾內亞", "tr": "Gine", "ro": "Guinea", "ar": "غينيا", "fa": "گینه", "yue": "幾內亞"},
    flag: "🇬🇳",
    code: "GN",
    dialCode: "224",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Guinea-Bissau",
    nameTranslations: {"sk": "Guinea-Bissau", "se": "Guinea-Bissau", "pl": "Gwinea Bissau", "no": "Guinea-Bissau", "ja": "ギニアビサウ", "it": "Guinea-Bissau", "zh": "几内亚比绍", "nl": "Guinee-Bissau", "de": "Guinea-Bissau", "fr": "Guinée-Bissau", "es": "Guinea-Bisáu", "en": "Guinea-Bissau", "pt_BR": "Guiné-bissau", "sr-Cyrl": "Гвинеја Бисао", "sr-Latn": "Gvineja Bisao", "zh_TW": "幾內亞比索", "tr": "Gine-Bissau", "ro": "Guineea-Bissau", "ar": "غينيا بيساو", "fa": "گینه بیسائو", "yue": "幾內亞比紹"},
    flag: "🇬🇼",
    code: "GW",
    dialCode: "245",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guyana",
    nameTranslations: {"sk": "Guyana", "se": "Guyana", "pl": "Gujana", "no": "Guyana", "ja": "ガイアナ", "it": "Guyana", "zh": "圭亚那", "nl": "Guyana", "de": "Guyana", "fr": "Guyana", "es": "Guyana", "en": "Guyana", "pt_BR": "Guiana", "sr-Cyrl": "Гвајана", "sr-Latn": "Gvajana", "zh_TW": "蓋亞那", "tr": "Guyana", "ro": "Guyana", "ar": "غيانا", "fa": "گویان", "yue": "圭亞那"},
    flag: "🇬🇾",
    code: "GY",
    dialCode: "592",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Haiti",
    nameTranslations: {"sk": "Haiti", "se": "Haiti", "pl": "Haiti", "no": "Haiti", "ja": "ハイチ", "it": "Haiti", "zh": "海地", "nl": "Haïti", "de": "Haiti", "fr": "Haïti", "es": "Haití", "en": "Haiti", "pt_BR": "Haiti", "sr-Cyrl": "Хаити", "sr-Latn": "Haiti", "zh_TW": "海地", "tr": "Haiti", "ro": "Haiti", "ar": "هايتي", "fa": "هائیتی", "yue": "海地"},
    flag: "🇭🇹",
    code: "HT",
    dialCode: "509",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Heard Island and Mcdonald Islands",
    nameTranslations: {"sk": "Heardov ostrov a Macdonaldove ostrovy", "se": "Heard- ja McDonald-sullot", "pl": "Wyspy Heard i McDonalda", "no": "Heard- og McDonaldøyene", "ja": "ハード島・マクドナルド諸島", "it": "Isole Heard e McDonald", "zh": "赫德岛和麦克唐纳群岛", "nl": "Heard en McDonaldeilanden", "de": "Heard und McDonaldinseln", "fr": "Îles Heard et McDonald", "es": "Islas Heard y McDonald", "en": "Heard & McDonald Islands", "pt_BR": "Ilhas Heard e McDonald", "sr-Cyrl": "Острва Херд и Макдоналд", "sr-Latn": "Ostrva Herd i Makdonald", "zh_TW": "赫德暨麥當勞群島", "tr": "Heard Adası ve McDonald Adaları", "ro": "Insula Heard și Insulele McDonald", "ar": "جزيرة هيرد وجزر ماكدونالد", "fa": "جزیره هرد و جزایر مک‌دونالد", "yue": "赫德岛同麦克唐纳群岛"},
    flag: "🇭🇲",
    code: "HM",
    dialCode: "672",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Holy See (Vatican City State)",
    nameTranslations: {"sk": "Vatikán", "se": "Vatikána", "pl": "Watykan", "no": "Vatikanstaten", "ja": "バチカン市国", "it": "Città del Vaticano", "zh": "梵蒂冈", "nl": "Vaticaanstad", "de": "Vatikanstadt", "fr": "État de la Cité du Vatican", "es": "Ciudad del Vaticano", "en": "Vatican City", "pt_BR": "Cidade do Vaticano", "sr-Cyrl": "Ватикан", "sr-Latn": "Vatikan", "zh_TW": "梵蒂岡", "tr": "Vatikan", "ro": "Vatican", "ar": "الفاتيكان", "fa": "واتیکان", "yue": "梵蒂岡城國"},
    flag: "🇻🇦",
    code: "VA",
    dialCode: "379",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Honduras",
    nameTranslations: {"sk": "Honduras", "se": "Honduras", "pl": "Honduras", "no": "Honduras", "ja": "ホンジュラス", "it": "Honduras", "zh": "洪都拉斯", "nl": "Honduras", "de": "Honduras", "fr": "Honduras", "es": "Honduras", "en": "Honduras", "pt_BR": "Honduras", "sr-Cyrl": "Хондурас", "sr-Latn": "Honduras", "zh_TW": "宏都拉斯", "tr": "Honduras", "ro": "Honduras", "ar": "هندوراس", "fa": "هندوراس", "yue": "洪都拉斯"},
    flag: "🇭🇳",
    code: "HN",
    dialCode: "504",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Hong Kong",
    nameTranslations: {"sk": "Hongkong – OAO Číny", "se": "Hongkong", "pl": "SRA Hongkong (Chiny)", "no": "Hongkong S.A.R. Kina", "ja": "中華人民共和国香港特別行政区", "it": "RAS di Hong Kong", "zh": "中国香港特别行政区", "nl": "Hongkong SAR van China", "de": "Sonderverwaltungsregion Hongkong", "fr": "R.A.S. chinoise de Hong Kong", "es": "RAE de Hong Kong (China)", "en": "Hong Kong SAR China", "pt_BR": "RAE de Hong Kong China", "sr-Cyrl": "Хонг Конг", "sr-Latn": "Hong Kong", "zh_TW": "香港", "tr": "Hong Kong", "ro": "Hong Kong", "ar": "هونغ كونغ", "fa": "هنگ کنگ", "yue": "香港"},
    flag: "🇭🇰",
    code: "HK",
    dialCode: "852",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Hungary",
    nameTranslations: {"sk": "Maďarsko", "se": "Ungár", "pl": "Węgry", "no": "Ungarn", "ja": "ハンガリー", "it": "Ungheria", "zh": "匈牙利", "nl": "Hongarije", "de": "Ungarn", "fr": "Hongrie", "es": "Hungría", "en": "Hungary", "pt_BR": "Hungria", "sr-Cyrl": "Мађарска", "sr-Latn": "Mađarska", "zh_TW": "匈牙利", "tr": "Macaristan", "ro": "Ungaria", "ar": "المجر", "fa": "مجارستان", "yue": "匈牙利"},
    flag: "🇭🇺",
    code: "HU",
    dialCode: "36",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Iceland",
    nameTranslations: {"sk": "Island", "se": "Islánda", "pl": "Islandia", "no": "Island", "ja": "アイスランド", "it": "Islanda", "zh": "冰岛", "nl": "IJsland", "de": "Island", "fr": "Islande", "es": "Islandia", "en": "Iceland", "pt_BR": "Islândia", "sr-Cyrl": "Исланд", "sr-Latn": "Island", "zh_TW": "冰島", "tr": "İzlanda", "ro": "Islanda", "ar": "آيسلندا", "fa": "ایسلند", "yue": "冰島"},
    flag: "🇮🇸",
    code: "IS",
    dialCode: "354",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "India",
    nameTranslations: {"sk": "India", "se": "India", "pl": "Indie", "no": "India", "ja": "インド", "it": "India", "zh": "印度", "nl": "India", "de": "Indien", "fr": "Inde", "es": "India", "en": "India", "pt_BR": "Índia", "sr-Cyrl": "Индија", "sr-Latn": "Indija", "zh_TW": "印度", "tr": "Hindistan", "ro": "India", "ar": "الهند", "fa": "هند", "yue": "印度"},
    flag: "🇮🇳",
    code: "IN",
    dialCode: "91",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Indonesia",
    nameTranslations: {"sk": "Indonézia", "se": "Indonesia", "pl": "Indonezja", "no": "Indonesia", "ja": "インドネシア", "it": "Indonesia", "zh": "印度尼西亚", "nl": "Indonesië", "de": "Indonesien", "fr": "Indonésie", "es": "Indonesia", "en": "Indonesia", "pt_BR": "Indonésia", "sr-Cyrl": "Индонезија", "sr-Latn": "Indonezija", "zh_TW": "印尼", "tr": "Endonezya", "ro": "Indonezia", "ar": "إندونيسيا", "fa": "اندونزی", "yue": "印尼"},
    flag: "🇮🇩",
    code: "ID",
    dialCode: "62",
    minLength: 10,
    maxLength: 13,
  ),
  Country(
    name: "Iran, Islamic Republic of Persian Gulf",
    nameTranslations: {"sk": "Irán", "se": "Iran", "pl": "Iran", "no": "Iran", "ja": "イラン", "it": "Iran", "zh": "伊朗", "nl": "Iran", "de": "Iran", "fr": "Iran", "es": "Irán", "en": "Iran", "pt_BR": "Irã", "sr-Cyrl": "Иран", "sr-Latn": "Iran", "zh_TW": "伊朗", "tr": "İran", "ro": "Iran", "ar": "إيران", "fa": "ایران", "yue": "伊朗"},
    flag: "🇮🇷",
    code: "IR",
    dialCode: "98",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Iraq",
    nameTranslations: {"sk": "Irak", "se": "Irak", "pl": "Irak", "no": "Irak", "ja": "イラク", "it": "Iraq", "zh": "伊拉克", "nl": "Irak", "de": "Irak", "fr": "Irak", "es": "Irak", "en": "Iraq", "pt_BR": "Iraque", "sr-Cyrl": "Ирак", "sr-Latn": "Irak", "zh_TW": "伊拉克", "tr": "Irak", "ro": "Irak", "ar": "العراق", "fa": "عراق", "yue": "伊拉克"},
    flag: "🇮🇶",
    code: "IQ",
    dialCode: "964",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Ireland",
    nameTranslations: {"sk": "Írsko", "se": "Irlánda", "pl": "Irlandia", "no": "Irland", "ja": "アイルランド", "it": "Irlanda", "zh": "爱尔兰", "nl": "Ierland", "de": "Irland", "fr": "Irlande", "es": "Irlanda", "en": "Ireland", "pt_BR": "Irlanda", "sr-Cyrl": "Ирска", "sr-Latn": "Irska", "zh_TW": "愛爾蘭", "tr": "İrlanda", "ro": "Irlanda", "ar": "أيرلندا", "fa": "ایرلند", "yue": "愛爾蘭"},
    flag: "🇮🇪",
    code: "IE",
    dialCode: "353",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "Isle of Man",
    nameTranslations: {"sk": "Ostrov Man", "se": "Mann-sullot", "pl": "Wyspa Man", "no": "Man", "ja": "マン島", "it": "Isola di Man", "zh": "马恩岛", "nl": "Isle of Man", "de": "Isle of Man", "fr": "Île de Man", "es": "Isla de Man", "en": "Isle of Man", "pt_BR": "Ilha de Man", "sr-Cyrl": "Острво Мен", "sr-Latn": "Ostrvo Men", "zh_TW": "曼島", "tr": "Man Adası", "ro": "Insula Man", "ar": "جزيرة مان", "fa": "جزیره مان", "yue": "马伊岛"},
    flag: "🇮🇲",
    code: "IM",
    dialCode: "44",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Israel",
    nameTranslations: {"sk": "Izrael", "se": "Israel", "pl": "Izrael", "no": "Israel", "ja": "イスラエル", "it": "Israele", "zh": "以色列", "nl": "Israël", "de": "Israel", "fr": "Israël", "es": "Israel", "en": "Israel", "pt_BR": "Israel", "sr-Cyrl": "Израел", "sr-Latn": "Izrael", "zh_TW": "以色列", "tr": "İsrail", "ro": "Israel", "ar": "إسرائيل", "fa": "إسرائيل", "yue": "以色列"},
    flag: "🇮🇱",
    code: "IL",
    dialCode: "972",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Campione d'Italia",
    nameTranslations: {"sk": "Taliansko", "se": "Itália", "pl": "Włochy", "no": "Italia", "ja": "イタリア", "it": "Italia", "zh": "意大利", "nl": "Italië", "de": "Italien", "fr": "Italie", "es": "Italia", "en": "Italy", "pt_BR": "Itália", "sr-Cyrl": "Италија", "sr-Latn": "Italija", "zh_TW": "義大利", "tr": "İtalya", "ro": "Italia", "ar": "إيطاليا", "fa": "ایتالیا", "yue": "意大利"},
    flag: "🇮🇹",
    code: "IT",
    dialCode: "39",
    minLength: 9,
    maxLength: 10,
  ),
  Country(
    name: "Jamaica",
    nameTranslations: {"sk": "Jamajka", "se": "Jamaica", "pl": "Jamajka", "no": "Jamaica", "ja": "ジャマイカ", "it": "Giamaica", "zh": "牙买加", "nl": "Jamaica", "de": "Jamaika", "fr": "Jamaïque", "es": "Jamaica", "en": "Jamaica", "pt_BR": "Jamaica", "sr-Cyrl": "Јамајка", "sr-Latn": "Jamajka", "zh_TW": "牙買加", "tr": "Jamaika", "ro": "Jamaica", "ar": "جامايكا", "fa": "جامائیکا", "yue": "牙買加"},
    flag: "🇯🇲",
    code: "JM",
    dialCode: "1876",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Japan",
    nameTranslations: {"sk": "Japonsko", "se": "Japána", "pl": "Japonia", "no": "Japan", "ja": "日本", "it": "Giappone", "zh": "日本", "nl": "Japan", "de": "Japan", "fr": "Japon", "es": "Japón", "en": "Japan", "pt_BR": "Japão", "sr-Cyrl": "Јапан", "sr-Latn": "Japan", "zh_TW": "日本", "tr": "Japonya", "ro": "Japonia", "ar": "اليابان", "fa": "ژاپن", "yue": "日本"},
    flag: "🇯🇵",
    code: "JP",
    dialCode: "81",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Jersey",
    nameTranslations: {"sk": "Jersey", "se": "Jersey", "pl": "Jersey", "no": "Jersey", "ja": "ジャージー", "it": "Jersey", "zh": "泽西岛", "nl": "Jersey", "de": "Jersey", "fr": "Jersey", "es": "Jersey", "en": "Jersey", "pt_BR": "Jersey", "sr-Cyrl": "Џерзи", "sr-Latn": "Džerzi", "zh_TW": "澤西", "tr": "Jersey", "ro": "Jersey", "ar": "جيرزي", "fa": "جرزی", "yue": "澤西"},
    flag: "🇯🇪",
    code: "JE",
    dialCode: "44",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Jordan",
    nameTranslations: {"sk": "Jordánsko", "se": "Jordánia", "pl": "Jordania", "no": "Jordan", "ja": "ヨルダン", "it": "Giordania", "zh": "约旦", "nl": "Jordanië", "de": "Jordanien", "fr": "Jordanie", "es": "Jordania", "en": "Jordan", "pt_BR": "Jordânia", "sr-Cyrl": "Јордан", "sr-Latn": "Jordan", "zh_TW": "約旦", "tr": "Mavera-i Ürdün", "ro": "Iordania", "ar": "الأردن", "fa": "اردن", "yue": "約旦"},
    flag: "🇯🇴",
    code: "JO",
    dialCode: "962",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Kazakhstan",
    nameTranslations: {"sk": "Kazachstan", "se": "Kasakstan", "pl": "Kazachstan", "no": "Kasakhstan", "ja": "カザフスタン", "it": "Kazakistan", "zh": "哈萨克斯坦", "nl": "Kazachstan", "de": "Kasachstan", "fr": "Kazakhstan", "es": "Kazajistán", "en": "Kazakhstan", "pt_BR": "Cazaquistão", "sr-Cyrl": "Казахстан", "sr-Latn": "Kazahstan", "zh_TW": "哈薩克", "tr": "Kazakistan", "ro": "Kazahstan", "ar": "كازاخستان", "fa": "قزاقستان", "yue": "哈薩克斯坦"},
    flag: "🇰🇿",
    code: "KZ",
    dialCode: "7",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Kenya",
    nameTranslations: {"sk": "Keňa", "se": "Kenia", "pl": "Kenia", "no": "Kenya", "ja": "ケニア", "it": "Kenya", "zh": "肯尼亚", "nl": "Kenia", "de": "Kenia", "fr": "Kenya", "es": "Kenia", "en": "Kenya", "pt_BR": "Quênia", "sr-Cyrl": "Кенија", "sr-Latn": "Kenija", "zh_TW": "肯亞", "tr": "Kenya", "ro": "Kenya", "ar": "كينيا", "fa": "كنيا", "yue": "肯雅"},
    flag: "🇰🇪",
    code: "KE",
    dialCode: "254",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Kiribati",
    nameTranslations: {"sk": "Kiribati", "se": "Kiribati", "pl": "Kiribati", "no": "Kiribati", "ja": "キリバス", "it": "Kiribati", "zh": "基里巴斯", "nl": "Kiribati", "de": "Kiribati", "fr": "Kiribati", "es": "Kiribati", "en": "Kiribati", "pt_BR": "Kiribati", "sr-Cyrl": "Кирибати", "sr-Latn": "Kiribati", "zh_TW": "吉里巴斯", "tr": "Kiribati", "ro": "Kiribati", "ar": "كيريباتي", "fa": "کیریباتی", "yue": "基里巴斯"},
    flag: "🇰🇮",
    code: "KI",
    dialCode: "686",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Korea, Democratic People's Republic of Korea",
    nameTranslations: {"sk": "Severná Kórea", "se": "Davvi-Korea", "pl": "Korea Północna", "no": "Nord-Korea", "ja": "北朝鮮", "it": "Corea del Nord", "zh": "朝鲜", "nl": "Noord-Korea", "de": "Nordkorea", "fr": "Corée du Nord", "es": "Corea del Norte", "en": "North Korea", "pt_BR": "Coreia do Norte", "sr-Cyrl": "Северна Кореја", "sr-Latn": "Severna Koreja", "zh_TW": "北韓", "tr": "Kuzey Kore", "ro": "Coreea de Nord", "ar": "كوريا الشمالية", "fa": "کره شمالی", "yue": "朝鮮（朝鮮民主主義人民共咊囯）"},
    flag: "🇰🇵",
    code: "KP",
    dialCode: "850",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Korea, Republic of South Korea",
    nameTranslations: {"sk": "Južná Kórea", "se": "Mátta-Korea", "pl": "Korea Południowa", "no": "Sør-Korea", "ja": "韓国", "it": "Corea del Sud", "zh": "韩国", "nl": "Zuid-Korea", "de": "Südkorea", "fr": "Corée du Sud", "es": "Corea del Sur", "en": "South Korea", "pt_BR": "Coreia do Sul", "sr-Cyrl": "Јужна Кореја", "sr-Latn": "Južna Koreja", "zh_TW": "南韓", "tr": "Güney Kore", "ro": "Coreea de Sud", "ar": "كوريا الجنوبية", "fa": "کره جنوبی", "yue": "韓國（大韓民國）"},
    flag: "🇰🇷",
    code: "KR",
    dialCode: "82",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Kuwait",
    nameTranslations: {"sk": "Kuvajt", "se": "Kuwait", "pl": "Kuwejt", "no": "Kuwait", "ja": "クウェート", "it": "Kuwait", "zh": "科威特", "nl": "Koeweit", "de": "Kuwait", "fr": "Koweït", "es": "Kuwait", "en": "Kuwait", "pt_BR": "Kuwait", "sr-Cyrl": "Кувајт", "sr-Latn": "Kuvajt", "zh_TW": "科威特", "tr": "Kuveyt", "ro": "Kuweit", "ar": "الكويت", "fa": "کویت", "yue": "科威特"},
    flag: "🇰🇼",
    code: "KW",
    dialCode: "965",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Kyrgyzstan",
    nameTranslations: {"sk": "Kirgizsko", "se": "Kirgisistan", "pl": "Kirgistan", "no": "Kirgisistan", "ja": "キルギス", "it": "Kirghizistan", "zh": "吉尔吉斯斯坦", "nl": "Kirgizië", "de": "Kirgisistan", "fr": "Kirghizistan", "es": "Kirguistán", "en": "Kyrgyzstan", "pt_BR": "Quirguistão", "sr-Cyrl": "Киргистан", "sr-Latn": "Kirgistan", "zh_TW": "吉爾吉斯", "tr": "Kırgızistan", "ro": "Kîrgîzstan", "ar": "قيرغيزستان", "fa": "قرقیزستان", "yue": "吉爾吉斯斯坦"},
    flag: "🇰🇬",
    code: "KG",
    dialCode: "996",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Laos",
    nameTranslations: {"sk": "Laos", "se": "Laos", "pl": "Laos", "no": "Laos", "ja": "ラオス", "it": "Laos", "zh": "老挝", "nl": "Laos", "de": "Laos", "fr": "Laos", "es": "Laos", "en": "Laos", "pt_BR": "Laos", "sr-Cyrl": "Лаос", "sr-Latn": "Laos", "zh_TW": "寮國", "tr": "Laos", "ro": "Laos", "ar": "لاوس", "fa": "لائوس", "yue": "老撾人民民主共和國"},
    flag: "🇱🇦",
    code: "LA",
    dialCode: "856",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Latvia",
    nameTranslations: {"sk": "Lotyšsko", "se": "Látvia", "pl": "Łotwa", "no": "Latvia", "ja": "ラトビア", "it": "Lettonia", "zh": "拉脱维亚", "nl": "Letland", "de": "Lettland", "fr": "Lettonie", "es": "Letonia", "en": "Latvia", "pt_BR": "Letônia", "sr-Cyrl": "Летонија", "sr-Latn": "Letonija", "zh_TW": "拉托維亞", "tr": "Letonya", "ro": "Letonia", "ar": "لاتفيا", "fa": "لتونی", "yue": "拉脫維亞"},
    flag: "🇱🇻",
    code: "LV",
    dialCode: "371",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Lebanon",
    nameTranslations: {"sk": "Libanon", "se": "Libanon", "pl": "Liban", "no": "Libanon", "ja": "レバノン", "it": "Libano", "zh": "黎巴嫩", "nl": "Libanon", "de": "Libanon", "fr": "Liban", "es": "Líbano", "en": "Lebanon", "pt_BR": "Líbano", "sr-Cyrl": "Либан", "sr-Latn": "Liban", "zh_TW": "黎巴嫩", "tr": "Lübnan", "ro": "Liban", "ar": "لبنان", "fa": "لبنان", "yue": "黎巴嫩"},
    flag: "🇱🇧",
    code: "LB",
    dialCode: "961",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Lesotho",
    nameTranslations: {"sk": "Lesotho", "se": "Lesotho", "pl": "Lesotho", "no": "Lesotho", "ja": "レソト", "it": "Lesotho", "zh": "莱索托", "nl": "Lesotho", "de": "Lesotho", "fr": "Lesotho", "es": "Lesoto", "en": "Lesotho", "pt_BR": "Lesoto", "sr-Cyrl": "Лесото", "sr-Latn": "Lesoto", "zh_TW": "賴索托", "tr": "Lesotho", "ro": "Lesotho", "ar": "ليسوتو", "fa": "لسوتو", "yue": "萊索托"},
    flag: "🇱🇸",
    code: "LS",
    dialCode: "266",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Liberia",
    nameTranslations: {"sk": "Libéria", "se": "Liberia", "pl": "Liberia", "no": "Liberia", "ja": "リベリア", "it": "Liberia", "zh": "利比里亚", "nl": "Liberia", "de": "Liberia", "fr": "Libéria", "es": "Liberia", "en": "Liberia", "pt_BR": "Libéria", "sr-Cyrl": "Либерија", "sr-Latn": "Liberija", "zh_TW": "賴比瑞亞", "tr": "Liberya", "ro": "Liberia", "ar": "ليبيريا", "fa": "لیبریا", "yue": "利比里亞"},
    flag: "🇱🇷",
    code: "LR",
    dialCode: "231",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Libyan Arab Jamahiriya",
    nameTranslations: {"sk": "Líbya", "se": "Libya", "pl": "Libia", "no": "Libya", "ja": "リビア", "it": "Libia", "zh": "利比亚", "nl": "Libië", "de": "Libyen", "fr": "Libye", "es": "Libia", "en": "Libya", "pt_BR": "Líbia", "sr-Cyrl": "Либија", "sr-Latn": "Libija", "zh_TW": "利比亞", "tr": "Libya", "ro": "Libia", "ar": "ليبيا", "fa": "لیبی", "yue": "利比亞"},
    flag: "🇱🇾",
    code: "LY",
    dialCode: "218",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Liechtenstein",
    nameTranslations: {"sk": "Lichtenštajnsko", "se": "Liechtenstein", "pl": "Liechtenstein", "no": "Liechtenstein", "ja": "リヒテンシュタイン", "it": "Liechtenstein", "zh": "列支敦士登", "nl": "Liechtenstein", "de": "Liechtenstein", "fr": "Liechtenstein", "es": "Liechtenstein", "en": "Liechtenstein", "pt_BR": "Liechtenstein", "sr-Cyrl": "Лихтенштајн", "sr-Latn": "Lihtenštajn", "zh_TW": "列支敦斯登", "tr": "Lihtenştayn", "ro": "Liechtenstein", "ar": "ليختنشتاين", "fa": "لیختن‌اشتاین", "yue": "列支敦士登"},
    flag: "🇱🇮",
    code: "LI",
    dialCode: "423",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Lithuania",
    nameTranslations: {"sk": "Litva", "se": "Lietuva", "pl": "Litwa", "no": "Litauen", "ja": "リトアニア", "it": "Lituania", "zh": "立陶宛", "nl": "Litouwen", "de": "Litauen", "fr": "Lituanie", "es": "Lituania", "en": "Lithuania", "pt_BR": "Lituânia", "sr-Cyrl": "Литванија", "sr-Latn": "Litvanija", "zh_TW": "立陶宛", "tr": "Litvanya", "ro": "Lituania", "ar": "ليتوانيا", "fa": "لیتوانی", "yue": "立陶宛"},
    flag: "🇱🇹",
    code: "LT",
    dialCode: "370",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Luxembourg",
    nameTranslations: {"sk": "Luxembursko", "se": "Luxembourg", "pl": "Luksemburg", "no": "Luxemburg", "ja": "ルクセンブルク", "it": "Lussemburgo", "zh": "卢森堡", "nl": "Luxemburg", "de": "Luxemburg", "fr": "Luxembourg", "es": "Luxemburgo", "en": "Luxembourg", "pt_BR": "Luxemburgo", "sr-Cyrl": "Луксенбург", "sr-Latn": "Luksenburg", "zh_TW": "盧森堡", "tr": "Lüksemburg", "ro": "Luxemburg", "ar": "لوكسمبورغ", "fa": "لوکزامبورگ", "yue": "盧森堡"},
    flag: "🇱🇺",
    code: "LU",
    dialCode: "352",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Macao",
    nameTranslations: {"sk": "Macao – OAO Číny", "se": "Makáo", "pl": "SRA Makau (Chiny)", "no": "Macao S.A.R. Kina", "ja": "中華人民共和国マカオ特別行政区", "it": "RAS di Macao", "zh": "中国澳门特别行政区", "nl": "Macau SAR van China", "de": "Sonderverwaltungsregion Macau", "fr": "R.A.S. chinoise de Macao", "es": "RAE de Macao (China)", "en": "Macao SAR China", "pt_BR": "RAE de Macau China", "sr-Cyrl": "Макао", "sr-Latn": "Makao", "zh_TW": "澳門", "tr": "Makao", "ro": "Macao", "ar": "ماكاو", "fa": "ماكائو", "yue": "澳門"},
    flag: "🇲🇴",
    code: "MO",
    dialCode: "853",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Macedonia",
    nameTranslations: {"sk": "Severné Macedónsko", "se": "North Macedonia", "pl": "Macedonia Północna", "no": "Nord-Makedonia", "ja": "北マケドニア", "it": "Macedonia del Nord", "zh": "北马其顿", "nl": "Noord-Macedonië", "de": "Nordmazedonien", "fr": "Macédoine du Nord", "es": "Macedonia del Norte", "en": "North Macedonia", "pt_BR": "Macedônia do Norte", "sr-Cyrl": "Северна Македонија", "sr-Latn": "Severna Makedonija", "zh_TW": "北馬其頓", "tr": "Kuzey Makedonya", "ro": "Macedonia de Nord", "ar": "مقدونيا", "fa": "مقدونیه", "yue": "馬其頓（前南斯拉夫共和國）"},
    flag: "🇲🇰",
    code: "MK",
    dialCode: "389",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Madagascar",
    nameTranslations: {"sk": "Madagaskar", "se": "Madagaskar", "pl": "Madagaskar", "no": "Madagaskar", "ja": "マダガスカル", "it": "Madagascar", "zh": "马达加斯加", "nl": "Madagaskar", "de": "Madagaskar", "fr": "Madagascar", "es": "Madagascar", "en": "Madagascar", "pt_BR": "Madagáscar", "sr-Cyrl": "Мадагаскар", "sr-Latn": "Madagaskar", "zh_TW": "馬達加斯加", "tr": "Madagaskar", "ro": "Madagascar", "ar": "مدغشقر", "fa": "ماداگاسکار", "yue": "馬達加斯加"},
    flag: "🇲🇬",
    code: "MG",
    dialCode: "261",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Malawi",
    nameTranslations: {"sk": "Malawi", "se": "Malawi", "pl": "Malawi", "no": "Malawi", "ja": "マラウイ", "it": "Malawi", "zh": "马拉维", "nl": "Malawi", "de": "Malawi", "fr": "Malawi", "es": "Malaui", "en": "Malawi", "pt_BR": "Malawi", "sr-Cyrl": "Малави", "sr-Latn": "Malavi", "zh_TW": "馬拉威", "tr": "Malavi", "ro": "Malawi", "ar": "مالاوي", "fa": "مالاوی", "yue": "馬拉維"},
    flag: "🇲🇼",
    code: "MW",
    dialCode: "265",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "Malaysia",
    nameTranslations: {"sk": "Malajzia", "se": "Malesia", "pl": "Malezja", "no": "Malaysia", "ja": "マレーシア", "it": "Malaysia", "zh": "马来西亚", "nl": "Maleisië", "de": "Malaysia", "fr": "Malaisie", "es": "Malasia", "en": "Malaysia", "pt_BR": "Malásia", "sr-Cyrl": "Малезија", "sr-Latn": "Malezija", "zh_TW": "馬來西亞", "tr": "Malezya", "ro": "Malaezia", "ar": "ماليزيا", "fa": "مالزی", "yue": "馬來西亞"},
    flag: "🇲🇾",
    code: "MY",
    dialCode: "60",
    minLength: 9,
    maxLength: 10,
  ),
  Country(
    name: "Maldives",
    nameTranslations: {"sk": "Maldivy", "se": "Malediivvat", "pl": "Malediwy", "no": "Maldivene", "ja": "モルディブ", "it": "Maldive", "zh": "马尔代夫", "nl": "Maldiven", "de": "Malediven", "fr": "Maldives", "es": "Maldivas", "en": "Maldives", "pt_BR": "Maldivas", "sr-Cyrl": "Малдиви", "sr-Latn": "Maldivi", "zh_TW": "馬爾地夫", "tr": "Maldivler", "ro": "Maldive", "ar": "جزر المالديف", "fa": "مالدیو", "yue": "馬爾代夫"},
    flag: "🇲🇻",
    code: "MV",
    dialCode: "960",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Mali",
    nameTranslations: {"sk": "Mali", "se": "Mali", "pl": "Mali", "no": "Mali", "ja": "マリ", "it": "Mali", "zh": "马里", "nl": "Mali", "de": "Mali", "fr": "Mali", "es": "Mali", "en": "Mali", "pt_BR": "Mali", "sr-Cyrl": "Мали", "sr-Latn": "Mali", "zh_TW": "馬里", "tr": "Mali", "ro": "Mali", "ar": "مالي", "fa": "مالی", "yue": "馬里"},
    flag: "🇲🇱",
    code: "ML",
    dialCode: "223",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Malta",
    nameTranslations: {"sk": "Malta", "se": "Málta", "pl": "Malta", "no": "Malta", "ja": "マルタ", "it": "Malta", "zh": "马耳他", "nl": "Malta", "de": "Malta", "fr": "Malte", "es": "Malta", "en": "Malta", "pt_BR": "Malta", "sr-Cyrl": "Малта", "sr-Latn": "Malta", "zh_TW": "馬爾他", "tr": "Malta", "ro": "Malta", "ar": "مالطا", "fa": "مالت", "yue": "馬耳他"},
    flag: "🇲🇹",
    code: "MT",
    dialCode: "356",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Marshall Islands",
    nameTranslations: {"sk": "Marshallove ostrovy", "se": "Marshallsullot", "pl": "Wyspy Marshalla", "no": "Marshalløyene", "ja": "マーシャル諸島", "it": "Isole Marshall", "zh": "马绍尔群岛", "nl": "Marshalleilanden", "de": "Marshallinseln", "fr": "Îles Marshall", "es": "Islas Marshall", "en": "Marshall Islands", "pt_BR": "Ilhas Marshall", "sr-Cyrl": "Маршалска Острва", "sr-Latn": "Maršalska Ostrva", "zh_TW": "馬紹爾群島", "tr": "Marshall Adaları", "ro": "Insulele Marshall", "ar": "جزر مارشال", "fa": "جزایر مارشال", "yue": "馬紹爾群島"},
    flag: "🇲🇭",
    code: "MH",
    dialCode: "692",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Martinique",
    nameTranslations: {"sk": "Martinik", "se": "Martinique", "pl": "Martynika", "no": "Martinique", "ja": "マルティニーク", "it": "Martinica", "zh": "马提尼克", "nl": "Martinique", "de": "Martinique", "fr": "Martinique", "es": "Martinica", "en": "Martinique", "pt_BR": "Martinica", "sr-Cyrl": "Мартиник", "sr-Latn": "Martinik", "zh_TW": "馬丁尼克", "tr": "Martinique", "ro": "Martinica", "ar": "مارتينيك", "fa": "مارتینیک", "yue": "马提尼克"},
    flag: "🇲🇶",
    code: "MQ",
    dialCode: "596",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Mauritania",
    nameTranslations: {"sk": "Mauritánia", "se": "Mauretánia", "pl": "Mauretania", "no": "Mauritania", "ja": "モーリタニア", "it": "Mauritania", "zh": "毛里塔尼亚", "nl": "Mauritanië", "de": "Mauretanien", "fr": "Mauritanie", "es": "Mauritania", "en": "Mauritania", "pt_BR": "Mauritânia", "sr-Cyrl": "Мауританија", "sr-Latn": "Mauritanija", "zh_TW": "茅利塔尼亞", "tr": "Moritanya", "ro": "Mauritania", "ar": "موريتانيا", "fa": "موریتانی", "yue": "毛里塔尼亞"},
    flag: "🇲🇷",
    code: "MR",
    dialCode: "222",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Mauritius",
    nameTranslations: {"sk": "Maurícius", "se": "Mauritius", "pl": "Mauritius", "no": "Mauritius", "ja": "モーリシャス", "it": "Mauritius", "zh": "毛里求斯", "nl": "Mauritius", "de": "Mauritius", "fr": "Maurice", "es": "Mauricio", "en": "Mauritius", "pt_BR": "Maurício", "sr-Cyrl": "Маурицијус", "sr-Latn": "Mauricijus", "zh_TW": "模里西斯", "tr": "Mauritius", "ro": "Mauritius", "ar": "موريشيوس", "fa": "موریس", "yue": "毛里求斯"},
    flag: "🇲🇺",
    code: "MU",
    dialCode: "230",
    minLength: 7,
    maxLength: 8,
  ),
  Country(
    name: "Mayotte",
    nameTranslations: {"sk": "Mayotte", "se": "Mayotte", "pl": "Majotta", "no": "Mayotte", "ja": "マヨット", "it": "Mayotte", "zh": "马约特", "nl": "Mayotte", "de": "Mayotte", "fr": "Mayotte", "es": "Mayotte", "en": "Mayotte", "pt_BR": "Mayotte", "sr-Cyrl": "Мајота", "sr-Latn": "Majota", "zh_TW": "馬約特", "tr": "Mayotte", "ro": "Mayotte", "ar": "مايوت", "fa": "مایوت", "yue": "馬約特"},
    flag: "🇾🇹",
    code: "YT",
    dialCode: "262",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mexico",
    nameTranslations: {"sk": "Mexiko", "se": "Meksiko", "pl": "Meksyk", "no": "Mexico", "ja": "メキシコ", "it": "Messico", "zh": "墨西哥", "nl": "Mexico", "de": "Mexiko", "fr": "Mexique", "es": "México", "en": "Mexico", "pt_BR": "México", "sr-Cyrl": "Мексико", "sr-Latn": "Meksiko", "zh_TW": "墨西哥", "tr": "Meksika", "ro": "Mexic", "ar": "المكسيك", "fa": "مکزیک", "yue": "墨西哥"},
    flag: "🇲🇽",
    code: "MX",
    dialCode: "52",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Micronesia, Federated States of Micronesia",
    nameTranslations: {"sk": "Mikronézia", "se": "Mikronesia", "pl": "Mikronezja", "no": "Mikronesiaføderasjonen", "ja": "ミクロネシア連邦", "it": "Micronesia", "zh": "密克罗尼西亚", "nl": "Micronesia", "de": "Mikronesien", "fr": "États fédérés de Micronésie", "es": "Micronesia", "en": "Micronesia", "pt_BR": "Micronésia", "sr-Cyrl": "Микронезија", "sr-Latn": "Mikronezija", "zh_TW": "密克羅尼西亞", "tr": "Mikronezya", "ro": "Micronezia", "ar": "ولايات ميكرونيسيا المتحدة", "fa": "ایالات فدرال میکرونزی", "yue": "密克罗尼西亚（聯邦）"},
    flag: "🇫🇲",
    code: "FM",
    dialCode: "691",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Moldova",
    nameTranslations: {"sk": "Moldavsko", "se": "Moldávia", "pl": "Mołdawia", "no": "Moldova", "ja": "モルドバ", "it": "Moldavia", "zh": "摩尔多瓦", "nl": "Moldavië", "de": "Republik Moldau", "fr": "Moldavie", "es": "Moldavia", "en": "Moldova", "pt_BR": "Moldova", "sr-Cyrl": "Молдавија", "sr-Latn": "Moldavija", "zh_TW": "摩爾多瓦", "tr": "Moldova", "ro": "Moldova", "ar": "مولدوفا", "fa": "مولداوی", "yue": "摩爾多瓦（共和國）"},
    flag: "🇲🇩",
    code: "MD",
    dialCode: "373",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Monaco",
    nameTranslations: {"sk": "Monako", "se": "Monaco", "pl": "Monako", "no": "Monaco", "ja": "モナコ", "it": "Monaco", "zh": "摩纳哥", "nl": "Monaco", "de": "Monaco", "fr": "Monaco", "es": "Mónaco", "en": "Monaco", "pt_BR": "Mônaco", "sr-Cyrl": "Монако", "sr-Latn": "Monako", "zh_TW": "摩納哥", "tr": "Monako", "ro": "Monaco", "ar": "موناكو", "fa": "موناكو", "yue": "摩納哥"},
    flag: "🇲🇨",
    code: "MC",
    dialCode: "377",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mongolia",
    nameTranslations: {"sk": "Mongolsko", "se": "Mongolia", "pl": "Mongolia", "no": "Mongolia", "ja": "モンゴル", "it": "Mongolia", "zh": "蒙古", "nl": "Mongolië", "de": "Mongolei", "fr": "Mongolie", "es": "Mongolia", "en": "Mongolia", "pt_BR": "Mongólia", "sr-Cyrl": "Монголија", "sr-Latn": "Mongolija", "zh_TW": "蒙古", "tr": "Moğolistan", "ro": "Mongolia", "ar": "منغوليا", "fa": "مغولستان", "yue": "蒙古"},
    flag: "🇲🇳",
    code: "MN",
    dialCode: "976",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Montenegro",
    nameTranslations: {"sk": "Čierna Hora", "se": "Montenegro", "pl": "Czarnogóra", "no": "Montenegro", "ja": "モンテネグロ", "it": "Montenegro", "zh": "黑山", "nl": "Montenegro", "de": "Montenegro", "fr": "Monténégro", "es": "Montenegro", "en": "Montenegro", "pt_BR": "Montenegro", "sr-Cyrl": "Црна Гора", "sr-Latn": "Crna Gora", "zh_TW": "蒙特內哥羅", "tr": "Karadağ", "ro": "Muntenegru", "ar": "الجبل الأسود", "fa": "مونته‌نگرو", "yue": "黑山"},
    flag: "🇲🇪",
    code: "ME",
    dialCode: "382",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Montserrat",
    nameTranslations: {"sk": "Montserrat", "se": "Montserrat", "pl": "Montserrat", "no": "Montserrat", "ja": "モントセラト", "it": "Montserrat", "zh": "蒙特塞拉特", "nl": "Montserrat", "de": "Montserrat", "fr": "Montserrat", "es": "Montserrat", "en": "Montserrat", "pt_BR": "Montserrat", "sr-Cyrl": "Монтсерат", "sr-Latn": "Montserat", "zh_TW": "蒙哲臘", "tr": "Montserrat", "ro": "Montserrat", "ar": "مونتسرات", "fa": "مونتسرات", "yue": "蒙特塞拉特"},
    flag: "🇲🇸",
    code: "MS",
    dialCode: "1664",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Morocco",
    nameTranslations: {"sk": "Maroko", "se": "Marokko", "pl": "Maroko", "no": "Marokko", "ja": "モロッコ", "it": "Marocco", "zh": "摩洛哥", "nl": "Marokko", "de": "Marokko", "fr": "Maroc", "es": "Marruecos", "en": "Morocco", "pt_BR": "Marrocos", "sr-Cyrl": "Мароко", "sr-Latn": "Maroko", "zh_TW": "摩洛哥", "tr": "Fas", "ro": "Maroc", "ar": "المغرب", "fa": "مراکش", "yue": "摩洛哥"},
    flag: "🇲🇦",
    code: "MA",
    dialCode: "212",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mozambique",
    nameTranslations: {"sk": "Mozambik", "se": "Mosambik", "pl": "Mozambik", "no": "Mosambik", "ja": "モザンビーク", "it": "Mozambico", "zh": "莫桑比克", "nl": "Mozambique", "de": "Mosambik", "fr": "Mozambique", "es": "Mozambique", "en": "Mozambique", "pt_BR": "Moçambique", "sr-Cyrl": "Мозамбик", "sr-Latn": "Mozambik", "zh_TW": "莫三比克", "tr": "Mozambik", "ro": "Mozambic", "ar": "موزمبيق", "fa": "موزامبیک", "yue": "莫桑比克"},
    flag: "🇲🇿",
    code: "MZ",
    dialCode: "258",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Myanmar",
    nameTranslations: {"sk": "Mjanmarsko", "se": "Burma", "pl": "Mjanma (Birma)", "no": "Myanmar (Burma)", "ja": "ミャンマー (ビルマ)", "it": "Myanmar (Birmania)", "zh": "缅甸", "nl": "Myanmar (Birma)", "de": "Myanmar", "fr": "Myanmar (Birmanie)", "es": "Myanmar (Birmania)", "en": "Myanmar (Burma)", "pt_BR": "Mianmar (Birmânia)", "sr-Cyrl": "Мјанмар (Бурма)", "sr-Latn": "Mjanmar (Burma)", "zh_TW": "緬甸", "tr": "Myanmar", "ro": "Myanmar", "ar": "ميانمار", "fa": "میانمار", "yue": "緬甸"},
    flag: "🇲🇲",
    code: "MM",
    dialCode: "95",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Namibia",
    nameTranslations: {"sk": "Namíbia", "se": "Namibia", "pl": "Namibia", "no": "Namibia", "ja": "ナミビア", "it": "Namibia", "zh": "纳米比亚", "nl": "Namibië", "de": "Namibia", "fr": "Namibie", "es": "Namibia", "en": "Namibia", "pt_BR": "Namibia", "sr-Cyrl": "Намибија", "sr-Latn": "Namibija", "zh_TW": "納米比亞", "tr": "Namibya", "ro": "Namibia", "ar": "ناميبيا", "fa": "نامیبیا", "yue": "納米比亞"},
    flag: "🇳🇦",
    code: "NA",
    dialCode: "264",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Nauru",
    nameTranslations: {"sk": "Nauru", "se": "Nauru", "pl": "Nauru", "no": "Nauru", "ja": "ナウル", "it": "Nauru", "zh": "瑙鲁", "nl": "Nauru", "de": "Nauru", "fr": "Nauru", "es": "Nauru", "en": "Nauru", "pt_BR": "Nauru", "sr-Cyrl": "Науру", "sr-Latn": "Nauru", "zh_TW": "諾魯", "tr": "Nauru", "ro": "Nauru", "ar": "ناورو", "fa": "نائورو", "yue": "瑙魯"},
    flag: "🇳🇷",
    code: "NR",
    dialCode: "674",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Nepal",
    nameTranslations: {"sk": "Nepál", "se": "Nepal", "pl": "Nepal", "no": "Nepal", "ja": "ネパール", "it": "Nepal", "zh": "尼泊尔", "nl": "Nepal", "de": "Nepal", "fr": "Népal", "es": "Nepal", "en": "Nepal", "pt_BR": "Nepal", "sr-Cyrl": "Непал", "sr-Latn": "Nepal", "zh_TW": "尼泊爾", "tr": "Nepal", "ro": "Nepal", "ar": "نيبال", "fa": "نپال", "yue": "尼泊爾"},
    flag: "🇳🇵",
    code: "NP",
    dialCode: "977",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Netherlands",
    nameTranslations: {"sk": "Holandsko", "se": "Vuolleeatnamat", "pl": "Holandia", "no": "Nederland", "ja": "オランダ", "it": "Paesi Bassi", "zh": "荷兰", "nl": "Nederland", "de": "Niederlande", "fr": "Pays-Bas", "es": "Países Bajos", "en": "Netherlands", "pt_BR": "Países Baixos", "sr-Cyrl": "Холандија", "sr-Latn": "Holandija", "zh_TW": "荷蘭", "tr": "Hollanda", "ro": "Olanda", "ar": "هولندا", "fa": "هلند", "yue": "荷蘭"},
    flag: "🇳🇱",
    code: "NL",
    dialCode: "31",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "New Caledonia",
    nameTranslations: {"sk": "Nová Kaledónia", "se": "Ođđa-Kaledonia", "pl": "Nowa Kaledonia", "no": "Ny-Caledonia", "ja": "ニューカレドニア", "it": "Nuova Caledonia", "zh": "新喀里多尼亚", "nl": "Nieuw-Caledonië", "de": "Neukaledonien", "fr": "Nouvelle-Calédonie", "es": "Nueva Caledonia", "en": "New Caledonia", "pt_BR": "Nova Caledônia", "sr-Cyrl": "Нова Каледонија", "sr-Latn": "Nova Kaledonija", "zh_TW": "新喀里多尼亞", "tr": "Yeni Kaledonya", "ro": "Noua Caledonie", "ar": "كاليدونيا الجديدة", "fa": "کالدونیای جدید", "yue": "新喀里多尼亚"},
    flag: "🇳🇨",
    code: "NC",
    dialCode: "687",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "New Zealand",
    nameTranslations: {"sk": "Nový Zéland", "se": "Ođđa-Selánda", "pl": "Nowa Zelandia", "no": "New Zealand", "ja": "ニュージーランド", "it": "Nuova Zelanda", "zh": "新西兰", "nl": "Nieuw-Zeeland", "de": "Neuseeland", "fr": "Nouvelle-Zélande", "es": "Nueva Zelanda", "en": "New Zealand", "pt_BR": "Nova Zelândia", "sr-Cyrl": "Нови Зеланд", "sr-Latn": "Novi Zeland", "zh_TW": "紐西蘭", "tr": "Yeni Zelanda", "ro": "Noua Zeelandă", "ar": "نيوزيلندا", "fa": "نیوزلند", "yue": "紐西蘭"},
    flag: "🇳🇿",
    code: "NZ",
    dialCode: "64",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Nicaragua",
    nameTranslations: {"sk": "Nikaragua", "se": "Nicaragua", "pl": "Nikaragua", "no": "Nicaragua", "ja": "ニカラグア", "it": "Nicaragua", "zh": "尼加拉瓜", "nl": "Nicaragua", "de": "Nicaragua", "fr": "Nicaragua", "es": "Nicaragua", "en": "Nicaragua", "pt_BR": "Nicarágua", "sr-Cyrl": "Никарагва", "sr-Latn": "Nikaragva", "zh_TW": "尼加拉瓜", "tr": "Nikaragua", "ro": "Nicaragua", "ar": "نيكاراغوا", "fa": "نیکاراگوئه", "yue": "尼加拉瓜"},
    flag: "🇳🇮",
    code: "NI",
    dialCode: "505",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Niger",
    nameTranslations: {"sk": "Niger", "se": "Niger", "pl": "Niger", "no": "Niger", "ja": "ニジェール", "it": "Niger", "zh": "尼日尔", "nl": "Niger", "de": "Niger", "fr": "Niger", "es": "Níger", "en": "Niger", "pt_BR": "Níger", "sr-Cyrl": "Нигер", "sr-Latn": "Niger", "zh_TW": "尼日爾", "tr": "Nijer", "ro": "Niger", "ar": "النيجر", "fa": "نیجر", "yue": "尼日爾"},
    flag: "🇳🇪",
    code: "NE",
    dialCode: "227",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Nigeria",
    nameTranslations: {"sk": "Nigéria", "se": "Nigeria", "pl": "Nigeria", "no": "Nigeria", "ja": "ナイジェリア", "it": "Nigeria", "zh": "尼日利亚", "nl": "Nigeria", "de": "Nigeria", "fr": "Nigéria", "es": "Nigeria", "en": "Nigeria", "pt_BR": "Nigéria", "sr-Cyrl": "Нигерија", "sr-Latn": "Nigerija", "zh_TW": "奈及利亞", "tr": "Nijerya", "ro": "Nigeria", "ar": "نيجيريا", "fa": "نیجریه", "yue": "尼日利亞"},
    flag: "🇳🇬",
    code: "NG",
    dialCode: "234",
    minLength: 10,
    maxLength: 11,
  ),
  Country(
    name: "Niue",
    nameTranslations: {"sk": "Niue", "se": "Niue", "pl": "Niue", "no": "Niue", "ja": "ニウエ", "it": "Niue", "zh": "纽埃", "nl": "Niue", "de": "Niue", "fr": "Niue", "es": "Niue", "en": "Niue", "pt_BR": "Niue", "sr-Cyrl": "Нијуе", "sr-Latn": "Nijue", "zh_TW": "紐埃", "tr": "Niue", "ro": "Niue", "ar": "نييوي", "fa": "نیووی", "yue": "紐埃"},
    flag: "🇳🇺",
    code: "NU",
    dialCode: "683",
    minLength: 4,
    maxLength: 4,
  ),
  Country(
    name: "Norfolk Island",
    nameTranslations: {"sk": "Norfolk", "se": "Norfolksullot", "pl": "Norfolk", "no": "Norfolkøya", "ja": "ノーフォーク島", "it": "Isola Norfolk", "zh": "诺福克岛", "nl": "Norfolk", "de": "Norfolkinsel", "fr": "Île Norfolk", "es": "Isla Norfolk", "en": "Norfolk Island", "pt_BR": "Ilha Norfolk", "sr-Cyrl": "Острво Норфок", "sr-Latn": "Ostrvo Norfok", "zh_TW": "諾福克島", "tr": "Norfolk Adası", "ro": "Insulele Norfolk", "ar": "جزيرة نورفولك", "fa": "جزیره نورفک", "yue": "诺福克岛"},
    flag: "🇳🇫",
    code: "NF",
    dialCode: "672",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Northern Mariana Islands",
    nameTranslations: {"sk": "Severné Mariány", "se": "Davvi-Mariánat", "pl": "Mariany Północne", "no": "Nord-Marianene", "ja": "北マリアナ諸島", "it": "Isole Marianne settentrionali", "zh": "北马里亚纳群岛", "nl": "Noordelijke Marianen", "de": "Nördliche Marianen", "fr": "Îles Mariannes du Nord", "es": "Islas Marianas del Norte", "en": "Northern Mariana Islands", "pt_BR": "Ilhas Marianas do Norte", "sr-Cyrl": "Северна Маријанска Острва", "sr-Latn": "Severna Marijanska Ostrva", "zh_TW": "北馬利安納群島", "tr": "Kuzey Mariana Adaları", "ro": "Insulelor Mariane de Nord", "ar": "جزر ماريانا الشمالية", "fa": "جزایر ماریانای شمالی", "yue": "北馬里亞納群島"},
    flag: "🇲🇵",
    code: "MP",
    dialCode: "1670",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Norway",
    nameTranslations: {"sk": "Nórsko", "se": "Norga", "pl": "Norwegia", "no": "Norge", "ja": "ノルウェー", "it": "Norvegia", "zh": "挪威", "nl": "Noorwegen", "de": "Norwegen", "fr": "Norvège", "es": "Noruega", "en": "Norway", "pt_BR": "Noruega", "sr-Cyrl": "Норвешка", "sr-Latn": "Norveška", "zh_TW": "挪威", "tr": "Norveç", "ro": "Norvegia", "ar": "النرويج", "fa": "نروژ", "yue": "挪威"},
    flag: "🇳🇴",
    code: "NO",
    dialCode: "47",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Oman",
    nameTranslations: {"sk": "Omán", "se": "Oman", "pl": "Oman", "no": "Oman", "ja": "オマーン", "it": "Oman", "zh": "阿曼", "nl": "Oman", "de": "Oman", "fr": "Oman", "es": "Omán", "en": "Oman", "pt_BR": "Omã", "sr-Cyrl": "Оман", "sr-Latn": "Oman", "zh_TW": "阿曼", "tr": "Umman", "ro": "Oman", "ar": "عمان", "fa": "عمان", "yue": "阿曼"},
    flag: "🇴🇲",
    code: "OM",
    dialCode: "968",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Pakistan",
    nameTranslations: {"sk": "Pakistan", "se": "Pakistan", "pl": "Pakistan", "no": "Pakistan", "ja": "パキスタン", "it": "Pakistan", "zh": "巴基斯坦", "nl": "Pakistan", "de": "Pakistan", "fr": "Pakistan", "es": "Pakistán", "en": "Pakistan", "pt_BR": "Paquistão", "sr-Cyrl": "Пакистан", "sr-Latn": "Pakistan", "zh_TW": "巴基斯坦", "tr": "Pakistan", "ro": "Pakistan", "ar": "باكستان", "fa": "پاکستان", "yue": "巴基斯坦"},
    flag: "🇵🇰",
    code: "PK",
    dialCode: "92",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Palau",
    nameTranslations: {"sk": "Palau", "se": "Palau", "pl": "Palau", "no": "Palau", "ja": "パラオ", "it": "Palau", "zh": "帕劳", "nl": "Palau", "de": "Palau", "fr": "Palaos", "es": "Palaos", "en": "Palau", "pt_BR": "Palau", "sr-Cyrl": "Палау", "sr-Latn": "Palau", "zh_TW": "帛琉", "tr": "Palau", "ro": "Palau", "ar": "بالاو", "fa": "پالائو", "yue": "帕劳"},
    flag: "🇵🇼",
    code: "PW",
    dialCode: "680",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Palestinian Territory, Occupied",
    nameTranslations: {"sk": "Palestínske územia", "se": "Palestina", "pl": "Terytoria Palestyńskie", "no": "Det palestinske området", "ja": "パレスチナ自治区", "it": "Territori palestinesi", "zh": "巴勒斯坦领土", "nl": "Palestijnse gebieden", "de": "Palästinensische Autonomiegebiete", "fr": "Territoires palestiniens", "es": "Territorios Palestinos", "en": "Palestinian Territories", "pt_BR": "Territórios Palestinos", "sr-Cyrl": "Палестина", "sr-Latn": "Palestina", "zh_TW": "巴勒斯坦", "tr": "Filistin", "ro": "Palestina", "ar": "فلسطين", "fa": "فلسطین", "yue": "巴勒斯坦，国"},
    flag: "🇵🇸",
    code: "PS",
    dialCode: "970",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Panama",
    nameTranslations: {"sk": "Panama", "se": "Panama", "pl": "Panama", "no": "Panama", "ja": "パナマ", "it": "Panamá", "zh": "巴拿马", "nl": "Panama", "de": "Panama", "fr": "Panama", "es": "Panamá", "en": "Panama", "pt_BR": "Panamá", "sr-Cyrl": "Панама", "sr-Latn": "Panama", "zh_TW": "巴拿馬", "tr": "Panama", "ro": "Panama", "ar": "بنما", "fa": "پاناما", "yue": "巴拿馬"},
    flag: "🇵🇦",
    code: "PA",
    dialCode: "507",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Papua New Guinea",
    nameTranslations: {"sk": "Papua-Nová Guinea", "se": "Papua-Ođđa-Guinea", "pl": "Papua-Nowa Gwinea", "no": "Papua Ny-Guinea", "ja": "パプアニューギニア", "it": "Papua Nuova Guinea", "zh": "巴布亚新几内亚", "nl": "Papoea-Nieuw-Guinea", "de": "Papua-Neuguinea", "fr": "Papouasie-Nouvelle-Guinée", "es": "Papúa Nueva Guinea", "en": "Papua New Guinea", "pt_BR": "Papua Nova Guiné", "sr-Cyrl": "Папуа Нова Гвинеја", "sr-Latn": "Papua Nova Gvineja", "zh_TW": "巴布亞新幾內亞", "tr": "Papua Yeni Gine", "ro": "Papua Noua Guinee", "ar": "بابوا غينيا الجديدة", "fa": "پاپوآ گینه نو", "yue": "巴布亚新几内亚"},
    flag: "🇵🇬",
    code: "PG",
    dialCode: "675",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Paraguay",
    nameTranslations: {"sk": "Paraguaj", "se": "Paraguay", "pl": "Paragwaj", "no": "Paraguay", "ja": "パラグアイ", "it": "Paraguay", "zh": "巴拉圭", "nl": "Paraguay", "de": "Paraguay", "fr": "Paraguay", "es": "Paraguay", "en": "Paraguay", "pt_BR": "Paraguai", "sr-Cyrl": "Парагвај", "sr-Latn": "Paragvaj", "zh_TW": "巴拉圭", "tr": "Paraguay", "ro": "Paraguay", "ar": "باراغواي", "fa": "پاراگوئه", "yue": "巴拉圭"},
    flag: "🇵🇾",
    code: "PY",
    dialCode: "595",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Peru",
    nameTranslations: {"sk": "Peru", "se": "Peru", "pl": "Peru", "no": "Peru", "ja": "ペルー", "it": "Perù", "zh": "秘鲁", "nl": "Peru", "de": "Peru", "fr": "Pérou", "es": "Perú", "en": "Peru", "pt_BR": "Peru", "sr-Cyrl": "Перу", "sr-Latn": "Peru", "zh_TW": "秘鲁", "tr": "Peru", "ro": "Peru", "ar": "بيرو", "fa": "پرو", "yue": "秘魯"},
    flag: "🇵🇪",
    code: "PE",
    dialCode: "51",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Philippines",
    nameTranslations: {"sk": "Filipíny", "se": "Filippiinnat", "pl": "Filipiny", "no": "Filippinene", "ja": "フィリピン", "it": "Filippine", "zh": "菲律宾", "nl": "Filipijnen", "de": "Philippinen", "fr": "Philippines", "es": "Filipinas", "en": "Philippines", "pt_BR": "Filipinas", "sr-Cyrl": "Филипини", "sr-Latn": "Filipini", "zh_TW": "菲律賓", "tr": "Filipinler", "ro": "Filipine", "ar": "الفلبين", "fa": "فیلیپین", "yue": "菲律賓"},
    flag: "🇵🇭",
    code: "PH",
    dialCode: "63",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Pitcairn",
    nameTranslations: {"sk": "Pitcairnove ostrovy", "se": "Pitcairn", "pl": "Pitcairn", "no": "Pitcairnøyene", "ja": "ピトケアン諸島", "it": "Isole Pitcairn", "zh": "皮特凯恩群岛", "nl": "Pitcairneilanden", "de": "Pitcairninseln", "fr": "Îles Pitcairn", "es": "Islas Pitcairn", "en": "Pitcairn Islands", "pt_BR": "Ilhas Pitcairn", "sr-Cyrl": "Острва Питкерн", "sr-Latn": "Ostrva Pitkern", "zh_TW": "皮特肯群島", "tr": "Pitcairn Adaları", "ro": "Insulele Pitcairn", "ar": "جزر بيتكيرن", "fa": "جزایر پیت‌کرن", "yue": "皮特凱恩"},
    flag: "🇵🇳",
    code: "PN",
    dialCode: "64",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Poland",
    nameTranslations: {"sk": "Poľsko", "se": "Polen", "pl": "Polska", "no": "Polen", "ja": "ポーランド", "it": "Polonia", "zh": "波兰", "nl": "Polen", "de": "Polen", "fr": "Pologne", "es": "Polonia", "en": "Poland", "pt_BR": "Polônia", "sr-Cyrl": "Пољска", "sr-Latn": "Poljska", "zh_TW": "波蘭", "tr": "Polonya", "ro": "Polonia", "ar": "بولندا", "fa": "لهستان", "yue": "波蘭"},
    flag: "🇵🇱",
    code: "PL",
    dialCode: "48",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Portugal",
    nameTranslations: {"sk": "Portugalsko", "se": "Portugála", "pl": "Portugalia", "no": "Portugal", "ja": "ポルトガル", "it": "Portogallo", "zh": "葡萄牙", "nl": "Portugal", "de": "Portugal", "fr": "Portugal", "es": "Portugal", "en": "Portugal", "pt_BR": "Portugal", "sr-Cyrl": "Португалија", "sr-Latn": "Portugalija", "zh_TW": "葡萄牙", "tr": "Portekiz", "ro": "Portugalia", "ar": "البرتغال", "fa": "پرتغال", "yue": "葡萄牙"},
    flag: "🇵🇹",
    code: "PT",
    dialCode: "351",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Puerto Rico",
    nameTranslations: {"sk": "Portoriko", "se": "Puerto Rico", "pl": "Portoryko", "no": "Puerto Rico", "ja": "プエルトリコ", "it": "Portorico", "zh": "波多黎各", "nl": "Puerto Rico", "de": "Puerto Rico", "fr": "Porto Rico", "es": "Puerto Rico", "en": "Puerto Rico", "pt_BR": "Porto Rico", "sr-Cyrl": "Порто Рико", "sr-Latn": "Porto Riko", "zh_TW": "波多黎各", "tr": "Porto Riko", "ro": "Puerto Rico", "ar": "بورتوريكو", "fa": "پورتوریکو", "yue": "波多黎各"},
    flag: "🇵🇷",
    code: "PR",
    dialCode: "1939",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Qatar",
    nameTranslations: {"sk": "Katar", "se": "Qatar", "pl": "Katar", "no": "Qatar", "ja": "カタール", "it": "Qatar", "zh": "卡塔尔", "nl": "Qatar", "de": "Katar", "fr": "Qatar", "es": "Catar", "en": "Qatar", "pt_BR": "Catar", "sr-Cyrl": "Катар", "sr-Latn": "Katar", "zh_TW": "卡達", "tr": "Katar", "ro": "Qatar", "ar": "قطر", "fa": "قطر", "yue": "卡塔爾"},
    flag: "🇶🇦",
    code: "QA",
    dialCode: "974",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Romania",
    nameTranslations: {"sk": "Rumunsko", "se": "Románia", "pl": "Rumunia", "no": "Romania", "ja": "ルーマニア", "it": "Romania", "zh": "罗马尼亚", "nl": "Roemenië", "de": "Rumänien", "fr": "Roumanie", "es": "Rumanía", "en": "Romania", "pt_BR": "Romênia", "sr-Cyrl": "Румунија", "sr-Latn": "Rumunija", "zh_TW": "羅馬尼亞", "tr": "Romanya", "ro": "România", "ar": "رومانيا", "fa": "رومانی", "yue": "羅馬尼亞"},
    flag: "🇷🇴",
    code: "RO",
    dialCode: "40",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Russia",
    nameTranslations: {"sk": "Rusko", "se": "Ruošša", "pl": "Rosja", "no": "Russland", "ja": "ロシア", "it": "Russia", "zh": "俄罗斯", "nl": "Rusland", "de": "Russland", "fr": "Russie", "es": "Rusia", "en": "Russia", "pt_BR": "Rússia", "sr-Cyrl": "Русија", "sr-Latn": "Rusija", "zh_TW": "俄羅斯", "tr": "Rusya", "ro": "Rusia", "ar": "روسيا", "fa": "روسیه", "yue": "俄儸斯聯邦"},
    flag: "🇷🇺",
    code: "RU",
    dialCode: "7",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Rwanda",
    nameTranslations: {"sk": "Rwanda", "se": "Rwanda", "pl": "Rwanda", "no": "Rwanda", "ja": "ルワンダ", "it": "Ruanda", "zh": "卢旺达", "nl": "Rwanda", "de": "Ruanda", "fr": "Rwanda", "es": "Ruanda", "en": "Rwanda", "pt_BR": "Ruanda", "sr-Cyrl": "Руанда", "sr-Latn": "Ruanda", "zh_TW": "盧安達", "tr": "Ruanda", "ro": "Rwanda", "ar": "رواندا", "fa": "رواندا", "yue": "盧旺達"},
    flag: "🇷🇼",
    code: "RW",
    dialCode: "250",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Reunion",
    nameTranslations: {"sk": "Réunion", "se": "Réunion", "pl": "Reunion", "no": "Réunion", "ja": "レユニオン", "it": "Riunione", "zh": "留尼汪", "nl": "Réunion", "de": "Réunion", "fr": "La Réunion", "es": "Reunión", "en": "Réunion", "pt_BR": "Reunião", "sr-Cyrl": "Реинион", "sr-Latn": "Reinion", "zh_TW": "留尼旺", "tr": "La Réunion", "ro": "La Réunion", "ar": "لا ريونيون", "fa": "رئونیون", "yue": "留尼汪"},
    flag: "🇷🇪",
    code: "RE",
    dialCode: "262",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Barthelemy",
    nameTranslations: {"sk": "Svätý Bartolomej", "se": "Saint Barthélemy", "pl": "Saint-Barthélemy", "no": "Saint-Barthélemy", "ja": "サン・バルテルミー", "it": "Saint-Barthélemy", "zh": "圣巴泰勒米", "nl": "Saint-Barthélemy", "de": "St. Barthélemy", "fr": "Saint-Barthélemy", "es": "San Bartolomé", "en": "St. Barthélemy", "pt_BR": "São Bartolomeu", "sr-Cyrl": "Сент Бартелеми", "sr-Latn": "Sent Bartelemi", "zh_TW": "聖巴瑟米", "tr": "Saint Barthélemy", "ro": "Saint Barthélemy", "ar": "سان بارتيلمي", "fa": "سن بارتلمی", "yue": "聖巴泰勒米"},
    flag: "🇧🇱",
    code: "BL",
    dialCode: "590",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Helena, Ascension and Tristan Da Cunha",
    nameTranslations: {"sk": "Svätá Helena", "se": "Saint Helena", "pl": "Wyspa Świętej Heleny", "no": "St. Helena", "ja": "セントヘレナ", "it": "Sant'Elena", "zh": "圣赫勒拿", "nl": "Sint-Helena", "de": "St. Helena", "fr": "Sainte-Hélène", "es": "Santa Elena", "en": "St. Helena", "pt_BR": "Santa Helena", "sr-Cyrl": "Света Јелена, Асенсион и Тристан да Куња", "sr-Latn": "Sveta Jelena, Asension i Tristan de Kunja", "zh_TW": "聖凱倫拿島", "tr": "Saint Helena", "ro": "Sfânta Elena", "ar": "سانت هيلانة وأسينشين وتريستان دا كونا", "fa": "سنت هلن", "yue": "圣赫勒拿、阿森松同特里斯坦·达库尼亚"},
    flag: "🇸🇭",
    code: "SH",
    dialCode: "290",
    minLength: 4,
    maxLength: 4,
  ),
  Country(
    name: "Saint Kitts and Nevis",
    nameTranslations: {"sk": "Svätý Krištof a Nevis", "se": "Saint Kitts ja Nevis", "pl": "Saint Kitts i Nevis", "no": "Saint Kitts og Nevis", "ja": "セントクリストファー・ネーヴィス", "it": "Saint Kitts e Nevis", "zh": "圣基茨和尼维斯", "nl": "Saint Kitts en Nevis", "de": "St. Kitts und Nevis", "fr": "Saint-Christophe-et-Niévès", "es": "San Cristóbal y Nieves", "en": "St. Kitts & Nevis", "pt_BR": "São Cristóvão e Nevis", "sr-Cyrl": "Сент Китс и Невис", "sr-Latn": "Sent Kits i Nevis", "zh_TW": "聖克里斯多福及尼維斯", "tr": "Saint Kitts ve Nevis", "ro": "Sfântul Kitts și Nevis", "ar": "سانت كيتس ونيفيس", "fa": "سنت کیتس و نویس", "yue": "圣基茨同尼维斯"},
    flag: "🇰🇳",
    code: "KN",
    dialCode: "1869",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saint Lucia",
    nameTranslations: {"sk": "Svätá Lucia", "se": "Saint Lucia", "pl": "Saint Lucia", "no": "St. Lucia", "ja": "セントルシア", "it": "Saint Lucia", "zh": "圣卢西亚", "nl": "Saint Lucia", "de": "St. Lucia", "fr": "Sainte-Lucie", "es": "Santa Lucía", "en": "St. Lucia", "pt_BR": "Santa Lúcia", "sr-Cyrl": "Света Луција", "sr-Latn": "Sveta Lucija", "zh_TW": "聖露西亞", "tr": "Saint Lucia", "ro": "Sfânta Elena", "ar": "سانت لوسيا", "fa": "سنت لوسیا", "yue": "聖盧西亞"},
    flag: "🇱🇨",
    code: "LC",
    dialCode: "1758",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saint Martin",
    nameTranslations: {"sk": "Svätý Martin (fr.)", "se": "Frankriikka Saint Martin", "pl": "Saint-Martin", "no": "Saint-Martin", "ja": "サン・マルタン", "it": "Saint Martin", "zh": "法属圣马丁", "nl": "Saint-Martin", "de": "St. Martin", "fr": "Saint-Martin", "es": "San Martín", "en": "St. Martin", "pt_BR": "São Martinho", "sr-Cyrl": "Свети Мартин", "sr-Latn": "Sveti Martin", "zh_TW": "聖馬丁", "tr": "Saint Martin", "ro": "Sfântul Martin", "ar": "تجمع سان مارتين", "fa": "سن مارتن", "yue": "聖馬丁（法國部分）"},
    flag: "🇲🇫",
    code: "MF",
    dialCode: "590",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Pierre and Miquelon",
    nameTranslations: {"sk": "Saint Pierre a Miquelon", "se": "Saint Pierre ja Miquelon", "pl": "Saint-Pierre i Miquelon", "no": "Saint-Pierre-et-Miquelon", "ja": "サンピエール島・ミクロン島", "it": "Saint-Pierre e Miquelon", "zh": "圣皮埃尔和密克隆群岛", "nl": "Saint-Pierre en Miquelon", "de": "St. Pierre und Miquelon", "fr": "Saint-Pierre-et-Miquelon", "es": "San Pedro y Miquelón", "en": "St. Pierre & Miquelon", "pt_BR": "São Pedro e Miquelon", "sr-Cyrl": "Сен Пјер и Микелон", "sr-Latn": "Sen Pjer i Mikelon", "zh_TW": "聖皮埃與密克隆群島", "tr": "Saint Pierre ve Miquelon", "ro": "Saint Pierre și Miquelon", "ar": "سان بيير وميكلون", "fa": "سن-پیر و میکلون", "yue": "聖皮埃尔同米克隆"},
    flag: "🇵🇲",
    code: "PM",
    dialCode: "508",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Saint Vincent and the Grenadines",
    nameTranslations: {"sk": "Svätý Vincent a Grenadíny", "se": "Saint Vincent ja Grenadine", "pl": "Saint Vincent i Grenadyny", "no": "St. Vincent og Grenadinene", "ja": "セントビンセント及びグレナディーン諸島", "it": "Saint Vincent e Grenadine", "zh": "圣文森特和格林纳丁斯", "nl": "Saint Vincent en de Grenadines", "de": "St. Vincent und die Grenadinen", "fr": "Saint-Vincent-et-les-Grenadines", "es": "San Vicente y las Granadinas", "en": "St. Vincent & Grenadines", "pt_BR": "São Vicente e Granadinas", "sr-Cyrl": "Свети Винсент и Гренадини", "sr-Latn": "Sveti Vinsent i Grenadini", "zh_TW": "聖文森及格瑞那丁", "tr": "Saint Vincent ve Grenadinler", "ro": "Sfântul Vincențiu și Grenadinele", "ar": "سانت فينسنت والغرينادين", "fa": "سنت وینسنت و گرنادین‌ها", "yue": "聖文森特同格林纳丁斯"},
    flag: "🇻🇨",
    code: "VC",
    dialCode: "1784",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Samoa",
    nameTranslations: {"sk": "Samoa", "se": "Samoa", "pl": "Samoa", "no": "Samoa", "ja": "サモア", "it": "Samoa", "zh": "萨摩亚", "nl": "Samoa", "de": "Samoa", "fr": "Samoa", "es": "Samoa", "en": "Samoa", "pt_BR": "Samoa", "sr-Cyrl": "Самоа", "sr-Latn": "Samoa", "zh_TW": "薩摩亞", "tr": "Samoa", "ro": "Samoa", "ar": "ساموا", "fa": "ساموآ", "yue": "薩摩亞"},
    flag: "🇼🇸",
    code: "WS",
    dialCode: "685",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "San Marino",
    nameTranslations: {"sk": "San Maríno", "se": "San Marino", "pl": "San Marino", "no": "San Marino", "ja": "サンマリノ", "it": "San Marino", "zh": "圣马力诺", "nl": "San Marino", "de": "San Marino", "fr": "Saint-Marin", "es": "San Marino", "en": "San Marino", "pt_BR": "San Marino", "sr-Cyrl": "Сан Марино", "sr-Latn": "San Marino", "zh_TW": "聖馬利諾", "tr": "San Marino", "ro": "San Marino", "ar": "سان مارينو", "fa": "سان مارینو", "yue": "聖馬力諾"},
    flag: "🇸🇲",
    code: "SM",
    dialCode: "378",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Sao Tome and Principe",
    nameTranslations: {"sk": "Svätý Tomáš a Princov ostrov", "se": "São Tomé ja Príncipe", "pl": "Wyspy Świętego Tomasza i Książęca", "no": "São Tomé og Príncipe", "ja": "サントメ・プリンシペ", "it": "São Tomé e Príncipe", "zh": "圣多美和普林西比", "nl": "Sao Tomé en Principe", "de": "São Tomé und Príncipe", "fr": "Sao Tomé-et-Principe", "es": "Santo Tomé y Príncipe", "en": "São Tomé & Príncipe", "pt_BR": "São Tomé e Príncipe", "sr-Cyrl": "Сао Томе и Принсипе", "sr-Latn": "Sao Tome i Prinsipe", "zh_TW": "聖多美普林西比", "tr": "São Tomé ve Príncipe", "ro": "Sao Tome şi Principe", "ar": "ساو تومي وبرينسيب", "fa": "سائوتومه و پرنسیپ", "yue": "聖多美和普林西比"},
    flag: "🇸🇹",
    code: "ST",
    dialCode: "239",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saudi Arabia",
    nameTranslations: {"sk": "Saudská Arábia", "se": "Saudi-Arábia", "pl": "Arabia Saudyjska", "no": "Saudi-Arabia", "ja": "サウジアラビア", "it": "Arabia Saudita", "zh": "沙特阿拉伯", "nl": "Saoedi-Arabië", "de": "Saudi-Arabien", "fr": "Arabie saoudite", "es": "Arabia Saudí", "en": "Saudi Arabia", "pt_BR": "Arábia Saudita", "sr-Cyrl": "Саудијска Арабија", "sr-Latn": "Saudijska Arabija", "zh_TW": "沙烏地阿拉", "tr": "Suudi Arabistan", "ro": "Arabia Saudită", "ar": "السعودية", "fa": "عربستان سعودی", "yue": "沙地阿拉伯"},
    flag: "🇸🇦",
    code: "SA",
    dialCode: "966",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Senegal",
    nameTranslations: {"sk": "Senegal", "se": "Senegal", "pl": "Senegal", "no": "Senegal", "ja": "セネガル", "it": "Senegal", "zh": "塞内加尔", "nl": "Senegal", "de": "Senegal", "fr": "Sénégal", "es": "Senegal", "en": "Senegal", "pt_BR": "Senegal", "sr-Cyrl": "Сенегал", "sr-Latn": "Senegal", "zh_TW": "塞內加爾", "tr": "Senegal", "ro": "Senegal", "ar": "السنغال", "fa": "سنگال", "yue": "塞內加爾"},
    flag: "🇸🇳",
    code: "SN",
    dialCode: "221",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Serbia",
    nameTranslations: {"sk": "Srbsko", "se": "Serbia", "pl": "Serbia", "no": "Serbia", "ja": "セルビア", "it": "Serbia", "zh": "塞尔维亚", "nl": "Servië", "de": "Serbien", "fr": "Serbie", "es": "Serbia", "en": "Serbia", "pt_BR": "Sérvia", "sr-Cyrl": "Србија", "sr-Latn": "Srbija", "zh_TW": "塞爾維亞", "tr": "Sırbistan", "ro": "Serbia", "ar": "صربيا", "fa": "صربستان", "yue": "塞爾維亞"},
    flag: "🇷🇸",
    code: "RS",
    dialCode: "381",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Seychelles",
    nameTranslations: {"sk": "Seychely", "se": "Seychellsullot", "pl": "Seszele", "no": "Seychellene", "ja": "セーシェル", "it": "Seychelles", "zh": "塞舌尔", "nl": "Seychellen", "de": "Seychellen", "fr": "Seychelles", "es": "Seychelles", "en": "Seychelles", "pt_BR": "Seychelles", "sr-Cyrl": "Сејшели", "sr-Latn": "Sejšeli", "zh_TW": "塞席爾", "tr": "Seyşeller", "ro": "Seychelles", "ar": "سيشل", "fa": "سیشل", "yue": "塞舌爾"},
    flag: "🇸🇨",
    code: "SC",
    dialCode: "248",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Sierra Leone",
    nameTranslations: {"sk": "Sierra Leone", "se": "Sierra Leone", "pl": "Sierra Leone", "no": "Sierra Leone", "ja": "シエラレオネ", "it": "Sierra Leone", "zh": "塞拉利昂", "nl": "Sierra Leone", "de": "Sierra Leone", "fr": "Sierra Leone", "es": "Sierra Leona", "en": "Sierra Leone", "pt_BR": "Serra Leoa", "sr-Cyrl": "Сијера Леоне", "sr-Latn": "Sijera Leone", "zh_TW": "獅子山", "tr": "Sierra Leone", "ro": "Sierra Leone", "ar": "سيراليون", "fa": "سیرالئون", "yue": "塞拉利昂"},
    flag: "🇸🇱",
    code: "SL",
    dialCode: "232",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Singapore",
    nameTranslations: {"sk": "Singapur", "se": "Singapore", "pl": "Singapur", "no": "Singapore", "ja": "シンガポール", "it": "Singapore", "zh": "新加坡", "nl": "Singapore", "de": "Singapur", "fr": "Singapour", "es": "Singapur", "en": "Singapore", "pt_BR": "Cingapura", "sr-Cyrl": "Сингапур", "sr-Latn": "Singapur", "zh_TW": "新加坡", "tr": "Singapur", "ro": "Singapore", "ar": "سنغافورة", "fa": "سنگاپور", "yue": "星架坡"},
    flag: "🇸🇬",
    code: "SG",
    dialCode: "65",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Slovakia",
    nameTranslations: {"sk": "Slovensko", "se": "Slovákia", "pl": "Słowacja", "no": "Slovakia", "ja": "スロバキア", "it": "Slovacchia", "zh": "斯洛伐克", "nl": "Slowakije", "de": "Slowakei", "fr": "Slovaquie", "es": "Eslovaquia", "en": "Slovakia", "pt_BR": "Eslováquia", "sr-Cyrl": "Словачка", "sr-Latn": "Slovačka", "zh_TW": "斯洛伐克", "tr": "Slovakya", "ro": "Slovacia", "ar": "سلوفاكيا", "fa": "اسلواکی", "yue": "斯洛伐克"},
    flag: "🇸🇰",
    code: "SK",
    dialCode: "421",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Slovenia",
    nameTranslations: {"sk": "Slovinsko", "se": "Slovenia", "pl": "Słowenia", "no": "Slovenia", "ja": "スロベニア", "it": "Slovenia", "zh": "斯洛文尼亚", "nl": "Slovenië", "de": "Slowenien", "fr": "Slovénie", "es": "Eslovenia", "en": "Slovenia", "pt_BR": "Eslovênia", "sr-Cyrl": "Словеније", "sr-Latn": "Slovenija", "zh_TW": "斯洛維尼亞", "tr": "Slovenya", "ro": "Slovenia", "ar": "سلوفينيا", "fa": "اسلوونی", "yue": "斯洛文尼亞"},
    flag: "🇸🇮",
    code: "SI",
    dialCode: "386",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Solomon Islands",
    nameTranslations: {"sk": "Šalamúnove ostrovy", "se": "Salomon-sullot", "pl": "Wyspy Salomona", "no": "Salomonøyene", "ja": "ソロモン諸島", "it": "Isole Salomone", "zh": "所罗门群岛", "nl": "Salomonseilanden", "de": "Salomonen", "fr": "Îles Salomon", "es": "Islas Salomón", "en": "Solomon Islands", "pt_BR": "Ilhas Salomão", "sr-Cyrl": "Соломонска Острва", "sr-Latn": "Solomonska Ostrva", "zh_TW": "所羅門群島", "tr": "Solomon Adaları", "ro": "Insulele Solomon", "ar": "جزر سليمان", "fa": "جزایر سلیمان", "yue": "所羅門群島"},
    flag: "🇸🇧",
    code: "SB",
    dialCode: "677",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Somalia",
    nameTranslations: {"sk": "Somálsko", "se": "Somália", "pl": "Somalia", "no": "Somalia", "ja": "ソマリア", "it": "Somalia", "zh": "索马里", "nl": "Somalië", "de": "Somalia", "fr": "Somalie", "es": "Somalia", "en": "Somalia", "pt_BR": "Somália", "sr-Cyrl": "Сомалија", "sr-Latn": "Somalija", "zh_TW": "索馬利亞", "tr": "Somali", "ro": "Somalia", "ar": "الصومال", "fa": "سومالی", "yue": "索馬里"},
    flag: "🇸🇴",
    code: "SO",
    dialCode: "252",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "South Africa",
    nameTranslations: {"sk": "Južná Afrika", "se": "Mátta-Afrihká", "pl": "Republika Południowej Afryki", "no": "Sør-Afrika", "ja": "南アフリカ", "it": "Sudafrica", "zh": "南非", "nl": "Zuid-Afrika", "de": "Südafrika", "fr": "Afrique du Sud", "es": "Sudáfrica", "en": "South Africa", "pt_BR": "África do Sul", "sr-Cyrl": "Јужноафричка Република", "sr-Latn": "Južnoafrička Republika", "zh_TW": "南非", "tr": "Güney Afrika", "ro": "Africa de Sud", "ar": "جنوب أفريقيا", "fa": "آفریقای جنوبی", "yue": "南非"},
    flag: "🇿🇦",
    code: "ZA",
    dialCode: "27",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "South Sudan",
    nameTranslations: {"sk": "Južný Sudán", "se": "Máttasudan", "pl": "Sudan Południowy", "no": "Sør-Sudan", "ja": "南スーダン", "it": "Sud Sudan", "zh": "南苏丹", "nl": "Zuid-Soedan", "de": "Südsudan", "fr": "Soudan du Sud", "es": "Sudán del Sur", "en": "South Sudan", "pt_BR": "Sudão do Sul", "sr-Cyrl": "Јужни Судан", "sr-Latn": "Južni Sudan", "zh_TW": "南蘇丹", "tr": "Güney Sudan", "ro": "Sudanul de Sud", "ar": "جنوب السودان", "fa": "سودان جنوبی", "yue": "南蘇丹"},
    flag: "🇸🇸",
    code: "SS",
    dialCode: "211",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "South Georgia and the South Sandwich Islands",
    nameTranslations: {
      "sk": "Južná Georgia a Južné Sandwichove ostrovy",
      "se": "Lulli Georgia ja Lulli Sandwich-sullot",
      "pl": "Georgia Południowa i Sandwich Południowy",
      "no": "Sør-Georgia og Sør-Sandwichøyene",
      "ja": "サウスジョージア・サウスサンドウィッチ諸島",
      "it": "Georgia del Sud e Sandwich australi",
      "zh": "南乔治亚和南桑威奇群岛",
      "nl": "Zuid-Georgia en Zuidelijke Sandwicheilanden",
      "de": "Südgeorgien und die Südlichen Sandwichinseln",
      "fr": "Géorgie du Sud et îles Sandwich du Sud",
      "es": "Islas Georgia del Sur y Sandwich del Sur",
      "en": "South Georgia & South Sandwich Islands",
      "pt_BR": "Geórgia do Sul e Ilhas Sandwich do Sul",
      "sr-Cyrl": "Јужна Џорџија и Јужна Сендвичка Острва",
      "sr-Latn": "Južna Džordžija i Južna Sendvička Ostrva",
      "zh_TW": "南喬治亞與南三明治群島 ",
      "tr": "Güney Georgia ve Güney Sandwich Adaları",
      "ro": "Georgia de Sud și Insulele Sandwich de Sud",
      "ar": "جورجيا الجنوبية وجزر ساندويتش الجنوبية",
      "fa": "جزایر جورجیای جنوبی و ساندویچ جنوبی",
      "yue": "南喬治亞州同南桑威奇群島"
    },
    flag: "🇬🇸",
    code: "GS",
    dialCode: "500",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Spain",
    nameTranslations: {"sk": "Španielsko", "se": "Spánia", "pl": "Hiszpania", "no": "Spania", "ja": "スペイン", "it": "Spagna", "zh": "西班牙", "nl": "Spanje", "de": "Spanien", "fr": "Espagne", "es": "España", "en": "Spain", "pt_BR": "Espanha", "sr-Cyrl": "Шпанија", "sr-Latn": "Španija", "zh_TW": "西班牙", "tr": "İspanya", "ro": "Spania", "ar": "إسبانيا", "fa": "اسپانیا", "yue": "西班牙"},
    flag: "🇪🇸",
    code: "ES",
    dialCode: "34",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Sri Lanka",
    nameTranslations: {"sk": "Srí Lanka", "se": "Sri Lanka", "pl": "Sri Lanka", "no": "Sri Lanka", "ja": "スリランカ", "it": "Sri Lanka", "zh": "斯里兰卡", "nl": "Sri Lanka", "de": "Sri Lanka", "fr": "Sri Lanka", "es": "Sri Lanka", "en": "Sri Lanka", "pt_BR": "Sri Lanka", "sr-Cyrl": "Шри Ланка", "sr-Latn": "Šri Lanka", "zh_TW": "斯里蘭卡", "tr": "Sri Lanka", "ro": "Sri Lanka", "ar": "سريلانكا", "fa": "سریلانکا", "yue": "斯里蘭卡"},
    flag: "🇱🇰",
    code: "LK",
    dialCode: "94",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Sudan",
    nameTranslations: {"sk": "Sudán", "se": "Davvisudan", "pl": "Sudan", "no": "Sudan", "ja": "スーダン", "it": "Sudan", "zh": "苏丹", "nl": "Soedan", "de": "Sudan", "fr": "Soudan", "es": "Sudán", "en": "Sudan", "pt_BR": "Sudão", "sr-Cyrl": "Судан", "sr-Latn": "Sudan", "zh_TW": "蘇丹", "tr": "Sudan", "ro": "Sudan", "ar": "السودان", "fa": "سودان", "yue": "蘇丹"},
    flag: "🇸🇩",
    code: "SD",
    dialCode: "249",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Suriname",
    nameTranslations: {"sk": "Surinam", "se": "Surinam", "pl": "Surinam", "no": "Surinam", "ja": "スリナム", "it": "Suriname", "zh": "苏里南", "nl": "Suriname", "de": "Suriname", "fr": "Suriname", "es": "Surinam", "en": "Suriname", "pt_BR": "Suriname", "sr-Cyrl": "Суринам", "sr-Latn": "Surinam", "zh_TW": "蘇利南", "tr": "Surinam", "ro": "Surinam", "ar": "سورينام", "fa": "سورینام", "yue": "蘇里南"},
    flag: "🇸🇷",
    code: "SR",
    dialCode: "597",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Svalbard and Jan Mayen",
    nameTranslations: {"sk": "Svalbard a Jan Mayen", "se": "Svalbárda ja Jan Mayen", "pl": "Svalbard i Jan Mayen", "no": "Svalbard og Jan Mayen", "ja": "スバールバル諸島・ヤンマイエン島", "it": "Svalbard e Jan Mayen", "zh": "斯瓦尔巴和扬马延", "nl": "Spitsbergen en Jan Mayen", "de": "Spitzbergen und Jan Mayen", "fr": "Svalbard et Jan Mayen", "es": "Svalbard y Jan Mayen", "en": "Svalbard & Jan Mayen", "pt_BR": "Svalbard e Jan Mayen", "sr-Cyrl": "Свалбард", "sr-Latn": "Svalbard", "zh_TW": "斯瓦巴及尖棉", "tr": "Svalbard ve Jan Mayen", "ro": "Svalbard și Jan Mayen", "ar": "سفالبارد ويان ماين", "fa": "سوالبارد و یان ماین", "yue": "斯瓦尔巴德同扬·马延"},
    flag: "🇸🇯",
    code: "SJ",
    dialCode: "47",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Eswatini",
    nameTranslations: {"sk": "Eswatini", "se": "Svazieana", "pl": "Eswatini", "no": "Eswatini", "ja": "エスワティニ", "it": "Swaziland", "zh": "斯威士兰", "nl": "eSwatini", "de": "Eswatini", "fr": "Eswatini", "es": "Esuatini", "en": "Eswatini", "pt_BR": "Eswatini", "sr-Cyrl": "Свазиланд", "sr-Latn": "Svaziland", "zh_TW": "史瓦帝尼", "tr": "Esvatini", "ro": "Eswatini", "ar": "إسواتيني", "fa": "اسواتینی", "yue": "斯威士蘭"},
    flag: "🇸🇿",
    code: "SZ",
    dialCode: "268",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Sweden",
    nameTranslations: {"sk": "Švédsko", "se": "Ruoŧŧa", "pl": "Szwecja", "no": "Sverige", "ja": "スウェーデン", "it": "Svezia", "zh": "瑞典", "nl": "Zweden", "de": "Schweden", "fr": "Suède", "es": "Suecia", "en": "Sweden", "pt_BR": "Suécia", "sr-Cyrl": "Шведска", "sr-Latn": "Švedska", "zh_TW": "瑞典", "tr": "İsveç", "ro": "Suedia", "ar": "السويد", "fa": "سوئد", "yue": "瑞典"},
    flag: "🇸🇪",
    code: "SE",
    dialCode: "46",
    minLength: 7,
    maxLength: 13,
  ),
  Country(
    name: "Switzerland",
    nameTranslations: {"sk": "Švajčiarsko", "se": "Šveica", "pl": "Szwajcaria", "no": "Sveits", "ja": "スイス", "it": "Svizzera", "zh": "瑞士", "nl": "Zwitserland", "de": "Schweiz", "fr": "Suisse", "es": "Suiza", "en": "Switzerland", "pt_BR": "Suíça", "sr-Cyrl": "Швајцарска", "sr-Latn": "Švajcarska", "zh_TW": "瑞士", "tr": "İsviçre", "ro": "Elveţia", "ar": "سويسرا", "fa": "سوئیس", "yue": "瑞士"},
    flag: "🇨🇭",
    code: "CH",
    dialCode: "41",
    minLength: 9,
    maxLength: 12,
  ),
  Country(
    name: "Syrian Arab Republic",
    nameTranslations: {"sk": "Sýria", "se": "Syria", "pl": "Syria", "no": "Syria", "ja": "シリア", "it": "Siria", "zh": "叙利亚", "nl": "Syrië", "de": "Syrien", "fr": "Syrie", "es": "Siria", "en": "Syria", "pt_BR": "Síria", "sr-Cyrl": "Сирија", "sr-Latn": "Sirija", "zh_TW": "敘利亞", "tr": "Suriye", "ro": "Siria", "ar": "سوريا", "fa": "سوریه", "yue": "阿拉伯敘利亞共和國"},
    flag: "🇸🇾",
    code: "SY",
    dialCode: "963",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Taiwan",
    nameTranslations: {"sk": "Taiwan", "se": "Taiwan", "pl": "Tajwan", "no": "Taiwan", "ja": "台湾", "it": "Taiwan", "zh": "台湾", "nl": "Taiwan", "de": "Taiwan", "fr": "Taïwan", "es": "Taiwán", "en": "Taiwan", "pt_BR": "Taiwan", "sr-Cyrl": "Тајван", "sr-Latn": "Tajvan", "zh_TW": "台灣", "tr": "Tayvan", "ro": "Taiwan", "ar": "تايوان", "fa": "تایوان", "yue": "台灣"},
    flag: "🇹🇼",
    code: "TW",
    dialCode: "886",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Tajikistan",
    nameTranslations: {"sk": "Tadžikistan", "se": "Tažikistan", "pl": "Tadżykistan", "no": "Tadsjikistan", "ja": "タジキスタン", "it": "Tagikistan", "zh": "塔吉克斯坦", "nl": "Tadzjikistan", "de": "Tadschikistan", "fr": "Tadjikistan", "es": "Tayikistán", "en": "Tajikistan", "pt_BR": "Tajiquistão", "sr-Cyrl": "Таџикистан", "sr-Latn": "Tadžikistan", "zh_TW": "塔吉克", "tr": "Tacikistan", "ro": "Tadiquistão", "ar": "طاجيكستان", "fa": "تاجیکستان", "yue": "塔吉克斯坦"},
    flag: "🇹🇯",
    code: "TJ",
    dialCode: "992",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Tanzania, United Republic of Tanzania",
    nameTranslations: {"sk": "Tanzánia", "se": "Tanzánia", "pl": "Tanzania", "no": "Tanzania", "ja": "タンザニア", "it": "Tanzania", "zh": "坦桑尼亚", "nl": "Tanzania", "de": "Tansania", "fr": "Tanzanie", "es": "Tanzania", "en": "Tanzania", "pt_BR": "Tanzânia", "sr-Cyrl": "Танзанија", "sr-Latn": "Tanzanija", "zh_TW": "坦尚尼亞", "tr": "Tanzanya", "ro": "Tanzania", "ar": "تنزانيا", "fa": "تانزانیا", "yue": "坦桑尼亞，聯合共和國"},
    flag: "🇹🇿",
    code: "TZ",
    dialCode: "255",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Thailand",
    nameTranslations: {"sk": "Thajsko", "se": "Thaieana", "pl": "Tajlandia", "no": "Thailand", "ja": "タイ", "it": "Thailandia", "zh": "泰国", "nl": "Thailand", "de": "Thailand", "fr": "Thaïlande", "es": "Tailandia", "en": "Thailand", "pt_BR": "Tailândia", "sr-Cyrl": "Тајланд", "sr-Latn": "Tajland", "zh_TW": "泰國", "tr": "Tayland", "ro": "Tailanda", "ar": "تايلاند", "fa": "تایلند", "yue": "泰國"},
    flag: "🇹🇭",
    code: "TH",
    dialCode: "66",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Timor-Leste",
    nameTranslations: {"sk": "Východný Timor", "se": "Nuorta-Timor", "pl": "Timor Wschodni", "no": "Øst-Timor", "ja": "東ティモール", "it": "Timor Est", "zh": "东帝汶", "nl": "Oost-Timor", "de": "Timor-Leste", "fr": "Timor oriental", "es": "Timor-Leste", "en": "Timor-Leste", "pt_BR": "Timor-Leste", "sr-Cyrl": "Источни Тимор", "sr-Latn": "Istočni Timor", "zh_TW": "東帝汶", "tr": "Doğu Timor", "ro": "Timorul de Est", "ar": "تيمور الشرقية", "fa": "تیمور شرقی", "yue": "東帝汶"},
    flag: "🇹🇱",
    code: "TL",
    dialCode: "670",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Togo",
    nameTranslations: {"sk": "Togo", "se": "Togo", "pl": "Togo", "no": "Togo", "ja": "トーゴ", "it": "Togo", "zh": "多哥", "nl": "Togo", "de": "Togo", "fr": "Togo", "es": "Togo", "en": "Togo", "pt_BR": "Ir", "sr-Cyrl": "Того", "sr-Latn": "Togo", "zh_TW": "多哥", "tr": "Togo", "ro": "Togo", "ar": "توغو", "fa": "توگو", "yue": "多哥"},
    flag: "🇹🇬",
    code: "TG",
    dialCode: "228",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Tokelau",
    nameTranslations: {"sk": "Tokelau", "se": "Tokelau", "pl": "Tokelau", "no": "Tokelau", "ja": "トケラウ", "it": "Tokelau", "zh": "托克劳", "nl": "Tokelau", "de": "Tokelau", "fr": "Tokelau", "es": "Tokelau", "en": "Tokelau", "pt_BR": "Tokelau", "sr-Cyrl": "Токелау", "sr-Latn": "Tokelau", "zh_TW": "托克勞", "tr": "Tokelau", "ro": "Tokelau", "ar": "توكيلاو", "fa": "توکلائو", "yue": "托克劳"},
    flag: "🇹🇰",
    code: "TK",
    dialCode: "690",
    minLength: 4,
    maxLength: 4,
  ),
  Country(
    name: "Tonga",
    nameTranslations: {"sk": "Tonga", "se": "Tonga", "pl": "Tonga", "no": "Tonga", "ja": "トンガ", "it": "Tonga", "zh": "汤加", "nl": "Tonga", "de": "Tonga", "fr": "Tonga", "es": "Tonga", "en": "Tonga", "pt_BR": "Tonga", "sr-Cyrl": "Тонга", "sr-Latn": "Tonga", "zh_TW": "東加", "tr": "Tonga", "ro": "Tonga", "ar": "تونغا", "fa": "تونگا", "yue": "湯加"},
    flag: "🇹🇴",
    code: "TO",
    dialCode: "676",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Trinidad and Tobago",
    nameTranslations: {"sk": "Trinidad a Tobago", "se": "Trinidad ja Tobago", "pl": "Trynidad i Tobago", "no": "Trinidad og Tobago", "ja": "トリニダード・トバゴ", "it": "Trinidad e Tobago", "zh": "特立尼达和多巴哥", "nl": "Trinidad en Tobago", "de": "Trinidad und Tobago", "fr": "Trinité-et-Tobago", "es": "Trinidad y Tobago", "en": "Trinidad & Tobago", "pt_BR": "Trinidad e Tobago", "sr-Cyrl": "Тринидад и Тобаго", "sr-Latn": "Trinidad i Tobago", "zh_TW": "千里達及托巴哥", "tr": "Trinidad ve Tobago", "ro": "Trinidad şi Tobago", "ar": "ترينيداد وتوباغو", "fa": "ترینیداد و توباگو", "yue": "特立尼達和多巴哥"},
    flag: "🇹🇹",
    code: "TT",
    dialCode: "1868",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Tunisia",
    nameTranslations: {"sk": "Tunisko", "se": "Tunisia", "pl": "Tunezja", "no": "Tunisia", "ja": "チュニジア", "it": "Tunisia", "zh": "突尼斯", "nl": "Tunesië", "de": "Tunesien", "fr": "Tunisie", "es": "Túnez", "en": "Tunisia", "pt_BR": "Tunísia", "sr-Cyrl": "Тунис", "sr-Latn": "Tunis", "zh_TW": "突尼西亞", "tr": "Tunus", "ro": "Tunisia", "ar": "تونس", "fa": "تونس", "yue": "突尼斯"},
    flag: "🇹🇳",
    code: "TN",
    dialCode: "216",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Turkey",
    nameTranslations: {"sk": "Turecko", "se": "Durka", "pl": "Turcja", "no": "Tyrkia", "ja": "トルコ", "it": "Turchia", "zh": "土耳其", "nl": "Turkije", "de": "Türkei", "fr": "Turquie", "es": "Turquía", "en": "Turkey", "pt_BR": "Peru", "sr-Cyrl": "Турска", "sr-Latn": "Turska", "zh_TW": "土耳其", "tr": "Türkiye", "ro": "Turcia", "ar": "تركيا", "fa": "ترکیه", "yue": "土耳其"},
    flag: "🇹🇷",
    code: "TR",
    dialCode: "90",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Turkmenistan",
    nameTranslations: {"sk": "Turkménsko", "se": "Turkmenistan", "pl": "Turkmenistan", "no": "Turkmenistan", "ja": "トルクメニスタン", "it": "Turkmenistan", "zh": "土库曼斯坦", "nl": "Turkmenistan", "de": "Turkmenistan", "fr": "Turkménistan", "es": "Turkmenistán", "en": "Turkmenistan", "pt_BR": "Turcomenistão", "sr-Cyrl": "Туркменистан", "sr-Latn": "Turkmenistan", "zh_TW": "土庫曼", "tr": "Türkmenistan", "ro": "Turkmenistan", "ar": "تركمانستان", "fa": "ترکمنستان", "yue": "土庫曼斯坦"},
    flag: "🇹🇲",
    code: "TM",
    dialCode: "993",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Turks and Caicos Islands",
    nameTranslations: {"sk": "Turks a Caicos", "se": "Turks ja Caicos-sullot", "pl": "Turks i Caicos", "no": "Turks- og Caicosøyene", "ja": "タークス・カイコス諸島", "it": "Isole Turks e Caicos", "zh": "特克斯和凯科斯群岛", "nl": "Turks- en Caicoseilanden", "de": "Turks- und Caicosinseln", "fr": "Îles Turques-et-Caïques", "es": "Islas Turcas y Caicos", "en": "Turks & Caicos Islands", "pt_BR": "Ilhas Turks e Caicos", "sr-Cyrl": "Туркс и Кајкос", "sr-Latn": "Turks i Kajkos", "zh_TW": "土克斯及開科斯群島", "tr": "Turks ve Caicos Adaları", "ro": "Insulele Turks și Caicos", "ar": "جزر توركس وكايكوس", "fa": "جزایر تورکس و کایکوس", "yue": "特克斯同凯科斯群岛"},
    flag: "🇹🇨",
    code: "TC",
    dialCode: "1649",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Tuvalu",
    nameTranslations: {"sk": "Tuvalu", "se": "Tuvalu", "pl": "Tuvalu", "no": "Tuvalu", "ja": "ツバル", "it": "Tuvalu", "zh": "图瓦卢", "nl": "Tuvalu", "de": "Tuvalu", "fr": "Tuvalu", "es": "Tuvalu", "en": "Tuvalu", "pt_BR": "Tuvalu", "sr-Cyrl": "Тувалу", "sr-Latn": "Tuvalu", "zh_TW": "圖瓦盧", "tr": "Tuvalu", "ro": "Tuvalu", "ar": "توفالو", "fa": "تووالو", "yue": "圖瓦盧"},
    flag: "🇹🇻",
    code: "TV",
    dialCode: "688",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Uganda (UG)",
    nameTranslations: {"sk": "Uganda", "se": "Uganda", "pl": "Uganda", "no": "Uganda", "ja": "ウガンダ", "it": "Uganda", "zh": "乌干达", "nl": "Oeganda", "de": "Uganda", "fr": "Ouganda", "es": "Uganda", "en": "Uganda", "pt_BR": "Uganda", "sr-Cyrl": "Уганда", "sr-Latn": "Uganda", "zh_TW": "烏干達", "tr": "Uganda", "ro": "Uganda", "ar": "أوغندا", "fa": "اوگاندا", "yue": "烏干達"},
    flag: "🇺🇬",
    code: "UG",
    dialCode: "256",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Ukraine (UA)",
    nameTranslations: {"sk": "Ukrajina", "se": "Ukraina", "pl": "Ukraina", "no": "Ukraina", "ja": "ウクライナ", "it": "Ucraina", "zh": "乌克兰", "nl": "Oekraïne", "de": "Ukraine", "fr": "Ukraine", "es": "Ucrania", "en": "Ukraine", "pt_BR": "Ucrânia", "sr-Cyrl": "Украјина", "sr-Latn": "Ukrajina", "zh_TW": "烏克蘭", "tr": "Ukrayna", "ro": "Ucraína", "ar": "أوكرانيا", "fa": "اوکراین", "yue": "烏克蘭"},
    flag: "🇺🇦",
    code: "UA",
    dialCode: "380",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "United Arab Emirates",
    nameTranslations: {"sk": "Spojené arabské emiráty", "se": "Ovttastuvvan Arábaemiráhtat", "pl": "Zjednoczone Emiraty Arabskie", "no": "De forente arabiske emirater", "ja": "アラブ首長国連邦", "it": "Emirati Arabi Uniti", "zh": "阿拉伯联合酋长国", "nl": "Verenigde Arabische Emiraten", "de": "Vereinigte Arabische Emirate", "fr": "Émirats arabes unis", "es": "Emiratos Árabes Unidos", "en": "United Arab Emirates", "pt_BR": "Emirados Árabes Unidos", "sr-Cyrl": "Уједињени Арапски Емирати", "sr-Latn": "Ujedinjeni Arapski Emirati", "zh_TW": "阿拉伯聯合大公國", "tr": "Birleşik Arap Emirlikleri", "ro": "Emiratele Arabe Unite", "ar": "الإمارات العربية المتحدة", "fa": "امارات متحده عربی", "yue": "阿拉伯聯合酋長國"},
    flag: "🇦🇪",
    code: "AE",
    dialCode: "971",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "United Kingdom (UK)",
    nameTranslations: {"sk": "Spojené kráľovstvo", "se": "Stuorra-Británnia", "pl": "Wielka Brytania", "no": "Storbritannia", "ja": "イギリス", "it": "Regno Unito", "zh": "英国", "nl": "Verenigd Koninkrijk", "de": "Vereinigtes Königreich", "fr": "Royaume-Uni", "es": "Reino Unido", "en": "United Kingdom", "pt_BR": "Reino Unido", "sr-Cyrl": "Уједињено Краљевство", "sr-Latn": "Ujedinjeno Kraljevstvo", "zh_TW": "英國", "tr": "Büyük Britanya ve Kuzey İrlanda Birleşik Krallığ", "ro": "Regatul Unit al Marii Britanii și Irlandei de Nord", "ar": "المملكة المتحدة", "fa": "بریتانیا", "yue": "大不列顛及北愛爾蘭聯合王國"},
    flag: "🇬🇧",
    code: "GB",
    dialCode: "44",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "United States (USA)",
    nameTranslations: {"sk": "Spojené štáty", "se": "Amerihká ovttastuvvan stáhtat", "pl": "Stany Zjednoczone", "no": "USA", "ja": "アメリカ合衆国", "it": "Stati Uniti", "zh": "美国", "nl": "Verenigde Staten", "de": "Vereinigte Staaten", "fr": "États-Unis", "es": "Estados Unidos", "en": "United States", "pt_BR": "Estados Unidos", "sr-Cyrl": "Сједињене Америчке Државе", "sr-Latn": "Sjedinjene Američke Države", "zh_TW": "美國", "tr": "Amerika Birleşik Devletleri", "ro": "Statele Unite ale Americii", "ar": "الولايات المتحدة", "fa": "ایالات متحده آمریکا", "yue": "美利堅郃眾囯"},
    flag: "🇺🇸",
    code: "US",
    dialCode: "1",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Uruguay (UY)",
    nameTranslations: {"sk": "Uruguaj", "se": "Uruguay", "pl": "Urugwaj", "no": "Uruguay", "ja": "ウルグアイ", "it": "Uruguay", "zh": "乌拉圭", "nl": "Uruguay", "de": "Uruguay", "fr": "Uruguay", "es": "Uruguay", "en": "Uruguay", "pt_BR": "Uruguai", "sr-Cyrl": "Уругвај", "sr-Latn": "Urugvaj", "zh_TW": "烏拉圭", "tr": "Uruguay", "ro": "Uruguay", "ar": "الأوروغواي", "fa": "اروگوئه", "yue": "烏拉圭"},
    flag: "🇺🇾",
    code: "UY",
    dialCode: "598",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Uzbekistan (UZ)",
    nameTranslations: {"sk": "Uzbekistan", "se": "Usbekistan", "pl": "Uzbekistan", "no": "Usbekistan", "ja": "ウズベキスタン", "it": "Uzbekistan", "zh": "乌兹别克斯坦", "nl": "Oezbekistan", "de": "Usbekistan", "fr": "Ouzbékistan", "es": "Uzbekistán", "en": "Uzbekistan", "pt_BR": "Uzbequistão", "sr-Cyrl": "Узбекистан", "sr-Latn": "Uzbekistan", "zh_TW": "烏玆別克", "tr": "Özbekistan", "ro": "Uzbekistan", "ar": "أوزبكستان", "fa": "ازبکستان", "yue": "月即別"},
    flag: "🇺🇿",
    code: "UZ",
    dialCode: "998",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Vanuatu",
    nameTranslations: {"sk": "Vanuatu", "se": "Vanuatu", "pl": "Vanuatu", "no": "Vanuatu", "ja": "バヌアツ", "it": "Vanuatu", "zh": "瓦努阿图", "nl": "Vanuatu", "de": "Vanuatu", "fr": "Vanuatu", "es": "Vanuatu", "en": "Vanuatu", "pt_BR": "Vanuatu", "sr-Cyrl": "Вануату", "sr-Latn": "Vanuatu", "zh_TW": "瓦努阿圖", "tr": "Vanuatu", "ro": "Vanuatu", "ar": "فانواتو", "fa": "وانواتو", "yue": "瓦努阿圖"},
    flag: "🇻🇺",
    code: "VU",
    dialCode: "678",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Venezuela, Bolivarian Republic of Venezuela",
    nameTranslations: {"sk": "Venezuela", "se": "Venezuela", "pl": "Wenezuela", "no": "Venezuela", "ja": "ベネズエラ", "it": "Venezuela", "zh": "委内瑞拉", "nl": "Venezuela", "de": "Venezuela", "fr": "Venezuela", "es": "Venezuela", "en": "Venezuela", "pt_BR": "Venezuela", "sr-Cyrl": "Венецуела", "sr-Latn": "Venecuela", "zh_TW": "委內瑞拉", "tr": "Venezuela", "ro": "Venezuela", "ar": "فنزويلا", "fa": "ونزوئلا", "yue": "委內瑞拉（玻利瓦爾共和國）"},
    flag: "🇻🇪",
    code: "VE",
    dialCode: "58",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Vietnam (VN)",
    nameTranslations: {"sk": "Vietnam", "se": "Vietnam", "pl": "Wietnam", "no": "Vietnam", "ja": "ベトナム", "it": "Vietnam", "zh": "越南", "nl": "Vietnam", "de": "Vietnam", "fr": "Vietnam", "es": "Vietnam", "en": "Vietnam", "pt_BR": "Vietnã", "sr-Cyrl": "Вијетнам", "sr-Latn": "Vijetnam", "zh_TW": "越南", "tr": "Vietnam", "ro": "Vietnam", "ar": "فيتنام", "fa": "ویتنام", "yue": "越南"},
    flag: "🇻🇳",
    code: "VN",
    dialCode: "84",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Virgin Islands, British",
    nameTranslations: {"sk": "Britské Panenské ostrovy", "se": "Brittania Virgin-sullot", "pl": "Brytyjskie Wyspy Dziewicze", "no": "De britiske jomfruøyene", "ja": "英領ヴァージン諸島", "it": "Isole Vergini Britanniche", "zh": "英属维尔京群岛", "nl": "Britse Maagdeneilanden", "de": "Britische Jungferninseln", "fr": "Îles Vierges britanniques", "es": "Islas Vírgenes Británicas", "en": "British Virgin Islands", "pt_BR": "Ilhas Virgens Britânicas", "sr-Cyrl": "Британска Девичанска Острва", "sr-Latn": "Britanska Devičanska Ostrva", "zh_TW": "英屬維京群島", "tr": "Britanya Virjin Adaları", "ro": "Insulele Virgine Britanice", "ar": "جزر العذراء البريطانية", "fa": "جزایر ویرجین بریتانیا", "yue": "維爾京群島（英國）"},
    flag: "🇻🇬",
    code: "VG",
    dialCode: "1284",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Virgin Islands, U.S.",
    nameTranslations: {"sk": "Americké Panenské ostrovy", "se": "AOS Virgin-sullot", "pl": "Wyspy Dziewicze Stanów Zjednoczonych", "no": "De amerikanske jomfruøyene", "ja": "米領ヴァージン諸島", "it": "Isole Vergini Americane", "zh": "美属维尔京群岛", "nl": "Amerikaanse Maagdeneilanden", "de": "Amerikanische Jungferninseln", "fr": "Îles Vierges des États-Unis", "es": "Islas Vírgenes de EE. UU.", "en": "U.S. Virgin Islands", "pt_BR": "Ilhas Virgens Americanas", "sr-Cyrl": "Амепичка Девичанска Острва", "sr-Latn": "Američka Devičanska Ostrva", "zh_TW": "美屬維京群島", "tr": "Amerika Birleşik Devletleri Virjin Adaları", "ro": "Insulele Virgine Americane", "ar": "جزر العذراء الأمريكية", "fa": "جزایر ویرجین ایالات متحده آمریکا", "yue": "維爾京群島（美國）"},
    flag: "🇻🇮",
    code: "VI",
    dialCode: "1340",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Wallis and Futuna",
    nameTranslations: {"sk": "Wallis a Futuna", "se": "Wallis ja Futuna", "pl": "Wallis i Futuna", "no": "Wallis og Futuna", "ja": "ウォリス・フツナ", "it": "Wallis e Futuna", "zh": "瓦利斯和富图纳", "nl": "Wallis en Futuna", "de": "Wallis und Futuna", "fr": "Wallis-et-Futuna", "es": "Wallis y Futuna", "en": "Wallis & Futuna", "pt_BR": "Wallis e Futuna", "sr-Cyrl": "Валис и Футуна", "sr-Latn": "Valis i Futuna", "zh_TW": "瓦利斯和富圖那", "tr": "Wallis ve Futuna", "ro": "Wallis și Futuna", "ar": "والس وفوتونا", "fa": "والیس و فوتونا", "yue": "瓦利斯同富图纳"},
    flag: "🇼🇫",
    code: "WF",
    dialCode: "681",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Yemen (YE)",
    nameTranslations: {"sk": "Jemen", "se": "Jemen", "pl": "Jemen", "no": "Jemen", "ja": "イエメン", "it": "Yemen", "zh": "也门", "nl": "Jemen", "de": "Jemen", "fr": "Yémen", "es": "Yemen", "en": "Yemen", "pt_BR": "Iémen", "sr-Cyrl": "Јемен", "sr-Latn": "Jemen", "zh_TW": "葉門", "tr": "Yemen", "ro": "Yemen", "ar": "اليمن", "fa": "یمن", "yue": "也門"},
    flag: "🇾🇪",
    code: "YE",
    dialCode: "967",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Zambia",
    nameTranslations: {"sk": "Zambia", "se": "Zambia", "pl": "Zambia", "no": "Zambia", "ja": "ザンビア", "it": "Zambia", "zh": "赞比亚", "nl": "Zambia", "de": "Sambia", "fr": "Zambie", "es": "Zambia", "en": "Zambia", "pt_BR": "Zâmbia", "sr-Cyrl": "Замбија", "sr-Latn": "Zambija", "zh_TW": "贊比亞", "tr": "Zambiya", "ro": "Zambia", "ar": "زامبيا", "fa": "زامبیا", "yue": "贊比亞"},
    flag: "🇿🇲",
    code: "ZM",
    dialCode: "260",
    minLength: 9,
    maxLength: 9,
  ),
  Country(name: "Zimbabwe",
      nameTranslations: {"sk": "Zimbabwe", "se": "Zimbabwe", "pl": "Zimbabwe", "no": "Zimbabwe", "ja": "ジンバブエ", "it": "Zimbabwe", "zh": "津巴布韦", "nl": "Zimbabwe", "de": "Simbabwe", "fr": "Zimbabwe", "es": "Zimbabue", "en": "Zimbabwe", "pt_BR": "Zimbábue", "sr-Cyrl": "Зимбабве", "sr-Latn": "Zimbabve", "zh_TW": "辛巴威", "tr": "Zimbabve", "ro": "Zimbabwe", "ar": "زيمبابوي", "fa": "زیمبابوه", "yue": "津巴布韋"},
      flag: "🇿🇼",
      code: "ZW",
      dialCode: "263",
      minLength: 9,
      maxLength: 9)
];

class Country {
  final String name;
  final Map<String, String> nameTranslations;
  final String flag;
  final String code;
  final String dialCode;
  final String regionCode;
  final int minLength;
  final int maxLength;

  const Country({
    required this.name,
    required this.flag,
    required this.code,
    required this.dialCode,
    required this.nameTranslations,
    required this.minLength,
    required this.maxLength,
    this.regionCode = "",
  });

  String get fullCountryCode {
    return dialCode + regionCode;
  }

  String get displayCC {
    if (regionCode != "") {
      return "$dialCode $regionCode";
    }
    return dialCode;
  }

  String localizedName(String languageCode) {
    return nameTranslations[languageCode] ?? name;
  }
}
