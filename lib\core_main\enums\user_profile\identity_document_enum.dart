enum IdentityDocument {
  drivingLicense('Driving License', 0),
  aad<PERSON>ar<PERSON>ard('Aadhaar Card', 1),
  passport('Passport', 2),
  panCard('PAN Card', 3),
  voterIDCard('Voter ID Card', 4);

  final String description;
  final int value;

  const IdentityDocument(this.description, this.value);

  static IdentityDocument? fromJson(int? value) {
    if (value == null) {
      throw ArgumentError.notNull("No enum found for the following value");
    }
    return IdentityDocument.values.firstWhere(
      (e) => e.value == value
    );
  }

  static int? to<PERSON>son(IdentityDocument? genderEnum) => genderEnum?.value;
}
