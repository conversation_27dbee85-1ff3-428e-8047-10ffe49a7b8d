import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/main.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:math' as math;

class LeadratCustomCamera extends StatefulWidget {
  final CameraLensDirection lensDirection;
  final ResolutionPreset resolutionPreset;
  final bool enableFlashToggle;
  final bool enableFlipCamera;
  final Color? backgroundColor;

  const LeadratCustomCamera({
    Key? key,
    this.lensDirection = CameraLensDirection.front,
    this.resolutionPreset = ResolutionPreset.high,
    this.enableFlashToggle = true,
    this.enableFlipCamera = true,
    this.backgroundColor = Colors.black,
  }) : super(key: key);

  static Future<XFile?> open({
    CameraLensDirection lensDirection = CameraLensDirection.back,
    ResolutionPreset resolutionPreset = ResolutionPreset.high,
    bool enableFlashToggle = true,
    bool enableFlipCamera = true,
    Color backgroundColor = Colors.black,
  }) async {
    final cameras = await availableCameras();

    final camera = cameras.firstWhere(
          (cam) => cam.lensDirection == lensDirection,
      orElse: () => cameras.first,
    );

    return Navigator.of(MyApp.navigatorKey.currentContext!).push(
      MaterialPageRoute(
        builder: (_) =>
            LeadratCustomCamera(
              lensDirection: camera.lensDirection,
              resolutionPreset: resolutionPreset,
              enableFlashToggle: enableFlashToggle,
              enableFlipCamera: enableFlipCamera,
              backgroundColor: backgroundColor,
            ),
      ),
    );
  }

  @override
  State<LeadratCustomCamera> createState() => _LeadratCustomCameraState();
}

class _LeadratCustomCameraState extends State<LeadratCustomCamera> {
  late CameraController _controller;
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  FlashMode _flashMode = FlashMode.off;

  @override
  void initState() {
    super.initState();
    _initCamera();
  }

  Future<void> _initCamera() async {
    final status = await Permission.camera.request();
    if (!status.isGranted) {
      Navigator.of(context).pop();
      return;
    }

    final cameras = await availableCameras();
    final selectedCamera = cameras.firstWhere(
          (cam) => cam.lensDirection == widget.lensDirection,
      orElse: () => cameras.first,
    );

    _controller = CameraController(
      selectedCamera,
      widget.resolutionPreset,
      enableAudio: false,
    );

    try {
      await _controller.initialize();
      if (mounted) setState(() => _isInitialized = true);
    } catch (e) {
      debugPrint('Camera initialization failed: $e');
    }
  }

  Future<void> _takePicture() async {
    if (_isTakingPicture || !_controller.value.isInitialized) return;
    setState(() => _isTakingPicture = true);

    try {
      final file = await _controller.takePicture();
      if (mounted) Navigator.of(context).pop(file);
    } catch (e) {
      debugPrint('Capture error: $e');
    } finally {
      setState(() => _isTakingPicture = false);
    }
  }

  void _toggleFlash() async {
    final newMode = _flashMode == FlashMode.off ? FlashMode.auto : FlashMode.off;
    try {
      await _controller.setFlashMode(newMode);
      setState(() => _flashMode = newMode);
    } catch (e) {
      debugPrint('Flash toggle failed: $e');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      body: !_isInitialized
          ? const Center(child: CircularProgressIndicator())
          : Stack(
        children: [
          widget.lensDirection == CameraLensDirection.front
              ? Transform(
            alignment: Alignment.center,
            transform: Matrix4.rotationY(math.pi),
            child: CameraPreview(_controller),
          )
              : CameraPreview(_controller),
          Positioned(
            bottom: 32,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (widget.enableFlashToggle)
                  IconButton(
                    icon: Icon(
                      _flashMode == FlashMode.off ? Icons.flash_off : Icons.flash_auto,
                      color: Colors.white,
                    ),
                    onPressed: _toggleFlash,
                  ),
                GestureDetector(
                  onTap: _takePicture,
                  child: Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      border: Border.all(color: Colors.grey, width: 4),
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
