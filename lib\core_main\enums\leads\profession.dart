import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum Profession {
  none(0, "None"),
  salaried(1, "Salaried"),
  business(2, "Business"),
  selfEmployed(3, "Self Employed"),
  doctor(4, "Doctor"),
  retired(5, "Retired"),
  housewife(6, "House Wife"),
  student(7, "Student"),
  unemployed(8, "Unemployed"),
  others(9, "Others");

  final int value;
  final String description;

  const Profession(this.value, this.description);
}
