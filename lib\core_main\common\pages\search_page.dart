import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/bloc/search_bloc/search_bloc.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/app_feature.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_model.dart';
import 'package:leadrat/features/lead/presentation/widgets/leads_item_skeleton_widget.dart';
import 'package:leadrat/features/lead/presentation/widgets/leads_item_widget.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_listing_management_model.dart';
import 'package:leadrat/features/listing_management/presentation/widgets/listing_management_item_widget.dart';
import 'package:leadrat/features/projects/presentation/items/item_project_model.dart';
import 'package:leadrat/features/projects/presentation/pages/project_info_page.dart';
import 'package:leadrat/features/projects/presentation/widgets/project_item_widget.dart';
import 'package:leadrat/features/projects/presentation/widgets/projects_skeleton_view_widget.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_model.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_item_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_skeleton_view_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SearchPage extends LeadratStatefulWidget {
  final AppModule appModule;

  const SearchPage({this.appModule = AppModule.lead, super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends LeadratState<SearchPage> with TickerProviderStateMixin {
  final SearchBloc searchLeadsBloc = getIt<SearchBloc>();
  late AnimationController _animationController;
  bool isStatusVisible = false;

  @override
  void onInit() {
    super.onInit();
    searchLeadsBloc.itemListScrollController.addListener(() => searchLeadsBloc.add(ScrolledEvent()));
    searchLeadsBloc.add(SearchInitialEvent(widget.appModule));

    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
    searchLeadsBloc.add(SearchDisposeEvent());
  }

  @override
  Widget buildContent(BuildContext context) {
    final searchText = _getSearchText(widget.appModule);
    return BlocConsumer<SearchBloc, SearchState>(
      listener: (context, state) {},
      builder: (context, state) {
        List<ItemLeadModel>? leads = [];
        List<ItemPropertyModel>? properties = [];
        List<ItemProjectModel>? projects = [];
        if (widget.appModule == AppModule.lead) {
          leads = state.pageState == PageState.success ? state.leads : null;
        } else if (widget.appModule == AppModule.property) {
          properties = state.pageState == PageState.success ? state.properties : null;
        } else if (widget.appModule == AppModule.project) {
          projects = state.pageState == PageState.success ? state.projects : null;
        }
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0,
            titleSpacing: 0,
            toolbarHeight: 80,
            automaticallyImplyLeading: false,
            title: Container(
              height: 80,
              padding: const EdgeInsets.symmetric(horizontal: 14),
              decoration: const BoxDecoration(
                color: ColorPalette.darkToneInk,
                image: DecorationImage(
                  image: AssetImage(ImageResources.imageTopBarBottomLeftPatternSearchPage),
                  alignment: Alignment.centerLeft,
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(5),
                  bottomRight: Radius.circular(5),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: SvgPicture.asset(ImageResources.iconBack),
                  ),
                  const Spacer(flex: 2),
                  Text(
                    'search',
                    style: LexendTextStyles.lexend18Bold.copyWith(
                      color: ColorPalette.primary,
                      fontSize: 20,
                    ),
                  ),
                  const Spacer(flex: 3),
                ],
              ),
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.fromLTRB(24, 20, 14, 20),
                  child: TextField(
                    controller: searchLeadsBloc.searchController,
                    autofocus: true,
                    cursorColor: ColorPalette.primaryGreen,
                    maxLines: 1,
                    textInputAction: TextInputAction.newline,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 0,
                      ),
                      fillColor: const Color.fromRGBO(73, 79, 86, 0.4),
                      filled: true,
                      hintText: 'Search $searchText by name, number, email or sl.no',
                      hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray600),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: ColorPalette.primaryLightColor, width: 1.0),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: ColorPalette.primaryLightColor, width: 1.0),
                      ),
                      suffixIcon: widget.appModule == AppModule.lead
                          ? IconButton(
                              icon: Icon(
                                state.isTagsVisible ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                                size: 30, // Bigger icon
                                color: Colors.grey, // Optional: set the color
                              ),
                              onPressed: () {
                                isStatusVisible = !isStatusVisible;
                                searchLeadsBloc.add(ToggleTagsEvent(isVisible: isStatusVisible));
                              },
                            )
                          : searchLeadsBloc.searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(
                                    Icons.clear,
                                    color: ColorPalette.gray500,
                                    size: 20,
                                  ),
                                  visualDensity: const VisualDensity(vertical: -4),
                                  onPressed: () {
                                    searchLeadsBloc.add(SearchInitialEvent(widget.appModule));
                                  },
                                )
                              : UnconstrainedBox(child: SvgPicture.asset(ImageResources.iconSearch, height: 30)),
                      enabled: true,
                    ),
                    onChanged: (value) {
                      searchLeadsBloc.add(SearchLeadEvent());
                    },
                    style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.white),
                  ),
                ),
                if (state.selectedItem.isNotEmpty && state.isTagsVisible)
                  Padding(
                    padding: const EdgeInsets.only(left: 15, right: 10, top: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 15, right: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  state.isRecentSearch ? 'selected search' : 'recent Search',
                                  style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.gray500),
                                ),
                                const SizedBox(height: 8),
                                Wrap(
                                  alignment: WrapAlignment.start,
                                  spacing: 5,
                                  runSpacing: 4,
                                  children: state.selectedItem.map((label) {
                                    final nonDeletableFields = {'LeadName', 'ContactNo', 'SerialNumber'};
                                    final isDeletable = !nonDeletableFields.contains(label?.propertyName);
                                    return RawChip(
                                      label: Text(
                                        label?.displayName ?? '',
                                        style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.gray500),
                                      ),
                                      backgroundColor: ColorPalette.gray900,
                                      shape: const StadiumBorder(side: BorderSide.none),
                                      side: BorderSide.none,
                                      deleteIcon: isDeletable ? const Icon(Icons.close, size: 16, color: ColorPalette.gray500) : null,
                                      onDeleted: isDeletable ? () => searchLeadsBloc.add(RemoveSelectedItemEvent(removeItem: label)) : null,
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (state.isTagsVisible)
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.3,
                    child: Scrollbar(
                      thumbVisibility: true, // Always show scrollbar
                      radius: const Radius.circular(8),
                      thickness: 6,
                      child: SingleChildScrollView(
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            child: Wrap(
                              spacing: 5,
                              runSpacing: 8,
                              children: state.labels.map((label) {
                                return RawChip(
                                  label: Text(
                                    label?.displayName ?? '',
                                    style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary),
                                  ),
                                  backgroundColor: Colors.black,
                                  side: const BorderSide(color: ColorPalette.primary),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  onPressed: () {
                                    searchLeadsBloc.add(RemoveLabelEvent(label));
                                  },
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                if (state.isMoreThanSevenSelected && state.isTagsVisible)
                  Text(
                    "Users can select up to seven (7) parameters at a time",
                    style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.red),
                  ),
                if (state.isTagsVisible) const Spacer(),
                if (state.isTagsVisible)
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Expanded(
                        child: LeadratFormButton(
                          buttonText: 'close',
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                          child: LeadratFormButton(
                        buttonText: 'Apply',
                        onPressed: () => searchLeadsBloc.add(UpdateRecentSearchEvent()),
                      )),
                    ],
                  ),
                if (state.pageState == PageState.loading && widget.appModule == AppModule.lead && !state.isTagsVisible)
                  Expanded(
                    child: ListView.builder(
                      itemCount: 10,
                      itemBuilder: (context, index) => const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 14),
                        child: Skeletonizer(
                            containersColor: ColorPalette.gray900,
                            effect: ShimmerEffect(
                              baseColor: ColorPalette.primaryLightColor,
                              highlightColor: ColorPalette.primaryColor,
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            child: LeadsItemSkeletonWidget()),
                      ),
                    ),
                  ),
                if (state.pageState == PageState.loading && widget.appModule == AppModule.property) const Expanded(child: SkeletonViewWidget()),
                if (state.pageState == PageState.loading && widget.appModule == AppModule.project) const Expanded(child: ProjectSkeletonViewWidget()),
                if (state.pageState == PageState.failure)
                  Expanded(
                    child: Center(
                      child: Text(state.errorMessage ?? 'No $searchText found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white)),
                    ),
                  ),
                if (state.pageState == PageState.success && widget.appModule == AppModule.lead && !state.isTagsVisible)
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Text(
                            '${state.totalCount} leads found',
                            style: LexendTextStyles.lexend12Medium.copyWith(
                              color: ColorPalette.primary,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        _buildLeadItems(state, leads),
                      ],
                    ),
                  ),
                if (state.pageState == PageState.success && widget.appModule == AppModule.project)
                  projects != null && projects.isNotEmpty
                      ? Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 24),
                                child: Text(
                                  '${state.totalCount} projects found',
                                  style: LexendTextStyles.lexend12Medium.copyWith(
                                    color: ColorPalette.primary,
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Expanded(
                                child: ListView.builder(
                                  controller: getIt<SearchBloc>().itemListScrollController,
                                  padding: EdgeInsets.zero,
                                  itemCount: state.loadMore ? projects.length + 1 : projects.length,
                                  itemBuilder: (context, index) {
                                    if (index < projects!.length) {
                                      return Container(
                                          margin: const EdgeInsets.only(left: 24, right: 12, bottom: 10),
                                          height: 135,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.0),
                                            border: Border(bottom: BorderSide(width: 1, color: ColorPalette.primaryGreen.withOpacity(0.24))),
                                            color: ColorPalette.gray900,
                                          ),
                                          child: ProjectItemWidget(
                                            onTapFunction: () {
                                              if (getIt<UsersDataRepository>().checkHasPermission(AppModule.project, CommandType.view)) {
                                                Navigator.push(context, MaterialPageRoute(builder: (context) => ProjectInfoPage(projectId: projects![index].id)));
                                              }
                                            },
                                            projectItem: projects[index],
                                          ));
                                    } else {
                                      return JumpingDots(color: ColorPalette.leadratGreen, radius: 4, numberOfDots: 5, animationDuration: const Duration(milliseconds: 200));
                                    }
                                  },
                                ),
                              )
                            ],
                          ),
                        )
                      : Expanded(
                          child: Center(
                            child: Text('No projects found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white)),
                          ),
                        ),
                if (state.pageState == PageState.success && widget.appModule == AppModule.property)
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Text(
                            '${state.totalCount} properties found',
                            style: LexendTextStyles.lexend12Medium.copyWith(
                              color: ColorPalette.primary,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        _buildPropertiesItems(state, properties),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getSearchText(AppModule appModule) {
    switch (appModule) {
      case AppModule.lead:
        return "lead";
      case AppModule.property:
        return "properties";
      case AppModule.project:
        return "projects";
      default:
        return "";
    }
  }

  Widget _buildLeadItems(SearchState state, List<ItemLeadModel>? leads) {
    return leads != null && leads.isNotEmpty
        ? Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: ListView.builder(
                controller: searchLeadsBloc.itemListScrollController,
                padding: EdgeInsets.zero,
                itemCount: state.loadMore ? leads.length + 1 : leads.length,
                itemBuilder: (context, index) {
                  if (index < leads.length) {
                    return LeadsItemWidget(
                      itemLeadModel: state.leads![index],
                      itemLeadCategoryModel: state.searchedLeadCategory,
                      appFeature: AppFeature.searchLeads,
                      animationController: _animationController,
                    );
                  } else {
                    return JumpingDots(color: ColorPalette.leadratGreen, radius: 4, numberOfDots: 5, animationDuration: const Duration(milliseconds: 2000));
                  }
                },
              ),
            ),
          )
        : Expanded(child: Center(child: Text('No leads found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white))));
  }

  _buildPropertiesItems(SearchState state, List<ItemPropertyModel>? properties) {
    final isPropertyListingEnabled = getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;
    if (isPropertyListingEnabled) return _buildPropertiesListingItems(state, state.propertyListings);
    return properties != null && properties.isNotEmpty
        ? Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: ListView.builder(
                controller: searchLeadsBloc.itemListScrollController,
                padding: EdgeInsets.zero,
                itemCount: state.loadMore ? properties.length + 1 : properties.length,
                itemBuilder: (context, index) {
                  if (index < properties.length) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 10, bottom: 10),
                      child: PropertyCardWidget(propertyItem: properties[index], index: index, isPropertyListingEnabled: isPropertyListingEnabled),
                    );
                  } else {
                    return JumpingDots(color: ColorPalette.leadratGreen, radius: 4, numberOfDots: 5, animationDuration: const Duration(milliseconds: 200));
                  }
                },
              ),
            ),
          )
        : Expanded(child: Center(child: Text('No property found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white))));
  }

  _buildPropertiesListingItems(SearchState state, List<ItemListingManagementModel>? properties) {
    return properties != null && properties.isNotEmpty
        ? Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: ListView.builder(
                controller: searchLeadsBloc.itemListScrollController,
                padding: EdgeInsets.zero,
                itemCount: state.loadMore ? properties.length + 1 : properties.length,
                itemBuilder: (context, index) {
                  if (index < properties.length) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 10, bottom: 10),
                      child: ListingManagementItemWidget(propertyItem: properties[index]),
                    );
                  } else {
                    return JumpingDots(color: ColorPalette.leadratGreen, radius: 4, numberOfDots: 5, animationDuration: const Duration(milliseconds: 200));
                  }
                },
              ),
            ),
          )
        : Expanded(child: Center(child: Text('No property found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white))));
  }
}
