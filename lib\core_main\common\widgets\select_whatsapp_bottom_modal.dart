import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/shapes/trapezium_painter.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/main.dart';

enum WhatsappType { regularWhatsapp, businessWhatsapp }

Future<WhatsappType?> selectWhatsappBottomModal() async {
  final context = MyApp.navigatorKey.currentState?.context;

  if (context == null) {
    debugPrint('Navigator context is null');
    return null;
  }

  final completer = Completer<WhatsappType?>();

  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -6,
            left: 0,
            right: 0,
            child: CustomPaint(
              size: Size(context.width(100), 6),
              painter: TrapeziumPainter(),
            ),
          ),
          Container(
            width: context.width(100),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            color: ColorPalette.primaryDarkColor,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "open with",
                      style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary),
                    ),
                    const SizedBox(
                      width: 40,
                      child: Divider(
                        color: ColorPalette.primary,
                        thickness: 2.0,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          completer.complete(WhatsappType.regularWhatsapp);
                        },
                        child: Column(
                          children: [
                            Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: 50,
                              margin: const EdgeInsets.only(bottom: 10),
                              child: SvgPicture.asset(
                                ImageResources.iconWhatsappLogo,
                                height: 50,
                              ),
                            ),
                            Text(
                              "Whatsapp",
                              style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          completer.complete(WhatsappType.businessWhatsapp);
                        },
                        child: Column(
                          children: [
                            Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: 50,
                              margin: const EdgeInsets.only(bottom: 10),
                              child: SvgPicture.asset(
                                ImageResources.iconBusinessWhatsappLogo,
                                height: 50,
                              ),
                            ),
                            Text(
                              "Business Whatsapp",
                              style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      );
    },
  );

  return completer.future;
}
