import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/shapes/sharp_divider_painter.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/widget_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/main.dart';

/// A bottom sheet widget that allows selection of items from a list.
///
/// This widget can be used for both single and multiple item selection.
/// It supports searching functionality and can be customized with a child widget.
///
/// Generic type [T] represents the type of the value associated with each selectable item.
class SelectableItemBottomSheet<T> extends LeadratStatefulWidget {
  /// The title of the bottom sheet.
  final String title;

  /// Callback function for single item selection.
  /// This should be provided when [isMultipleSelection] is false.
  final Function(SelectableItem<T> selectedValue)? onItemSelected;

  /// Callback function for multiple item selection.
  /// This should be provided when [isMultipleSelection] is true.
  final Function(List<SelectableItem<T>> selectedValues)? onItemsSelected;

  /// List of selectable items to display in the bottom sheet.
  final List<SelectableItem<T>> selectableItems;

  /// Initially selected item for single selection mode.
  /// This should be provided when [isMultipleSelection] is false.
  final SelectableItem<T>? selectedItem;

  /// Initially selected items for multiple selection mode.
  /// This should be provided when [isMultipleSelection] is true.
  final List<SelectableItem<T>>? initialSelectedItems;

  /// Flag to enable multiple selection mode.
  /// If true, multiple items can be selected simultaneously.
  final bool isMultipleSelection;

  /// Flag to enable search functionality.
  /// If true, a search bar will be displayed at the top of the item list.
  final bool canSearchItems;

  /// Add custom hint text to search items
  final String searchHintText;

  /// Optional custom child widget to display instead of the default selection indicator.
  final Widget? child;

  /// Optional custom child padding to add padding around a child.
  final EdgeInsets childPadding;

  /// Optional check if can user tap or not.
  final bool isEnabled;

  final bool saveSelectedValues;

  final String? disabledSnackBarMessage;

  final bool canAddNewItems;

  /// deselect single selection item
  final bool canDeselectSingleItem;

  /// Constructs a SelectableItemBottomSheet.
  ///
  /// The [title] and [selectableItems] parameters are required.
  /// Either [onItemSelected] (for single selection) or [onItemsSelected] (for multiple selection) must be provided.
  const SelectableItemBottomSheet({
    Key? key,
    required this.title,
    required this.selectableItems,
    this.onItemSelected,
    this.onItemsSelected,
    this.selectedItem,
    this.initialSelectedItems,
    this.isMultipleSelection = false,
    this.canSearchItems = false,
    this.child,
    this.childPadding = EdgeInsets.zero,
    this.searchHintText = "Search item here",
    this.isEnabled = true,
    this.saveSelectedValues = true,
    this.disabledSnackBarMessage,
    this.canAddNewItems = false,
    this.canDeselectSingleItem = false,
  })  : assert((isMultipleSelection && onItemsSelected != null) || (!isMultipleSelection && onItemSelected != null), "Provide onItemSelected for single selection or onItemsSelected for multiple selection"),
        super(key: key);

  @override
  State<StatefulWidget> createState() => _SelectableItemBottomSheetState<T>();
}

class _SelectableItemBottomSheetState<T> extends LeadratState<SelectableItemBottomSheet<T>> {
  late List<SelectableItem<T>> _items;
  late List<SelectableItem<T>> _filteredItems;
  late List<SelectableItem<T>> _selectedItems;
  late List<SelectableItem<T>> _tempSelectedItems;
  late TextEditingController _searchController;
  bool isAllSelected = false;

  @override
  void initState() {
    super.initState();

    _initializeItems();
  }

  @override
  void didUpdateWidget(SelectableItemBottomSheet<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reinitialize items if the widget's properties have changed
    if (oldWidget.selectableItems != widget.selectableItems || oldWidget.selectedItem != widget.selectedItem || oldWidget.initialSelectedItems != widget.initialSelectedItems) {
      _initializeItems();
    }
  }

  /// Initializes the items and selection states.
  /// This method is called in both initState and didUpdateWidget to ensure
  /// the widget's state is always up to date with the latest props.
  void _initializeItems() {
    _items = List.from(widget.selectableItems);
    _filteredItems = List.from(_items);
    _selectedItems = widget.isMultipleSelection ? (widget.initialSelectedItems != null ? List.from(widget.initialSelectedItems!) : []) : (widget.selectedItem != null ? [widget.selectedItem!] : []);
    _tempSelectedItems = List.from(_selectedItems);
    _searchController = TextEditingController();

    for (var item in _selectedItems) {
      final index = _items.indexWhere((element) => element.value == item.value);
      if (index != -1) {
        _items[index] = _items[index].copyWith(isSelected: true);
      }
    }
    _updateFilteredItems();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Filters the items based on the search query.
  void _searchItems(String value) {
    setState(() {
      if (value.length > 1) {
        _filteredItems = _items.where((element) => element.title.toLowerCase().contains(value.toLowerCase())).toList();
      } else {
        _updateFilteredItems();
      }
    });
  }

  /// Updates the filtered items list to reflect the current selection state.
  void _updateFilteredItems() {
    final combined = [
      ...(_tempSelectedItems),
      ..._items.where((item) => !_tempSelectedItems.any((s) => s.value == item.value)),
    ];
    final uniqueCombined = combined.toSet().toList();
    _filteredItems = uniqueCombined;
    isAllSelected = _tempSelectedItems.where((element) => element.isEnabled).length == _items.where((element) => element.isEnabled).length;
  }

  /// Toggles the selection state of an item.
  void _toggleItemSelection(SelectableItem<T> item) {
    setState(() {
      if (widget.isMultipleSelection) {
        item.isSelected = !item.isSelected;
        if (item.isSelected) {
          _tempSelectedItems.add(item);
        } else {
          _tempSelectedItems.removeWhere((element) => element.value == item.value);
        }
        isAllSelected = _tempSelectedItems.where((element) => element.isEnabled).length == _items.where((element) => element.isEnabled).length;
      } else {
        _tempSelectedItems.clear();

        if (widget.canDeselectSingleItem) {
          if (item.isSelected) {
            _tempSelectedItems.clear();
            item.isSelected = false;
          } else {
            _tempSelectedItems.clear();
            _tempSelectedItems.add(item);
            for (var element in _items) {
              element.isSelected = element.value == item.value;
            }
          }
        } else {
          _tempSelectedItems.add(item);
          for (var element in _items) {
            element.isSelected = element.value == item.value;
          }
        }
      }
    });
  }

  /// Add new item method
  void _addNewItem(String newItemTitle) {
    if (newItemTitle.trim().isEmpty) {
      return;
    }
    setState(() {
      final newItem = SelectableItem<T>(title: newItemTitle, value: "${newItemTitle}_${_items.length}" as T);
      _items.add(newItem);
      _toggleItemSelection(newItem);
      _updateFilteredItems();
    });
  }

  // Select All Items
  void _selectAllItems() {
    setState(() {
      if (isAllSelected) {
        _items = _items.map((item) => item.copyWith(isSelected: false)).toList();
        _tempSelectedItems.clear();
        isAllSelected = false;
      } else {
        _items = _items.map((item) => item.copyWith(isSelected: item.isEnabled)).toList();
        _tempSelectedItems = List<SelectableItem<T>>.from(_items.where((element) => element.isSelected));
        isAllSelected = true;
      }
    });
    _updateFilteredItems();
  }

  /// Shows the modal bottom sheet with the list of selectable items.
  void _showItemListModal(BuildContext context) {
    if ((!widget.isEnabled || _filteredItems.isEmpty) && !(widget.canAddNewItems)) {
      if (widget.disabledSnackBarMessage?.isNotNullOrEmpty() ?? false) {
        LeadratCustomSnackbar.show(navigatorKey: MyApp.navigatorKey, type: SnackbarType.error, message: widget.disabledSnackBarMessage!);
      }
      return;
    }
    FocusScope.of(context).unfocus();
    showModalBottomSheet(
      context: context,
      isDismissible: !widget.isMultipleSelection,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(0.0),
        ),
      ),
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return DraggableScrollableSheet(
                initialChildSize: 0.6,
                minChildSize: 0.5,
                maxChildSize: 0.7,
                expand: false,
                builder: (_, scrollController) {
                  return WillPopScope(
                    onWillPop: () async {
                      _tempSelectedItems = List.from(_selectedItems);
                      _updateFilteredItems();
                      return true;
                    },
                    child: Container(
                      color: ColorPalette.white,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Header
                          Container(
                            width: MediaQuery.of(context).size.width,
                            color: ColorPalette.primaryTextColor,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 20, right: 10, top: 22, bottom: 20),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    widget.title,
                                    style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.primaryDarkColor, overflow: TextOverflow.ellipsis),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      _tempSelectedItems = List.from(_selectedItems);
                                      _updateFilteredItems();
                                      Navigator.pop(context);
                                    },
                                    child: SvgPicture.asset(ImageResources.iconRoundedClose),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          CustomPaint(
                            size: const Size(double.infinity, 4),
                            painter: SharpDividerPainter(),
                          ),
                          // Search bar (if enabled)
                          if (widget.canSearchItems)
                            Padding(
                              padding: const EdgeInsets.only(left: 24, right: 14, top: 10, bottom: 10),
                              child: TextField(
                                controller: _searchController,
                                cursorColor: ColorPalette.primaryGreen,
                                style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryColor),
                                onChanged: (value) {
                                  setState(() {
                                    _searchItems(value);
                                  });
                                },
                                decoration: InputDecoration(
                                  hintText: widget.searchHintText,
                                  hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray500),
                                  prefixIcon: const Icon(Icons.search, color: ColorPalette.gray500, size: 20),
                                  suffixIcon: _searchController.text.isNotEmpty
                                      ? IconButton(
                                          icon: const Icon(
                                            Icons.clear,
                                            color: ColorPalette.gray500,
                                            size: 20,
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              _searchController.clear();
                                              _updateFilteredItems();
                                            });
                                          },
                                        )
                                      : null,
                                ),
                              ),
                            ),
                          // List of items
                          Expanded(
                            child: (_filteredItems.isEmpty)
                                ? _buildEmptyWidget()
                                : Column(
                                    children: [
                                      if (widget.isMultipleSelection) ...[
                                        ListTile(
                                          contentPadding: const EdgeInsets.only(left: 24, right: 14),
                                          title: Text(
                                            "Select All ${_tempSelectedItems.isNotEmpty ? '(${_tempSelectedItems.length})' : ''}",
                                            style: LexendTextStyles.lexend12Medium.copyWith(color: isAllSelected ? ColorPalette.primaryGreen : ColorPalette.darkGray),
                                          ),
                                          onTap: () {
                                            setState(() {
                                              _selectAllItems();
                                            });
                                          },
                                          trailing: Checkbox(
                                            value: isAllSelected,
                                            activeColor: ColorPalette.primaryGreen,
                                            visualDensity: const VisualDensity(vertical: -4, horizontal: -4),
                                            onChanged: (value) {
                                              setState(() {
                                                _selectAllItems();
                                              });
                                            },
                                          ),
                                        ),
                                        const Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Divider(thickness: .2, height: .5),
                                        ),
                                      ],
                                      Expanded(
                                        child: ListView.builder(
                                          controller: scrollController,
                                          itemCount: _filteredItems.length,
                                          itemBuilder: (context, index) {
                                            final item = _filteredItems[index];
                                            return Column(
                                              children: [
                                                ListTile(
                                                  contentPadding: const EdgeInsets.only(left: 24, right: 14),
                                                  title: Text.rich(
                                                    TextSpan(children: [
                                                      TextSpan(
                                                        text: item.title,
                                                        style: LexendTextStyles.lexend12Regular.copyWith(
                                                            color: _tempSelectedItems.contains(item)
                                                                ? ColorPalette.primaryGreen
                                                                : (item.isEnabled)
                                                                    ? ColorPalette.darkGray
                                                                    : ColorPalette.gray500),
                                                      ),
                                                      if (!item.isEnabled) TextSpan(text: "  (disabled)", style: LexendTextStyles.lexend10Light.copyWith(color: _tempSelectedItems.contains(item) ? ColorPalette.primaryGreen : ColorPalette.fadedRed)),
                                                    ]),
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                  trailing: _tempSelectedItems.contains(item)
                                                      ? Container(
                                                          alignment: Alignment.center,
                                                          width: 22,
                                                          height: 22,
                                                          margin: const EdgeInsets.only(right: 8),
                                                          decoration: BoxDecoration(
                                                            borderRadius: BorderRadius.circular(100),
                                                            border: Border.all(color: ColorPalette.primaryGreen, width: 1.5),
                                                          ),
                                                          child: const Icon(
                                                            Icons.check_rounded,
                                                            color: ColorPalette.primaryGreen,
                                                            size: 16,
                                                          ),
                                                        )
                                                      : null,
                                                  onTap: () {
                                                    FocusScope.of(context).unfocus();
                                                    if (!item.isEnabled) return;
                                                    setState(() {
                                                      _toggleItemSelection(item);
                                                    });
                                                    if (!widget.isMultipleSelection) {
                                                      widget.onItemSelected?.call(item);
                                                      Navigator.pop(context);
                                                    }
                                                  },
                                                ),
                                                if (index != _filteredItems.length - 1)
                                                  const Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 10),
                                                    child: Divider(
                                                      thickness: .2,
                                                      height: .5,
                                                    ),
                                                  ),
                                              ],
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                          // Footer buttons for multiple selection mode
                          if (widget.isMultipleSelection)
                            Container(
                              padding: const EdgeInsets.only(left: 24, right: 14, top: 10, bottom: 10),
                              decoration: BoxDecoration(color: ColorPalette.white, boxShadow: [
                                BoxShadow(
                                  color: ColorPalette.gray500.withOpacity(.4),
                                  spreadRadius: 5,
                                  blurRadius: 7,
                                  offset: const Offset(0, 3),
                                )
                              ]),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                      onPressed: () {
                                        _tempSelectedItems = List.from(_selectedItems);
                                        _updateFilteredItems();
                                        FocusScope.of(context).unfocus();
                                        Navigator.pop(context);
                                      },
                                      child: Text(
                                        "Cancel",
                                        style: LexendTextStyles.lexend11Medium.copyWith(
                                          color: ColorPalette.primaryColor,
                                          decoration: TextDecoration.underline,
                                        ),
                                      )),
                                  const SizedBox(width: 10),
                                  ElevatedButton(
                                    onPressed: () {
                                      _selectedItems = List.from(_tempSelectedItems);
                                      widget.onItemsSelected?.call(_selectedItems);
                                      FocusScope.of(context).unfocus();
                                      Navigator.pop(context);
                                    },
                                    style: ElevatedButton.styleFrom(shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)), backgroundColor: ColorPalette.primaryColor),
                                    child: Text('Save', style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.white)),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    ).whenComplete(
      () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
    ).then((_) {
      setState(() {
        if (widget.isMultipleSelection) {
          _tempSelectedItems = List.from(_selectedItems);
          for (var filterElement in _filteredItems) {
            filterElement.isSelected = _selectedItems.any((selectedElement) => selectedElement.value == filterElement.value || selectedElement.title == filterElement.title);
          }
        } else {
          _selectedItems = List.from(_tempSelectedItems);
        }
        _updateFilteredItems();
        isAllSelected = _tempSelectedItems.where((element) => element.isEnabled).length == _items.where((element) => element.isEnabled).length;
      });
    });
    _searchController.text = "";
    FocusManager.instance.primaryFocus?.unfocus();
  }

  @override
  Widget buildContent(BuildContext context) {
    if (widget.child != null) {
      return GestureDetector(onTap: () => _showItemListModal(context), child: widget.child!);
    }
    return GestureDetector(
      onTap: () => _showItemListModal(context),
      child: Padding(
        padding: widget.childPadding,
        child: Container(
          padding: const EdgeInsets.only(left: 24, right: 18, top: 11, bottom: 11),
          decoration: BoxDecoration(
            color: ColorPalette.superSilver,
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _selectedItems.isEmpty
                    ? "Select"
                    : _selectedItems.length == 1
                        ? _selectedItems.first.title
                        : "${_selectedItems.length} items selected",
                style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primaryDarkColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ).withFlexible(),
              const SizedBox(width: 10),
              const Icon(
                Icons.keyboard_arrow_down_rounded,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    if (widget.canAddNewItems && _searchController.text.trim().isNotEmpty) {
      return ListTile(
        contentPadding: const EdgeInsets.only(left: 24, right: 14),
        onTap: () {
          final newItemTitle = _searchController.text.trim();
          FocusManager.instance.primaryFocus?.unfocus();
          _addNewItem(newItemTitle);
          _searchController.clear();
          setState(() {});
        },
        title: Text(_searchController.text.trim(), style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.darkGray)),
        trailing: const Icon(
          Icons.add_circle_outline,
          color: ColorPalette.primaryGreen,
        ),
      );
    }
    return Center(child: Text("No data found\ntry again later", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightDarkBackground)));
  }
}

/// Represents an item that can be selected in the SelectableItemBottomSheet.
class SelectableItem<T> {
  /// The title of the selectable item.
  final String title;

  /// The isEnabled of the selectable item.
  final bool isEnabled;

  /// Indicates whether the item is currently selected.
  bool isSelected;

  /// The value associated with the selectable item.
  final T? value;

  final String? imageResource;

  SelectableItem({required this.title, this.isSelected = false, this.value, this.isEnabled = true, this.imageResource});

  /// Creates a copy of the SelectableItem with optional parameter overrides.
  SelectableItem<T> copyWith({
    String? title,
    bool? isSelected,
    T? value,
    bool? isEnabled,
    String? imageResource,
  }) {
    return SelectableItem(
      title: title ?? this.title,
      isSelected: isSelected ?? this.isSelected,
      value: value ?? this.value,
      isEnabled: isEnabled ?? this.isEnabled,
      imageResource: imageResource ?? this.imageResource,
    );
  }
}
